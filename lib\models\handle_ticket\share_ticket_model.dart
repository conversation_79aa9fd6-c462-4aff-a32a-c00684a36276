class ShareDataModel {
  final int id;
  final String? taskId;
  final String procInstId;
  final String type; 
  final String sharedUser;
  final String createdUser;
  final String createdDate;
  final bool? isDeleted;
  final String sharedUserInfo;
  final String createdUserInfo;

  ShareDataModel({
    required this.id,
    this.taskId,
    required this.procInstId,
    required this.type,
    required this.sharedUser,
    required this.createdUser,
    required this.createdDate,
    this.isDeleted,
    required this.sharedUserInfo,
    required this.createdUserInfo,
  });

  factory ShareDataModel.fromJson(Map<String, dynamic> json) {
    return ShareDataModel(
      id: json['id'],
      taskId: json['taskId'],
      procInstId: json['procInstId'],
      type: json['type'],
      sharedUser: json['sharedUser'],
      createdUser: json['createdUser'],
      createdDate: json['createdDate'],
      isDeleted: json['isDeleted'],
      sharedUserInfo: json['sharedUserInfo'],
      createdUserInfo: json['createdUserInfo'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'procInstId': procInstId,
      'type': type,
      'sharedUser': sharedUser,
      'createdUser': createdUser,
      'createdDate': createdDate,
      'isDeleted': isDeleted,
      'sharedUserInfo': sharedUserInfo,
      'createdUserInfo': createdUserInfo,
    };
  }
}

class SharePaginationModel {
  final int page;
  final int limit;
  final String? search;
  final String? sortBy;
  final String? sortType;
  final int size;
  final List<ShareDataModel> content;
  final int totalElements;
  final int number;
  final int numberOfElements;
  final int totalPages;
  final bool first;
  final bool last;

  SharePaginationModel({
    required this.page,
    required this.limit,
    this.search,
    this.sortBy,
    this.sortType,
    required this.size,
    required this.content,
    required this.totalElements,
    required this.number,
    required this.numberOfElements,
    required this.totalPages,
    required this.first,
    required this.last,
  });

  factory SharePaginationModel.fromJson(Map<String, dynamic> json) {
    var contentList = json['content'] as List;
    List<ShareDataModel> content = contentList.map((item) => ShareDataModel.fromJson(item)).toList();

    return SharePaginationModel(
      page: json['page'] ?? 0,
      limit: json['limit'] ?? 0,
      search: json['search'],
      sortBy: json['sortBy'],
      sortType: json['sortType'],
      size: json['size'] ?? 0,
      content: content,
      totalElements: json['totalElements'] ?? 0,
      number: json['number'] ?? 0,
      numberOfElements: json['numberOfElements'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      first: json['first'] ?? false,
      last: json['last'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'size': size,
      'content': content.map((e) => e.toJson()).toList(),
      'totalElements': totalElements,
      'number': number,
      'numberOfElements': numberOfElements,
      'totalPages': totalPages,
      'first': first,
      'last': last,
    };
  }
}

class ShareResponseModel {
  final int code;
  final SharePaginationModel data;
  final String message;

  ShareResponseModel({
    required this.code,
    required this.data,
    required this.message,
  });

  factory ShareResponseModel.fromJson(Map<String, dynamic> json) {
    return ShareResponseModel(
      code: json['code'],
      data: SharePaginationModel.fromJson(json['data']),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'data': data.toJson(),
      'message': message,
    };
  }
}
