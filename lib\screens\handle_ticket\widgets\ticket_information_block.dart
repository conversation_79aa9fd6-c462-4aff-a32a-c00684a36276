import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_sdk/widgets/custom_info_row.dart';

class TicketInformationBlock extends StatefulWidget {
  final String title;
  final dynamic ticket;
  const TicketInformationBlock({super.key, required this.title, this.ticket});

  @override
  State<TicketInformationBlock> createState() => _TicketInformationBlockState();
}

class _TicketInformationBlockState extends State<TicketInformationBlock> {
  late Map<String, String> ticketInformationData;
  final isTablet = DeviceUtils.isTablet;
  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    String getTicketProperty(String propertyName, [String defaultValue = '']) {
      if (widget.ticket == null) return defaultValue;

      try {
        if (widget.ticket is Map) {
          return (widget.ticket[propertyName]?.toString() ?? defaultValue);
        } else {
          return ((widget.ticket as dynamic).$propertyName?.toString() ?? defaultValue);
        }
      } catch (e) {
        debugPrint('Error accessing $propertyName: $e');
        return defaultValue;
      }
    }

    final isFromNotification = widget.ticket is Map && widget.ticket['fromNotification'] == true;

    if (isFromNotification) {
      ticketInformationData = {
        'Mã': getTicketProperty('requestCode'),
        'Tên phiếu yêu cầu': getTicketProperty('ticketTitle'),
      };
    } else if (widget.ticket is Map<String, dynamic>) {
      var ticket = widget.ticket as Map<String, dynamic>;
      ticketInformationData = {
        'Mã': ticket['requestCode']?.toString() ?? '',
        'Tên phiếu yêu cầu': ticket['ticketTitle']?.toString() ?? '',
      };
    } else if (widget.ticket is MyPycContent) {
      var ticket = widget.ticket as MyPycContent;
      ticketInformationData = {
        'Mã': ticket.requestCode ?? '',
        'Tên phiếu yêu cầu': ticket.ticketTitle ?? '',
      };
    } else if (widget.ticket is ApproveContent) {
      var ticket = widget.ticket as ApproveContent;
      ticketInformationData = {
        'Mã': ticket.requestCode ?? '',
        'Tên phiếu yêu cầu': ticket.ticketTitle ?? '',
      };
    } else {
      ticketInformationData = {
        'Mã': '',
        'Tên phiếu yêu cầu': '',
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    return isTablet
        ? Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: getColorSkin().white,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: _buildTabletContent())
        : CustomExpandedList<String>(
            expandedSvgIconPath: StringImage.ic_arrow_up,
            collapsedSvgIconPath: StringImage.ic_arrow_right,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            title: widget.title,
            cardColor: Colors.white,
            isBuildLeading: true,
            iconPath: StringImage.ticket_info,
            iconWidth: 24.w,
            iconHeight: 24.h,
            isExpanded: true,
            titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
            child: _buildFormContent(),
          );
  }

  Widget _buildFormContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...ticketInformationData.entries.map((entry) => Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: InfoRow(
                textAlign: TextAlign.left,
                label: entry.key,
                value: entry.value,
                textStyle: getTypoSkin().label4Regular.copyWith(color: getColorSkin().ink1),
              ),
            )),
      ],
    );
  }

  Widget _buildTabletContent() {
    final nonEmptyValues =
        ticketInformationData.entries.where((entry) => entry.value.isNotEmpty).map((entry) => entry.value).toList();

    return Container(
      height: 40.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          if (nonEmptyValues.isNotEmpty) Text(nonEmptyValues.join(' - ')),
        ],
      ),
    );
  }
}
