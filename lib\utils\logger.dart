import 'package:logger/logger.dart';
import 'package:intl/intl.dart';

class LoggerConfig {
  static final DateFormat _dateFormatter = DateFormat('dd/MM/yyyy HH:mm:ss.SSS');

  static String _timeFormatter(DateTime time) {
    return _dateFormatter.format(time);
  }

  static final Logger logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: _timeFormatter,
    ),
  );
}
