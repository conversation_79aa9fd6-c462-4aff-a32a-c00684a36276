import 'dart:developer' as developer;
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:intl/intl.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

String formatTimestamp(int? ms) {
  if (ms == null) return '';
  try {
    final dtUtc = DateTime.fromMillisecondsSinceEpoch(ms, isUtc: true);
    final dtLocal = dtUtc.toLocal();
    return DateFormat('dd/MM/yyyy HH:mm').format(dtLocal);
  } catch (e) {
    developer.log('Error formatting timestamp $ms: $e');
    return '';
  }
}

class InfoDialog extends StatefulWidget {
  final String title;
  final String code;
  final String ticketId;

  const InfoDialog({
    super.key,
    required this.title,
    required this.code,
    required this.ticketId,
  });

  @override
  State<InfoDialog> createState() => _InfoDialogState();
}

class _InfoDialogState extends State<InfoDialog> {
  bool _isSlaExpanded = false;
  List<StatusContentModel> ticketStatusList = [];
  List<StatusContentModel> taskStatusList = [];
  bool _isStatusTicketLoaded = false;
  bool _isStatusTaskLoaded = false;

  @override
  void initState() {
    super.initState();
    _fetchTicketStatus();
    _fetchTaskStatus();

    int? ticketIdInt = int.tryParse(widget.ticketId);
    if (ticketIdInt != null) {
      context.read<TicketProcessDetailBloc>().add(
            LoadTicketProcessDetailById(ticketId: ticketIdInt),
          );
    }
    developer.log('Ticket ID123445: $ticketIdInt');
    context.read<TicketProcessDetailBloc>().add(
          LoadUserTaskInfo(ticketId: widget.ticketId),
        );
  }

  void _fetchTicketStatus() {
    context.read<PycBloc>().add(FetchStatusTicket(
          StatusTicketRequest(
            code: "STATUS_TICKET",
            type: "system",
            page: 1,
            limit: 9999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));
  }

  void _fetchTaskStatus() {
    context.read<PycBloc>().add(FetchStatusTicket(
          StatusTicketRequest(
            code: "STATUS_TASK",
            type: "system",
            page: 1,
            limit: 9999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));
  }

  bool get isDataReady {
    final isStatusLoaded = _isStatusTicketLoaded && _isStatusTaskLoaded;

    final isTicketLoaded = context.read<TicketProcessDetailBloc>().state is TicketProcessDetailByIdLoaded;

    return isStatusLoaded && isTicketLoaded;
  }

  String getStatusDisplayName(String? statusCode, bool isTask) {
    if (statusCode == null) return '';
    final statusList = isTask ? taskStatusList : ticketStatusList;
    final status = statusList.firstWhere(
      (s) => s.configValue == statusCode,
      orElse: () => StatusContentModel(),
    );
    return status.configName ?? statusCode;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<PycBloc, PycState>(
          listenWhen: (prev, curr) =>
              prev.statusTicketStatus != curr.statusTicketStatus ||
              prev.statusTicketResponseModel != curr.statusTicketResponseModel,
          listener: (context, state) {
            if (state.statusTicketStatus == PycStatus.loaded &&
                state.statusTicketResponseModel != null &&
                state.statusTicketRequestModel != null) {
              final code = state.statusTicketRequestModel!.code;
              final list = state.statusTicketResponseModel!.data.content ?? [];
              if (code == "STATUS_TICKET") {
                setState(() {
                  ticketStatusList = list;
                  _isStatusTicketLoaded = true;
                });
              } else if (code == "STATUS_TASK") {
                setState(() {
                  taskStatusList = list;
                  _isStatusTaskLoaded = true;
                });
              }
            }
          },
        ),
      ],
      child: Dialog(
        backgroundColor: getColorSkin().white,
        insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: getColorSkin().grey4Background,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(widget.title, style: getTypoSkin().medium20),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Icon(Icons.close, color: getColorSkin().black, size: 24.sp),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  child: !isDataReady
                      ? Center(child: AppConstraint.buildLoading(context))
                      : BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
                          builder: (context, state) {
                            if (state is TicketProcessDetailByIdLoaded) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildInfoSection(state.thongTinChungModel),
                                  SizedBox(height: 8.h),
                                  InkWell(
                                    onTap: () {
                                      setState(() => _isSlaExpanded = !_isSlaExpanded);
                                    },
                                    child: Row(
                                      children: [
                                        Text(
                                          _isSlaExpanded ? "Ẩn thông tin SLA" : "Hiện thông tin SLA",
                                          style: getTypoSkin().title5Medium.copyWith(color: getColorSkin().primaryBlue),
                                        ),
                                        SizedBox(width: 4.w),
                                        Icon(
                                          _isSlaExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                          color: getColorSkin().primaryBlue,
                                          size: 18.sp,
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (_isSlaExpanded) _buildSlaSection(state.thongTinChungModel),
                                ],
                              );
                            } else if (state is TicketProcessDetailError) {
                              return Center(
                                child: Text(
                                  'Error: ${state.message}',
                                  style: getTypoSkin().body2Regular.copyWith(color: Colors.red),
                                ),
                              );
                            }

                            return Center(child: AppConstraint.buildLoading(context));
                          },
                        ),
                ),
              ),
            ),
            // Close button
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Align(
                alignment: Alignment.centerRight,
                child: SizedBox(
                  width: 100.w,
                  height: 40.h,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: getColorSkin().primaryBlue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text('Đóng', style: getTypoSkin().title5Medium.copyWith(color: getColorSkin().white)),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(ThongTinChungModel data) {
    return BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
      builder: (context, state) {
        List<Map<String, String>> infoItems = [];

        if (widget.code == 'GENERAL_INFO') {
          final createdMs = data.ticketCreatedTime;
          final formattedCreatedTime = formatTimestamp(createdMs);
          String creator = data.fullName ?? '';
          if (state is UserTaskInfoLoaded) {
            final c = state.getCreator();
            if (c != null) creator = '${c.username} - ${c.fullName} - ${c.title}';
          }
          final statusName = getStatusDisplayName(data.ticketStatus, false);

          infoItems = [
            {'label': 'Tên phiếu yêu cầu', 'value': data.ticketTitle ?? ''},
            {'label': 'Trạng thái', 'value': statusName},
            {'label': 'Thời gian đề trình', 'value': formattedCreatedTime},
            {'label': 'Loại tờ trình', 'value': data.submissionTypeName ?? ''},
            {'label': 'Loại yêu cầu', 'value': data.procServiceName ?? ''},
            {'label': 'Người tạo', 'value': creator},
            {'label': 'Mã công ty', 'value': data.companyCode ?? ''},
            {'label': 'Mức độ ưu tiên', 'value': data.priority ?? ''},
          ];
        } else if (widget.code == 'STEP_DETAIL_INFO') {
          final taskList = data.ticketTaskDtoList;
          final currentTask = taskList.isNotEmpty == true ? taskList[0] : null;
          final startMs = currentTask?.taskStartedTime;
          final formattedStart = formatTimestamp(startMs);

          String assignee = currentTask?.taskAssignee ?? '';
          if (state is UserTaskInfoLoaded) {
            final a = state.getCurrentAssignee();
            if (a != null) assignee = '${a.username} - ${a.fullName} - ${a.title}';
          }
          final taskStatusName = getStatusDisplayName(currentTask?.taskStatus, true);

          infoItems = [
            {'label': 'Tên bước', 'value': currentTask?.taskName ?? ''},
            {'label': 'Người ký', 'value': assignee},
            {'label': 'Trạng thái', 'value': taskStatusName},
            {'label': 'Thời gian tiếp nhận', 'value': formattedStart},
            {'label': 'Mã công ty', 'value': data.companyCode ?? ''},
            {'label': 'Mức độ ưu tiên', 'value': data.priority ?? 'Trung bình'},
            {'label': 'Loại tờ trình', 'value': data.submissionTypeName ?? ''},
            {'label': 'Người thực hiện', 'value': assignee},
          ];
        }

        if (state is TicketProcessDetailLoading) {
          return Center(child: AppConstraint.buildLoading(context));
        }

        return Column(
          children: infoItems.map((item) => _buildInfoRow(item['label']!, item['value']!)).toList(),
        );
      },
    );
  }

  Widget _buildSlaSection(ThongTinChungModel data) {
    List<Map<String, String>> slaItems = [];

    if (widget.code == 'GENERAL_INFO') {
      final resp = data.slaResponse;
      String slaHours = '';
      if (resp != null) {
        try {
          final val = resp is double ? resp : double.parse(resp.toString());
          slaHours = val % 1 == 0 ? '${val.toInt()}h' : '${val}h';
        } catch (e) {
          developer.log('Error parsing slaResponse: $e');
        }
      }
      final finishMs = data.slaFinishTimeProcess;
      final expected = formatTimestamp(finishMs);
      slaItems = [
        {'label': 'Cam kết hoàn thành', 'value': slaHours},
        {'label': 'Hoàn thành dự kiến', 'value': expected},
        {'label': 'Hoàn thành thực tế', 'value': ''},
      ];
    } else if (widget.code == 'STEP_DETAIL_INFO') {
      final finishVal = data.slaFinish;
      String slaFinishHours = '';
      if (finishVal != null) {
        try {
          final val = finishVal is double ? finishVal : double.parse(finishVal.toString());
          slaFinishHours = val % 1 == 0 ? '${val.toInt()}h' : '${val}h';
        } catch (e) {
          developer.log('Error parsing slaFinish: $e');
        }
      }
      final finishMs = data.slaFinishTime;
      final expected = formatTimestamp(finishMs);
      slaItems = [
        {'label': 'Cam kết hoàn thành', 'value': slaFinishHours},
        {'label': 'Hoàn thành dự kiến', 'value': expected},
        {'label': 'Hoàn thành thực tế', 'value': ''},
      ];
    }

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        border: Border.all(color: getColorSkin().grey4Background, width: 1.h),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.h),
            decoration: BoxDecoration(
              color: getColorSkin().grey4Background,
              borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
            ),
            child: Text('Thời gian hoàn thành', style: getTypoSkin().title5Medium),
          ),
          ...slaItems.map((item) => _buildInfoRow(item['label']!, item['value']!)),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final isStatus = label == 'Trạng thái';
    Color? bg, fg;

    if (isStatus) {
      String? code;
      if (widget.code == 'GENERAL_INFO') {
        code = BlocProvider.of<TicketProcessDetailBloc>(context).state is TicketProcessDetailByIdLoaded
            ? (BlocProvider.of<TicketProcessDetailBloc>(context).state as TicketProcessDetailByIdLoaded)
                .thongTinChungModel
                .ticketStatus
            : null;
      } else {
        if (BlocProvider.of<TicketProcessDetailBloc>(context).state is TicketProcessDetailByIdLoaded) {
          final data = (BlocProvider.of<TicketProcessDetailBloc>(context).state as TicketProcessDetailByIdLoaded)
              .thongTinChungModel;
          final tasks = data.ticketTaskDtoList;
          final t = tasks.isNotEmpty == true ? tasks[0] : null;
          code = t?.taskStatus;
        }
      }

      final pyc = context.read<PycBloc>().state;
      bg = pyc.getStatusBackgroundColor(code);
      fg = pyc.getStatusColor(code);
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("$label:", style: getTypoSkin().body2Regular.copyWith(color: getColorSkin().ink1)),
          SizedBox(width: 8.w),
          if (isStatus)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
              decoration: BoxDecoration(color: bg, borderRadius: BorderRadius.circular(24.r)),
              child: Text(value, style: getTypoSkin().body2Regular.copyWith(color: fg), textAlign: TextAlign.center),
            )
          else
            Expanded(
              child: Text(value, style: getTypoSkin().body2Regular),
            ),
        ],
      ),
    );
  }
}
