class CurrencyRequestModel {
  int? page;
  int? size;
  int? limit;
  String? sortBy;
  String? sortType;

  CurrencyRequestModel(
      {this.page, this.size, this.limit, this.sortBy, this.sortType});

  CurrencyRequestModel.fromJson(Map<String, dynamic> json) {
    page = json['page'];
    size = json['size'];
    limit = json['limit'];
    sortBy = json['sortBy'];
    sortType = json['sortType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['page'] = this.page;
    data['size'] = this.size;
    data['limit'] = this.limit;
    data['sortBy'] = this.sortBy;
    data['sortType'] = this.sortType;
    return data;
  }
}
