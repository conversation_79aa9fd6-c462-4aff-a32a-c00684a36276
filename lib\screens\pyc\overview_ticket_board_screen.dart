import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:eapprove/blocs/account/user_list_state.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/chart/chart_event.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/blocs/feature/feature_bloc.dart';
import 'package:eapprove/blocs/feature/feature_event.dart';
import 'package:eapprove/blocs/feature/feature_state.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/models/feature_respone_model.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/pyc/widgets/status_card_model.dart';
import 'package:eapprove/screens/pyc/widgets/status_grid.dart';
import 'package:eapprove/screens/pyc/widgets/tab_bar_over_test.dart';
import 'package:eapprove/screens/setting_screen.dart';
import 'package:eapprove/services/global_navigation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:developer';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';

class OverviewTicketScreen extends StatefulWidget {
  final bool? isEmbedded;

  const OverviewTicketScreen({super.key, this.isEmbedded});

  @override
  State<OverviewTicketScreen> createState() => _OverviewTicketScreenState();
}

class _OverviewTicketScreenState extends State<OverviewTicketScreen> {
  final List<String> filterTabs = const [
    'Của tôi',
    'Thực hiện',
    'Phê duyệt',
    'Trợ lý',
  ];
  bool _isFirstLoad = true;
  bool _isAdmin = false;
  final Map<int, bool> _tabVisibility = {
    0: true, // Của tôi luôn hiển thị
    1: false, // Thực hiện
    2: true, // Phê duyệt
    3: false, // Trợ lý
  };
  Feature? _featureData;
  
  @override
  void initState() {
    super.initState();
    if (_isFirstLoad) {
      _loadData();
      context.read<UserListBloc>().add(CheckUserRoles());
      context.read<FeatureBloc>().add(const LoadFeatures());
      _isFirstLoad = false;
    }
    
    context.read<UserListBloc>().stream.listen((state) {
      if (state is UserListLoaded) {
        setState(() {
          _isAdmin = state.isAdmin;
        });
        _updateTabVisibility(context.read<PycBloc>().state);
        _updateFeatureVisibility();
      }
    });
  }

  void _updateTabVisibility(PycState state) {
    if (_featureData != null) {
      setState(() {
        if (_isAdmin) {
          _tabVisibility[0] = true; // My Ticket
          _tabVisibility[1] = true; // Execution
          _tabVisibility[2] = true; // Approval
          _tabVisibility[3] = true; // Assistant
        } else {
          _tabVisibility[0] = true; // My Ticket luôn hiển thị
          _tabVisibility[2] = true; // Approval luôn hiển thị
          
          final featureCheckData = _featureData?.data?.featureCheckData;
          
          _tabVisibility[1] = (featureCheckData?.countDataByBpmExecution ?? 0) > 0;
          _tabVisibility[3] = (featureCheckData?.countDataByBpmAssistant ?? 0) > 0;
          
          log("Tab visibility updated based on API counts:");
          log("Tab 1 (Execution): ${_tabVisibility[1]}, count: ${featureCheckData?.countDataByBpmExecution}");
          log("Tab 3 (Assistant): ${_tabVisibility[3]}, count: ${featureCheckData?.countDataByBpmAssistant}");
        }
      });
    } else {
      if (_isAdmin) {
        _tabVisibility[1] = true;
        _tabVisibility[3] = true;
      } else {
        final my = state.myPycCountResponseModel?.data;
        final ex = state.procCountResponseModel?.data;
        final assist = state.assisPycCountResponseModel?.data;

        _tabVisibility[1] = ex != null && (ex.ongoing! > 0 || ex.completed! > 0 || ex.recalled! > 0 || ex.cancel! > 0) ||
            state.filterList.where((item) => item.type == 'execution' && item.status == 'pin').isNotEmpty;
        _tabVisibility[3] = assist != null && assist.any((e) => (e.total ?? 0) > 0) ||
            state.filterList.where((item) => item.type == 'assistant' && item.status == 'pin').isNotEmpty;
      }
    }
  }

  void _updateFeatureVisibility() {
    final pycState = context.read<PycBloc>().state;

    final myTicketCount = pycState.myPycCountResponseModel?.data;
    final executionCount = pycState.procCountResponseModel?.data;
    final approvalCount = pycState.appCountResponseModel?.data;
    final assistCount = pycState.assisPycCountResponseModel?.data;

    final filterCounts = {
      1: pycState.filterList.where((item) => item.type == 'ticket' && item.status == "pin").length,
      2: pycState.filterList.where((item) => item.type == 'execution' && item.status == "pin").length,
      3: pycState.filterList.where((item) => item.type == 'approval' && item.status == "pin").length,
      4: pycState.filterList.where((item) => item.type == 'assistant' && item.status == "pin").length,
    };

    context.read<FeatureBloc>().add(UpdateFeatureVisibility(
          isAdmin: _isAdmin,
          pycCounts: {
            'myTicketCount': myTicketCount,
            'executionCount': executionCount,
            'approvalCount': approvalCount,
            'assistCount': assistCount,
            'filterCounts': filterCounts,
          },
        ));
  }

  void _loadData() {
    Box box = Hive.box('authentication');
    final username = box.get('username', defaultValue: '');
    final bloc = context.read<PycBloc>();
    debugPrint('Loading data...');
    bloc.add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
    bloc.add(FetchProcAppCount(ProcAppCountRequestModel(
      type: "EXECUTION",
      filterChangeAssignee: false,
      search: "",
    )));
    bloc.add(FetchProcAppCount(ProcAppCountRequestModel(
      type: "APPROVAL",
      filterChangeAssignee: false,
      search: "",
    )));
    bloc.add(FetchAssisPycCount(AssisPycCountRequestModel(
      assistantEmail: username,
      searchKey: "",
    )));
    for (final type in ['ticket', 'execution', 'approval', 'assistant']) {
      bloc.add(FetchFilter(FilterRequestModel(type: type)));
    }
    context.read<FormBloc>().add(GetListBaseUrl());
    context.read<ChartBloc>().add(FetchChartsByUser());
    context.read<UserListBloc>().add(FetchUserInfoByToken());
  }

  void _loadTab(int index) {
    debugPrint("🔄 Reloading data for tab: $index");
    final bloc = context.read<PycBloc>();
    Box box = Hive.box('authentication');
    final username = box.get('username', defaultValue: '');
    switch (index) {
      case 0:
        bloc.add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
        break;
      case 1:
        bloc.add(FetchProcAppCount(
          ProcAppCountRequestModel(
            type: "EXECUTION",
            filterChangeAssignee: false,
            search: "",
          ),
        ));
        break;
      case 2:
        bloc.add(FetchProcAppCount(
          ProcAppCountRequestModel(
            type: "APPROVAL",
            filterChangeAssignee: false,
            search: "",
          ),
        ));
        break;
      case 3:
        bloc.add(FetchAssisPycCount(
          AssisPycCountRequestModel(
            assistantEmail: username,
            searchKey: "",
          ),
        ));
        break;
    }

    final type = _mapFilterIndexToType(index);
    if (type != null) {
      bloc.add(FetchFilter(FilterRequestModel(type: type)));
    }
  }

  List<RequestStatusCardModel> buildCardsByFilterIndex({
    required int index,
    required PycState state,
  }) {
    final Map<String, int> countMap;

    switch (index) {
      case 0:
        final d = state.myPycCountResponseModel?.data;
        countMap = {
          'ONGOING': d?.ongoing ?? 0,
          'COMPLETED': d?.completed ?? 0,
          'RECALLED': d?.recalled ?? 0,
          'CANCEL': d?.cancel ?? 0,
          'DRAFT': d?.draft ?? 0,
          'SHARED': d?.shared ?? 0,
          'SHARE': d?.share ?? 0,
        };
        break;

      case 1:
        final d = state.procCountRequestModel?.type == 'EXECUTION' ? state.procCountResponseModel?.data : null;
        countMap = {
          'PROCESSING': d?.ongoing ?? 0,
          'COMPLETED': d?.completed ?? 0,
          'RECALLED': d?.recalled ?? 0,
          'CANCEL': d?.cancel ?? 0,
        };
        break;

      case 2:
        final d = state.appCountRequestModel?.type == 'APPROVAL' ? state.appCountResponseModel?.data : null;
        countMap = {
          'APPROVAL': d?.ongoing ?? 0,
          'APPROVED': d?.completed ?? 0,
          'RECALLED': d?.recalled ?? 0,
          'CANCEL': d?.cancel ?? 0,
        };
        break;

      case 3:
        final assisList = state.assisPycCountResponseModel?.data ?? [];

        countMap = {
          for (final e in assisList) (e.tab ?? '').toUpperCase(): e.total ?? 0,
        };

        final orderedStatusKeys = [
          'PROCESSING',
          'COMPLETE',
          'CANCEL',
          'SHARED',
        ];

        final cards = orderedStatusKeys
            .map((status) => RequestStatusCardModel.fromStatusKey(
                  status,
                  countMap[status] ?? 0,
                ))
            .toList();

        final filterType = _mapFilterIndexToType(index);

        final pinnedFilterCount =
            state.filterList.where((item) => item.type == filterType && item.status == "pin").length;
        cards.add(RequestStatusCardModel(
          title: "Bộ lọc",
          count: pinnedFilterCount,
          icon: FIconData.icBlueFilter,
        ));

        return cards;

      default:
        countMap = {};
    }

    final cards = countMap.entries
        .map(
          (e) => RequestStatusCardModel.fromStatusKey(e.key, e.value),
        )
        .toList();
    final filterType = _mapFilterIndexToType(index);

    final pinnedFilterCount = state.filterList.where((item) => item.type == filterType && item.status == "pin").length;
    cards.add(RequestStatusCardModel(
      title: "Bộ lọc",
      count: pinnedFilterCount,
      icon: FIconData.icBlueFilter,
    ));

    return cards;
  }

  String? _mapFilterIndexToType(int index) {
    switch (index) {
      case 0:
        return 'ticket';
      case 1:
        return 'execution';
      case 2:
        return 'approval';
      case 3:
        return 'assistant';
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: CustomAppBar(
            automaticallyImplyLeading: widget.isEmbedded ?? false,
            title: 'Phiếu yêu cầu',
            textColor: getColorSkin().ink1,
            titleTextStyle: getTypoSkin().medium20,
            titleSpacing: -10.w,
            actions: [
              GestureDetector(
                onTap: () {
                  context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EAppSettingScreen(),
                    ),
                  ).then((_) {
                    context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
                  });
                },
                child: SvgPicture.asset(StringImage.ic_user),
              ),
            ],
            actionsPadding: EdgeInsets.only(right: 18.w),
            backgroundColor: Colors.transparent,
            leading: IconButton(
              onPressed: () => GlobalNavigation().handleBackNavigation(context),
              icon: SvgPicture.asset(StringImage.ic_arrow_left),
            )),
        body: MultiBlocListener(
          listeners: [
            BlocListener<AuthenticationBloc, AuthenticationState>(
              listener: (context, state) {
                if (state is TokenSuccess && !_isFirstLoad) {
                  _loadData();
                }
              },
            ),
            BlocListener<UserListBloc, UserListState>(
              listener: (context, state) {
                if (state is UserListLoaded) {
                  setState(() {
                    _isAdmin = state.isAdmin;
                    _updateTabVisibility(context.read<PycBloc>().state);
                  });
                  _updateFeatureVisibility();
                }
              },
            ),
            BlocListener<FeatureBloc, FeatureState>(
              listener: (context, state) {
                if (state is FeatureLoaded) {
                  setState(() {
                    _featureData = state.featureData;
                    
                    if (_isAdmin) {
                      _tabVisibility[0] = true; // My Ticket
                      _tabVisibility[1] = true; // Execution
                      _tabVisibility[2] = true; // Approval
                      _tabVisibility[3] = true; // Assistant
                    } else {
                      _tabVisibility[0] = true; // My Ticket luôn hiển thị
                      _tabVisibility[2] = true; // Approval luôn hiển thị
                      
                      final featureCheckData = _featureData?.data?.featureCheckData;
                      
                      _tabVisibility[1] = (featureCheckData?.countDataByBpmExecution ?? 0) > 0;
                      _tabVisibility[3] = (featureCheckData?.countDataByBpmAssistant ?? 0) > 0;
                      
                      log("Tab visibility updated from FeatureBloc:");
                      log("Tab 1 (Execution): ${_tabVisibility[1]}, count: ${featureCheckData?.countDataByBpmExecution}");
                      log("Tab 3 (Assistant): ${_tabVisibility[3]}, count: ${featureCheckData?.countDataByBpmAssistant}");
                    }
                  });
                }
              },
            ),
          ],
          child: BlocBuilder<PycBloc, PycState>(
            builder: (context, state) {
              
              final List<Widget> tabContents = [];
              final List<String> visibleTitles = [];

              for (int i = 0; i < filterTabs.length; i++) {
                if (_tabVisibility[i] == true) {
                  visibleTitles.add(filterTabs[i]);
                  final cards = buildCardsByFilterIndex(index: i, state: state);
                  tabContents.add(StatusGrid(cards: cards, filterIndex: i));
                }
              }
              
              // Find the index of "Phê duyệt" in visibleTitles
              int initialIndex = visibleTitles.indexOf("Phê duyệt");
              // If "Phê duyệt" is not found, default to 0
              if (initialIndex == -1) {
                initialIndex = 0;
              }

              return Padding(
                padding: const EdgeInsets.only(top: 16),
                child: CustomTabBarOverTest(
                  tabTitles: visibleTitles,
                  tabContents: tabContents,
                  selectedBackgroundColor: getColorSkin().secondaryColor1TagBackground,
                  unselectedBackgroundColor: getColorSkin().grey3Background,
                  selectedTextColor: getColorSkin().secondaryColor1,
                  unselectedTextColor: getColorSkin().subtitle,
                  borderRadius: 8,
                  tabPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  tabBarPadding: EdgeInsets.symmetric(horizontal: 16.w),
                  contentPadding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 8.h),
                  lazyLoad: false,
                  initialIndex: initialIndex,
                  onTabChanged: (tabIndex) {
                    final actualIndex = _tabVisibility.entries.where((entry) => entry.value).elementAt(tabIndex).key;
                    _loadTab(actualIndex);
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
