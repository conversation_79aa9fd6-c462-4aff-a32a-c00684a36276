import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';

abstract class TicketDialogActionEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class ShowLoadingEvent extends TicketDialogActionEvent {}

class TicketDialogActionInitialEvent extends TicketDialogActionEvent {}

class UploadFileEvent extends TicketDialogActionEvent {
  final List<PlatformFile> files;
  final void Function(List<dynamic>) onSuccess;
  final void Function(String) onError;

  UploadFileEvent(this.files, {required this.onSuccess, required this.onError});

  @override
  List<Object?> get props => [files];
}

class CancelDraftEvent extends TicketDialogActionEvent {
  final String ticketProcId;
  final String reason;
  final String? filePath;

  CancelDraftEvent({
    required this.ticketProcId,
    required this.reason,
    this.filePath,
  });

  @override
  List<Object?> get props => [ticketProcId, reason, filePath];
}

class CancelSubmitEvent extends TicketDialogActionEvent {
  final String ticketProcId;
  final String taskDefKey;
  final int ticketId;
  final String reason;
  final List<String> filePaths;

  CancelSubmitEvent({
    required this.ticketProcId,
    required this.taskDefKey,
    required this.ticketId,
    required this.reason,
    required this.filePaths,
  });

  @override
  List<Object?> get props => [ticketProcId, taskDefKey, ticketId, reason, filePaths];
}

class ReturnDraftEvent extends TicketDialogActionEvent {
  final Map<String, dynamic>? otherVariables;
  final String ticketProcId;
  final String reason;
  final String? filePath;

  ReturnDraftEvent({
    this.otherVariables,
    required this.ticketProcId,
    required this.reason,
    this.filePath,
  });

  @override
  List<Object?> get props => [ticketProcId, reason, filePath];
}

class ReturnSubmitEvent extends TicketDialogActionEvent {
  final String procInstId;
  final String procDefId;
  final String taskDefKey;
  final String reason;
  final int ticketId;
  final String taskId;
  final List<String>? filePaths;
  final List<String>? fileNames;
  final List<int>? fileSizes;

  ReturnSubmitEvent({
    required this.procInstId,
    required this.procDefId,
    required this.taskDefKey,
    required this.reason,
    required this.ticketId,
    required this.taskId,
    this.filePaths,
    this.fileNames,
    this.fileSizes,
  });

  @override
  List<Object?> get props =>
      [procInstId, procDefId, taskDefKey, reason, ticketId, taskId, filePaths, fileNames, fileSizes];
}

class AuthorizeListGetListEvent extends TicketDialogActionEvent {
  final String? search;
  AuthorizeListGetListEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class AuthorizeEvent extends TicketDialogActionEvent {
  final String taskDefKey;
  final String reason;
  final String ticketId;
  final String taskId;
  final String email;
  final int id;
  final String title;

  AuthorizeEvent({
    required this.taskDefKey,
    required this.reason,
    required this.ticketId,
    required this.taskId,
    required this.email,
    required this.id,
    required this.title,
  });

  @override
  List<Object?> get props => [taskDefKey, reason, ticketId, taskId, email, id, title];
}
