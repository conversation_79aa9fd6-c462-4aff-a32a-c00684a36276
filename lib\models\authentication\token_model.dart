import 'package:eapprove/models/meta.dart';

class TokenRequest {
  final String clientId;
  final String clientSecret;
  final String grantType;
  final String code;
  final String redirectUri;

  TokenRequest({
    required this.clientId,
    required this.clientSecret,
    required this.grantType,
    required this.code,
    required this.redirectUri,
  });

  Map<String, dynamic> toJson() => {
        'client_id': clientId,
        'client_secret': clientSecret,
        'grant_type': grantType,
        'code': code,
        'redirect_uri': redirectUri,
      };
}

class RefreshTokenRequest {
  final String clientId;
  final String clientSecret;
  final String grantType;
  final String refreshToken;
  final String redirectUri;

  RefreshTokenRequest({
    required this.clientId,
    required this.clientSecret,
    required this.grantType,
    required this.refreshToken,
    required this.redirectUri,
  });

  Map<String, dynamic> toJson() => {
        'client_id': clientId,
        'client_secret': clientSecret,
        'grant_type': grantType,
        'refresh_token': refreshToken,
        'redirect_uri': redirectUri,
      };
}

class TokenResponse {
  final Meta meta;
  final TokenData? data;

  TokenResponse({
    required this.meta,
    this.data,
  });

  factory TokenResponse.fromJson(Map<String, dynamic> json) {
    return TokenResponse(
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
      data: json['data'] != null
          ? TokenData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  factory TokenResponse.error(int code, String message) {
    return TokenResponse(
      meta: Meta(code: code, message: message),
      data: null,
    );
  }
}

class TokenData {
  final String accessToken;
  final int expiresIn;
  final int refreshExpiresIn;
  final String refreshToken;
  final String tokenType;
  final int notBeforePolicy;
  final String sessionState;
  final String scope;

  TokenData({
    required this.accessToken,
    required this.expiresIn,
    required this.refreshExpiresIn,
    required this.refreshToken,
    required this.tokenType,
    required this.notBeforePolicy,
    required this.sessionState,
    required this.scope,
  });

  factory TokenData.fromJson(Map<String, dynamic> json) {
    return TokenData(
      accessToken: json['access_token'] as String,
      expiresIn: json['expires_in'] as int,
      refreshExpiresIn: json['refresh_expires_in'] as int,
      refreshToken: json['refresh_token'] as String,
      tokenType: json['token_type'] as String,
      notBeforePolicy: json['not-before-policy'] as int,
      sessionState: json['session_state'] as String,
      scope: json['scope'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'access_token': accessToken,
        'expires_in': expiresIn,
        'refresh_expires_in': refreshExpiresIn,
        'refresh_token': refreshToken,
        'token_type': tokenType,
        'not-before-policy': notBeforePolicy,
        'session_state': sessionState,
        'scope': scope,
      };

  DateTime get expiryTime {
    final now = DateTime.now();
    return now.add(Duration(seconds: expiresIn));
  }

  DateTime get refreshExpiryTime {
    final now = DateTime.now();
    return now.add(Duration(seconds: refreshExpiresIn));
  }
}
