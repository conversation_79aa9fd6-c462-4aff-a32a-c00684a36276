import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';

class CustomDropdownMultiForm extends StatefulWidget {
  final String label;
  final String placeholder;
  final List<SelectItem> options;
  final List<SelectItem> selectedItems;
  final ValueChanged<List<SelectItem>> onSelected;
  final bool isFilled;
  final BoxDecoration decoration;
  final bool isDisabled;
  final double dropdownHeight;
  final bool isRequired;
  final String? errorText;
  final bool showError;
  final EdgeInsetsGeometry? margin;
  final bool showLabel;

  const CustomDropdownMultiForm({
    this.showLabel = true,
    Key? key,
    required this.label,
    required this.placeholder,
    required this.options,
    required this.selectedItems,
    required this.onSelected,
    this.isFilled = false,
    required this.decoration,
    this.isDisabled = false,
    required this.dropdownHeight,
    this.isRequired = false,
    this.errorText,
    this.showError = false,
    this.margin,
  }) : super(key: key);

  @override
  State<CustomDropdownMultiForm> createState() => _CustomDropdownMultiFormState();
}

class _CustomDropdownMultiFormState extends State<CustomDropdownMultiForm> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isDropdownOpen = false;
  }

  void _toggleDropdown() {
    if (widget.isDisabled) return;

    if (_isDropdownOpen) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
  }

  void _showOverlay() {
    _overlayEntry = OverlayEntry(
      builder: (context) => _buildDropdownOverlay(),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isDropdownOpen = true;
  }

  Widget _buildDropdownOverlay() {
    return Positioned(
      width: MediaQuery.of(context).size.width,
      child: Column(
        children: [
          CompositedTransformFollower(
            link: _layerLink,
            showWhenUnlinked: false,
            offset: const Offset(0, 0),
            child: Material(
              elevation: 8,
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: 200.h,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: widget.options.map((option) {
                      final isSelected = widget.selectedItems
                          .any((item) => item.value == option.value);
                      return ListTile(
                        title: Text(option.label),
                        trailing: Checkbox(
                          value: isSelected,
                          onChanged: (bool? value) {
                            final newItems = List<SelectItem>.from(widget.selectedItems);
                            if (value == true) {
                              if (!newItems.any((item) => item.value == option.value)) {
                                newItems.add(option);
                              }
                            } else {
                              newItems.removeWhere((item) => item.value == option.value);
                            }
                            widget.onSelected(newItems);
                          },
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.label.isNotEmpty && widget.showLabel)
                Padding(
                  padding: EdgeInsets.only(bottom: 8.h),
                  child: Row(
                    children: [
                      Text(
                        widget.label,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (widget.isRequired)
                        Padding(
                          padding: EdgeInsets.only(left: 4.w),
                          child: Text(
                            '*',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 14.sp,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              CompositedTransformTarget(
                link: _layerLink,
                child: GestureDetector(
                  onTap: _toggleDropdown,
                  child: Container(
                    height: widget.dropdownHeight,
                    decoration: widget.decoration,
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 12.w),
                            child: _buildSelectedValues(),
                          ),
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: getColorSkin().ink1,
                        ),
                        SizedBox(width: 8.w),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          if (widget.showError && widget.errorText != null)
            Padding(
              padding: EdgeInsets.only(top: 4.h, left: 2.w),
              child: Text(
                widget.errorText!,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12.sp,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSelectedValues() {
    if (widget.selectedItems.isEmpty) {
      return Text(
        widget.placeholder,
        style: TextStyle(
          color: getColorSkin().ink3,
          fontSize: 14.sp,
        ),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: widget.selectedItems.map((item) {
          return Padding(
            padding: EdgeInsets.only(right: 4.w),
            child: Chip(
              label: Text(
                item.label,
                style: TextStyle(fontSize: 12.sp),
              ),
              onDeleted: widget.isDisabled
                  ? null
                  : () {
                      final newItems = List<SelectItem>.from(widget.selectedItems)
                        ..remove(item);
                      widget.onSelected(newItems);
                    },
              backgroundColor: getColorSkin().lightGray.withOpacity(0.3),
              deleteIconColor: getColorSkin().ink1,
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 0),
            ),
          );
        }).toList(),
      ),
    );
  }
} 