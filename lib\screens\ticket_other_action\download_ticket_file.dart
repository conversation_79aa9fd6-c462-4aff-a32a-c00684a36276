import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/attachment_list_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/attachment_list_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/attachment_list_state.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DownloadTicketFile extends StatefulWidget {
  final int ticketId;
  final String ticketStartUserId;

  const DownloadTicketFile({
    super.key,
    required this.ticketId,
    required this.ticketStartUserId
  });

  @override
  State<DownloadTicketFile> createState() => _DownloadTicketFileState();
}

class _DownloadTicketFileState extends State<DownloadTicketFile> {
  @override
  void initState() {
    super.initState();
    context
        .read<AttachmentListBloc>()
        .add(GetAttachmentList(ticketId: widget.ticketId, ticketStartUserId: widget.ticketStartUserId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttachmentListBloc, AttachmentListState>(
      buildWhen: (previous, current) {
        return previous.status != current.status ||
              previous.contentFile != current.contentFile ||
              previous.contentFileOther != current.contentFileOther ||
              previous.errorMessage != current.errorMessage;
      },
      builder: (context, state) {
        if (state.status == ServiceStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (state.status == ServiceStatus.failure) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pop();
          });
          return const SizedBox.shrink();
        }

        if (state.status == ServiceStatus.success) {
          try {
            final pdfDocument = state.contentFile.firstWhere((doc) => doc.extension.toLowerCase() == 'pdf');

            // Download the first PDF document
            pdfDocument.downloadFile().whenComplete(() {
              if (context.mounted) Navigator.of(context).pop();
            }).catchError((e) {
              LoggerConfig.logger.e('Download failed: $e');
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            });

            return const Center(child: CircularProgressIndicator());
          } catch (e) {
            LoggerConfig.logger.e('Error processing attachments: $e');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Navigator.of(context).pop();
            });
            return const SizedBox.shrink();
          }
        }

        return const SizedBox.shrink();
      },
    );
  }
}