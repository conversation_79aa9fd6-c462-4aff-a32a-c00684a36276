import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'dart:async';

class CustomBottomSheet<T> extends StatefulWidget {
  final String? title;
  final Widget? children;
  final Widget? btnBottom;
  final bool isScrollControlled;
  final Color? backgroundColor;
  final bool isDismissible;
  final bool enableDrag;
  final double? height;
  final ShapeBorder? shape;
  final List<T>? menuItems;
  final Function(T)? onMenuItemTap;
  final Widget Function(T item, Function(T)? onTap)? itemBuilder;
  final bool showDividers;
  final IconButton? closeIcon;
  final bool? isSearch;
  final String? hintSearch;
  final Function(String)? onChanged;

  const CustomBottomSheet({
    super.key,
    this.title,
    this.children,
    this.btnBottom,
    this.isScrollControlled = true,
    this.backgroundColor,
    this.isDismissible = true,
    this.enableDrag = true,
    this.height,
    this.shape,
    this.menuItems,
    this.onMenuItemTap,
    this.itemBuilder,
    this.showDividers = true,
    this.closeIcon,
    this.isSearch = false,
    this.hintSearch,
    this.onChanged,
  });

  @override
  State<CustomBottomSheet<T>> createState() => _CustomBottomSheetState<T>();

  static Future<dynamic> show<T>({
    required BuildContext context,
    String? title,
    Widget? children,
    Widget? btnBottom,
    bool isScrollControlled = true,
    Color? backgroundColor,
    bool isDismissible = true,
    bool enableDrag = true,
    double? height,
    ShapeBorder? shape,
    List<T>? menuItems,
    Function(T)? onMenuItemTap,
    Widget Function(T item, Function(T)? onTap)? itemBuilder,
    bool showDividers = true,
    IconButton? closeIcon,
    bool isSearch = false,
    String? hintSearch,
    Function(String)? onChanged,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      barrierColor: const Color(0xff000810).withOpacity(0.7),
      backgroundColor: backgroundColor ?? getColorSkin().white,
      shape: shape ??
          const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
      useSafeArea: true,
      builder: (context) {
        // var insetsHeight = MediaQuery.of(context).viewInsets.bottom;
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.84,
          child: CustomBottomSheet<T>(
            title: title,
            btnBottom: btnBottom,
            backgroundColor: backgroundColor,
            isDismissible: isDismissible,
            enableDrag: enableDrag,
            menuItems: menuItems,
            onMenuItemTap: onMenuItemTap,
            itemBuilder: itemBuilder,
            showDividers: showDividers,
            closeIcon: closeIcon,
            children: children,
            isSearch: isSearch,
            hintSearch: hintSearch,
            onChanged: onChanged,
          ),
        );
      },
    );
  }
}

class _CustomBottomSheetState<T> extends State<CustomBottomSheet<T>> {
  final TextEditingController _searchController = TextEditingController();
  List<T>? _filteredItems;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.menuItems;
  }

  void _handleSearch(String value) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        widget.onChanged?.call(value);
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildDefaultMenuItem(T item, Function(T)? onTap) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Text(
        item.toString(),
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? Colors.white,
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                if (widget.title != null) ...[
                  widget.closeIcon != null
                      ? Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => Navigator.pop(context),
                            ),
                            Expanded(
                              child: Text(
                                widget.title!,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            const SizedBox(width: 48),
                          ],
                        )
                      : Text(
                          widget.title!,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                  const SizedBox(height: 8),
                ],
                if (widget.isSearch == true)
                  CustomTextField(
                    controller: _searchController,
                    onChanged: _handleSearch,
                    hintText: widget.hintSearch ?? "Tìm kiếm",
                    prefixIcon:
                        Icon(Icons.search, color: getColorSkin().subtitle),
                    backgroundColor: getColorSkin().white,
                    onClear: () {
                      _searchController.clear();
                      _handleSearch("");
                    },
                    showClearIcon: true,
                  ),
                const SizedBox(height: 8),
                if (widget.menuItems != null)
                  ListView.separated(
                    shrinkWrap: true,
                    itemCount: _filteredItems?.length ?? 0,
                    separatorBuilder: (context, index) => widget.showDividers
                        ? Divider(color: Colors.grey.shade300)
                        : const SizedBox.shrink(),
                    itemBuilder: (context, index) => GestureDetector(
                      onTap: () =>
                          widget.onMenuItemTap?.call(_filteredItems![index]),
                      child: widget.itemBuilder?.call(
                              _filteredItems![index], widget.onMenuItemTap) ??
                          _buildDefaultMenuItem(
                              _filteredItems![index], widget.onMenuItemTap),
                    ),
                  )
                else if (widget.children != null)
                  widget.children!,
                  
                if (widget.btnBottom != null) ...[
                  const SizedBox(height: 16),
                  widget.btnBottom!,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ServiceItem {
  final String title;
  final String? iconPath;
  final bool hasChevron;
  final List<ServiceItem>? children;
  bool isExpanded;

  ServiceItem({
    required this.title,
    this.iconPath,
    this.hasChevron = false,
    this.children,
    this.isExpanded = false,
  });
  @override
  String toString() => title;
}
