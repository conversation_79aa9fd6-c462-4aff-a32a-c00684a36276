class TitleByUserResponse {
  final int code;
  final String message;
  final List<TitleByUserData> data;

  TitleByUserResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory TitleByUserResponse.fromJson(Map<String, dynamic> json) {
    return TitleByUserResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: (json['data'] as List?)
              ?.map((e) => TitleByUserData.fromJson(e))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((e) => e.toJson()).toList(),
    };
  }

  bool get isSuccess => code == 1;
}

class TitleByUserData {
  final String chartNodeLevel;
  final String title;
  final String username;

  TitleByUserData({
    required this.chartNodeLevel,
    required this.title,
    required this.username,
  });

  factory TitleByUserData.from<PERSON><PERSON>(Map<String, dynamic> json) {
    return TitleByUserData(
      chartNodeLevel: json['chartNodeLevel'] ?? '',
      title: json['title'] ?? '',
      username: json['username'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'chartNodeLevel': chartNodeLevel,
      'title': title,
      'username': username,
    };
  }
}