import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/scheduler.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_event.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/assistant_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/cancel_ticket_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/recover_request_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/assistant_opinion_event.dart';
import 'package:eapprove/blocs/ticket_other_action/event/cancel_ticket_event.dart';
import 'package:eapprove/blocs/ticket_other_action/event/recover_request_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/assistant_opinion_state.dart';
import 'package:eapprove/blocs/ticket_other_action/state/cancel_ticket_state.dart';
import 'package:eapprove/blocs/ticket_other_action/state/recover_request_state.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/authorize_tab.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/approve_tab.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_sdk/widgets/form.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:eapprove/services/api_form_service.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart' as snackbar_core;

class InfoTicketOtherActionDialog extends StatefulWidget {
  final String title;
  final String code;
  final String ticketId;
  final String ticketProcId;
  final String taskDefKey;
  final dynamic ticket;

  const InfoTicketOtherActionDialog({
    super.key,
    required this.title,
    required this.code,
    required this.ticketId,
    required this.ticketProcId,
    required this.taskDefKey,
    required this.ticket,
  });

  @override
  State<InfoTicketOtherActionDialog> createState() => _InfoTicketOtherActionDialogState();
}

class _InfoTicketOtherActionDialogState extends State<InfoTicketOtherActionDialog> {
  final _formKey = GlobalKey<FFormState>();
  final _authorizedForm = GlobalKey<FFormState>();
  final Map<String, String?> _fieldErrors = {};
  late TextEditingController _reasonController;
  final TextEditingController _delegateReasonController = TextEditingController();
  final TextEditingController _approveContent = TextEditingController();
  final bool _isDone = false;
  final bool _isSigned = false;
  final bool _isInPerformPhase = false;
  bool _isLoading = false;
  late Box box;
  late String username;
  List<PlatformFile> selectedFiles = [];
  SelectItem? _selectedDelegate;
  @override
  void initState() {
    super.initState();
    _reasonController = TextEditingController();

    // Initialize blocs
    context.read<TicketDialogActionBloc>().add(TicketDialogActionInitialEvent());
    context.read<TicketDialogActionBloc>().add(AuthorizeListGetListEvent());

    if (widget.ticket != null) {
      final processId = int.tryParse(widget.ticketId.toString()) ?? 0;
      final procDefId = widget.ticketProcId;

      developer.log('TicketOtherAction - Initializing with:', name: 'TicketOtherAction');
      developer.log('ProcessId: $processId', name: 'TicketOtherAction');
      developer.log('ProcDefId: $procDefId', name: 'TicketOtherAction');
      developer.log('Ticket: ${widget.ticket.toString()}', name: 'TicketOtherAction');

      // Load check type data
      context.read<CheckTypeBloc>().add(CheckTypeRequested(
            serviceId: processId,
            procDefId: procDefId,
          ));

      // Load approval data
      context.read<FormBloc>().add(GetApprovalVinaphacoRequested(
            procDefId: procDefId,
          ));

      // Load base URLs
      context.read<FormBloc>().add(GetListBaseUrl());

      // Load user info
      context.read<UserListBloc>().add(FetchUserInfoByToken());

      // Get signature
      _getSignature();
    } else {
      developer.log('TicketOtherAction - Widget.ticket is null', name: 'TicketOtherAction');
    }
  }

  void _getSignature() async {
    final Box authBox = Hive.box('authentication');
    final String? currentUsername = authBox.get('username');

    final user = await ApiFormService().getByUsername(currentUsername ?? "");
    final title = await ApiFormService().getTitleByUsername(currentUsername ?? "");
    developer.log('getByUsernamegetByUsername: $user', name: 'TicketOtherAction');
    developer.log('getTitleByUsernamegetTitleByUsername: $title', name: 'TicketOtherAction');
  }

  @override
  void dispose() {
    _reasonController.dispose();
    _delegateReasonController.dispose();
    _approveContent.dispose();
    super.dispose();
  }

  void _showLoadingDialog() {
    if (_isLoading) return;
    setState(() => _isLoading = true);
    showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );
  }

  void _hideLoadingDialog() {
    if (_isLoading && mounted) {
      try {
        Navigator.of(context, rootNavigator: true).pop();
        setState(() => _isLoading = false);
      } catch (e) {
        developer.log('Error hiding loading dialog: $e', name: 'TicketOtherAction');
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _pickFile() async {
    final result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result != null) {
      final newFiles = result.files.where((newFile) => !selectedFiles.any((f) => f.name == newFile.name)).toList();

      setState(() {
        selectedFiles.addAll(newFiles);
      });
    }
  }

  //Cancel

  void _handleSubmitCacel() async {
    setState(() => _fieldErrors.clear());
    if (_formKey.currentState?.validate() != true) return;
    print('HUYYY NE' + widget.ticketProcId + widget.taskDefKey + widget.ticketId);
    final ticketId = int.tryParse(widget.ticketId);
    if (ticketId == null) return;

    // _showLoadingDialog();
    final cancelBloc = context.read<CancelTicketBloc>();

    if (selectedFiles.isEmpty) {
      cancelBloc.add(SubmitCancelTicketEvent(
        ticketProcId: widget.ticketProcId,
        taskDefKey: widget.taskDefKey,
        ticketId: ticketId,
        reason: _reasonController.text,
        filePaths: [],
      ));
      _hideLoadingDialog();
      return;
    }

    cancelBloc.add(UploadCancelFileEvent(selectedFiles));

    final uploadState = await cancelBloc.stream.firstWhere(
      (state) => state.status == CancelTicketStatus.success || state.status == CancelTicketStatus.failure,
    );

    _hideLoadingDialog();

    if (uploadState.status == CancelTicketStatus.failure) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(uploadState.error ?? 'Tải tệp lên thất bại')),
      );
      return;
    }
    cancelBloc.add(SubmitCancelTicketEvent(
      ticketProcId: widget.ticketProcId,
      taskDefKey: widget.taskDefKey,
      ticketId: ticketId,
      reason: _reasonController.text,
      filePaths: uploadState.filePaths,
    ));
  }

  void _handleDraftCancel() async {
    final ticketId = int.tryParse(widget.ticketId);
    if (ticketId == null) return;

    _showLoadingDialog();
    final cancelBloc = context.read<CancelTicketBloc>();
    if (selectedFiles.isNotEmpty) {
      cancelBloc.add(UploadCancelFileEvent(selectedFiles));
      final uploadState = await cancelBloc.stream.firstWhere(
        (state) => state.status == CancelTicketStatus.success || state.status == CancelTicketStatus.failure,
      );
      _hideLoadingDialog();

      if (uploadState.status == CancelTicketStatus.failure) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(uploadState.error ?? 'Tải tệp lên thất bại')),
        );
        return;
      }

      developer.log("abc : ${uploadState.filePaths.toString()} ");

      cancelBloc.add(SaveCancelDraftEvent(
          ticketProcId: widget.ticketProcId, reason: _reasonController.text, filePath: uploadState.filePaths.first));
    } else {
      cancelBloc.add(SaveCancelDraftEvent(
        ticketProcId: widget.ticketProcId,
        reason: _reasonController.text,
        filePath: null,
      ));
      _hideLoadingDialog();
    }
  }

  void _handleAssistant(int status) async {
    Box box = Hive.box('authentication');
    String? username = box.get('username');
    final ticketId = int.tryParse(widget.ticketId);
    if (ticketId == null) return;

    _showLoadingDialog();
    final cancelBloc = context.read<AssistantOpinionBloc>();
    if (selectedFiles.isNotEmpty) {
      cancelBloc.add(UploadAssistantOpinionlFileEvent(selectedFiles));
      final uploadState = await cancelBloc.stream.firstWhere(
        (state) => state.status == CancelTicketStatus.success || state.status == CancelTicketStatus.failure,
      );
      _hideLoadingDialog();

      if (uploadState.status == CancelTicketStatus.failure) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(uploadState.error ?? 'Tải tệp lên thất bại')),
        );
        return;
      }

      developer.log("abc : ${uploadState.url.toString()} ");

      cancelBloc.add(SubmitAssistantOpinionEvent(
          ticketProcId: widget.ticketProcId,
          opinion: _reasonController.text,
          status: status,
          assistantEmail: username ?? "",
          ticketId: ticketId,
          url: uploadState.url));
    } else {
      cancelBloc.add(SubmitAssistantOpinionEvent(
          ticketProcId: widget.ticketProcId,
          opinion: _reasonController.text,
          status: status,
          assistantEmail: username ?? "",
          ticketId: ticketId,
          url: []));
      _hideLoadingDialog();
    }
  }

  void _handleSubmitDelegate() {
    if (_authorizedForm.currentState?.validate() != true) return;
    if (_selectedDelegate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vui lòng chọn người được ủy quyền')),
      );
      return;
    }

    final ticketId = int.tryParse(widget.ticketId) ?? 0;
    final procId = widget.ticketProcId;
    final reason = _delegateReasonController.text;

    context.read<TicketDialogActionBloc>().add(
          AuthorizeEvent(
            email: _selectedDelegate?.value ?? '',
            id: ticketId,
            taskId: widget.taskDefKey,
            reason: reason,
            taskDefKey: widget.taskDefKey,
            ticketId: procId,
            title: _selectedDelegate?.data?["title"] ?? '',
          ),
        );
  }

  void _handleSubmitRecoverRequest() async {
    setState(() => _fieldErrors.clear());
    if (_formKey.currentState?.validate() != true) return;

    final ticketId = int.tryParse(widget.ticketId);
    if (ticketId == null) return;
    _showLoadingDialog();

    // _showLoadingDialog();
    final recoverRequestBloc = context.read<RecoverRequestBloc>();
    if (selectedFiles.isNotEmpty) {
      recoverRequestBloc.add(UploadRecoverRequestEventFile(selectedFiles));
      final uploadState = await recoverRequestBloc.stream.firstWhere(
        (state) => state.status == RecoverRequestStatus.success || state.status == RecoverRequestStatus.failure,
      );
      _hideLoadingDialog();
      if (uploadState.status == RecoverRequestStatus.failure) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(uploadState.error ?? 'Tải tệp lên thất bại')),
        );
        return;
      }
    } else {
      recoverRequestBloc.add(SubmitRecoverRequestEvent(
        ticketProcId: widget.ticketProcId,
        taskDefKey: widget.taskDefKey,
        ticketId: ticketId,
        reason: _reasonController.text,
        filePaths: [],
      ));
      _hideLoadingDialog();
    }
  }

  String _handleTitleReason() {
    switch (widget.code) {
      case "CANCEL_TICKET":
        return "Lý do hủy";
      case "RECOVER_REQUEST":
        return "Lý do thu hồi";
      case "ASSISTANT_OPINION":
        return "Nội dung ý kiến";
      case "RETURN":
        return "Lý do ";
      case "INHERIT":
        return "Lý do";
      case "DELEGATE":
        return "Lý do";
      default:
        return "";
    }
  }

  String _handlePlaceholderReason() {
    switch (widget.code) {
      case "CANCEL_TICKET":
        return "Ban kiểm soát phê duyệt";
      case "RECOVER_REQUEST":
        return "Nhập lý do thu hồi";
      case "ASSISTANT_OPINION":
        return "Ban kiểm soát phê duyệt";
      case "RETURN":
        return "Ban kiểm soát phê duyệt";
      case "INHERIT":
        return "Nhập lý do kế thừa";
      case "DELEGATE":
        return "Lý do";
      default:
        return "";
    }
  }

  void _handleDraftSubmit() {
    switch (widget.code) {
      case "CANCEL_TICKET":
        _handleDraftCancel();
        break;
      case "ASSISTANT_OPINION":
        _handleAssistant(0);
        break;

      default:
        break;
    }
  }

  void _handleSubmit() {
    switch (widget.code) {
      case "DELEGATE":
        _handleSubmitDelegate();
        break;
      case "CANCEL_TICKET":
        _handleSubmitCacel();
        break;
      case "RECOVER_REQUEST":
        _handleSubmitRecoverRequest();
        break;
      case "ASSISTANT_OPINION":
        _handleAssistant(1);
        break;
      default:
        break;
    }
  }

  void _handleApproveSuccess() async {
    try {
      context.read<BottomNavBloc>().add(SetBottomNavVisibility(true));
    } catch (e) {
      developer.log('Error setting bottom nav: $e', name: 'TicketOtherAction');
    }

    if (DeviceUtils.isTablet) {
      final result = {
        'success': true,
        'shouldRefreshData': true, 
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } else {
      final result = {
        'success': true,
        'shouldNavigateToOverview': true,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CancelTicketBloc, CancelTicketState>(
          listener: _handleCancelListener,
        ),
        BlocListener<TicketDialogActionBloc, TicketDialogActionState>(
          listener: (context, state) {
            print('stateeeeeeeeeeeeeeeeee: $state}');
            if (state is DraftCancelTicketLoaded ||
                state is CancelTicketLoaded ||
                state is DraftReturnTicketLoaded ||
                state is ReturnTicketLoaded ||
                state is AuthorizedLoaded) {
              String message = '';
              if (state is DraftCancelTicketLoaded) {
                message = state.message;
              } else if (state is CancelTicketLoaded) {
                message = state.message;
              } else if (state is DraftReturnTicketLoaded) {
                message = state.message;
              } else if (state is ReturnTicketLoaded) {
                message = state.message;
              } else if (state is AuthorizedLoaded) {
                message = state.message;
              }

              _hideLoadingDialog();
              SnackbarCore.success(message);
              Navigator.of(context).pop();
            }

            if (state is UploadFileError ||
                state is DraftCancelTicketError ||
                state is DraftReturnTicketError ||
                state is ReturnTicketError ||
                state is CancelTicketError ||
                state is AuthorizedError) {
              final errorMessage = (state as dynamic).message;
              _hideLoadingDialog();

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Lỗi: $errorMessage')),
              );
            }
          },
        ),
        BlocListener<FormBloc, FormInfoState>(
          listener: (context, state) {
            developer.log('Form State Changed:', name: 'TicketOtherAction');
            developer.log('IsLoading: ${state.isLoading}', name: 'TicketOtherAction');
            developer.log('HasData: ${state.hasData}', name: 'TicketOtherAction');
            developer.log('FormResponse: ${state.formResponse?.toString()}', name: 'TicketOtherAction');
          },
        ),
        BlocListener<CheckTypeBloc, CheckTypeState>(
          listener: (context, state) {
            developer.log('CheckType State Changed:', name: 'TicketOtherAction');
            developer.log('State: $state', name: 'TicketOtherAction');
          },
        ),
      ],
      child: _buildDialogContent(),
    );
  }

  Widget _buildContentByBloc() {
    switch (widget.code) {
      case "CANCEL_TICKET":
        return BlocConsumer<CancelTicketBloc, CancelTicketState>(
          listener: _handleCancelListener,
          builder: (context, state) => _buildDialogContent(),
        );
      case "RECOVER_REQUEST":
        return BlocConsumer<RecoverRequestBloc, RecoverRequestState>(
          listener: _handleRecoverListener,
          builder: (context, state) => _buildDialogContent(),
        );
      case "ASSISTANT_OPINION":
        return BlocConsumer<AssistantOpinionBloc, AssistantOpinionState>(
          listener: _handleAssistantListener,
          builder: (context, state) => _buildDialogContent(),
        );
      case "RETURN":
        return BlocConsumer<CancelTicketBloc, CancelTicketState>(
          listener: _handleCancelListener,
          builder: (context, state) => _buildDialogContent(),
        );
      case "DELEGATE":
        return BlocConsumer<TicketDialogActionBloc, TicketDialogActionState>(
          listener: _handleDelegateListener,
          builder: (context, state) {
            if (state is TicketDialogActionLoading) {
              return const Center(child: CircularProgressIndicator());
            }
            return _buildDialogContent();
          },
        );
      case "APPROVE":
        return _buildDialogContent();
      case "PERFORM":
        return _buildDialogContent();
      default:
        return const SizedBox.shrink();
    }
  }

  void _handleCancelListener(BuildContext context, CancelTicketState state) {
    if (state.status == CancelTicketStatus.success) {
      if (state.actionType == "draft") {
        SnackbarCore.success("Lưu nháp thành công");
      } else if (state.actionType == "submit") {
        SnackbarCore.success("Hủy phiếu thành công");
      }
      Navigator.pop(context, {
        'success': true,
        'shouldNavigateToOverview': true,
      });
    } else if (state.status == CancelTicketStatus.failure) {
      SnackbarCore.error(state.error ?? 'Đã xảy ra lỗi');
    }
  }

  void _handleRecoverListener(BuildContext context, RecoverRequestState state) {
    if (state.status == RecoverRequestStatus.success) {
      if (state.actionType == "draft") {
        SnackbarCore.success("Lưu nháp thành công");
      } else if (state.actionType == "submit") {
        SnackbarCore.success("Hủy phiếu thành công");
      }
      Navigator.of(context).pop();
    } else if (state.status == RecoverRequestStatus.failure) {
      SnackbarCore.error(state.error ?? 'Đã xảy ra lỗi');
    }
  }

  void _handleAssistantListener(BuildContext context, AssistantOpinionState state) {
    if (state.status == AssistantOpinionStatus.success) {
      Navigator.of(context).pop();
    } else if (state.status == AssistantOpinionStatus.failure) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(state.error ?? 'Đã xảy ra lỗi')),
      );
    }
  }

  void _handleDelegateListener(BuildContext context, TicketDialogActionState state) {
    if (state is AuthorizedLoaded) {
      Navigator.of(context).pop();
    } else if (state is AuthorizedError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(state.message)),
      );
    }
  }

  Widget _buildDialogContent() {
    final isTablet = DeviceUtils.isTablet;
    if (isTablet) {
      // UI tablet giống hình bạn gửi
      return Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          decoration: BoxDecoration(
            color: getColorSkin().white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: getColorSkin().grey4Background),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Center(
                      child: Text(widget.title, style: getTypoSkin().medium20),
                    ),
                    Positioned(
                      right: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Icon(Icons.close, color: getColorSkin().black),
                      ),
                    ),
                  ],
                ),
              ),
              Divider(height: 1, color: getColorSkin().grey4Background),
              // Form content
              if (widget.code == 'APPROVE' || widget.code == 'PERFORM')
                Expanded(
                  child: ApproveTab(
                    isDone: _isDone,
                    isSigned: _isSigned,
                    isInPerformPhase: widget.code == 'PERFORM',
                    isNotForHandleModal: true,
                    ticket: widget.ticket,
                    onApproveSuccess: _handleApproveSuccess,
                  ),
                )
              else
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lý do hủy
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: _handleTitleReason(),
                              style: TextStyle(
                                color: getColorSkin().black,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                      SizedBox(height: 12),
                      CustomTextField(
                        hintText: _handlePlaceholderReason(),
                        maxLines: 4,
                        isRequired: true,
                        backgroundColor: getColorSkin().white,
                        controller: _reasonController,
                        showClearIcon: false,
                        maxLength: 50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      SizedBox(height: 16),
                      // Upload
                      Row(
                        children: [
                          Text("Chứng từ đính kèm", style: TextStyle(fontSize: 15)),
                          SizedBox(width: 8),
                          ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: getColorSkin().white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6.r),
                                  side: BorderSide(color: getColorSkin().ink5, width: 1),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                              ),
                              onPressed: _pickFile,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SvgPicture.asset(StringImage.ic_upload, width: 20.w, height: 20.h),
                                  SizedBox(width: 8.w),
                                  Text(
                                    'Upload',
                                    style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                  )
                                ],
                              ))
                        ],
                      ),
                      if (selectedFiles.isNotEmpty) ...[
                        SizedBox(height: 12),
                        ...selectedFiles.map((file) => Row(
                              children: [
                                Icon(Icons.attach_file, size: 18, color: getColorSkin().primaryBlue),
                                SizedBox(width: 4.w),
                                Expanded(child: Text(file.name, overflow: TextOverflow.ellipsis)),
                                IconButton(
                                  icon: Icon(Icons.close, size: 16),
                                  onPressed: () => setState(() => selectedFiles.remove(file)),
                                ),
                              ],
                            )),
                      ],
                      SizedBox(height: 32),
                      // Footer
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          OutlinedButton(
                            onPressed: _handleDraftSubmit,
                            style: OutlinedButton.styleFrom(
                              minimumSize: Size(100.w, 40.h),
                              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                              side: BorderSide(color: getColorSkin().primaryBlue),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Lưu nháp',
                              style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
                            ),
                          ),
                          SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: _handleSubmit,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: getColorSkin().primaryBlue,
                              minimumSize: Size(100.w, 40.h),
                              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Trả về',
                              style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    }
    // Mobile UI
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with title and close button
          Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Center(
                      child: Text(
                        widget.title,
                        style: getTypoSkin().medium20,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Positioned(
                      left: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Icon(
                          Icons.close,
                          color: getColorSkin().black,
                          size: 24.sp,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                height: 1,
                thickness: 1,
                color: getColorSkin().grey4Background,
                indent: 0,
                endIndent: 0,
              ),
            ],
          ),
          // Form content
          Flexible(
            child: FForm(
              key: _formKey,
              child: Padding(
                padding: EdgeInsets.all(28.w),
                child: widget.code == 'DELEGATE'
                    ? AuthorizeTab(
                        formKey: _authorizedForm,
                        delegateReasonController: _delegateReasonController,
                        selectedDelegate: _selectedDelegate,
                        onSelected: (v) => setState(() => _selectedDelegate = v),
                        onSearchChanged: (value) {
                          context.read<TicketDialogActionBloc>().add(
                                AuthorizeListGetListEvent(search: value),
                              );
                        },
                        ticketId: widget.ticketProcId,
                        taskId: widget.taskDefKey,
                        taskDefKey: _getTaskDefKey() ?? '',
                        id: int.tryParse(widget.ticketId) ?? 0,
                        isNotForDelegate: true,
                      )
                    : widget.code == 'APPROVE' || widget.code == 'PERFORM'
                        ? ApproveTab(
                            isDone: _isDone,
                            isSigned: _isSigned,
                            isInPerformPhase: widget.code == 'PERFORM',
                            isNotForHandleModal: true,
                            ticket: widget.ticket,
                            onApproveSuccess: _handleApproveSuccess,
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: _handleTitleReason(),
                                      style: TextStyle(
                                          color: getColorSkin().black, fontSize: 14.sp, fontWeight: FontWeight.w400),
                                    ),
                                    TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                                  ],
                                ),
                              ),
                              SizedBox(height: 8.h),
                              CustomTextField(
                                padding: EdgeInsets.symmetric(vertical: 12.h),
                                hintText: _handlePlaceholderReason(),
                                maxLines: 4,
                                isRequired: true,
                                backgroundColor: getColorSkin().white,
                                controller: _reasonController,
                                showClearIcon: false,
                                maxLength: 50,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              SizedBox(height: 8.h),

                              // Upload files
                              Row(
                                children: [
                                  Text("Chứng từ đính kèm",
                                      style: TextStyle(color: getColorSkin().black, fontSize: 14.sp)),
                                  InkWell(
                                    onTap: _pickFile,
                                    child: Container(
                                      margin: EdgeInsets.only(left: 12.w),
                                      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: getColorSkin().grey4Background),
                                        borderRadius: BorderRadius.circular(6.r),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.file_upload_outlined, size: 16.sp, color: getColorSkin().black),
                                          Text("Upload", style: TextStyle(fontSize: 14.sp)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              if (selectedFiles.isNotEmpty) ...[
                                SizedBox(height: 12.h),
                                ...selectedFiles.map((file) => Container(
                                      margin: EdgeInsets.only(bottom: 8.h),
                                      child: Row(
                                        children: [
                                          Icon(Icons.attach_file, size: 16.sp, color: getColorSkin().primaryBlue),
                                          SizedBox(width: 4.w),
                                          Expanded(
                                            child: Text(
                                              file.name,
                                              style: TextStyle(fontSize: 14.sp, color: getColorSkin().primaryBlue),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () => setState(() => selectedFiles.remove(file)),
                                            child: Padding(
                                              padding: EdgeInsets.all(4.w),
                                              child:
                                                  Icon(Icons.close, size: 16.sp, color: getColorSkin().grey3Background),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )),
                              ],

                              SizedBox(height: 24.h),
                              _buildFooter(),
                            ],
                          ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        children: [
          switch (widget.code) {
            "CANCEL_TICKET" || "ASSISTANT_OPINION" || "RETURN" => Padding(
                padding: EdgeInsets.only(right: 12.w),
                child: OutlinedButton(
                  onPressed: _handleDraftSubmit,
                  style: OutlinedButton.styleFrom(
                    minimumSize: Size(0, 40.h),
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    side: BorderSide(color: getColorSkin().primaryBlue),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'Lưu nháp',
                    style: getTypoSkin().title5Medium.copyWith(
                          color: getColorSkin().primaryBlue,
                        ),
                  ),
                ),
              ),
            _ => Container(),
          },
          Expanded(
            child: SizedBox(
              height: 40.h,
              child: ElevatedButton(
                onPressed: _handleSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: getColorSkin().primaryBlue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: Text(
                  'Gửi',
                  style: getTypoSkin().title5Medium.copyWith(
                        color: getColorSkin().white,
                      ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String? _getTaskDefKey() {
    return widget.taskDefKey;
  }
}
