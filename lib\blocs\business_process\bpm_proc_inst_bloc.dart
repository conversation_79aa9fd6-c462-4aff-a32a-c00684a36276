import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:eapprove/models/form/bpmProcInst_create_request_model.dart';
import 'package:eapprove/models/form/bpmProcInst_create_response_model.dart';
import 'dart:developer' as developer;

part 'bpm_proc_inst_event.dart';
part 'bpm_proc_inst_state.dart';

class BpmProcInstBloc extends Bloc<BpmProcInstEvent, BpmProcInstState> {
  final FormRepository _repository;

  BpmProcInstBloc({required FormRepository repository})
      : _repository = repository,
        super(BpmProcInstInitial()) {
    on<CreateInstRequested>(_onCreateBpmProcInstRequested);
  }

  Future<void> _onCreateBpmProcInstRequested(
    CreateInstRequested event,
    Emitter<BpmProcInstState> emit,
  ) async {
    try {
      emit(BpmProcInstLoading());
      developer.log('CreateInstRequested: ${event.requestBody.toJson()}', name: 'BpmProcInstBloc');
      final response = await _repository.createBpmProcInst(
        procDefId: event.procDefId,
        ticketId: event.ticketId,
        requestBody: event.requestBody,
      );

        if (response.code == 1 && response.message == 'Success') {
          emit(BpmProcInstSuccess(response));
        } else {
          emit(BpmProcInstFailure(response.message ?? 'Unknown error'));
        }
    } catch (e) {
      emit(BpmProcInstFailure(e.toString()));
    }
  }
} 