class UserDropdownItem {
  final int id;
  final String name;
  final String firstname;
  final String lastname;
  final String chartName;
  final int chartId;
  final String email;
  final String username;
  final String? chartShortName;
  final String? chartNodeName;
  final String companyCode;
  final String shortName;
  final int concurrently;
  final String title;

  UserDropdownItem({
    required this.id,
    required this.name,
    required this.firstname,
    required this.lastname,
    required this.chartName,
    required this.chartId,
    required this.email,
    required this.username,
    this.chartShortName,
    this.chartNodeName,
    required this.companyCode,
    required this.shortName,
    required this.concurrently,
    required this.title,
  });

  factory UserDropdownItem.fromJson(Map<String, dynamic> json) {
    return UserDropdownItem(
      id: json['id'],
      name: json['name'],
      firstname: json['firstname'],
      lastname: json['lastname'],
      chartName: json['chartName'],
      chartId: json['chartId'],
      email: json['email'],
      username: json['username'],
      chartShortName: json['chartShortName'],
      chartNodeName: json['chartNodeName'],
      companyCode: json['companyCode'],
      shortName: json['shortName'],
      concurrently: json['concurrently'],
      title: json['title'],
    );
  }

  String get displayText => '$username - $name - $title - $chartName';
}

class UserDropdownResponse {
  final int code;
  final String message;
  final List<UserDropdownItem> data;

  UserDropdownResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory UserDropdownResponse.fromJson(Map<String, dynamic> json) {
    var dataList = json['data'] as List;
    List<UserDropdownItem> data = dataList.map((item) => UserDropdownItem.fromJson(item)).toList();

    return UserDropdownResponse(
      code: json['code'],
      message: json['message'],
      data: data,
    );
  }
}
