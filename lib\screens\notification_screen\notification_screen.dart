import 'dart:async';
import 'dart:developer';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/notification/notification_bloc.dart';
import 'package:eapprove/blocs/notification/notification_event.dart';
import 'package:eapprove/blocs/notification/notification_state.dart';
import 'package:eapprove/models/notification/notification_model.dart';
import 'package:eapprove/screens/handle_ticket/handle_ticket_screen.dart';
import 'package:eapprove/screens/notification_screen/widgets/filter_notification.dart';
import 'package:eapprove/screens/notification_screen/widgets/notifications_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/screens/login/login_screen.dart';
import 'package:eapprove/services/token.dart' as token_service;

class NotificationScreen extends StatefulWidget {
  static const routeName = "/notification_screen";

  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => const NotificationScreen());
  }

  final VoidCallback? onClose;
  final bool showInitialLoading;
  const NotificationScreen({super.key, this.onClose, this.showInitialLoading = false});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  List<NotificationData> allNotifications = [];
  List<NotificationData> filteredNotifications = [];
  TextEditingController searchController = TextEditingController();
  late ScrollController _scrollController;
  int _currentPage = 0;
  bool _isFetchingMore = false;
  final int _pageSize = 20;
  String selectedFilter = 'all';
  Timer? _searchDebounceTimer;
  bool hasLoadedData = false;
  bool shouldShowInitialLoading = true;

  @override
  void initState() {
    super.initState();
    debugPrint("==> NotificationScreen INIT");
    _scrollController = ScrollController()..addListener(_onScroll);

    shouldShowInitialLoading = widget.showInitialLoading;
    hasLoadedData = false;

    if (shouldShowInitialLoading) {
      _fetchNotifications(isRefresh: true);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    debugPrint("==> NotificationScreen didChangeDependencies");
    if (!hasLoadedData) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) _fetchNotifications(isRefresh: true);
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    debugPrint("==> NotificationScreen DISPOSE");
    searchController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void _fetchNotifications({bool isRefresh = false}) {
    if (isRefresh) {
      _currentPage = 0;
      _isFetchingMore = false;
      setState(() {
        allNotifications.clear();
        filteredNotifications.clear();
      });
    }
    context.read<NotiBloc>().add(GetNotification(
          page: _currentPage,
          size: _pageSize,
          systems: ['EAPP'],
          search: searchController.text,
        ));
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;
    final state = context.read<NotiBloc>().state;
    if (state is NotiLoaded) {
      debugPrint("[_onScroll] hasReachedMax=${state.hasReachedMax}");
      if (state.hasReachedMax) {
        debugPrint("[_onScroll] ❌ Reached max items, no more loading");
        return;
      }
    }

    if (_isBottom && !_isFetchingMore) {
      debugPrint("[_onScroll] ✅ Triggering load more at page $_currentPage + 1");
      _isFetchingMore = true;
      _currentPage++;
      context.read<NotiBloc>().add(GetNotification(
            page: _currentPage,
            size: _pageSize,
            systems: ['EAPP'],
            search: searchController.text,
          ));
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll - 500);
  }

  void searchNotifications(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      setState(() {
        _currentPage = 0;
        hasLoadedData = false;

        _isFetchingMore = false;
        allNotifications.clear();
        filteredNotifications.clear();
      });

      context.read<NotiBloc>().add(GetNotification(
            page: 0,
            size: _pageSize,
            systems: ['EAPP'],
            search: query,
          ));
    });
  }

  void _showMarkAllAsReadDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomDialog(
          title: 'Xác nhận',
          content: 'Bạn có chắc chắn muốn đánh dấu tất cả thông báo là đã đọc?',
          onConfirm: markAllAsRead,
          onCancel: () => Navigator.of(context).pop(),
          cancelButtonText: 'Hủy',
          confirmButtonText: 'Đồng ý',
        );
      },
    );
  }

  void markAllAsRead() {
    context.read<NotiBloc>().add(ReadAllNotification());
    context.read<NotiBloc>().add(FetchUnreadAmount());
    setState(() {
      for (var notification in allNotifications) {
        notification.seen = true;
      }
      filteredNotifications = List.from(allNotifications);
    });
  }

  void _handleEApproveNotification(NotificationData notification) async {
    try {
      final String? urlTicket = notification.extraInfo?.urlTicket;
      log("Processing eApprove notification with urlTicket: $urlTicket");

      if (urlTicket == null || !urlTicket.startsWith('/bpm/manage-ticket/show/')) {
        log("Invalid or missing urlTicket: $urlTicket");
        context.go('/notification_screen/detail/${notification.id}');
        return;
      }

      String maPhieu = "";
      String requestSubject = "";
      if (notification.payload?.body != null) {
        final String body = notification.payload!.body!;
        log("Notification body: $body");
        final regexMaPhieu = RegExp(r'data-value="{{txt_maPhieu}}"><span[^>]*>([^<]+)</span>');
        final matchMaPhieu = regexMaPhieu.firstMatch(body);
        maPhieu = matchMaPhieu?.group(1)?.trim() ?? "";
        log("Extracted maPhieu: $maPhieu");

        final regexRequestSubject = RegExp(r'data-value="{{requestSubject}}"><span[^>]*>([^<]+)</span>');
        final matchRequestSubject = regexRequestSubject.firstMatch(body);
        requestSubject = matchRequestSubject?.group(1)?.trim() ?? "";
        log("Extracted requestSubject: $requestSubject");
      }

      final parts = urlTicket.replaceFirst('/bpm/manage-ticket/show/', '').split('/');
      if (parts.length < 3) {
        log("Invalid urlTicket format - missing parts: $urlTicket");
        context.go('/notification_screen/detail/${notification.id}');
        return;
      }

      final procDefId = parts[0];
      final ticketId = parts[1];
      final taskDefKey = parts[2];
      log("Parsed ticket info - procDefId: $procDefId, ticketId: $ticketId, taskDefKey: $taskDefKey");

      final token_service.TokenManager tokenManager = token_service.TokenManager();
      final token = await tokenManager.getFormattedToken();
      if (token == null) {
        log("No token available for eApprove navigation");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Authentication required")),
        );
        return;
      }

      Box eapproveTicketBox = await Hive.openBox('eapproveTicketBox');
      await eapproveTicketBox.put('procDefId', procDefId);
      await eapproveTicketBox.put('ticketId', ticketId);
      await eapproveTicketBox.put('taskDefKey', taskDefKey);
      await eapproveTicketBox.put('fromNotification', true);
      await eapproveTicketBox.put('maPhieu', maPhieu.isEmpty ? ticketId : maPhieu);
      await eapproveTicketBox.put('requestSubject', requestSubject);
      await eapproveTicketBox.put('processingId', DateTime.now().millisecondsSinceEpoch.toString());

      // Create ticket object for HandleTicketScreen
      final ticket = {
        'ticketId': ticketId,
        'procDefId': procDefId,
        'ticketTitle': requestSubject.isEmpty ? notification.payload?.title ?? 'Chi tiết phiếu' : requestSubject,
        'maPhieu': maPhieu.isEmpty ? ticketId : maPhieu,

        'ticketTaskDtoList': [
          {'taskDefKey': taskDefKey}
        ],
        'status': 'PENDING', // Adjust based on actual notification status if available
      };

      // Navigate to HandleTicketScreen while keeping the background and AppBar
      if (DeviceUtils.isTablet) {
        // For tablet, navigate to a new screen
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HandleTicketScreen(
              ticket: ticket,
              isEmbedded: false,
              onTicketDataLoaded: (data) {
                // Handle ticket data loaded if needed
              },
            ),
          ),
        );

        // Handle result when screen is closed
        if (result == true) {
          // Refresh notifications or update state as needed
          setState(() {
            // Update state if needed
          });
        } else if (result == 'logout' && mounted) {
          final userBloc = context.read<UserBloc>();
          final authBloc = context.read<AuthenticationBloc>();
          await userBloc.clearUserInfo();
          authBloc.add(LogoutRequested());
          await Future.delayed(const Duration(milliseconds: 300));
          context.go(LoginScreen.routeName);
        }
      } else {
        // For mobile, navigate to a new screen
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HandleTicketScreen(
              ticket: ticket,
              isEmbedded: false,
              onTicketDataLoaded: (data) {
                // Handle ticket data loaded if needed
              },
            ),
          ),
        );

        // Handle result when screen is closed
        if (result == true) {
          // Refresh notifications or update state as needed
          setState(() {
            // Update state if needed
          });
        } else if (result == 'logout' && mounted) {
          final userBloc = context.read<UserBloc>();
          final authBloc = context.read<AuthenticationBloc>();
          await userBloc.clearUserInfo();
          authBloc.add(LogoutRequested());
          await Future.delayed(const Duration(milliseconds: 300));
          context.go(LoginScreen.routeName);
        }
      }

      await eapproveTicketBox.delete('procDefId');
      await eapproveTicketBox.delete('ticketId');
      await eapproveTicketBox.delete('taskDefKey');
      await eapproveTicketBox.delete('fromNotification');
      await eapproveTicketBox.delete('maPhieu');
      await eapproveTicketBox.delete('requestSubject');
      await eapproveTicketBox.delete('processingId');
    } catch (e, stackTrace) {
      log("Error processing eApprove notification: $e");
      log("StackTrace: $stackTrace");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to process eApprove notification")),
      );
    }
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      title: Text("Thông báo", style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1)),
      centerTitle: true,
      leading: IconButton(
        icon: SvgPicture.asset(StringImage.ic_arrow_left),
        onPressed: () {
          if (DeviceUtils.isTablet) {
            if (widget.onClose != null) {
              widget.onClose!();
            }
          } else {
           context.read<BottomNavBloc>().add(const SwitchTab(0));
          }
        },
      ),
      actions: [
        IconButton(
          icon: SvgPicture.asset(StringImage.ic_readAll, width: 24.w, height: 24.h),
          onPressed: _showMarkAllAsReadDialog,
        ),
        IconButton(
          icon: SvgPicture.asset(StringImage.ic_filtered),
          onPressed: () {
            _showNotificationFilterSheet();
          },
        ),
        SizedBox(width: 6.w),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
      color: getColorSkin().white,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: getColorSkin().dividers2),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              StringImage.ic_search,
              width: 16.w,
              height: 16.h,
              colorFilter: ColorFilter.mode(getColorSkin().subtitle, BlendMode.srcIn),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: TextField(
                controller: searchController,
                onChanged: (value) {
                  _searchDebounceTimer?.cancel();
                  _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
                    debugPrint("Searching notifications with query: $value");
                    searchNotifications(value);
                  });
                },
                style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink1),
                decoration: InputDecoration(
                  hintText: "Tìm kiếm thông báo",
                  hintStyle: getTypoSkin().label4Regular.copyWith(color: getColorSkin().subtitle),
                  border: InputBorder.none,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationFilterSheet() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10.r)),
      ),
      builder: (_) => NotificationFilterSheet(
        selectedFilter: selectedFilter,
        onFilterChanged: (filter) {
          setState(() {
            selectedFilter = filter;
            searchNotifications(searchController.text);
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: SafeArea(
        child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar: _buildAppBar(),
            body: BlocListener<NotiBloc, NotiState>(
              listener: (context, state) {
                if (state is NotiLoaded) {
                  setState(() {
                    hasLoadedData = true;
                    shouldShowInitialLoading = false;
                    if (_currentPage == 0) {
                      allNotifications = List.from(state.notifications);
                    } else {
                      allNotifications.addAll(state.notifications);
                    }
                    filteredNotifications = List.from(allNotifications);
                    _isFetchingMore = false;
                  });
                }
              },
              child: Column(
                children: [
                  _buildSearchBar(),
                  Expanded(
                    child: BlocBuilder<NotiBloc, NotiState>(
                      builder: (context, state) {
                        if ((state is NotiLoading || shouldShowInitialLoading || !hasLoadedData) &&
                            allNotifications.isEmpty) {
                          return _buildNotificationShimmer();
                        }
                        if (hasLoadedData && filteredNotifications.isEmpty) {
                          return _buildEmptyView();
                        }
                        return RefreshIndicator(
                          backgroundColor: getColorSkin().white,
                          color: getColorSkin().primaryBlue,
                          onRefresh: () async {
                            _fetchNotifications(isRefresh: true);
                          },
                          child: ListView.builder(
                            controller: _scrollController,
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemCount: filteredNotifications.length + (_isFetchingMore ? 1 : 0),
                            itemBuilder: (context, index) {
                              if (index < filteredNotifications.length) {
                                final item = filteredNotifications[index];
                                debugPrint("[ListView] Rendering item $index: ${item.payload?.title}");
                                return _buildNotificationCard(item);
                              } else {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 20),
                                  child: Center(child: AppConstraint.buildLoading(context)),
                                );
                              }
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            )),
      ),
    );
  }

  Widget _buildNotificationCard(NotificationData notification) {
    debugPrint("Render card: ${notification.id} seen=${notification.seen}");
    return GestureDetector(
      key: ValueKey(notification.id),
      onTap: () async {
        try {
          debugPrint("Tapped notification ID: ${notification.id}");
          debugPrint("Notification system: ${notification.system}");
          debugPrint("Notification seen: ${notification.seen}");
          debugPrint("Notification payload: ${notification.payload}");
          debugPrint("Notification extraInfo: ${notification.extraInfo}");

          if (notification.id == null || notification.payload == null) {
            debugPrint("Error: Notification ID or payload is null");
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Invalid notification data")),
            );
            return;
          }

          if (!(notification.seen ?? false)) {
            context.read<NotiBloc>().add(ReadNotification(id: notification.id!));
            setState(() {
              for (var n in allNotifications) {
                if (n.id == notification.id) {
                  n.seen = true;
                  debugPrint("Marked as seen: ${n.id}");
                }
              }
              filteredNotifications = List.from(allNotifications);
              searchNotifications(searchController.text);
            });
            context.read<NotiBloc>().add(FetchUnreadAmount());
          }

          // Handle EAPP notification
          String? systemLower = notification.system?.toLowerCase();
          if (systemLower == 'eapp' && notification.extraInfo?.urlTicket != null) {
            _handleEApproveNotification(notification);
            return;
          }

          // Handle URL if present
          if (notification.payload?.body?.contains("href=") == true) {
            final linkMatch = RegExp(r'href="([^"]*)"').firstMatch(notification.payload!.body!);
            final url = linkMatch?.group(1);
            if (url != null) {
              debugPrint("Notification contains link: $url");
              try {
                final uri = Uri.parse(url);
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri, mode: LaunchMode.inAppWebView);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("Cannot open link")),
                  );
                }
              } catch (e) {
                debugPrint("Error launching URL: $e");
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text("Failed to open link")),
                );
              }
              return;
            }
          }

          final ticket = {
            'ticketId': notification.id,
            'ticketTitle': notification.payload?.title ?? 'Chi tiết phiếu',
            'status': notification.seen ?? false ? 'READ' : 'PENDING',
          };

          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HandleTicketScreen(
                ticket: ticket,
                isEmbedded: DeviceUtils.isTablet,
              ),
            ),
          );
        } catch (e, stackTrace) {
          debugPrint("Error in onTap: $e");
          debugPrint("StackTrace: $stackTrace");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Failed to process notification")),
          );
        }
      },
      child: NotificationCard(
        key: ValueKey(notification.id),
        title: notification.payload?.title ?? '',
        description: notification.payload?.body ?? '',
        date: notification.receiveTime != null
            ? DateTime.fromMillisecondsSinceEpoch(notification.receiveTime!).toString()
            : '',
        icon: StringImage.ic_megaphone,
        isSeen: notification.seen ?? true,
      ),
    );
  }

  Widget _buildNotificationShimmer() {
    return AppConstraint.buildShimmer(
      child: Padding(
        padding: EdgeInsets.only(top: 16.h),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                physics: NeverScrollableScrollPhysics(),
                itemCount: 5,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Container(
                      margin: EdgeInsets.only(bottom: 20.h),
                      padding: EdgeInsets.all(15.r),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                        color: getColorSkin().white,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Icon shimmer
                          Container(
                            height: 24.h,
                            width: 24.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(100),
                              color: getColorSkin().white,
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Title and date shimmer
                                Row(
                                  children: [
                                    Container(
                                      height: 16.h,
                                      width: 100.w,
                                      decoration: BoxDecoration(
                                        color: getColorSkin().white,
                                        borderRadius: BorderRadius.circular(4.r),
                                      ),
                                    ),
                                    SizedBox(width: 5.w),
                                    Container(
                                      height: 16.h,
                                      width: 80.w,
                                      decoration: BoxDecoration(
                                        color: getColorSkin().white,
                                        borderRadius: BorderRadius.circular(4.r),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 8.h),
                                // Content lines shimmer
                                Container(
                                  height: 12.h,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: getColorSkin().white,
                                    borderRadius: BorderRadius.circular(4.r),
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                Container(
                                  height: 12.h,
                                  width: double.infinity * 0.9,
                                  decoration: BoxDecoration(
                                    color: getColorSkin().white,
                                    borderRadius: BorderRadius.circular(4.r),
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                Container(
                                  height: 12.h,
                                  width: double.infinity * 0.7,
                                  decoration: BoxDecoration(
                                    color: getColorSkin().white,
                                    borderRadius: BorderRadius.circular(4.r),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            StringImage.ic_no_result,
          ),
          SizedBox(height: 12.h),
          Text(
            'Không có thông báo',
            style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink2),
          ),
        ],
      ),
    );
  }
}
