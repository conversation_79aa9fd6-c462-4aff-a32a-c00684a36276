import 'package:eapprove/models/account/chart_node_by_code_model.dart';
import 'package:eapprove/models/form/list_base_url_response.dart';
import 'package:equatable/equatable.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';
import 'package:eapprove/models/form/form_xml_response_model.dart';
import 'package:eapprove/models/account/chart_node_response_model.dart';
import 'package:eapprove/models/account/chart_active_response_model.dart';
import 'package:eapprove/models/form/md_service_response.dart';
import 'package:eapprove/models/form/submitter_info_response_model.dart';
import 'package:eapprove/models/form/tao_to_trinh_response_model.dart';
import 'package:eapprove/models/form/confirm_submit_response_model.dart';
import 'package:eapprove/models/form/upload_file_response_model.dart';
import 'package:eapprove/models/form/bpmProcInst_create_response_model.dart';
import 'package:eapprove/models/form/user_info_model.dart';
import 'package:eapprove/models/form/title_by_user_model.dart';
import 'package:eapprove/models/form/all_submission_type_response.dart'
    as submission;

import 'package:eapprove/models/form/service_by_id_with_submission.dart';
import 'package:eapprove/models/form/account_multi_chart_response_model.dart';
import 'package:eapprove/models/form/priority_management_response_model.dart';

class FormInfoState extends Equatable {
  final bool isLoading;
  final bool isSubmitting;
  final bool isUploadingFile;
  final bool isPrintingFile;
  final String? errorMessage;
  final bool isNetworkError;
  final BpmnFormXMLResponse? xmlResponse;
  final BpmnDefinition? bpmnDefinition;
  final FormResponse? formResponse;
  final dynamic userInfoResponse;
  final dynamic departmentInfoResponse;
  final dynamic informForUserInfoResponse;
  final ChartResponseModel? chartData;
  final ChartNodeResponseModel? chartNodeData;
  final MdServiceResponseModel? mdServiceResponse;
  final SubmitterInfoResponseModel? submitterInfoResponse;
  final TaoToTrinhResponseModel? taoToTrinhResponseModel;
  final ConfirmSubmitResponseModel? confirmSubmitResponseModel;
  final UploadSingleFileResponseModel? uploadFileResponse;
  final UploadMultiFileResponseModel? uploadMultipleFileResponse;
  final BpmProcInstCreateResponseModel? bpmProcInstCreateResponse;
  final dynamic callServiceResponse;
  final List<ListBaseUrlResponseModel>? listBaseUrlResponse;
  final ServiceByIdWithPermissionResponse? serviceByIdWithPermissionResponse;
  final String? submissionTypeName;
  final UserInfoByUserNameResponse? userInfoByUsernameResponse;
  final TitleByUserResponse? titleByUserResponse;
  final String? defaultSignatureResponse;
  final submission.AllSubmissionTypeResponse? allSubmissionType;
  final submission.Data? submissionType;
  final String? name;
  final String? fileType;
  final ChartNodeByCodeModel? chartNodeCustomData;
  final bool? isFormLoading;
  final bool hasPrintSignZone;
  final AccountMultiChartResponse? accountMultiChartResponse;
  final PriorityManagementResponseModel? priorityManagementResponse;

  const FormInfoState(
      {this.isLoading = false,
      this.isSubmitting = false,
      this.isUploadingFile = false,
      this.isPrintingFile = false,
      this.errorMessage,
      this.isNetworkError = false,
      this.xmlResponse,
      this.bpmnDefinition,
      this.formResponse,
      this.userInfoResponse,
      this.departmentInfoResponse,
      this.informForUserInfoResponse,
      this.chartData,
      this.chartNodeData,
      this.mdServiceResponse,
      this.submitterInfoResponse,
      this.taoToTrinhResponseModel,
      this.confirmSubmitResponseModel,
      this.uploadFileResponse,
      this.uploadMultipleFileResponse,
      this.bpmProcInstCreateResponse,
      this.callServiceResponse,
      this.listBaseUrlResponse,
      this.serviceByIdWithPermissionResponse,
      this.submissionTypeName,
      this.userInfoByUsernameResponse,
      this.titleByUserResponse,
      this.defaultSignatureResponse,
      this.allSubmissionType,
      this.submissionType,
      this.name,
      this.fileType,
      this.chartNodeCustomData,
      this.isFormLoading,
      this.hasPrintSignZone = false,
      this.accountMultiChartResponse,
      this.priorityManagementResponse});

  bool get hasError => errorMessage != null;
  bool get hasData => formResponse != null;

  @override
  List<Object?> get props => [
        isLoading,
        isSubmitting,
        isUploadingFile,
        isPrintingFile,
        errorMessage,
        isNetworkError,
        xmlResponse,
        bpmnDefinition,
        formResponse,
        userInfoResponse,
        departmentInfoResponse,
        informForUserInfoResponse,
        chartData,
        chartNodeData,
        mdServiceResponse,
        submitterInfoResponse,
        taoToTrinhResponseModel,
        confirmSubmitResponseModel,
        uploadFileResponse,
        uploadMultipleFileResponse,
        bpmProcInstCreateResponse,
        callServiceResponse,
        listBaseUrlResponse,
        serviceByIdWithPermissionResponse,
        submissionTypeName,
        userInfoByUsernameResponse,
        titleByUserResponse,
        defaultSignatureResponse,
        allSubmissionType,
        submissionType,
        name,
        fileType,
        chartNodeCustomData,
        isFormLoading,
        hasPrintSignZone,
        accountMultiChartResponse,
        priorityManagementResponse,
      ];

  FormInfoState copyWith({
    bool? isLoading,
    bool? isSubmitting,
    bool? isUploadingFile,
    bool? isPrintingFile,
    String? errorMessage,
    bool? isNetworkError,
    BpmnFormXMLResponse? xmlResponse,
    BpmnDefinition? bpmnDefinition,
    FormResponse? formResponse,
    dynamic userInfoResponse,
    dynamic departmentInfoResponse,
    dynamic informForUserInfoResponse,
    ChartResponseModel? chartData,
    ChartNodeResponseModel? chartNodeData,
    MdServiceResponseModel? mdServiceResponse,
    SubmitterInfoResponseModel? submitterInfoResponse,
    TaoToTrinhResponseModel? taoToTrinhResponseModel,
    ConfirmSubmitResponseModel? confirmSubmitResponseModel,
    UploadSingleFileResponseModel? uploadFileResponse,
    UploadMultiFileResponseModel? uploadMultipleFileResponse,
    BpmProcInstCreateResponseModel? bpmProcInstCreateResponse,
    dynamic callServiceResponse,
    List<ListBaseUrlResponseModel>? listBaseUrlResponse,
    ServiceByIdWithPermissionResponse? serviceByIdWithPermissionResponse,
    String? submissionTypeName,
    UserInfoByUserNameResponse? userInfoByUsernameResponse,
    TitleByUserResponse? titleByUserResponse,
    String? defaultSignatureResponse,
    submission.AllSubmissionTypeResponse? allSubmissionType,
    submission.Data? submissionType,
    String? name,
    String? fileType,
    ChartNodeByCodeModel? chartNodeCustomData,
    bool? isFormLoading,
    bool? hasPrintSignZone,
    AccountMultiChartResponse? accountMultiChartResponse,
    PriorityManagementResponseModel? priorityManagementResponse,
  }) {
    return FormInfoState(
      isLoading: isLoading ?? this.isLoading,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isUploadingFile: isUploadingFile ?? this.isUploadingFile,
      isPrintingFile: isPrintingFile ?? this.isPrintingFile,
      errorMessage: errorMessage ?? this.errorMessage,
      isNetworkError: isNetworkError ?? this.isNetworkError,
      xmlResponse: xmlResponse ?? this.xmlResponse,
      bpmnDefinition: bpmnDefinition ?? this.bpmnDefinition,
      formResponse: formResponse ?? this.formResponse,
      userInfoResponse: userInfoResponse ?? this.userInfoResponse,
      departmentInfoResponse:
          departmentInfoResponse ?? this.departmentInfoResponse,
      informForUserInfoResponse:
          informForUserInfoResponse ?? this.informForUserInfoResponse,
      chartData: chartData ?? this.chartData,
      chartNodeData: chartNodeData ?? this.chartNodeData,
      mdServiceResponse: mdServiceResponse ?? this.mdServiceResponse,
      submitterInfoResponse:
          submitterInfoResponse ?? this.submitterInfoResponse,
      taoToTrinhResponseModel:
          taoToTrinhResponseModel ?? this.taoToTrinhResponseModel,
      confirmSubmitResponseModel:
          confirmSubmitResponseModel ?? this.confirmSubmitResponseModel,
      uploadFileResponse: uploadFileResponse ?? this.uploadFileResponse,
      uploadMultipleFileResponse:
          uploadMultipleFileResponse ?? this.uploadMultipleFileResponse,
      bpmProcInstCreateResponse:
          bpmProcInstCreateResponse ?? this.bpmProcInstCreateResponse,
      callServiceResponse: callServiceResponse ?? this.callServiceResponse,
      listBaseUrlResponse: listBaseUrlResponse ?? this.listBaseUrlResponse,
      serviceByIdWithPermissionResponse: serviceByIdWithPermissionResponse ??
          this.serviceByIdWithPermissionResponse,
      submissionTypeName: submissionTypeName ?? this.submissionTypeName,
      userInfoByUsernameResponse:
          userInfoByUsernameResponse ?? this.userInfoByUsernameResponse,
      titleByUserResponse: titleByUserResponse ?? this.titleByUserResponse,
      defaultSignatureResponse:
          defaultSignatureResponse ?? this.defaultSignatureResponse,
      allSubmissionType: allSubmissionType ?? this.allSubmissionType,
      submissionType: submissionType ?? this.submissionType,
      name: name ?? this.name,
      fileType: fileType ?? this.fileType,
      chartNodeCustomData: chartNodeCustomData ?? this.chartNodeCustomData,
      isFormLoading: isFormLoading ?? this.isFormLoading,
      hasPrintSignZone: hasPrintSignZone ?? this.hasPrintSignZone,
      accountMultiChartResponse: accountMultiChartResponse ?? this.accountMultiChartResponse,
      priorityManagementResponse: priorityManagementResponse ?? this.priorityManagementResponse,
    );
  }

  bool get hasUserInfo => userInfoResponse != null;
  bool get hasDepartmentInfo => departmentInfoResponse != null;
  bool get hasInformForUserInfo => informForUserInfoResponse != null;
  bool get hasChartData => chartData != null;
  bool get hasChartNodeData => chartNodeData != null;
  bool get hasMdServiceData => mdServiceResponse != null;
  bool get hasUploadFileResponse => uploadFileResponse != null;
  bool get hasUploadMultipleFileResponse => uploadMultipleFileResponse != null;
  bool get hasBpmProcInstCreateResponse => bpmProcInstCreateResponse != null;
}
