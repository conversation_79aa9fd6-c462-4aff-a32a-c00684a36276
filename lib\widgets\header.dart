import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class Header extends StatefulWidget {
  static const routeName = "/handle-ticket";
  final String title;

  const Header({super.key, required this.title});

  @override
  State<Header> createState() => _HeaderState();
}

class _HeaderState extends State<Header> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final double maxHeight = MediaQuery.of(context).size.height * 0.3;

    return Container(
      color: const Color(0xFF30AEEF),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Back button
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(true),
                  ),
                  const Text(
                    'Trở lại',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),

              // Title area with maxHeight
              LayoutBuilder(
                builder: (context, constraints) {
                  final textSpan = TextSpan(
                    text: widget.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  );

                  final textPainter = TextPainter(
                    text: textSpan,
                    maxLines: 2,
                    textDirection: TextDirection.ltr,
                  )..layout(maxWidth: constraints.maxWidth - 70);

                  final isOverflowing = textPainter.didExceedMaxLines;

                  return ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: maxHeight,
                    ),
                    child: SingleChildScrollView(
                      physics: _isExpanded
                          ? const BouncingScrollPhysics()
                          : const NeverScrollableScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!_isExpanded)
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Expanded(
                                  child: Text(
                                    widget.title,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                if (isOverflowing)
                                  GestureDetector(
                                    onTap: () =>
                                        setState(() => _isExpanded = true),
                                    child: const Padding(
                                      padding: EdgeInsets.only(left: 8),
                                      child: Text(
                                        'Xem thêm',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            )
                          else
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: '${widget.title} ',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: 'Thu gọn',
                                    style: const TextStyle(
                                      color: Color(0xFF30AEEF),
                                      fontSize: 14,
                                      fontWeight: FontWeight.normal,
                                    ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () =>
                                          setState(() => _isExpanded = false),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
