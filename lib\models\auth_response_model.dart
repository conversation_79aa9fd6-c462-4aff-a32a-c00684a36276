class AuthResponse {
  final String code;
  final String email;
  final String sessionState;
  final bool is2FA;
  final String redirectUri;

  AuthResponse({
    required this.code,
    required this.email,
    required this.sessionState,
    required this.is2FA,
    required this.redirectUri,
  });

  // factory AuthResponse.fromJson(Map<String, dynamic> json) {
  //   print("📂 Chuyển đổi JSON thành AuthResponse: $json");

  //   if (!json.containsKey("code")) {
  //     throw Exception("Lỗi: API không trả về trường 'code'");
  //   }

  //   return AuthResponse(
  //     code: json['data']['code'],
  //     email: json['data']["email"] ?? "",
  //     sessionState: json['data']['session_state'],
  //     is2FA: json['data']['is2FA'],
  //     redirectUri: json['data']['redirect_uri'],
  //   );
  // }
  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    print("📂 Chuyển đổi JSON thành AuthResponse: $json");

    // Kiểm tra API có trả về key "data" và key "code" bên trong không
    if (!json.containsKey("data") ||
        json["data"] == null ||
        !json["data"].containsKey("code") ||
        json["data"]["code"] == null) {
      throw Exception("Lỗi: API không trả về trường 'code'. Response: $json");
    }

    return AuthResponse(
      code: json['data']['code'],
      email: json['data']["email"] ?? "",
      sessionState: json['data']['session_state'] ?? "",
      is2FA: json['data']['is2FA'] ?? false,
      redirectUri: json['data']['redirect_uri'] ?? "",
    );
  }
}

class TokenResponse {
  final String token;

  TokenResponse({required this.token});

  factory TokenResponse.fromJson(Map<String, dynamic> json) {
    return TokenResponse(token: json['data']['access_token']);
  }
}
