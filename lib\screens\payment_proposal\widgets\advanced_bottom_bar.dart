import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/screens/payment_proposal/widgets/more_options.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Widget buildBottomBar(
  BuildContext context, {
  required String buttonText,
  VoidCallback? onButtonPressed,
  String? nextRoute,
  Widget? nextScreen,
}) {
  final isTablet = DeviceUtils.isTablet;
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
    decoration: BoxDecoration(
      color: getColorSkin().white,
      border: Border(
        top: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
    ),
    child: isTablet
        ? SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Container(
                height: 46.h,
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: getColorSkin().white,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0D000000),
                      offset: Offset(0, -4),
                      blurRadius: 8,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                  FFilledButton(
                    onPressed: () {},
                    size: FButtonSize.size32,
                    backgroundColor: getColorSkin().red,
                    child: Text(
                      'Hủy',
                      style: getTypoSkin()
                          .medium14
                          .copyWith(color: getColorSkin().white),
                    ),
                  ),
                  SizedBox(
                    width: 9.w,
                  ),
                  FFilledButton(
                      onPressed: () {},
                      backgroundColor: getColorSkin().transparent,
                      size: FButtonSize.size32,
                      child: Text(
                        'Lịch thanh toán',
                        style: getTypoSkin()
                            .medium14
                            .copyWith(color: getColorSkin().primaryBlue),
                      )),
                  SizedBox(width: 9.w),
                  FFilledButton(
                    onPressed: () {},
                    backgroundColor: getColorSkin().transparent,
                    size: FButtonSize.size32,
                    child: Text(
                      'Lưu nháp',
                      style: getTypoSkin()
                          .medium14
                          .copyWith(color: getColorSkin().primaryBlue),
                    ),
                  ),
                  SizedBox(width: 9.w),
                  FFilledButton(
                    onPressed: onButtonPressed == null
                        ? null
                        : () {
                            onButtonPressed();

                            if (nextRoute != null) {
                              Navigator.pushNamed(context, nextRoute);
                            }
                          },
                    backgroundColor: getColorSkin().primaryBlue,
                    size: FButtonSize.size32,
                    child: Text(
                      'Đệ trình',
                      style: getTypoSkin()
                          .medium14
                          .copyWith(color: getColorSkin().white),
                    ),
                  )
                ]),
              ),
            ),
          )
        : SafeArea(
            child: Row(
              children: [
                const MoreOptionsDropdown(),
                SizedBox(width: 10.w),
                GestureDetector(
                  onTap: () {
                    showCustomDialog(
                      context: context,
                      title: 'Xóa nội dung tờ trình',
                      content: 'Bạn có muốn xóa nội dung không?',
                      onConfirm: () {
                        debugPrint('Deleting');
                        Navigator.of(context).pop();
                      },
                      cancelButtonText: 'Không',
                      cancelButtonColor: getColorSkin().secondaryText,
                      cancelTextColor: getColorSkin().white,
                      confirmButtonText: 'Có',
                      confirmButtonColor: getColorSkin().red,
                    );
                  },
                  child: Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: getColorSkin().red2,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: 11.w, vertical: 11.h),
                    child: SvgPicture.asset(StringImage.ic_red_trash,
                        width: 12.w, height: 12.h),
                  ),
                ),
                SizedBox(width: 10.w),
                Container(
                  width: 40.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: getColorSkin().blue2,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 11.w, vertical: 11.h),
                  child: SvgPicture.asset(StringImage.ic_bag,
                      width: 16.w, height: 16.h),
                ),
                const Spacer(),
                SizedBox(
                  width: 180.w,
                  child: ElevatedButton(
                    onPressed: onButtonPressed == null
                        ? null
                        : () {
                            onButtonPressed();

                            if (nextRoute != null) {
                              Navigator.pushNamed(context, nextRoute);
                            }
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: getColorSkin().primaryBlue,
                      padding: EdgeInsets.symmetric(vertical: 14.h),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    child: Text(
                      buttonText,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
  );
}
