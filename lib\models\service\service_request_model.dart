class ServiceRequestModel {
  final String? serviceName;
  final String? processName;
  final List<int>? serviceType;
  final int? chartId;
  final int? limit;
  final int? size;
  final int? page;
  final String? sortBy;
  final String? sortType;
  final List<String>? sortCompanyCode;
  final bool? admin;

  ServiceRequestModel({
    this.serviceName,
    this.processName,
    this.serviceType,
    this.chartId,
    this.limit,
    this.size,
    this.page,
    this.sortBy,
    this.sortType,
    this.sortCompanyCode,
    this.admin,
  });

  Map<String, dynamic> toJson() {
    return {
      "serviceName": serviceName,
      "processName": processName,
      "serviceType": serviceType,
      "chartId": chartId,
      "limit": limit,
      "size": size,
      "page": page,
      "sortBy": sortBy,
      "sortType": sortType,
      "sortCompanyCode": sortCompanyCode,
      "admin": admin,
    };
  }

}
