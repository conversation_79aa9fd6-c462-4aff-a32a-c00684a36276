import 'package:eapprove/screens/ticket_other_action/widget/comment_model.dart';
import 'package:eapprove/screens/ticket_other_action/widget/file_attachment_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class CommentWidget extends StatelessWidget {
  final CommentModel comment;
  final String currentUsername;
  final bool isReply;
  final bool showReplyInput;
  final List<Widget>? replies;
  final Function(int) onReply;
  final Function(int, String, bool, List?) onEdit;
  final Function(int) onDelete;
  final Widget? replyInputWidget;
  final bool showDeleteButton;
  final bool showEditButton;

  const CommentWidget({
    super.key,
    required this.comment,
    required this.currentUsername,
    this.isReply = false,
    this.showReplyInput = false,
    this.replies,
    required this.onReply,
    required this.onEdit,
    required this.onDelete,
    this.replyInputWidget,
    this.showDeleteButton = true,
    this.showEditButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = comment.createdUser == currentUsername;
    String initial = '';

    if (comment.username.isNotEmpty) {
      if (comment.username.contains('-')) {
        final parts = comment.username.split('-');
        if (parts.length > 1 && parts[1].trim().isNotEmpty) {
          initial = parts[1].trim()[0];
        } else {
          initial = comment.username[0];
        }
      } else {
        initial = comment.username[0];
      }
    }

    String shortUsername = comment.username;
    if (comment.username.contains('-')) {
      shortUsername = comment.username.split('-')[0].trim();
    }

    List<String> actionList = [];
    if (isCurrentUser) {
      if (comment.typeDiscussion == 0) {
        // comment riêng tư của user
        actionList = isReply 
          ? (showDeleteButton ? ['Sửa tham vấn', 'Xoá'] : (showEditButton ? ['Sửa tham vấn'] : [])) 
          : (showDeleteButton 
              ? (showEditButton ? ['Trả lời', 'Sửa tham vấn', 'Xoá'] : ['Trả lời', 'Xoá']) 
              : (showEditButton ? ['Trả lời', 'Sửa tham vấn'] : ['Trả lời']));
      } else {
        // comment công khai của user
        actionList = isReply 
          ? (showDeleteButton ? ['Sửa tham vấn', 'Xoá'] : (showEditButton ? ['Sửa tham vấn'] : [])) 
          : (showDeleteButton 
              ? (showEditButton ? ['Trả lời', 'Sửa tham vấn', 'Xoá'] : ['Trả lời', 'Xoá']) 
              : (showEditButton ? ['Trả lời', 'Sửa tham vấn'] : ['Trả lời']));
      }
    } else {
      // comment của người khác, chỉ cho phép trả lời nếu không phải là trả lời
      actionList = isReply ? [] : ['Trả lời'];
    }

    return Padding(
      padding: EdgeInsets.only(bottom: replies != null ? 16.h : 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: isReply ? 12.r : 16.r,
                backgroundColor: _getAvatarColor(comment.username),
                child: Text(
                  initial.toUpperCase(),
                  style: getTypoSkin().title5Medium.copyWith(
                        color: getColorSkin().white,
                        fontSize: isReply ? 10.sp : 14.sp,
                      ),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                          _formatDisplayUsername(comment.username),
                          style: getTypoSkin().title5Medium.copyWith(
                            fontSize: isReply ? 12.sp : 14.sp,
                          ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                          ),
                        ),
                        Text(
                          ' • ${comment.formattedDate}',
                          style: getTypoSkin().regular12.copyWith(
                                color: getColorSkin().ink3,
                                fontSize: isReply ? 10.sp : 12.sp,
                              ),
                        ),
                      ],
                    ),
                    Html(
                      data: comment.content,
                      style: {
                        "a": Style(
                          textDecoration: TextDecoration.none,
                          color: getColorSkin().primaryBlue,
                        ),
                        "p": Style(
                          margin: Margins.zero,
                        ),
                        "body": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                        ),
                        "span": Style(
                          textDecoration: TextDecoration.none,
                        ),
                        "b": Style(
                          textDecoration: TextDecoration.none,
                        ),
                        "*": Style(
                          textDecoration: TextDecoration.none,
                        ),
                      },
                    ),
                    if (comment.files != null && comment.files!.isNotEmpty)
                      FileAttachmentPreview(files: comment.files!),
                    Padding(
                      padding: EdgeInsets.only(top: 4.h),
                      child: Wrap(
                        spacing: 8.w,
                        children: actionList
                            .map((action) => GestureDetector(
                                  onTap: () {
                                    if (action == 'Xoá') {
                                      onDelete(comment.id);
                                    } else if (action == 'Sửa tham vấn') {
                                      onEdit(comment.id, comment.content, comment.typeDiscussion == 0, comment.files);
                                    } else if (action == 'Trả lời') {
                                      onReply(comment.id);
                                    }
                                  },
                                  child: Text(
                                    action,
                                    style: TextStyle(
                                      color: action == 'Xoá' ? getColorSkin().red : getColorSkin().primaryBlue,
                                      fontSize: isReply ? 12.sp : 14.sp,
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                    if (showReplyInput && replyInputWidget != null) replyInputWidget!,
                    if (replies != null)
                      Padding(
                        padding: EdgeInsets.only(top: 12.h),
                        child: Column(children: replies!),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getAvatarColor(String username) {
    final colors = [
      getColorSkin().primaryBlue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    int hash = 0;
    for (int i = 0; i < username.length; i++) {
      hash = username.codeUnitAt(i) + ((hash << 5) - hash);
    }

    return colors[hash.abs() % colors.length];
  }
   String _formatDisplayUsername(String rawUsername) {
    final parts = rawUsername.split('-').map((e) => e.trim()).toList();

    if (parts.length >= 3) {
      return '${parts[0]} - ${parts[1]} - ${parts[2]}';
    } else if (parts.length == 2) {
      return '${parts[0]} - ${parts[1]}';
    } else {
      return rawUsername.trim();
    }
  }
}
