import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/repositories/account_repository.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:eapprove/screens/authorize_management/authorize_management_screen.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/handle_ticket/handle_ticket_screen.dart';
import 'package:eapprove/screens/login/login_screen.dart';
import 'package:eapprove/screens/notification_screen/notification_screen.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/setting_screen.dart';
import 'package:eapprove/services/global_context_handler.dart';
import 'package:eapprove/services/token.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:eapprove/main.dart' show navigatorKey;


GoRouter createAppRouter(AuthenticationBloc authBloc) {
  return GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: LoginScreen.routeName,
    observers: [BotToastNavigatorObserver(), ThemeObserver()],
    refreshListenable: GoRouterRefreshStream(authBloc.stream),
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authState = authBloc.state;
      final tokenManger = TokenManager();

      // Handle root path
      if (state.uri.path == '/') {
        return LoginScreen.routeName;
      }

      // Handle login path
      if (state.uri.path == LoginScreen.routeName) {
        if (authState is TokenSuccess && tokenManger.getFormattedToken() != null) {
          return BottomNavScreen.routeName;
        }
        return null;
      }

      // Handle other paths
      if (authState is AuthenticationFailure) {
        return LoginScreen.routeName;
      }

      return null;
    },
    routes: [
      GoRoute(
        path: LoginScreen.routeName,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: BottomNavScreen.routeName,
        builder: (context, state) => BottomNavScreen(),
      ),
      GoRoute(
        path: AuthorizeManagementScreen.routeName,
        builder: (context, state) => const AuthorizeManagementScreen(),
      ),
      GoRoute(
        path: '/ticket-form/:procDefId',
        builder: (context, state) {
          final procDefId = state.pathParameters['procDefId'];
          final formRepository = RepositoryProvider.of<FormRepository>(context);
          final accountRepository = RepositoryProvider.of<AccountRepository>(context);

          return BlocProvider<FormBloc>(
            create: (context) => FormBloc(
              formRepository: formRepository,
              accountRepository: accountRepository,
            ),
            child: TicketFormScreen(
              procDefId: procDefId,
              title: 'Tờ trình đề nghị thanh toán',
            ),
          );
        },
      ),
      GoRoute(
        path: EAppSettingScreen.routeName,
        builder: (context, state) => const EAppSettingScreen(),
      ),
      GoRoute(
        path: NotificationScreen.routeName,
        builder: (context, state) => const NotificationScreen(),
      ),
      GoRoute(
        path: '/notification_screen/detail/:id',
        builder: (context, state) {
          final id = state.pathParameters['id'];
          return HandleTicketScreen(
            ticket: {'ticketId': id, 'ticketTitle': 'Chi tiết phiếu', 'status': 'PENDING'},
            isEmbedded: false,
          );
        },
      ),
    ],
  );
}
