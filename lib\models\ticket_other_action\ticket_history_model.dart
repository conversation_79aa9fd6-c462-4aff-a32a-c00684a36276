import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/models/ticket_other_action/action_model.dart';
import 'package:eapprove/models/ticket_other_action/status_model.dart';
import 'package:eapprove/utils/extension.dart';

class TicketHistoryModel {
  final String username; //Người thực hiện
  final String position; //Chức danh
  final String action; //Hành động
  final String? description; //Nội dung
  final String date; //Thời gian thực hiện
  final List<DocumentModel>? attachments; //Danh sách tài liệu

  TicketHistoryModel({
    required this.username,
    required this.position,
    required this.action,
    this.description,
    required this.date,
    this.attachments,
  });

  //Mapping dữ liệu,
  //inputFormat và outputFormat sẽ lấy theo format thời gian của hệ thống
  //outputFormat sẽ trả về format thời gian mong muốn

  factory TicketHistoryModel.fromJson(Map<String, dynamic> json) {
    final totalAttachments = json['attachFiles'] != null ? json['attachFiles'].split(',')?.length : 0;
    List<DocumentModel> attachments = [];
    if (totalAttachments > 0) {
      for (var i = 0; i < totalAttachments; i++) {
        final fileName = json['attachFilesName'][i];
        final fileUrl = json['attachFiles'][i];
        final fileType = fileName.split('.').last;
        final fileSize = json['attachFilesSize'][i];
        final fileExtension = fileName.split('.').last;
        attachments.add(DocumentModel(
          id: i.toString(),
          name: fileName,
          url: fileUrl,
          type: FileType.fromCode(fileType),
          size: fileSize,
          extension: fileExtension,
        ));
      }
    }
    final userInfo = json['userInfo'] as Map<String, dynamic>?;
    return TicketHistoryModel(
      username: '${json['actionUser']} - ${userInfo?['fullName'] ?? ''}',
      position: userInfo?['userTitle'] ?? '',
      action: ActionCode.fromCode(json['action']).name,
      description: json['note'] ?? '',
      date: json['time'].toString().toDateTimeString(),
      attachments: attachments,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'position': position,
      'action': action,
      'description': description,
      'date': date,
      'attachments': attachments?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'TicketHistoryModel(username: $username, position: $position, action: $action, description: $description, date: $date)';
  }
}
