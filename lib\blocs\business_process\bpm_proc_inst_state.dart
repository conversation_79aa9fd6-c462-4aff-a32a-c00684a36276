part of 'bpm_proc_inst_bloc.dart';

abstract class BpmProcInstState extends Equatable {
  const BpmProcInstState();

  @override
  List<Object> get props => [];
}

class BpmProcInstInitial extends BpmProcInstState {}

class BpmProcInstLoading extends BpmProcInstState {}

class BpmProcInstSuccess extends BpmProcInstState {
  final BpmProcInstCreateResponseModel response;

  const BpmProcInstSuccess(this.response);

  @override
  List<Object> get props => [response];
}

class BpmProcInstFailure extends BpmProcInstState {
  final String error;

  const BpmProcInstFailure(this.error);

  @override
  List<Object> get props => [error];
} 