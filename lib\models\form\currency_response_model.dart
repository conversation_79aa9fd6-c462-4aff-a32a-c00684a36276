import 'dart:convert';

class CurrencyResponseModel {
  final int code;
  final String message;
  final PaginatedData data;

  const CurrencyResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory CurrencyResponseModel.fromJson(Map<String, dynamic> json) {
    return CurrencyResponseModel(
      code: json['code'] as int,
      message: json['message'] as String,
      data: PaginatedData.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  factory CurrencyResponseModel.fromJsonString(String jsonString) {
    return CurrencyResponseModel.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }

  bool get isSuccess => code == 1;
}

class PaginatedData {
  final int page;
  final int limit;
  final String? search;
  final String? sortBy;
  final String? sortType;
  final int size;
  final List<CurrencyData> content;
  final int totalElements;
  final int number;
  final int numberOfElements;
  final int totalPages;
  final bool first;
  final bool last;

  const PaginatedData({
    required this.page,
    required this.limit,
    this.search,
    this.sortBy,
    this.sortType,
    required this.size,
    required this.content,
    required this.totalElements,
    required this.number,
    required this.numberOfElements,
    required this.totalPages,
    required this.first,
    required this.last,
  });

  factory PaginatedData.fromJson(Map<String, dynamic> json) {
    return PaginatedData(
      page: json['page'] as int,
      limit: json['limit'] as int,
      search: json['search'] as String?,
      sortBy: json['sortBy'] as String?,
      sortType: json['sortType'] as String?,
      size: json['size'] as int,
      content: (json['content'] as List<dynamic>)
          .map((item) => CurrencyData.fromJson(item as Map<String, dynamic>))
          .toList(),
      totalElements: json['totalElements'] as int,
      number: json['number'] as int,
      numberOfElements: json['numberOfElements'] as int,
      totalPages: json['totalPages'] as int,
      first: json['first'] as bool,
      last: json['last'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'size': size,
      'content': content.map((item) => item.toJson()).toList(),
      'totalElements': totalElements,
      'number': number,
      'numberOfElements': numberOfElements,
      'totalPages': totalPages,
      'first': first,
      'last': last,
    };
  }
}

class CurrencyData {
  final int id;
  final String name;
  final String code;
  final String? anotherName;
  final int? roundingRules;
  final String? description;
  final int? createdAt;
  final int? updatedAt;
  final String? userCreate;
  final String? userUpdated;

  const CurrencyData({
    required this.id,
    required this.name,
    required this.code,
    this.anotherName,
    this.roundingRules,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.userCreate,
    this.userUpdated,
  });

  factory CurrencyData.fromJson(Map<String, dynamic> json) {
    return CurrencyData(
      id: json['id'] as int,
      name: json['name'] as String,
      code: json['code'] as String,
      anotherName: json['anotherName'] as String?,
      roundingRules: json['roundingRules'] as int?,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int?,
      updatedAt: json['updatedAt'] as int?,
      userCreate: json['userCreate'] as String?,
      userUpdated: json['userUpdated'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'anotherName': anotherName,
      'roundingRules': roundingRules,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'userCreate': userCreate,
      'userUpdated': userUpdated,
    };
  }
}