import 'dart:convert';

class UserInfoResponse {
  final String code;
  final String? message;
  final String status;
  final int timestamp;
  final int elapsedTimeMs;
  final String path;
  final String requestId;
  final UserInfo data;

  UserInfoResponse({
    required this.code,
    this.message,
    required this.status,
    required this.timestamp,
    required this.elapsedTimeMs,
    required this.path,
    required this.requestId,
    required this.data,
  });

  factory UserInfoResponse.fromJson(Map<String, dynamic> json) {
    return UserInfoResponse(
      code: json["code"],
      message: json["message"],
      status: json["status"],
      timestamp: json["timestamp"],
      elapsedTimeMs: json["elapsedTimeMs"],
      path: json["path"],
      requestId: json["requestId"],
      data: UserInfo.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "code": code,
      "message": message,
      "status": status,
      "timestamp": timestamp,
      "elapsedTimeMs": elapsedTimeMs,
      "path": path,
      "requestId": requestId,
      "data": data.toJson(),
    };
  }
}

class UserInfo {
  final String username;
  final String userId;
  final String positionCode;
  final String positionName;
  final String orgCode;
  final String orgName;
  final String companyCode;
  final String companyName;
  final String logoCompany;

  UserInfo({
    required this.username,
    required this.userId,
    required this.positionCode,
    required this.positionName,
    required this.orgCode,
    required this.orgName,
    required this.companyCode,
    required this.companyName,
    required this.logoCompany,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      username: json["username"] ?? "",
      userId: json["userId"],
      positionCode: json["positionCode"],
      positionName: json["positionName"],
      orgCode: json["orgCode"],
      orgName: json["orgName"],
      companyCode: json["companyCode"],
      companyName: json["companyName"],
      logoCompany: json["logoCompany"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "username": username,
      "userId": userId,
      "positionCode": positionCode,
      "positionName": positionName,
      "orgCode": orgCode,
      "orgName": orgName,
      "companyCode": companyCode,
      "companyName": companyName,
      "logoCompany": logoCompany,
    };
  }
}

// Chuyển đổi từ JSON String sang Model
UserInfoResponse userInfoFromJson(String str) => UserInfoResponse.fromJson(json.decode(str));

// Chuyển đổi từ Model sang JSON String
String userInfoToJson(UserInfoResponse data) => json.encode(data.toJson());
