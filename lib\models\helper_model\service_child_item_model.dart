class TicketChildItem {
  final String title;
  final String? procDefId;
  final bool hasChevron;
  final List<TicketChildItem>? children;
  final int? serviceType;
  final String? url;

  TicketChildItem({
    required this.title,
    this.procDefId,
    this.hasChevron = false,
    this.children,
    this.serviceType,
    this.url,
  });

  @override
  String toString() => title;

  factory TicketChildItem.fromJson(Map<String, dynamic> json) {
    return TicketChildItem(
      title: json['title'] ?? '',
      procDefId: json['procDefId'],
      hasChevron: json['hasChevron'] ?? false,
      serviceType: json['serviceType'],
      url: json['url'],
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => TicketChildItem.fromJson(child))
          .toList(),
    );
  }
}

class TicketServiceItem {
  final String title;
  final String iconPath;
  final bool isExpanded;
  final List<TicketChildItem> children;

  TicketServiceItem({
    required this.title,
    required this.iconPath,
    this.isExpanded = false,
    required this.children,
  });

  factory TicketServiceItem.fromJson(Map<String, dynamic> json) {
    return TicketServiceItem(
      title: json['title'] ?? '',
      iconPath: json['iconPath'] ?? '',
      isExpanded: json['isExpanded'] ?? false,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => TicketChildItem.fromJson(child))
          .toList() ?? [],
    );
  }
}