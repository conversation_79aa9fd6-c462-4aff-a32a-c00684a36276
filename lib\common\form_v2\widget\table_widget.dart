import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TableWidget extends StatefulWidget {
  final FormItemInfo item;
  final FormStateManager formStateManager;

  const TableWidget({
    Key? key,
    required this.item,
    required this.formStateManager,
  }) : super(key: key);

  @override
  State<TableWidget> createState() => _TableWidgetState();
}

class _TableWidgetState extends State<TableWidget> {
  late List<Map<String, dynamic>> _data;
  late List<Map<String, dynamic>> _columns;
  late String _tableName;

  @override
  void initState() {
    super.initState();
    _tableName = widget.item.name.toString();
    _initializeData();
    _initializeColumns();
  }

  void _initializeData() {
    final currentData = widget.formStateManager.getFieldValue(_tableName);
    if (currentData is List) {
      _data = List<Map<String, dynamic>>.from(currentData);
    } else {
      _data = [];
      widget.formStateManager.setFieldValue(_tableName, _data);
    }
  }

  void _initializeColumns() {
    _columns = [];
    if (widget.item.optionConfig?['columns'] is List) {
      for (var column in widget.item.optionConfig!['columns']) {
        _columns.add({
          'name': column['name'],
          'label': column['label'] ?? column['name'],
          'type': column['type'] ?? 'text',
          'flex': column['flex'] ?? 1,
          'validation': column['validation'] ?? {},
          'format': column['format'] ?? {},
          'options': column['options'] ?? [],
        });
      }
    }
  }

  void _addNewRow() {
    setState(() {
      Map<String, dynamic> newRow = {};
      for (var column in _columns) {
        String fieldName = column['name'];
        newRow[fieldName] = column['defaultValue'] ?? '';
      }
      _data.add(newRow);
      widget.formStateManager.setFieldValue(_tableName, _data);
    });
  }

  void _removeRow(int index) {
    setState(() {
      _data.removeAt(index);
      widget.formStateManager.setFieldValue(_tableName, _data);
    });
  }

  void _updateRow(int index, String fieldName, dynamic value) {
    setState(() {
      _data[index][fieldName] = value;
      widget.formStateManager.setFieldValue(_tableName, _data);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.item.label ?? widget.item.name.toString(),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildTableHeader(),
              ..._data
                  .asMap()
                  .entries
                  .map((entry) => _buildTableRow(entry.key, entry.value))
                  .toList(),
            ],
          ),
        ),
        const SizedBox(height: 8),
        ElevatedButton.icon(
          onPressed: _addNewRow,
          icon: const Icon(Icons.add),
          label: const Text('Thêm dòng'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          ..._columns
              .map((column) => _buildHeaderCell(
                    column['label'] ?? column['name'],
                    flex: column['flex'] ?? 1,
                  ))
              .toList(),
          _buildHeaderCell('', flex: 1),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildTableRow(int index, Map<String, dynamic> row) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          ..._columns
              .map((column) => Expanded(
                    flex: column['flex'] ?? 1,
                    child: _buildField(
                      column: column,
                      value: row[column['name']],
                      onChanged: (value) =>
                          _updateRow(index, column['name'], value),
                    ),
                  ))
              .toList(),
          Expanded(
            flex: 1,
            child: IconButton(
              icon: const Icon(Icons.delete_outline, color: Colors.red),
              onPressed: () => _removeRow(index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildField({
    required Map<String, dynamic> column,
    required dynamic value,
    required Function(dynamic) onChanged,
  }) {
    String type = column['type'] ?? 'text';
    List<Map<String, dynamic>>? options = column['options'];
    Map<String, dynamic>? validation = column['validation'];
    Map<String, dynamic>? format = column['format'];

    switch (type) {
      case 'dropdown':
        return _buildDropdownField(
          value: value?.toString(),
          options: options ?? [],
          validation: validation,
          onChanged: (value) => onChanged(value),
        );
      case 'number':
        return _buildNumberField(
          value: value?.toString() ?? '',
          validation: validation,
          format: format,
          onChanged: (value) => onChanged(value),
        );
      default:
        return _buildTextField(
          value: value?.toString() ?? '',
          validation: validation,
          onChanged: onChanged,
        );
    }
  }

  Widget _buildDropdownField({
    String? value,
    required List<Map<String, dynamic>> options,
    Map<String, dynamic>? validation,
    required Function(String?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: DropdownButton<String>(
        value: value,
        isExpanded: true,
        underline: Container(),
        onChanged: onChanged,
        items: options.map((option) {
          return DropdownMenuItem<String>(
            value: option['value']?.toString(),
            child: Text(option['label']?.toString() ?? ''),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNumberField({
    required String value,
    Map<String, dynamic>? validation,
    Map<String, dynamic>? format,
    required Function(String) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: TextFormField(
        initialValue: value,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9,]')),
        ],
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
          suffixText: format?['type'] == 'currency' ? 'VND' : null,
        ),
        onChanged: (text) {
          if (format?['type'] == 'currency') {
            final number = _parseCurrency(text);
            if (number != null) {
              onChanged(_formatCurrency(number));
            }
          } else {
            onChanged(text);
          }
        },
      ),
    );
  }

  Widget _buildTextField({
    required String value,
    Map<String, dynamic>? validation,
    required Function(String) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: TextFormField(
        initialValue: value,
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        onChanged: onChanged,
      ),
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        )} VND';
  }

  double? _parseCurrency(String value) {
    try {
      return double.parse(value.replaceAll(',', '').replaceAll(' VND', ''));
    } catch (e) {
      return null;
    }
  }
}
