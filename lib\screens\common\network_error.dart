import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:eapprove/assets/StringIcon.dart';

class NetworkErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onRetry;
  final bool showDialog;

  const NetworkErrorDialog({
    super.key,
    this.title = 'Mất kết nối mạng',
    this.message = 'Bạn vui lòng kiểm tra lại internet và kết nối lại',
    required this.onRetry,
    this.showDialog = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showDialog) {
      return const SizedBox.shrink();
    }

    return WillPopScope(
      // Prevent dismissing with back button
      onWillPop: () async => false,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        elevation: 0,
        backgroundColor: Colors.white,
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Info icon in blue circle
              Container(
                width: 70.w,
                height: 70.w,
                decoration: const BoxDecoration(
                  color: Color(0xFF00A0E9),
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  StringImage.ic_exclamation,
                  width: 40.w,
                  height: 40.w,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                title,
                style: getTypoSkin().bodyRegular14Bold.copyWith(
                      color: getColorSkin().black,
                    ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 6.h),
              Text(
                message,
                style: getTypoSkin().buttonRegular14.copyWith(
                      color: getColorSkin().black,
                    ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16.h),
              // "Tải lại" button
              SizedBox(
                width: 120.w,
                child: FFilledButton(
                  backgroundColor: getColorSkin().primaryBlue,
                  onPressed: onRetry,
                  size: FButtonSize.size40,
                  child: Text(
                    'Tải lại',
                    style: getTypoSkin().medium16.copyWith(
                          color: getColorSkin().white,
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void showNetworkErrorDialog({
  required BuildContext context,
  required VoidCallback onRetry,
  String? title,
  String? message,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (dialogContext) => NetworkErrorDialog(
      title: title ?? 'Mất kết nối mạng',
      message: message ?? 'Bạn vui lòng kiểm tra lại internet và kết nối lại',
      onRetry: () {
        // Check connectivity before dismissing
        Connectivity().checkConnectivity().then((results) {
          final isConnected = results.any((result) => result != ConnectivityResult.none);

          // Close the current dialog
          if (Navigator.of(dialogContext, rootNavigator: true).canPop()) {
            Navigator.of(dialogContext, rootNavigator: true).pop();
          }

          if (isConnected) {
            // Execute the retry callback
            onRetry();
          } else {
            // Still disconnected, show dialog again after a short delay
            Future.delayed(const Duration(milliseconds: 300), () {
              showNetworkErrorDialog(
                context: context,
                onRetry: onRetry,
                title: title,
                message: message,
              );
            });
          }
        });
      },
    ),
  );
}

class NetworkErrorWrapper extends StatelessWidget {
  final bool isNetworkError;
  final Widget child;
  final VoidCallback onRetry;
  final String? title;
  final String? message;

  const NetworkErrorWrapper({
    super.key,
    required this.isNetworkError,
    required this.child,
    required this.onRetry,
    this.title,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    if (isNetworkError) {
      // Show dialog on the next frame to avoid build issues
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (dialogContext) => NetworkErrorDialog(
            title: title ?? 'Mất kết nối mạng',
            message: message ?? 'Bạn vui lòng kiểm tra lại internet và kết nối lại',
            onRetry: onRetry,
            showDialog: true,
          ),
        );
      });
    }
    return child;
  }
}
