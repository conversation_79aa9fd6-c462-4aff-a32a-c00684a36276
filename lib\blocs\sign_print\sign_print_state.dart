import 'package:equatable/equatable.dart';
import 'package:eapprove/models/form/sign_print_response_model.dart';

abstract class SignPrintState extends Equatable {
  const SignPrintState();

  @override
  List<Object?> get props => [];
}

class SignPrintInitial extends SignPrintState {}

class SignPrintLoading extends SignPrintState {}

class SignPrintLoaded extends SignPrintState {
  final SignPrintResponseModel signPrintData;

  const SignPrintLoaded(this.signPrintData);

  @override
  List<Object?> get props => [signPrintData];
}

class SignPrintError extends SignPrintState {
  final String message;

  const SignPrintError(this.message);

  @override
  List<Object?> get props => [message];
} 