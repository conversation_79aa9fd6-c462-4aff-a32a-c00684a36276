import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_state.dart';
import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/common/form/custom_dropdown_multi_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'dart:developer' as developer;

import 'package:hive_flutter/hive_flutter.dart';
import 'package:collection/collection.dart';

// Constants
const _kDefaultBorderRadius = 10.0;

mixin DropdownDisplayMixin on State<DropdownWidget> {
  List<String> _getDefaultDisplayFields(String orgchartType) {
    switch (orgchartType) {
      case 'department':
        return ['departmentName'];
      case 'position':
        return ['positionName'];
      case 'user':
      default:
        return ['username'];
    }
  }

  String _getDefaultValueField(String orgchartType) {
    switch (orgchartType) {
      case 'department':
        return 'username';
      case 'position':
        return 'positionId';
      case 'user':
      default:
        return 'username';
    }
  }

  String _getDefaultLabel(dynamic info, String orgchartType) {
    // Handle both Map and object with toJson() method
    final infoData = info is Map ? info : info.toJson();

    switch (orgchartType) {
      case 'department':
        return '${infoData['username']?.toString() ?? ''}_${infoData['name']?.toString() ?? ''}_${infoData['position']?.toString() ?? ''}';
      case 'position':
        return infoData['position']?.toString() ?? '';
      case 'user':
      default:
        return infoData['username']?.toString() ?? '';
    }
  }

  String getValueField() {
    final optionType = widget.data.optionType;
    final orgchartType =
        widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';

    if (optionType == 'orgchart') {
      return widget.data.optionConfig?['valueField']?.toString() ??
          _getDefaultValueField(orgchartType);
    }

    if (optionType == 'api_link') {
      return widget.data.optionConfig?['apiValue']?.toString() ?? 'id';
    }

    if (optionType == 'masterData') {
      return widget.data.optionConfig?['masterDataValue']?.toString() ?? 'id';
    }
    return 'value'; // for 'normal' and fallback
  }
}

class DropdownWidget extends StatefulWidget {
  final FormItemInfo data;
  final FormStateManager stateManager;
  final int? rowIndex;
  final bool? isShowLabel;

  const DropdownWidget({
    Key? key,
    required this.data,
    required this.stateManager,
    this.rowIndex,
    this.isShowLabel = true,
  }) : super(key: key);

  @override
  State<DropdownWidget> createState() => _DropdownWidgetState();
}

class _DropdownWidgetState extends State<DropdownWidget>
    with DropdownDisplayMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(DropdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DropdownBloc, DropdownState>(
      listener: (context, state) {
        final key = widget.data.parentName?.isNotEmpty == true
            ? '${widget.data.parentName}.${widget.data.name}'
            : widget.data.name ?? "";

        final isLoading = state.isLoading[key] ?? false;
        if (!isLoading) {
          final response = state.dropdownResponses[key];
          if (response != null) {
            if (!response.isSuccess) {
              // developer.log('Error: ${response.errorMessage}',
              //     name: 'dropdown_widget');
            } else {
              final currentValue = widget.stateManager.getFieldValue(
                widget.data.name ?? '',
                rowIndex: widget.rowIndex,
              );

              handleFeedDataTo(currentValue ?? '', response);
            }
          }
        }
      },
      child: BlocBuilder<DropdownBloc, DropdownState>(
        builder: (context, state) {
          final key = widget.data.parentName?.isNotEmpty == true
              ? '${widget.data.parentName}.${widget.data.name}'
              : widget.data.name ?? "";
          final response = state.dropdownResponses[key];
          final loading = state.isLoading[key] ?? false;
          if (loading) {
            return _buildShimmerLoading();
          }
          return renderDropdownMenu(response ?? BaseResponseModel(data: []));
        },
      ),
    );
  }

  void handleFeedDataTo(String selectedValue, BaseResponseModel response) {
    final feedDataTo =
        widget.data.optionConfig?['feedDataTo'] as List<dynamic>?;
    if (feedDataTo == null || feedDataTo.isEmpty) return;

    final String valueField = getValueField();

    // Find the selected item in the response data
    final selectedItem = response.data.firstWhereOrNull(
      (item) {
        if (item == null) return false;
        // Handle both Map and object with toJson() method
        final itemData = item is Map ? item : item.toJson();
        final matches = itemData[valueField]?.toString().toLowerCase() ==
            selectedValue.toString().toLowerCase();

        return matches;
      },
    );

    if (selectedItem == null) {
      if (feedDataTo.any((feed) =>
          feed['input']?.toString().contains('slt_hinhThucMuaHang') == true)) {
        // developer.log('🔍 slt_hinhThucMuaHang - no matching item found',
        //     name: 'FormStateManager');
      }
      return;
    }

    // Process each feed configuration
    for (final feed in feedDataTo) {
      final input = feed['input']?.toString().replaceAll('@', '');
      final fieldFeed = feed['fieldFeed']?.toString();

      if (input == null ||
          input.isEmpty ||
          fieldFeed == null ||
          fieldFeed.isEmpty) {
        continue;
      }

      try {
        // Get the value to feed
        final value = selectedItem is Map
            ? selectedItem[fieldFeed]?.toString() ?? ''
            : selectedItem.toJson()[fieldFeed]?.toString() ?? '';

        final currentWidget = widget.stateManager.findWidgetByName(input);

        final currentValue = widget.stateManager.getFieldValue(
          input,
          rowIndex: widget.rowIndex,
        );

        if (currentValue != value) {
          widget.stateManager.setFieldValue(
            input,
            value,
            parentName: currentWidget?.parentName,
            rowIndex: widget.rowIndex,
            optionSelect: _buildOptionSelect(
                response, SelectItem(label: value, value: value)),
            fromFunction: 'handleFeedDataTo',
          );
        }
      } catch (e) {
        developer.log('Error setting field value: $e', name: 'dropdown_widget');
      }
    }
  }

  List<SelectItem> _buildDropdownItems(BaseResponseModel response) {
    // debugPrint("buildDropdownItemsbuildDropdownItems ${widget.data.name} ${widget.data.optionConfig?['apiURLBase']}");
    final valueField = getValueField();
    if (widget.data.optionType == 'masterData') {
      if (response.data.isNotEmpty) {
        final items = response.data.map((item) {
          // Handle both Map and object with toJson() method
          final itemData = item is Map ? item : item.toJson();
          final displayFields = widget.data.optionConfig?['masterDataDisplay']
                  as List<dynamic>? ??
              [];
          final displayValues = displayFields
              .map((field) => itemData[field]?.toString() ?? '')
              .where((value) => value.isNotEmpty)
              .toList();

          return SelectItem(
            label: displayValues.isNotEmpty
                ? displayValues.join('_')
                : itemData['id']?.toString() ?? '',
            value: itemData[valueField]?.toString() ?? '',
          );
        }).toList();
        return items;
      }
      return [];
    }
    if (widget.data.optionType == 'orgchart') {
      final orgchartType =
          widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';

      final displayFields =
          widget.data.optionConfig?['displayField'] as List<dynamic>? ??
              _getDefaultDisplayFields(orgchartType);

      final items = response.data.map((info) {
        // Handle both Map and object with toJson() method
        final infoData = info is Map ? info : info.toJson();

        final displayValues = displayFields
            .map((field) => infoData[field]?.toString() ?? '')
            .where((value) => value.isNotEmpty)
            .toList();

        String label;
        String value;

        if (orgchartType == 'department') {
          value = infoData[valueField]?.toString() ?? '';
          label = displayValues.isNotEmpty
              ? displayValues.join('_')
              : '${infoData[valueField]?.toString() ?? ''}_${infoData['name']?.toString() ?? ''}_${infoData['position']?.toString() ?? ''}';
        } else {
          label = displayValues.isNotEmpty
              ? displayValues.join('_')
              : _getDefaultLabel(infoData, orgchartType);
          value = infoData[valueField]?.toString() ?? '';
        }

        return SelectItem(label: label, value: value);
      }).toList();

      return items;
    }

    if (widget.data.optionType == 'api_link') {
      final seenCostItemCodes = <String>{};
      final uniqueData = response.data.where((item) {
        final itemData = item is Map ? item : item.toJson();
        final code = itemData[valueField]?.toString() ?? '';
        if (seenCostItemCodes.contains(code)) {
          return false;
        } else {
          seenCostItemCodes.add(code);
          return true;
        }
      }).toList();
      final displayFields =
          widget.data.optionConfig?['apiDisplay'] as List<dynamic>? ?? [];

      final items = uniqueData.map((item) {
        // Handle both Map and object with toJson() method
        final itemData = item is Map ? item : item.toJson();

        if (displayFields.isEmpty) {
          return SelectItem(
              label: itemData[valueField]?.toString() ?? '',
              value: itemData[valueField]?.toString() ?? '');
        }

        final displayValues = displayFields
            .map((field) => itemData[field]?.toString() ?? '')
            .where((value) => value.isNotEmpty)
            .toList();

        return SelectItem(
          label: displayValues.isNotEmpty
              ? displayValues.join('_')
              : itemData[valueField]?.toString() ?? '',
          value: itemData[valueField]?.toString() ?? '',
        );
      }).toList();
      return items;
    }

    // Handle static options
    if (widget.data.optionType == 'normal' && widget.data.option != null) {
      return widget.data.option!.map((option) {
        return SelectItem(
          label: option['label']?.toString() ?? '',
          value: option['value']?.toString() ?? '',
        );
      }).toList();
    }

    return [];
  }

  Widget _buildShimmerLoading() {
    return AppConstraint.buildShimmer(
      child: Container(
        width: double.infinity,
        height: 40.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
        ),
      ),
    );
  }

  

  List<Map<String, dynamic>>? _buildOptionSelect(
      BaseResponseModel response, SelectItem defaultValue) {
    final valueField = getValueField();
    final matchingItems = response.data
        .where((item) => item[valueField] == defaultValue.value)
        .toList();
    final optionSelect = {
      'value': defaultValue.value,
      'label': defaultValue.label,
      'options': matchingItems.isNotEmpty ? matchingItems[0] : null
    };
    return matchingItems.isNotEmpty ? [optionSelect] : null;
  }

  Widget renderDropdownMenu(BaseResponseModel response) {
    final items = _buildDropdownItems(response);
    // if(widget.data.name == 'mtx_phongBanAn'){
    //   developer.log('renderDropdownMenu: ${widget.data.name} ${items}', name: 'renderDropdownMenu');
    // }

    final Box authBox = Hive.box('authentication');
    final String? username = authBox.get('username');

    SelectItem defaultValue =
        SelectItem(label: '', value: ''); // Initialize with default value
    final valueOnSelect = widget.stateManager.getFieldValue(
      widget.data.name ?? '',
      rowIndex: widget.rowIndex,
    );

    // debugPrint('valueOnSelect: ${widget.data.name} $valueOnSelect');
    if (widget.data.value != null && widget.data.value.isNotEmpty) {
      defaultValue = items.firstWhere(
        (item) => item.value == widget.data.value,
        orElse: () => SelectItem(label: '', value: ''),
      );
    }
    if (widget.data.isUserCurrentUserLogin == true) {
      if (items.isNotEmpty) {
        defaultValue = items.firstWhere(
          (item) => item.value == username,
          orElse: () => SelectItem(label: '', value: ''),
        );
      }
    }
    if (widget.data.defaultSelect == true) {
      defaultValue =
          items.isNotEmpty ? items.first : SelectItem(label: '', value: '');
    }
    if (items.isNotEmpty && valueOnSelect != null && valueOnSelect.isNotEmpty) {
      defaultValue = items.firstWhere(
        (item) => item.value == valueOnSelect,
        orElse: () => SelectItem(label: '', value: ''),
      );
    }
    if (defaultValue.value.isNotEmpty) {
      final currentValue = widget.stateManager.getFieldValue(
        widget.data.name ?? '',
        rowIndex: widget.rowIndex,
      );

      if (currentValue != defaultValue.value) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.stateManager.setFieldValue(
            widget.data.name ?? '',
            defaultValue.value,
            parentName: widget.data.parentName,
            rowIndex: widget.rowIndex,
            optionSelect: _buildOptionSelect(response, defaultValue),
            fromFunction: 'widget.data.defaultSelect',
          );
        });
      }
    }
    // if (widget.data.display == false) {
    //   return const SizedBox.shrink();
    // }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if(widget.isShowLabel == true)
        FormLabel(
            displayName: widget.data.displayName,
            label: widget.data.label,
            suggestText: widget.data.suggestText,
            isRequired: widget.data.validations?['required'] == true,
          ),
        Container(
            margin: EdgeInsets.only(top: 8.h),
            child: widget.data.validations?['isMulti'] == true
                ? CustomDropdownMultiForm(
                    label: widget.data.displayName ?? '',
                    placeholder: widget.data.placeholder ?? 'Select options',
                    options: items,
                    selectedItems: items
                        .where((item) =>
                            widget.data.value != null &&
                            (widget.data.value as List).contains(item.value))
                        .toList(),
                    onSelected: (selectedItems) {
                      widget.data.value =
                          selectedItems.map((item) => item.value).toList();
                      widget.stateManager.setFieldValue(
                        widget.data.name ?? '',
                        widget.data.value,
                        parentName: widget.data.parentName,
                        rowIndex: widget.rowIndex,
                        optionSelect:
                            _buildOptionSelect(response, defaultValue),
                        fromFunction: 'onSelectedMulti',
                      );
                    },
                    isFilled: true,
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(_kDefaultBorderRadius.r),
                      border:
                          Border.all(color: getColorSkin().lightGray, width: 1),
                      color: widget.data.readonly == true
                          ? getColorSkin().whiteSmoke
                          : getColorSkin().white,
                    ),
                    isDisabled: widget.data.readonly == true,
                    dropdownHeight: 40.h,
                    isRequired: widget.data.validations?['required'] == true,
                  )
                : CustomDropdownMenu(
                    showLabel: false,
                    key: widget.data.name != null
                        ? Key(widget.data.name!)
                        : null,
                    label: widget.data.displayName ?? widget.data.label ?? '',
                    placeholder: widget.data.placeholder ?? 'Select an option',
                    options: items,
                    onSelected: (selectedItem) {
                      if (selectedItem != null) {
                        final newValue = selectedItem.value;
                        // if (widget.data.value != newValue) {
                        // widget.data.value = newValue;
                        widget.stateManager.setFieldValue(
                          widget.data.name ?? '',
                          newValue,
                          parentName: widget.data.parentName,
                          rowIndex: widget.rowIndex,
                          optionSelect:
                              _buildOptionSelect(response, selectedItem),
                          fromFunction: 'onSelected',
                        );
                        // Only handle feed data when a new value is selected
                        handleFeedDataTo(newValue, response);
                      } else {
                        //   widget.data.value = null;
                        widget.stateManager.setFieldValue(
                          widget.data.name ?? '',
                          null,
                          parentName: widget.data.parentName,
                          rowIndex: widget.rowIndex,
                        );
                      }
                    },
                    isFilled: true,
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(_kDefaultBorderRadius.r),
                      border:
                          Border.all(color: getColorSkin().lightGray, width: 1),
                      color: widget.data.readonly == true
                          ? getColorSkin().whiteSmoke
                          : getColorSkin().white,
                    ),
                    defaultValue: defaultValue,
                    showDeleteIcon: widget.data.value != null &&
                        widget.data.readonly == false,
                    isDisabled: widget.data.readonly == true,
                    dropdownHeight: 40.h,
                    isRequired: widget.data.validations?['required'] == true,
                  ))
      ],
    );
  }
}
