import 'package:eapprove/enum/enum.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/helper/event_expression.dart';
import 'dart:developer' as developer;

class FormValidator {
  final Map<String, dynamic> formState;
  final Map<String, TextEditingController> controllers;

  FormValidator({
    required this.formState,
    required this.controllers,
  });

  // Validate a single field
  String? validateField(FormItemInfo widget) {
    if (widget.validations == null) return null;

    final value = formState[widget.name.toString()];
    developer.log('formStateValue: ${widget.name} - $value',
        name: 'validateField');
    // Required validation

    if (widget.type == FormItemType.fileUpload) {
      return null;
    }

    if (widget.validations!['required'] == true &&
        (value == null || value.toString().isEmpty)) {
      return '${widget.label} là bắt buộc';
    }

    // Add more validation rules here as needed
    return null;
  }

  // Evaluate event expressions
  Map<String, dynamic> evaluateEventExpressions(
      FormItemInfo widget, String? fromFunction) {

    final expressions = widget.eventExpression?.map((e) {
      developer.log('econditionResult: ${widget.name} ${e['conditionResult']}', name: 'evaluateEventExpressions');
      return {
        'condition': e['condition'],
        'expression': e['expression'],
        'conditionResult': e['conditionResult'],
        'properties': e['properties'] is List 
            ? (e['properties'] as List).map((p) {
                if (p is! Map<String, dynamic>) {
                  return <String, dynamic>{};
                }
                return {
                  'field': p['field'],
                };
              }).toList()
            : [],
      };
    }).toList();
    final results =
        evaluateConditions(expressions ?? [], formState, fromFunction, widget.name);

    // developer.log('evaluateConditionsresults: ${widget.name}  $results', name: 'evaluateEventExpressions');
    return results;
  }
}
