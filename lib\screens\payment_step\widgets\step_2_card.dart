import 'package:eapprove/screens/payment_step/widgets/information_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';

class EmployeeNavigationCard extends StatefulWidget {
  const EmployeeNavigationCard({super.key});

  @override
  State<EmployeeNavigationCard> createState() => _EmployeeNavigationCardState();
}

class _EmployeeNavigationCardState extends State<EmployeeNavigationCard> {
  final List<Map<String, dynamic>> employees = [
    {
      'name': 'Tran Huu Hoa',
      'department': 'DES',
      'gender': 'HoaTH2 (DES)',
      'workCode': '0049',
      'recruitmentType': 'Tuyển dụng mới',
      'startDate': 'Nhân viên',
      'endDate': 'DES',
      'registrationDate': 'Tran Hu<PERSON>g',
      'computerRequirement': 'Lãnh đạo',
      'workHours': 'Nhân viên',
      'specialRequirements': 'Mô tả về nội dụng công việc ticket này',
      'informationTable': {
        'supplierCode': 'Công ty cổ phần thương mại và phát triển hệ thống PC',
        'deliveryLocation': 'Hà Nội',
        'supplierDeliveryTime': '05',
        'estimatedTime': '05',
        'paymentMethod': 'TM/CK',
        'productSpecification': 'Tuyển dụng mới',
        'productWarranty': '5 năm',
        'inventoryStatus': 'Còn hàng',
        'additionalInfo': 'Đang vận chuyển',
        'priceQuote': '1,5,6_Cat6, RAM, Ổ cứng, Hệ thốngPC.pdf'
      }
    },
    {
      'name': 'Nguyen Van A',
      'department': 'IT',
      'gender': 'ANV5 (IT)',
      'workCode': '0050',
      'recruitmentType': 'Tái tuyển dụng',
      'startDate': 'Quản lý',
      'endDate': 'IT',
      'registrationDate': 'Nguyen Minh Quan',
      'computerRequirement': 'Nhân viên',
      'workHours': 'Quản lý',
      'specialRequirements': 'Cần hỗ trợ thiết lập môi trường làm việc cho nhân viên mới',
      'informationTable': {
        'supplierCode': 'Công ty TNHH phát triển công nghệ IT Solutions',
        'deliveryLocation': 'TP HCM',
        'supplierDeliveryTime': '07',
        'estimatedTime': '10',
        'paymentMethod': 'CK',
        'productSpecification': 'Tái tuyển dụng',
        'productWarranty': '3 năm',
        'inventoryStatus': 'Đặt hàng',
        'additionalInfo': 'Chờ phê duyệt',
        'priceQuote': '2,1,5_Laptop, Màn hình, Chuột.pdf'
      }
    }
  ];

  int currentIndex = 0;

  void goToPrevious() {
    if (currentIndex > 0) {
      setState(() {
        currentIndex--;
      });
    }
  }

  void goToNext() {
    if (currentIndex < employees.length - 1) {
      setState(() {
        currentIndex++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Card(
          color: getColorSkin().white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Navigation header with arrows
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: goToPrevious,
                      icon: Icon(Icons.chevron_left,
                          color: currentIndex > 0
                              ? getColorSkin().colorPrimary
                              : getColorSkin().lightGray),
                    ),
                    Text(
                      'Nhân viên ${currentIndex + 1}/${employees.length}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    IconButton(
                      onPressed: goToNext,
                      icon: Icon(Icons.chevron_right,
                          color: currentIndex < employees.length - 1
                              ? getColorSkin().colorPrimary
                              : getColorSkin().lightGray),
                    ),
                  ],
                ),

                SizedBox(height: 16.h),
                CustomTextForm(
                    showDeleteButton: false,
                    initialValue: employees[currentIndex]['name'],
                    labelText: 'Tên nhân viên',
                    hintText: 'Tên nhân viên',
                    onChanged: (value) {},
                    filled: true,
                    fillColor: getColorSkin().white
                ),

                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  label: 'Bộ phận làm việc',
                  options: [
                    SelectItem(label: 'DES', value: 'DES'),
                    SelectItem(label: 'IT', value: 'IT'),
                  ],
                  onSelected: (value) {},
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border:Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(label: employees[currentIndex]['department'], value: employees[currentIndex]['department']),
                ),

                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  label: 'Giới tính',
                  placeholder: 'Giới tính',
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'HoaTH2 (DES)', value: 'HoaTH2 (DES)'),
                    SelectItem(label: 'ANV5 (IT)', value: 'ANV5 (IT)'),
                  ],
                  onSelected: (value) {},
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border:Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(label: employees[currentIndex]['gender'], value: employees[currentIndex]['gender']),
                  showDeleteIcon: false,
                ),

                SizedBox(height: 16.h),
                CustomTextForm(
                  showDeleteButton: false,
                  initialValue: employees[currentIndex]['workCode'],
                  labelText: 'Vị trí công việc',
                  onChanged: (value) {},
                  filled: true,
                    fillColor: getColorSkin().white
                ),
                SizedBox(height: 16.h),
                const InformationTable(),
                SizedBox(height: 16.h),

                CustomDropdownMenu(
                  label: 'Tuyển dụng mới/Tái tuyển dụng',
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'Tuyển dụng mới', value: 'Tuyển dụng mới'),
                    SelectItem(label: 'Tái tuyển dụng', value: 'Tái tuyển dụng'),
                  ],
                  onSelected: (value) {},
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(
                      label: employees[currentIndex]['recruitmentType'] ?? 'Tuyển dụng mới',
                      value: employees[currentIndex]['recruitmentType'] ?? 'Tuyển dụng mới'
                  ),
                ),
                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  label: 'Ngày bắt đầu',
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'Nhân viên', value: 'Nhân viên'),
                    SelectItem(label: 'HR', value: 'HR'),
                  ],
                  onSelected: (value) {},
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(
                      label: employees[currentIndex]['startDate'],
                      value: employees[currentIndex]['startDate']
                  ),
                ),
                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  label: 'Ngày kết thúc',
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'DES', value: 'DES'),
                    SelectItem(label: '27-02-2025', value: '27-02-2025'),
                  ],
                  onSelected: (value) {},
                  isFilled: true,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border:Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(
                      label: employees[currentIndex]['endDate'],
                      value: employees[currentIndex]['endDate']
                  ),
                ),
                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  label: 'Ngày đăng ký làm việc',
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'Tran Huu Thang', value: 'tran huu thang'),
                    SelectItem(label: '28-02-2025', value: '28-02-2025'),
                  ],
                  onSelected: (value) {},
                  isFilled: true,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border:Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(
                      label: employees[currentIndex]['registrationDate'],
                      value: employees[currentIndex]['registrationDate']
                  ),
                ),
                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  label: 'Cần máy tính bàn',
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'Lãnh đạo', value: 'Lãnh đạo'),
                    SelectItem(label: 'Nhân viên', value: 'Nhân viên'),
                  ],
                  onSelected: (value) {},
                  isFilled: true,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border:Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(
                      label: employees[currentIndex]['computerRequirement'] ?? 'Lãnh đạo',
                      value: employees[currentIndex]['computerRequirement'] ?? 'Lãnh đạo'
                  ),
                ),
                SizedBox(height: 16.h),
                CustomDropdownMenu(
                  label: 'Vé giữ xe tháng',
                  showDeleteIcon: false,
                  dropdownHeight: 40.h,
                  options: [
                    SelectItem(label: 'Nhân viên', value: 'Nhân viên'),
                    SelectItem(label: 'Quản lý', value: 'Quản lý'),
                  ],
                  onSelected: (value) {},
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(color: getColorSkin().lightGray, width: 1)
                  ),
                  defaultValue: SelectItem(
                      label: employees[currentIndex]['workHours'] ?? 'Nhân viên',
                      value: employees[currentIndex]['workHours'] ?? 'Nhân viên'
                  ),
                ),
                SizedBox(height: 16.h),
                CustomTextForm(
                    maxLines: 4,
                    showDeleteButton: false,
                    // initialValue: 'Trung bình',
                    labelText: 'Yêu cầu đặc biệt',
                    hintText: 'Mô tả về nội dung công việc ticket này',
                    onChanged: (value) {},
                    filled: true,
                    fillColor: getColorSkin().white
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding:  EdgeInsets.symmetric(vertical: 12.h, horizontal: 4.w),
          child: CustomDropdownMenu(
            isFilled: true,
            label: 'Thông báo đến người liên quan',
            showDeleteIcon: false,
            dropdownHeight: 40.h,
            options: [
              SelectItem(label: 'Nhân viên', value: 'Nhân viên'),
              SelectItem(label: 'Quản lý', value: 'Quản lý'),
            ],
            onSelected: (value) {},
            decoration: BoxDecoration(
                color: getColorSkin().white,
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(color: getColorSkin().lightGray, width: 1)
            ),
            defaultValue: SelectItem(
                label: employees[currentIndex]['related'] ?? 'Nhân viên liên quan',
                value: employees[currentIndex]['related'] ?? 'Nhân viên liên quan'
            ),
          ),
        ),
      ],
    );
  }
}