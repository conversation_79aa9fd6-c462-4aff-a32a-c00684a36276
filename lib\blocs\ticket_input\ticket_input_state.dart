import 'package:equatable/equatable.dart';
import 'package:eapprove/models/ticket_input_model.dart';

abstract class TicketInputState extends Equatable {
  const TicketInputState();

  @override
  List<Object> get props => [];
}

class TicketInputInitial extends TicketInputState {}

class TicketInputLoading extends TicketInputState {}

class TicketInputLoaded extends TicketInputState {
  final TicketInputModel ticketInput;

  const TicketInputLoaded(this.ticketInput);

  @override
  List<Object> get props => [ticketInput];
}

class TicketInputError extends TicketInputState {
  final String message;

  const TicketInputError(this.message);

  @override
  List<Object> get props => [message];
} 