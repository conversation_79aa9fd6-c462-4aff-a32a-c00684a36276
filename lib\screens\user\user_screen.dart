import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/account_model.dart';
import 'package:eapprove/screens/authorize_management/authorize_management_screen.dart';
import 'package:eapprove/screens/login/login_screen.dart';
import 'package:eapprove/screens/user/user_information_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_sdk/widgets/custom_bottomsheet.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:eapprove/screens/user/switch_account_screen.dart';

class UserScreen extends StatefulWidget {
  const UserScreen({super.key});

  @override
  State<UserScreen> createState() => _UserScreeenState();
}

class _UserScreeenState extends State<UserScreen> {
  int? selectedIndex; // Lưu vị trí tài khoản được chọn

  // Danh sách tài khoản giả lập
  final List<Map<String, String>> accounts = [
    {
      "image": StringImage.logo_switch_account_1,
      "name": "Phạm Võ Tuấn Ngọc",
      "id": "nve1000000001",
      "email": "<EMAIL>",
      "phone": "**********",
      "company": "1000 — Công ty Cổ phần tập đoàn Đất Xanh",
      "position": "******** — Giám đốc Bán Hàng",
      "role": "CV0023 — Giám đốc bán hàng",
    },
    {
      "image": StringImage.logo_switch_account_1,
      "name": "Phạm Võ Tuấn Ngọc",
      "id": "nve1260000001",
      "email": "<EMAIL>",
      "phone": "**********",
      "company": "1260 — Công ty Cổ phần Cara Group",
      "position": "******** — Giám đốc Bán Hàng",
      "role": "CV0023 — Giám đốc bán hàng",
    },
    {
      "image": StringImage.logo_switch_account_1,
      "name": "Phạm Võ Tuấn Ngọc",
      "id": "nve1270000001",
      "email": "<EMAIL>",
      "phone": "**********",
      "company": "1260 — Công ty Cổ phần Cara Group",
      "position": "******** — Giám đốc Bán Hàng",
      "role": "CV0023 — Giám đốc bán hàng",
    },
  ];

  void showAccountBottomSheet(
      BuildContext context, List<AccountModel> accounts) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return GestureDetector(
          onTap: () => Navigator.pop(context),
          child: Container(
            color: Colors.transparent,
            child: DraggableScrollableSheet(
              initialChildSize: 0.5,
              minChildSize: 0.5,
              maxChildSize: 0.8,
              builder: (context, scrollController) {
                return Container(
                  padding: EdgeInsets.only(top: 4.h),
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            icon: SvgPicture.asset(StringImage.ic_close),
                            onPressed: () {
                              Navigator.pop(context);
                            },
                          ),
                          Expanded(
                            child: Text(
                              "Đổi tài khoản",
                              textAlign: TextAlign.center,
                              style: getTypoSkin()
                                  .medium16
                                  .copyWith(color: getColorSkin().ink1),
                            ),
                          ),
                        ],
                      ),
                      //SizedBox(height: 16.h),
                      CustomDivider(
                        indent: 30,
                        endIndent: 30,
                        color: getColorSkin().ink5,
                      ),
                      SizedBox(height: 10.h),
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: accounts.length,
                          itemBuilder: (context, index) {
                            final account = accounts[index];
                            return Column(
                              children: [
                                ListTile(
                                  leading: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.r),
                                    child: SvgPicture.asset(
                                      StringImage.ic_user_solid,
                                      width: 48.w,
                                      height: 48.h,
                                    ),
                                  ),
                                  title:
                                      // Text("${account.name} - ${account.id}",
                                      //     style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1)),
                                      Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${account.name} - ${account.id}",
                                          style: getTypoSkin()
                                              .medium14
                                              .copyWith(
                                                  color: getColorSkin().ink1),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      IconButton(
                                          onPressed: () {
                                            debugPrint(
                                                'Selected Account: ${account.name}');
                                          },
                                          icon: SvgPicture.asset(
                                              StringImage.ic_arrow_right))
                                    ],
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Email : ${account.email}",
                                          style: getTypoSkin()
                                              .body2Regular
                                              .copyWith(
                                                  color: getColorSkin().ink1)),
                                      Text("Liên Hệ : ${account.phone}",
                                          style: getTypoSkin()
                                              .body2Regular
                                              .copyWith(
                                                  color: getColorSkin().ink1)),
                                      Text("Công ty : ${account.company}",
                                          style: getTypoSkin()
                                              .body2Regular
                                              .copyWith(
                                                  color: getColorSkin().ink1)),
                                      Text("Chức danh : ${account.position}",
                                          style: getTypoSkin()
                                              .body2Regular
                                              .copyWith(
                                                  color: getColorSkin().ink1)),
                                      Text("Chức vụ : ${account.role}",
                                          style: getTypoSkin()
                                              .body2Regular
                                              .copyWith(
                                                  color: getColorSkin().ink1)),
                                    ],
                                  ),
                                  onTap: () {
                                    debugPrint(
                                        'Selected Account: ${account.name}');
                                  },
                                ),
                                CustomDivider(
                                  indent: 30,
                                  endIndent: 30,
                                  color: getColorSkin().ink5,
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: Column(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                color: Colors.white,
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const UserInformationScreen()),
                          );
                        },
                        child: Row(
                          children: [
                            Stack(
                              children: [
                                const CircleAvatar(
                                  radius: 30,
                                  backgroundImage: NetworkImage(
                                      'https://i.pravatar.cc/100?img=3'),
                                ),
                                Positioned(
                                  bottom: -6.h,
                                  right: -4.w,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 5.w, vertical: 5.h),
                                    width: 24.w,
                                    height: 24.h,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: getColorSkin().white,
                                      border: Border.all(
                                          color: getColorSkin().ink5,
                                          width: 1.w),
                                    ),
                                    child: SvgPicture.asset(
                                      StringImage.ic_camera,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(width: 14.w),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Tran Huu Hoa',
                                  style: getTypoSkin()
                                      .medium16
                                      .copyWith(color: getColorSkin().ink1),
                                ),
                                Text(
                                  '<EMAIL>',
                                  style: getTypoSkin()
                                      .bodyRegular14
                                      .copyWith(color: const Color(0xFF8D8D8D)),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        showAccountBottomSheet(context,
                            accounts.map(AccountModel.fromMap).toList());
                      },
                      icon: SvgPicture.asset(
                        StringImage.ic_arrow_down,
                        width: 18.w,
                        height: 9.h,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 4.h),
              _buildMenuItem(Image.asset(StringImage.ic_switchAccount),
                  'Quản lý ủy quyền', true, getColorSkin().ink1, () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const AuthorizeManagementScreen()),
                );
              }),
              _buildMenuItem(Image.asset(StringImage.ic_switchAccount),
                  'Đổi tài khoản', true, getColorSkin().ink1, () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const SwitchAccountScreen()),
                );
              }),
              _buildMenuItem(Image.asset(StringImage.ic_setting), 'Setting',
                  false, getColorSkin().ink1, () {
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //       builder: (context) => const SettingScreen()),
                // );
              }),
              _buildMenuItem(FIcon(icon: FIconData.icLogOut), 'Đăng xuất',
                  false, getColorSkin().red, () {
                showLogoutDialog(context);
              }),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _buildMenuItem(Widget leadingIcon, String title, bool rightArrow,
    Color textColor, VoidCallback? onTap) {
  return Container(
    margin: EdgeInsets.only(bottom: 4.h),
    color: Colors.white,
    child: ListTile(
      leading: leadingIcon,
      title: Text(
        title,
        style: getTypoSkin().bodyRegular14.copyWith(color: textColor),
      ),
      trailing:
          rightArrow ? SvgPicture.asset(StringImage.ic_arrow_right) : null,
      onTap: onTap,
    ),
  );
}

void showLogoutDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        backgroundColor: Colors.white,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 10.h),
              Text(
                'Đăng xuất',
                style:
                    getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
              ),
              SizedBox(height: 10.h),
              Text(
                'Bạn có muốn thoát khỏi hệ thống không?',
                textAlign: TextAlign.center,
                style: getTypoSkin().bodyRegular14.copyWith(
                      color: getColorSkin().ink1,
                    ),
              ),
              SizedBox(height: 20.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: getColorSkin().ink3, // Màu xám
                        shape: RoundedRectangleBorder(),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Text(
                          'Hủy',
                          style: getTypoSkin()
                              .bodyRegular14
                              .copyWith(color: getColorSkin().white),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const LoginScreen()),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: getColorSkin().red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.w),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Text(
                          'Đăng xuất',
                          style: getTypoSkin()
                              .bodyRegular14
                              .copyWith(color: getColorSkin().white),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      );
    },
  );
}
