import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_sdk/widgets/custom_button.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';

import 'package:flutter_svg/flutter_svg.dart';

class SwitchAccountScreen extends StatefulWidget {
  const SwitchAccountScreen({super.key});

  @override
  State<SwitchAccountScreen> createState() => _SwitchAccountScreenState();
}

class _SwitchAccountScreenState extends State<SwitchAccountScreen> {
  int? selectedIndex; // Lưu vị trí tài khoản đư<PERSON><PERSON> chọn

  // Danh sách tài khoản gi<PERSON> lập
  final List<Map<String, String>> accounts = [
    {
      "image": StringImage.logo_switch_account_1,
      "name": "<PERSON><PERSON><PERSON>",
      "id": "nve1000000001",
      "email": "<EMAIL>",
      "phone": "**********",
      "company": "1000 — Công ty Cổ phần tập đoàn Đất Xanh",
      "position": "******** — Giám đốc Bán Hàng",
      "role": "CV0023 — Giám đốc bán hàng",
    },
    {
      "image": StringImage.logo_switch_account_2,
      "name": "Phạm Võ Tuấn Ngọc",
      "id": "nve1260000001",
      "email": "<EMAIL>",
      "phone": "**********",
      "company": "1260 — Công ty Cổ phần Cara Group",
      "position": "******** — Giám đốc Bán Hàng",
      "role": "CV0023 — Giám đốc bán hàng",
    },
    {
      "image": StringImage.logo_switch_account_3,
      "name": "Phạm Võ Tuấn Ngọc",
      "id": "nve1270000001",
      "email": "<EMAIL>",
      "phone": "**********",
      "company": "1260 — Công ty Cổ phần Cara Group",
      "position": "******** — Giám đốc Bán Hàng",
      "role": "CV0023 — Giám đốc bán hàng",
    },
  ];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: FIcon(icon: FIconData.icBack),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          "Đổi tài khoản",
          style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
        ),
        centerTitle: true,
      ),
      body: Container(
        color: getColorSkin().ink5,
        padding: EdgeInsets.only(top: 4.h),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: accounts.length,
                itemBuilder: (context, index) {
                  final account = accounts[index];
                  final isSelected = selectedIndex == index;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedIndex = index;
                      });
                    },
                    child: Column(children: [
                      CustomDivider(
                        indent: 30,
                        endIndent: 30,
                        color: getColorSkin().ink5,
                      ),
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: isSelected ? getColorSkin().ink4 : getColorSkin().white,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: Image.asset(
                                account["image"]!,
                                width: 60.w,
                                height: 27.w,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return SvgPicture.asset(
                                    StringImage.ic_user_solid,
                                    width: 60.w,
                                  );
                                },
                              ),
                            ),
                            SizedBox(width: 16.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("${account['name']} - ${account['id']}",
                                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1)),
                                  SizedBox(height: 4.h),
                                  Text("Email : ${account['email']}",
                                      style: getTypoSkin().body2Regular.copyWith(color: getColorSkin().ink1)),
                                  Text("Liên Hệ : ${account['phone']}",
                                      style: getTypoSkin().body2Regular.copyWith(color: getColorSkin().ink1)),
                                  Text("Công ty : ${account['company']}",
                                      style: getTypoSkin().body2Regular.copyWith(color: getColorSkin().ink1)),
                                  Text("Chức danh : ${account['position']}",
                                      style: getTypoSkin().body2Regular.copyWith(color: getColorSkin().ink1)),
                                  Text("Chức vụ : ${account['role']}",
                                      style: getTypoSkin().body2Regular.copyWith(color: getColorSkin().ink1)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ]),
                  );
                },
              ),
            ),
            selectedIndex != null
                ? Container(
                    margin: EdgeInsets.only(bottom: 20.h),
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4, spreadRadius: 1)],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: CustomButton(
                            label: "Huỷ",
                            onPressed: () => Navigator.pop(context),
                            btnStyle: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(getColorSkin().ink3),
                              shape: WidgetStateProperty.all(
                                  RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r))),
                              padding: WidgetStateProperty.all(EdgeInsets.symmetric(vertical: 6.h)),
                            ),
                            textStyle: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().white),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          child: CustomButton(
                            label: "Chuyển",
                            onPressed: selectedIndex != null
                                ? () {
                                    _showConfirmDialog();
                                  }
                                : null,
                            btnStyle: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(getColorSkin().primaryBlue),
                              shape: WidgetStateProperty.all(
                                  RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r))),
                              padding: WidgetStateProperty.all(EdgeInsets.symmetric(vertical: 6.h)),
                            ),
                            textStyle: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().white),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(),
          ],
        ),
      ),
    );
    ;
  }

  void _showConfirmDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("Xác nhận chuyển tài khoản"),
          content: Text("Bạn có chắc muốn chuyển sang tài khoản này không?"),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Hủy", style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text("Chuyển tài khoản thành công!")),
                );
              },
              child: Text("Xác nhận", style: TextStyle(color: Colors.blue)),
            ),
          ],
        );
      },
    );
  }
}
