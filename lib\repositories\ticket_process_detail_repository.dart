import 'package:eapprove/models/handle_ticket/approve_response_model.dart';
import 'package:eapprove/models/handle_ticket/inheritable_tasks_model.dart';
import 'package:eapprove/models/handle_ticket/share_ticket_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_phieu_response_model.dart';
import 'package:eapprove/models/handle_ticket/user_drop_down_model.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:eapprove/models/pyc/ticket_process_detail_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'dart:convert';
import 'dart:developer' as developer;

import 'package:flutter/cupertino.dart';

import 'package:eapprove/models/handle_ticket/assistant_opinion_request_model.dart';
import 'package:eapprove/models/handle_ticket/assistant_opinion_response_model.dart';

class TicketProcessDetailRepository {
  final ApiService _apiService;

  TicketProcessDetailRepository({required ApiService apiService}) : _apiService = apiService;

  //14.4 thong tin chung
  Future<ThongTinChungModel> getTicketProcessDetailById({
    required dynamic ticketId,
  }) async {
    try {
      developer.log('ticketId getTicketProcessDetailById: $ticketId');

      final endpoint = 'business-process/bpmProcInst/search/$ticketId';

      final response = await _apiService.get(endpoint);
      debugPrint('response getTicketProcessDetailById: ${response.body}');
      if (response.statusCode == 200) {
        developer.log('getTicketProcessDetailById response: ${response.body}');
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return ThongTinChungModel.fromJson(jsonData['data']);
      } else {
        throw Exception('Failed to load ticket process detail by id: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getTicketProcessDetailById: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

//14.5 thong tin phieu
  Future<ThongTinPhieuResponseModel> getThongTinPhieu({required String ticketId}) async {
    try {
      final response = await _apiService.get('business-process/actHiVarInst/getByTicket/$ticketId');
      developer.log('Raw response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // Handle List response
        if (jsonData is List<dynamic>) {
          if (jsonData.isEmpty) {
            throw Exception('getThongTinPhieu returned an empty list');
          }

          // Create a wrapper structure matching your model
          final wrappedData = {
            'code': 200, // Default success code
            'data': jsonData,
            'message': 'Success'
          };

          return ThongTinPhieuResponseModel.fromJson(wrappedData);
        }
        // Handle Map response (for compatibility)
        else if (jsonData is Map<String, dynamic>) {
          developer.log('getThongTinPhieu response: $jsonData');
          return ThongTinPhieuResponseModel.fromJson(jsonData);
        } else {
          throw Exception('Unexpected response format: ${jsonData.runtimeType}');
        }
      } else {
        throw Exception('Failed to load getThongTinPhieu: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getThongTinPhieu: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  //14.6 chi tiet phieu
  Future<(int statusCode, TicketProcessDetailModel?)> getTicketProcessDetail({
    required dynamic procInstId,
    required String taskDefKey,
    required String user,
    String? status,
  }) async {
    try {
      final endpoint =
          'business-process/loadActiveTask/getDetail?procInstId=$procInstId&taskDefKey=$taskDefKey&user=$user${status != null ? '&status=$status' : ''}';

      final response = await _apiService.get(endpoint);
      final Map<String, dynamic> jsonData = json.decode(response.body);

      if (response.statusCode == 200) {
        return (response.statusCode, TicketProcessDetailModel.fromJson(jsonData));
      } else {
        // Trả về statusCode và null model
        return (response.statusCode, null);
        
      }
      
    } catch (e) {
      debugPrint('Error in getTicketProcessDetail: $e');
      return (500, null);
    }
  }

  //14.7 thong tin dau vao
  Future<ThongTinDauVaoResponseModel> getThongTinDauVao({required String taskId, required String type}) async {
    try {
      final response = await _apiService.get('business-process/actHiVarInst/getByTask?taskId=$taskId&type=$type');
      developer.log('response: ${response.body}');

      if (response.statusCode == 200) {
        developer.log('getThongTinDauVao response: ${response.body}');
        final Map<String, dynamic> jsonData = json.decode(response.body);

        // Check if data is null before processing
        if (jsonData['data'] == null) {
          throw Exception('API returned null data');
        }

        return ThongTinDauVaoResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load getThongTinDauVao: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getThongTinDauVao: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  //lấy file pdf
  Future<String> getSignedFileUrl(String fileName) async {
    try {
      final endpoint = 'business-process/file/getFileUrl?fileName=$fileName';
      final response = await _apiService.post(endpoint, {});

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        if (jsonData['code'] == 1) {
          final url = jsonData['data'] as String;
          return url;
        } else {
          throw Exception('Failed to get file URL: ${jsonData['message']}');
        }
      } else {
        throw Exception('Failed to get file URL: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting file URL: $e');
    }
  }

//lấy thông tin người dùng
  Future<UserTaskInfoResponse> getAllUserTaskInfo({required String ticketId}) async {
    try {
      final response = await _apiService.get('business-process/bpmProcInst/getAllUserTaskInfo?ticketId=$ticketId');
      debugPrint("responsegetAllUserTaskInfo::: ${response.statusCode}");
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return UserTaskInfoResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to get user task info: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getAllUserTaskInfo: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

//chia sẻ
  Future<ShareResponseModel> getShareTickets({
    required String procInstId,
    String search = "",
    String sortBy = "createdDate",
    String sortType = "DESC",
    int page = 1,
    int limit = 10,
    String type = "SHARED",
    bool checkFollow = false,
  }) async {
    try {
      final requestBody = {
        "search": search,
        "sortBy": sortBy,
        "sortType": sortType,
        "page": page,
        "limit": limit,
        "type": type,
        "procInstId": procInstId,
        "checkFollow": checkFollow,
      };

      final response = await _apiService.post(
        'business-process/bpmProcInst/share/load',
        requestBody,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return ShareResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load share tickets: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getShareTickets: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<UserDropdownResponse> getUsersForDropdown() async {
    try {
      final response = await _apiService.get(
        'customer/chart/getChartInfoRole',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return UserDropdownResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load users: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getUsersForDropdown: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<bool> addShareTicket({
    required String procInstId,
    required List<String> sharedUsers,
  }) async {
    try {
      final requestBody = {"procInstId": procInstId, "listUser": sharedUsers, "type": "shared"};

      final response = await _apiService.post(
        'business-process/bpmProcInst/share/create',
        requestBody,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData['code'] == 1;
      } else {
        throw Exception('Failed to add share ticket: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in addShareTicket: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<bool> deleteShareTicket(int shareId) async {
    try {
      final response = await _apiService.delete(
        'business-process/bpmProcInst/share/delete?shareId=$shareId',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData['code'] == 1;
      } else {
        throw Exception('Failed to delete share ticket: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in deleteShareTicket: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<AssistantOpinionResponseModel> getAssistantOpinion({required AssistantOpinionRequestModel requestBody}) async {
    try {
      final response =
          await _apiService.post('business-process/bpmProcInst/get-assistants-opinion', requestBody.toJson());
      developer.log('getAssistantOpinion response: ${response.body}');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return AssistantOpinionResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to get assistant opinion: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getAssistantOpinion: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<ApproveResponseModel> approveTicket({
    required dynamic taskId,
    required dynamic procInstId,
    required bool signComplete,
    required dynamic body,
  }) async {
    try {
      final response =
          await _apiService.post('business-process/task/$taskId/$procInstId/complete?signComplete=$signComplete', body);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return ApproveResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to approve ticket: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in approveTicket: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> signPrintZone({
    required Map<String, dynamic> requestBody,
  }) async {
    try {
      final response = await _apiService.post('business-process/print-sign-zone/sign', requestBody);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception('Failed to sign print zone: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in signPrintZone: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  /// Lấy danh sách các tasks có thể kế thừa
  Future<InheritableTasksResponse> getInheritableTasksList({
    required String procDefId,
    required String ticketId,
  }) async {
    try {
      final endpoint = 'business-process/template-manage/getLstTasksInherits/$procDefId/$ticketId';
      final response = await _apiService.get(endpoint);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return InheritableTasksResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load inheritable tasks list: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getInheritableTasksList: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }

  /// Lưu nháp
  Future<Map<String, dynamic>> saveDraft({
    required String taskId,
    required String procInstId,
    required Map<String, dynamic> variables,
  }) async {
    try {
      final requestBody = {
        "variables": variables,
        "businessKey": null,
      };

      final response = await _apiService.post(
        'business-process/task/$taskId/$procInstId/draft',
        requestBody,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception('Failed to save draft: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in saveDraft: $e, StackTrace: $stackTrace');
      rethrow;
    }
  }
}
