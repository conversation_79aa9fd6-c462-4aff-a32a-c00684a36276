class AccountResponse {
  String? code;
  String? message;
  String? status;
  int? timestamp;
  int? elapsedTimeMs;
  String? path;
  String? requestId;
  Data? data;

  AccountResponse(
      {this.code, this.message, this.status, this.timestamp, this.elapsedTimeMs, this.path, this.requestId, this.data});

  factory AccountResponse.fromJson(Map<String, dynamic> json) {
    return AccountResponse(
      code: json['code'],
      message: json['message'],
      status: json['status'],
      timestamp: json['timestamp'],
      elapsedTimeMs: json['elapsedTimeMs'],
      path: json['path'],
      requestId: json['requestId'],
      data: json['data'] != null ? Data.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'message': message,
        'status': status,
        'timestamp': timestamp,
        'elapsedTimeMs': elapsedTimeMs,
        'path': path,
        'requestId': requestId,
        //
        if (data != null) 'data': data!.toJson(), // Gọn hơn
      };
}

class Data {
  final List<UserList>? userList;
  final UserADM? userADM;
  final int? totalRecord;
  final List<dynamic>? intraUserList;
  final String? logoCompany;

  Data({
    this.userList,
    this.userADM,
    this.totalRecord,
    this.intraUserList,
    this.logoCompany,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      userList: (json['userList'] as List?)?.map((e) => UserList.fromJson(e)).toList(),
      userADM: json['userADM'] != null ? UserADM.fromJson(json['userADM']) : null,
      totalRecord: json['totalRecord'],
      intraUserList: json['intraUserList'] as List<dynamic>?,
      logoCompany: json['logoCompany'],
    );
  }

  /// ✅ **Hàm `toJson()` gọn gàng hơn**
  Map<String, dynamic> toJson() => {
        if (userList != null) 'userList': userList!.map((e) => e.toJson()).toList(),
        'totalRecord': totalRecord,
        'intraUserList': intraUserList,
        'logoCompany': logoCompany,
        if (userADM != null) 'userADM': userADM!.toJson(), 
      };
}

class JobTitles {
  final String? parentCode;
  final String? parentName;
  final int? type;
  final String? jobTitleCode;
  final String? orgCode;
  final String? startDate;
  final String? endDate;
  final String? positionCode;
  final String? typeObject;
  final String? jobTilteName; // Giữ nguyên lỗi chính tả
  final String? orgName;
  final String? positionName;

  const JobTitles({
    this.parentCode,
    this.parentName,
    this.type,
    this.jobTitleCode,
    this.orgCode,
    this.startDate,
    this.endDate,
    this.positionCode,
    this.typeObject,
    this.jobTilteName,
    this.orgName,
    this.positionName,
  });

  factory JobTitles.fromJson(Map<String, dynamic> json) => JobTitles(
        parentCode: json['parentCode'],
        parentName: json['parentName'],
        type: json['type'],
        jobTitleCode: json['jobTitleCode'],
        orgCode: json['orgCode'],
        startDate: json['startDate'],
        endDate: json['endDate'],
        positionCode: json['positionCode'],
        typeObject: json['typeObject'],
        jobTilteName: json['jobTilteName'],
        orgName: json['orgName'],
        positionName: json['positionName'],
      );

  Map<String, dynamic> toJson() => {
        'parentCode': parentCode,
        'parentName': parentName,
        'type': type,
        'jobTitleCode': jobTitleCode,
        'orgCode': orgCode,
        'startDate': startDate,
        'endDate': endDate,
        'positionCode': positionCode,
        'typeObject': typeObject,
        'jobTilteName': jobTilteName,
        'orgName': orgName,
        'positionName': positionName,
      };
}

class UserADM {
  final String? userId;
  final String? userName;
  final dynamic password;
  final dynamic passwordExpireStatus;
  final String? fullName;
  final dynamic firstName;
  final dynamic priority;
  final int? status;
  final String? accountStartDate;
  final String? accountEndDate;
  final String? linkedAccountList;
  final String? alias;
  final String? phone;
  final String? orgCode;
  final dynamic companyCode;
  final dynamic companyName;
  final dynamic orgLevel;
  final String? telegramChatId;
  final String? totpEmail;
  final String? totpPhone;
  final String? totpTelegram;
  final dynamic fax;
  final String? email;
  final dynamic address;
  final dynamic lastChangePassword;
  final dynamic lastBlockDate;
  final dynamic loginFailureCount;
  final dynamic deptId;
  final String? position;
  final dynamic positionId;
  final dynamic staffCode;
  final dynamic sex;
  final dynamic mobile;
  final String? userType;
  final dynamic birthday;
  final dynamic yearOfBirth;
  final List<dynamic>? groups;
  final String? referCode;
  final dynamic orgId;
  final dynamic idNo;
  final dynamic totalRecord;
  final String? createdAt;
  final String? createdBy;
  final String? updatedAt;
  final String? updatedBy;
  final String? emailChangePassword;
  final int? isAdmin;
  final List<JobTitles>? jobTitles;
  final dynamic listOrg;
  final bool? isLoginThirdParty;
  final dynamic positionCode;
  final dynamic jobTitleCode;
  final dynamic positionName;
  final dynamic jobTitleName;

  const UserADM({
    this.userId,
    this.userName,
    this.password,
    this.passwordExpireStatus,
    this.fullName,
    this.firstName,
    this.priority,
    this.status,
    this.accountStartDate,
    this.accountEndDate,
    this.linkedAccountList,
    this.alias,
    this.phone,
    this.orgCode,
    this.companyCode,
    this.companyName,
    this.orgLevel,
    this.telegramChatId,
    this.totpEmail,
    this.totpPhone,
    this.totpTelegram,
    this.fax,
    this.email,
    this.address,
    this.lastChangePassword,
    this.lastBlockDate,
    this.loginFailureCount,
    this.deptId,
    this.position,
    this.positionId,
    this.staffCode,
    this.sex,
    this.mobile,
    this.userType,
    this.birthday,
    this.yearOfBirth,
    this.groups,
    this.referCode,
    this.orgId,
    this.idNo,
    this.totalRecord,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.emailChangePassword,
    this.isAdmin,
    this.jobTitles,
    this.listOrg,
    this.isLoginThirdParty,
    this.positionCode,
    this.jobTitleCode,
    this.positionName,
    this.jobTitleName,
  });

  factory UserADM.fromJson(Map<String, dynamic> json) {
    return UserADM(
      userId: json['userId'],
      userName: json['userName'],
      password: json['password'],
      passwordExpireStatus: json['passwordExpireStatus'],
      fullName: json['fullName'],
      firstName: json['firstName'],
      priority: json['priority'],
      status: json['status'],
      accountStartDate: json['accountStartDate'],
      accountEndDate: json['accountEndDate'],
      linkedAccountList: json['linkedAccountList'],
      alias: json['alias'],
      phone: json['phone'],
      orgCode: json['orgCode'],
      companyCode: json['companyCode'],
      companyName: json['companyName'],
      orgLevel: json['orgLevel'],
      telegramChatId: json['telegramChatId'],
      totpEmail: json['totpEmail'],
      totpPhone: json['totpPhone'],
      totpTelegram: json['totpTelegram'],
      fax: json['fax'],
      email: json['email'],
      address: json['address'],
      lastChangePassword: json['lastChangePassword'],
      lastBlockDate: json['lastBlockDate'],
      loginFailureCount: json['loginFailureCount'],
      deptId: json['deptId'],
      position: json['position'],
      positionId: json['positionId'],
      staffCode: json['staffCode'],
      sex: json['sex'],
      mobile: json['mobile'],
      userType: json['userType'],
      birthday: json['birthday'],
      yearOfBirth: json['yearOfBirth'],
      groups: json['groups'],
      referCode: json['referCode'],
      orgId: json['orgId'],
      idNo: json['idNo'],
      totalRecord: json['totalRecord'],
      createdAt: json['createdAt'],
      createdBy: json['createdBy'],
      updatedAt: json['updatedAt'],
      updatedBy: json['updatedBy'],
      emailChangePassword: json['emailChangePassword'],
      isAdmin: json['isAdmin'],
      jobTitles: (json['jobTitles'] as List?)?.map((v) => JobTitles.fromJson(v)).toList(),
      listOrg: json['listOrg'],
      isLoginThirdParty: json['isLoginThirdParty'],
      positionCode: json['positionCode'],
      jobTitleCode: json['jobTitleCode'],
      positionName: json['positionName'],
      jobTitleName: json['jobTitleName'],
    );
  }

  get isNotEmpty => null;

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'password': password,
      'passwordExpireStatus': passwordExpireStatus,
      'fullName': fullName,
      'firstName': firstName,
      'priority': priority,
      'status': status,
      'accountStartDate': accountStartDate,
      'accountEndDate': accountEndDate,
      'linkedAccountList': linkedAccountList,
      'alias': alias,
      'phone': phone,
      'orgCode': orgCode,
      'companyCode': companyCode,
      'companyName': companyName,
      'orgLevel': orgLevel,
      'telegramChatId': telegramChatId,
      'totpEmail': totpEmail,
      'totpPhone': totpPhone,
      'totpTelegram': totpTelegram,
      'fax': fax,
      'email': email,
      'address': address,
      'lastChangePassword': lastChangePassword,
      'lastBlockDate': lastBlockDate,
      'loginFailureCount': loginFailureCount,
      'deptId': deptId,
      'position': position,
      'positionId': positionId,
      'staffCode': staffCode,
      'sex': sex,
      'mobile': mobile,
      'userType': userType,
      'birthday': birthday,
      'yearOfBirth': yearOfBirth,
      'groups': groups,
      'referCode': referCode,
      'orgId': orgId,
      'idNo': idNo,
      'totalRecord': totalRecord,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'updatedAt': updatedAt,
      'updatedBy': updatedBy,
      'emailChangePassword': emailChangePassword,
      'isAdmin': isAdmin,
      'jobTitles': jobTitles?.map((v) => v.toJson()).toList(),
      'listOrg': listOrg,
      'isLoginThirdParty': isLoginThirdParty,
      'positionCode': positionCode,
      'jobTitleCode': jobTitleCode,
      'positionName': positionName,
      'jobTitleName': jobTitleName,
    };
  }
}

class UserList {
  final String? userId;
  final String? userName;
  final String? password;
  final String? passwordExpireStatus;
  final String? fullName;
  final String? firstName;
  final String? priority;
  final int? status;
  final String? accountStartDate;
  final String? accountEndDate;
  final String? linkedAccountList;
  final String? alias;
  final String? phone;
  final String? orgCode;
  final String? companyCode;
  final String? companyName;
  final String? orgLevel;
  final String? telegramChatId;
  final String? totpEmail;
  final String? totpPhone;
  final String? totpTelegram;
  final String? fax;
  final String? email;
  final String? address;
  final String? lastChangePassword;
  final String? lastBlockDate;
  final String? loginFailureCount;
  final String? deptId;
  final String? position;
  final String? positionId;
  final String? staffCode;
  final String? sex;
  final String? mobile;
  final String? userType;
  final String? birthday;
  final String? yearOfBirth;
  final List<dynamic>? groups;
  final String? referCode;
  final String? orgId;
  final String? idNo;
  final String? totalRecord;
  final String? createdAt;
  final String? createdBy;
  final String? updatedAt;
  final String? updatedBy;
  final String? emailChangePassword;
  final int? isAdmin;
  final List<JobTitles>? jobTitles;
  final String? listOrg;
  final String? isLoginThirdParty;
  final String? positionCode;
  final String? jobTitleCode;
  final String? positionName;
  final String? jobTitleName;

  const UserList({
    this.userId,
    this.userName,
    this.password,
    this.passwordExpireStatus,
    this.fullName,
    this.firstName,
    this.priority,
    this.status,
    this.accountStartDate,
    this.accountEndDate,
    this.linkedAccountList,
    this.alias,
    this.phone,
    this.orgCode,
    this.companyCode,
    this.companyName,
    this.orgLevel,
    this.telegramChatId,
    this.totpEmail,
    this.totpPhone,
    this.totpTelegram,
    this.fax,
    this.email,
    this.address,
    this.lastChangePassword,
    this.lastBlockDate,
    this.loginFailureCount,
    this.deptId,
    this.position,
    this.positionId,
    this.staffCode,
    this.sex,
    this.mobile,
    this.userType,
    this.birthday,
    this.yearOfBirth,
    this.groups,
    this.referCode,
    this.orgId,
    this.idNo,
    this.totalRecord,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.emailChangePassword,
    this.isAdmin,
    this.jobTitles,
    this.listOrg,
    this.isLoginThirdParty,
    this.positionCode,
    this.jobTitleCode,
    this.positionName,
    this.jobTitleName,
  });

  factory UserList.fromJson(Map<String, dynamic> json) => UserList(
        userId: json['userId'],
        userName: json['userName'],
        password: json['password'],
        passwordExpireStatus: json['passwordExpireStatus'],
        fullName: json['fullName'],
        firstName: json['firstName'],
        priority: json['priority'],
        status: json['status'],
        accountStartDate: json['accountStartDate'],
        accountEndDate: json['accountEndDate'],
        linkedAccountList: json['linkedAccountList'],
        alias: json['alias'],
        phone: json['phone'],
        orgCode: json['orgCode'],
        companyCode: json['companyCode'],
        companyName: json['companyName'],
        orgLevel: json['orgLevel'],
        telegramChatId: json['telegramChatId'],
        totpEmail: json['totpEmail'],
        totpPhone: json['totpPhone'],
        totpTelegram: json['totpTelegram'],
        fax: json['fax'],
        email: json['email'],
        address: json['address'],
        lastChangePassword: json['lastChangePassword'],
        lastBlockDate: json['lastBlockDate'],
        loginFailureCount: json['loginFailureCount'],
        deptId: json['deptId'],
        position: json['position'],
        positionId: json['positionId'],
        staffCode: json['staffCode'],
        sex: json['sex'],
        mobile: json['mobile'],
        userType: json['userType'],
        birthday: json['birthday'],
        yearOfBirth: json['yearOfBirth'],
        groups: json['groups'],
        referCode: json['referCode'],
        orgId: json['orgId'],
        idNo: json['idNo'],
        totalRecord: json['totalRecord'],
        createdAt: json['createdAt'],
        createdBy: json['createdBy'],
        updatedAt: json['updatedAt'],
        updatedBy: json['updatedBy'],
        emailChangePassword: json['emailChangePassword'],
        isAdmin: json['isAdmin'],
        jobTitles: (json['jobTitles'] as List<dynamic>?)?.map((e) => JobTitles.fromJson(e)).toList(),
        listOrg: json['listOrg'],
        isLoginThirdParty: json['isLoginThirdParty'],
        positionCode: json['positionCode'],
        jobTitleCode: json['jobTitleCode'],
        positionName: json['positionName'],
        jobTitleName: json['jobTitleName'],
      );

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'userName': userName,
        'password': password,
        'passwordExpireStatus': passwordExpireStatus,
        'fullName': fullName,
        'firstName': firstName,
        'priority': priority,
        'status': status,
        'accountStartDate': accountStartDate,
        'accountEndDate': accountEndDate,
        'linkedAccountList': linkedAccountList,
        'alias': alias,
        'phone': phone,
        'orgCode': orgCode,
        'companyCode': companyCode,
        'companyName': companyName,
        'orgLevel': orgLevel,
        'telegramChatId': telegramChatId,
        'totpEmail': totpEmail,
        'totpPhone': totpPhone,
        'totpTelegram': totpTelegram,
        'fax': fax,
        'email': email,
        'address': address,
        'lastChangePassword': lastChangePassword,
        'lastBlockDate': lastBlockDate,
        'loginFailureCount': loginFailureCount,
        'deptId': deptId,
        'position': position,
        'positionId': positionId,
        'staffCode': staffCode,
        'sex': sex,
        'mobile': mobile,
        'userType': userType,
        'birthday': birthday,
        'yearOfBirth': yearOfBirth,
        'groups': groups,
        'referCode': referCode,
        'orgId': orgId,
        'idNo': idNo,
        'totalRecord': totalRecord,
        'createdAt': createdAt,
        'createdBy': createdBy,
        'updatedAt': updatedAt,
        'updatedBy': updatedBy,
        'emailChangePassword': emailChangePassword,
        'isAdmin': isAdmin,
        'jobTitles': jobTitles?.map((e) => e.toJson()).toList(),
        'listOrg': listOrg,
        'isLoginThirdParty': isLoginThirdParty,
        'positionCode': positionCode,
        'jobTitleCode': jobTitleCode,
        'positionName': positionName,
        'jobTitleName': jobTitleName,
      };
}
