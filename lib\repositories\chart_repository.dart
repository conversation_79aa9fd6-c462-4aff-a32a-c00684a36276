import 'package:eapprove/models/chart/chart_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'dart:convert';
import 'dart:developer' as developer;
class ChartRepository {
  final ApiService _apiService;

  ChartRepository(this._apiService);


  Future<ChartResponse> fetchChartsByUser() async {
    try {
      final response = await _apiService.get(
        'customer/chart/by-user',
      );
      developer.log('fetchChartsByUser - response: ${response.body}');
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return ChartResponse.fromJson(responseData);
      } else {
        throw Exception('Failed to fetch charts');
      }
    } catch (e) {
      throw Exception('Error fetching charts: $e');
    }
  }
} 