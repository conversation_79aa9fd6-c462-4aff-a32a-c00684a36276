class ConsultationOpinionModel {
  final int id;
  final String? procInstId;
  final int? ticketId;
  final String? content;
  final String? createdUser;
  final int? createdDate;
  final List<ConsultationOpinionModel>? discussionReplies; // ✅ Đặt đúng tên

  ConsultationOpinionModel({
    required this.id,
    this.procInstId,
    this.ticketId,
    this.content,
    this.createdUser,
    this.createdDate,
    this.discussionReplies,
  });

  factory ConsultationOpinionModel.fromJson(Map<String, dynamic> json) {
    return ConsultationOpinionModel(
      id: json['id'],
      procInstId: json['procInstId'],
      ticketId: json['ticketId'],
      content: json['content'],
      createdUser: json['createdUser'],
      createdDate: json['createdDate'],
      discussionReplies: (json['discussionReplys'] as List<dynamic>?)
          ?.map((e) => ConsultationOpinionModel.fromJson(e))
          .toList(), // 👈 Parse đúng tên JSO<PERSON>
    );
  }
}
