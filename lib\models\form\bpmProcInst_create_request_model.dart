import 'package:eapprove/models/form/tao_to_trinh_request_body.dart';

class BpmProcInstCreateRequestModel {
  String? oldProcInstId;
  Map<String, dynamic>? variables;
  bool? isDraft;
  int? serviceId;
  List<String>? linkProcInstId;
  bool? receiveMail;
  bool? isAssistant;
  List<String>? notifyUsers;
  int? priorityId;
  int? chartId;
  List<String>? assistantEmail;
  int? submissionType;
  String? companyCode;
  String? submissionTypeName;
  String? chartNodeName;
  String? chartNodeCode;
  int? chartNodeId;
  String? chartName;
  String? forceViewUrl;
  String? cancelTicketId;
  List<String>? approvedBudgetIds;

  BpmProcInstCreateRequestModel({
    this.oldProcInstId,
    this.variables,
    this.isDraft,
    this.serviceId,
    this.linkProcInstId,
    this.receiveMail,
    this.isAssistant,
    this.notifyUsers,
    this.priorityId,
    this.chartId,
    this.assistantEmail,
    this.submissionType,
    this.companyCode,
    this.submissionTypeName,
    this.chartNodeName,
    this.chartNodeCode,
    this.chartNodeId,
    this.chartName,
    this.forceViewUrl,
    this.cancelTicketId,
      this.approvedBudgetIds,
  });

  BpmProcInstCreateRequestModel.fromJson(Map<String, dynamic> json) {
    oldProcInstId = json['oldProcInstId'];
    if (json['variables'] != null) {
      variables = <String, Variable>{};
      json['variables'].forEach((key, value) {
        variables![key] = Variable.fromJson(value);
      });
    }
    isDraft = json['isDraft'];
    serviceId = json['serviceId'];
    if (json['linkProcInstId'] != null) {
      linkProcInstId = List<String>.from(json['linkProcInstId']);
    }
    receiveMail = json['receiveMail'];
    isAssistant = json['isAssistant'];
    if (json['notifyUsers'] != null) {
      notifyUsers = List<String>.from(json['notifyUsers']);
    }
    priorityId = json['priorityId'];
    chartId = json['chartId'];
    if (json['assistantEmail'] != null) {
      assistantEmail = List<String>.from(json['assistantEmail']);
    }
    submissionType = json['submissionType'];
    companyCode = json['companyCode'];
    submissionTypeName = json['submissionTypeName'];
    chartNodeName = json['chartNodeName'];
    chartNodeCode = json['chartNodeCode'];
    chartNodeId = json['chartNodeId'];
    chartName = json['chartName'];
    forceViewUrl = json['forceViewUrl'];
    cancelTicketId = json['cancelTicketId'];
    if (json['approvedBudgetIds'] != null) {
      approvedBudgetIds = List<String>.from(json['approvedBudgetIds']);
    }
    
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (oldProcInstId != null) {
      data['oldProcInstId'] = oldProcInstId;
    }
    if (variables != null) {
      data['variables'] = variables;
    }
    if (isDraft != null) {
      data['isDraft'] = isDraft;
    }
    if (serviceId != null) {
      data['serviceId'] = serviceId;
    }
    if (linkProcInstId != null) {
      data['linkProcInstId'] = linkProcInstId;
    }
    if (receiveMail != null) {
      data['receiveMail'] = receiveMail;
    }
    if (isAssistant != null) {
      data['isAssistant'] = isAssistant;
    }
    if (notifyUsers != null) {
      data['notifyUsers'] = notifyUsers;
    }
    if (priorityId != null) {
      data['priorityId'] = priorityId;
    }
    if (chartId != null) {
      data['chartId'] = chartId;
    }
    if (assistantEmail != null) {
      data['assistantEmail'] = assistantEmail;
    }
    if (submissionType != null) {
      data['submissionType'] = submissionType;
    }
    if (companyCode != null) {
      data['companyCode'] = companyCode;
    }
    if (submissionTypeName != null) {
      data['submissionTypeName'] = submissionTypeName;
    }
    if (chartNodeName != null) {
      data['chartNodeName'] = chartNodeName;
    }
    if (chartNodeCode != null) {
      data['chartNodeCode'] = chartNodeCode;
    }
    if (chartNodeId != null) {
      data['chartNodeId'] = chartNodeId;
    }
    if (chartName != null) {
      data['chartName'] = chartName;
    }
    if (forceViewUrl != null) {
      data['forceViewUrl'] = forceViewUrl;
    }
    if (cancelTicketId != null) {
      data['cancelTicketId'] = cancelTicketId;
    }
    if (approvedBudgetIds != null) {
      data['approvedBudgetIds'] = approvedBudgetIds;
    }
    
    return data;
  }
}

class Variable {
  String? value;
  String? type;
  Map<String, dynamic>? valueInfo;

  Variable({
    this.value,
    this.type,
    this.valueInfo,
  });

  Variable.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    type = json['type'];
    if (json['valueInfo'] != null) {
      valueInfo = Map<String, dynamic>.from(json['valueInfo']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (value != null) {
      data['value'] = value;
    }
    if (type != null) {
      data['type'] = type;
    }
    if (valueInfo != null) {
      data['valueInfo'] = Map<String, dynamic>.from(valueInfo!);
    }
    return data;
  }
}
