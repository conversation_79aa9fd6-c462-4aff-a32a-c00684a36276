import 'dart:convert';
import 'package:eapprove/common/form/common_form_widget.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'dart:developer' as developer;

class TableWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;
  final bool? isReadOnly;
  const TableWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
    this.isReadOnly,
  }) : super(key: key);

  @override
  State<TableWidget> createState() => _TableWidgetState();
}

class _TableWidgetState extends State<TableWidget> {
  List<Map<String, dynamic>> _tableData = [];
  List<FormItemInfo> _columns = [];
  List<FormItemInfo> _headerColumns = [];
  List<FormItemInfo> _rawColumns = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchText = '';
  Map<int, Map<String, FocusNode>> focusNodeMap = {};

  @override
  void initState() {
    super.initState();
    _parseColumns();
    _searchController.addListener(_onSearchChanged);
  }

  void _initializeFocusNodes() {
    for (int rowIndex = 0; rowIndex < _tableData.length; rowIndex++) {
      focusNodeMap[rowIndex] ??= {}; // nếu dòng chưa có map thì tạo mới
      for (var column in _rawColumns) {
        focusNodeMap[rowIndex]![column.name ?? ''] ??= FocusNode();
      }
    }
  }

  void _onSearchChanged() {
    if (mounted) {
      setState(() {
        _searchText = _searchController.text;
      });
    }
  }

  @override
  void didUpdateWidget(TableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {}
  }

  @override
  void dispose() {
    for (var row in focusNodeMap.values) {
      for (var node in row.values) {
        node.dispose();
      }
    }
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _parseColumns() {
    _columns = [];

    // First, get the columns from the table configuration
    if (widget.data.columns != null && widget.data.columns!.isNotEmpty) {
      // Sort columns by colSortOrder

      final columns = widget.data.columns;
      final totalColumns = columns?.length ?? 0;
      final halfPoint = (totalColumns / 2).ceil();

      // First half for header columns
      var headerColumns = columns?.sublist(0, halfPoint) ?? [];
      // Second half for raw default columns
      final rawColumns = columns?.sublist(halfPoint) ?? [];

      bool isHideTitle = headerColumns
              ?.where((element) => element.hideTableTitle == true)
              .isNotEmpty ==
          true;

      if (isHideTitle) {
        headerColumns = rawColumns;
      }

      for (var col in headerColumns!) {
        //   // Skip column placeholders (that don't have real form elements)r

        if (col.hideColumn == true) continue;

        _headerColumns.add(col);
      }

      for (var col in rawColumns!) {
        //   // Skip column placeholders (that don't have real form elements)r

        if (col.hideColumn == true) continue;

        _rawColumns.add(col);
      }

      Map<String, dynamic> emptyRow = {};

      // Initialize with empty values for each column
      for (var column in _rawColumns) {
        emptyRow[column.name ?? ''] = '';
      }

      _tableData.add(emptyRow);
      // _handleAddRow();
    }
  }

  Map<String, dynamic> _createEmptyRow() {
    Map<String, dynamic> emptyRow = {};

    // Initialize with empty values for each column
    for (var column in _rawColumns) {
      emptyRow[column.name ?? ''] = '';
    }

    return emptyRow;
  }

  void _handleAddRow() {
    setState(() {
      _tableData.add(_createEmptyRow());
    });

    _updateParentValue();
  }

  void _handleDeleteRow(int index) {
    if (index < 0 || index >= _tableData.length) return;

    setState(() {
      _tableData.removeAt(index);
    });

    // If no rows left, add an empty one
    if (_tableData.isEmpty) {
      setState(() {
        _tableData.add(_createEmptyRow());
      });
    }

    _updateParentValue();
  }

  void _updateCellValue(int rowIndex, String columnName, dynamic value) {
    if (rowIndex < 0 || rowIndex >= _tableData.length) return;

    setState(() {
      _tableData[rowIndex][columnName] = value;
    });

    _updateParentValue();
  }

  void _updateParentValue() {
    widget.data.value = _tableData;
    widget.onChange?.call(widget.data.name ?? '', _tableData);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      alignment: Alignment.centerLeft,
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: _buildTableContent(),
            ),
          ),

          // Add row button
          if (widget.data.isHideAddColumnTable != true &&
              widget.data.readonly != true &&
              widget.isReadOnly != true)
            _buildTableActions(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.data.label ?? 'Table',
            style: getTypoSkin().bodyRegular14.copyWith(
                  color: getColorSkin().ink1,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildTableContent() {
    // Filter data based on search
    final filteredData = _searchText.isEmpty
        ? _tableData
        : _tableData.where((row) {
            return row.values.any((value) {
              return value != null &&
                  value
                      .toString()
                      .toLowerCase()
                      .contains(_searchText.toLowerCase());
            });
          }).toList();

    // If no data after filtering, show empty search result
    // if (filteredData.isEmpty) {
    //   return _buildEmptyState("No matching results");
    // }

    // developer.log('TableWidgetTableWidget: ${visibleColumns.map((e) => e.name).toList()}', name: 'TableWidget');
    // Build the table using Table widget for consistent spacing

    return Table(
      border: TableBorder.all(color: getColorSkin().grey2),
      columnWidths: _getColumnWidths(_headerColumns),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: [
        // Header row
        // if (!visibleColumns.every((col) => col.hideTitle))
        TableRow(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
          ),
          children: [
            // STT column (row number) if not hidden
            if (widget.data.isHideStt != true)
              TableCell(
                child: Padding(
                  padding: EdgeInsets.all(8.w),
                  child: Text(
                    'STT',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                      color: getColorSkin().ink1,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

            // Column headers
            ..._headerColumns.map((column) {
              // Skip hidden title columns
              // if (column.hideTitle) return const SizedBox.shrink()
              return TableCell(
                child: Padding(
                  padding: EdgeInsets.all(8.w),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        fit: FlexFit.loose,
                        child: RichText(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          softWrap: true,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: column.labelInTable ??
                                    column.label ??
                                    'label',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                              ),
                              if (column.validations?['required'] == true)
                                const TextSpan(
                                  text: ' *',
                                  style: TextStyle(color: Colors.red),
                                ),
                              if (column.tooltip?.isNotEmpty == true)
                                WidgetSpan(
                                  child: Padding(
                                    padding: EdgeInsets.only(left: 4.w),
                                    child: GestureDetector(
                                      onTap: () {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content:
                                                  Text(column.tooltip ?? '')),
                                        );
                                      },
                                      child: Icon(Icons.help_outline, size: 20),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),

            // Action column (delete button)
            if (widget.data.isHideDeleteColumnTable != true)
              TableCell(
                child: Center(
                  child: Text(
                    '',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                  ),
                ),
              ),
          ],
        ),

        // Data rows
        ...filteredData.asMap().entries.map((entry) {
          final rowIndex = entry.key;
          final rowData = entry.value;

          return TableRow(
            decoration: BoxDecoration(
              color: rowIndex.isEven ? Colors.white : Colors.grey.shade50,
            ),
            children: [
              // STT column (row number) if not hidden
              if (widget.data.isHideStt != true)
                TableCell(
                  child: Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Text(
                      '${rowIndex + 1}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: getColorSkin().ink1,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),

              // Data cells
              ..._rawColumns.map((column) {
                return TableCell(
                  verticalAlignment: TableCellVerticalAlignment.top,
                  child: Padding(
                    padding: EdgeInsets.all(4.w),
                    child: _buildWidget(column, rowIndex, column.name ?? ''),
                  ),
                );
              }).toList(),

              // Delete row button
              if (widget.data.isHideDeleteColumnTable != true)
                TableCell(
                  child: Center(
                    child: widget.isReadOnly == true
                        ? SizedBox.shrink()
                        : IconButton(
                            icon: Icon(
                              Icons.delete_outline,
                              color: Colors.red,
                              size: 18.sp,
                            ),
                            onPressed: widget.data.readonly == true
                                ? null
                                : () => _handleDeleteRow(rowIndex),
                            constraints: BoxConstraints(
                              minWidth: 30.w,
                              minHeight: 30.h,
                            ),
                            padding: EdgeInsets.zero,
                          ),
                  ),
                ),
            ],
          );
        }).toList(),
      ],
    );
  }

  Map<int, TableColumnWidth> _getColumnWidths(
      List<FormItemInfo> visibleColumns) {
    Map<int, TableColumnWidth> columnWidths = {};

    // Add STT column width if not hidden
    if (widget.data.isHideStt != true) {
      columnWidths[0] = FixedColumnWidth(50.w);
    }

    // Add column widths for each visible column
    int columnIndex = widget.data.isHideStt != true ? 1 : 0;
    for (var column in visibleColumns) {
      // Get width from column config or use default
      double width = 200.w;

      // Try to get width from formItem
      if (column != null && column!.width != null) {
        try {
          width = double.parse(column!.width!) * 1.w;
        } catch (e) {
          // Use default if parsing fails
        }
      }

      columnWidths[columnIndex] = FixedColumnWidth(width);
      columnIndex++;
    }

    // Add action column width if delete button is shown
    if (widget.data.isHideDeleteColumnTable != true) {
      columnWidths[columnIndex] = FixedColumnWidth(50.w);
    }

    return columnWidths;
  }

  List<DropdownMenuItem<String>> _getDropdownItems(FormItemInfo formItem) {
    List<Map<String, dynamic>> options = [];

    // Combine options from both sources
    if (formItem.options != null && formItem.options!.isNotEmpty) {
      options.addAll(formItem.options!);
    }

    if (formItem.option != null && formItem.option!.isNotEmpty) {
      options.addAll(formItem.option!);
    }

    return options.map<DropdownMenuItem<String>>((option) {
      return DropdownMenuItem<String>(
        value: option['value']?.toString() ?? '',
        child: Text(
          option['label']?.toString() ?? '',
          style: TextStyle(fontSize: 12.sp),
          overflow: TextOverflow.ellipsis,
        ),
      );
    }).toList();
  }

  Widget _buildEmptyState(String message) {
    return Container(
      height: 120.h,
      width: 300.w,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        border: Border.all(color: getColorSkin().grey2),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.table_chart_outlined,
            size: 32.sp,
            color: Colors.grey,
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableActions() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            onPressed: _handleAddRow,
            icon: Icon(Icons.add, size: 16.sp),
            label: Text(
              'Thêm dòng',
              style: TextStyle(fontSize: 12.sp),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: getColorSkin().primaryBlue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWidget(
      FormItemInfo formWidget, int rowIndex, String columnName) {
    // Get event expression results before building the widget
    final eventResults = formWidget.eventExpression?.isNotEmpty == true
        ? widget.stateManager
            .getEventExpressionResults(formWidget.name.toString())
        : null;

    // developer.log('eventResultsTABLE: ${formWidget.name} $eventResults',
    //     name: 'table_widget');

    // Process event expression results
    bool shouldDisplay = formWidget.display ?? true;
    String currentLabel = formWidget.label ?? '';
    bool currentReadonly = formWidget.readonly ?? false;
    String currentSuggestText = formWidget.suggestText ?? '';
    bool currentRequired = formWidget.validations?['required'] ?? false;
    Map<String, dynamic>? currentOptionConfig = formWidget.optionConfig;

    if (eventResults != null) {
      // Handle switchField configuration
      if (eventResults['switchField'] != null &&
          formWidget.switchFieldConfig != null) {
        final switchFieldValue = eventResults['switchField'].toString();
        final config = formWidget.switchFieldConfig!.firstWhere(
          (c) {
            final typedConfig = Map<String, dynamic>.from(c);
            return typedConfig['switchFieldName'] == switchFieldValue;
          },
          orElse: () => <String, dynamic>{},
        );

        if (config.isNotEmpty) {
          final typedConfig = Map<String, dynamic>.from(config);
          currentOptionConfig = typedConfig['optionConfig'] != null
              ? Map<String, dynamic>.from(typedConfig['optionConfig'])
              : null;
          currentLabel = typedConfig['label']?.toString() ?? currentLabel;
          currentReadonly = typedConfig['readonly'] as bool? ?? currentReadonly;
          final validations = typedConfig['validations'] != null
              ? Map<String, dynamic>.from(typedConfig['validations'])
              : null;
          currentRequired =
              validations?['required'] as bool? ?? currentRequired;
        }
      }

      // Handle ten_truong (field name)
      final tenTruong = eventResults['ten_truong']?.toString();
      if (tenTruong != null) {
        currentLabel = tenTruong;
      }

      // Handle bat_buoc (required)
      final batBuoc = eventResults['bat_buoc']?.toString();
      if (batBuoc != null) {
        currentRequired = batBuoc.toLowerCase() == 'true';
      }

      // Handle chi_duoc_doc (readonly)
      final chiDuocDoc = eventResults['chi_duoc_doc']?.toString();
      if (chiDuocDoc != null) {
        currentReadonly = chiDuocDoc.toLowerCase() == 'true';
      }

      // Handle huong_dan (suggest text)
      final huongDan = eventResults['huong_dan']?.toString();
      if (huongDan != null) {
        currentSuggestText = huongDan;
      }

      // Handle hien_thi (display)
      final hienThi = eventResults['hien_thi']?.toString();
      if (hienThi != null) {
        shouldDisplay = hienThi.toLowerCase() != 'false';
      }
    }

    // Create a copy of the widget with current values
    final currentWidget = formWidget.copyWith(
      label: currentLabel,
      readonly: widget.isReadOnly == true ? true : currentReadonly,
      suggestText: currentSuggestText,
      validations: {...?formWidget.validations, 'required': currentRequired},
      display: shouldDisplay,
      optionConfig: currentOptionConfig,
    );

    return FormItemWidget(
      data: currentWidget,
      stateManager: widget.stateManager,
      focusNode: focusNodeMap[rowIndex]?[columnName],
      rowIndex: rowIndex,
      isShowLabel: false,
    );
  }
}

class TableColumn {
  final String id;
  final String colName;
  final String label;
  final String name;
  final FormItemInfo? formItem;
  final bool hideTitle;
  final bool hideColumn;

  TableColumn({
    required this.id,
    required this.colName,
    required this.label,
    required this.name,
    this.formItem,
    this.hideTitle = false,
    this.hideColumn = false,
  });
}
