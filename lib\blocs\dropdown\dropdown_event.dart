import 'package:eapprove/models/form/call_service_request_model.dart';
import 'package:equatable/equatable.dart';
import 'package:eapprove/models/form/individual_info_request_model.dart';
import 'package:eapprove/models/form/md_service_request_model.dart';

abstract class DropdownEvent extends Equatable {
  const DropdownEvent();

  @override
  List<Object?> get props => [];
}

class GetDropdownDataRequested extends DropdownEvent {
  final IndividualInfoRequestModel requestModel;
  final String requestKey;
  final bool forceRefresh;

  const GetDropdownDataRequested({
    required this.requestModel,
    required this.requestKey,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [requestModel, requestKey, forceRefresh];
}

class ResetDropdownData extends DropdownEvent {}

class GetDropdownDataFromAPILink extends DropdownEvent {
  final Map<String, dynamic> requestModel;
  final String requestKey;
  final String endpoint;
  final String apiURL;
  final bool forceRefresh;

  const GetDropdownDataFromAPILink({
    required this.requestModel,
    required this.requestKey,
    required this.endpoint,
    required this.apiURL,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [requestModel, requestKey, endpoint, apiURL];
}

class GetDropdownDataFromAPILinkService extends DropdownEvent {
  final CallServiceRequestModel requestModel;
  final String requestKey;
  final String apiURL;
  final bool forceRefresh;

  const GetDropdownDataFromAPILinkService({
    required this.requestModel,
    required this.requestKey,
    required this.apiURL,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [requestModel, requestKey, apiURL];
}

class GetDropdownDataFromMasterData extends DropdownEvent {
  final MdServiceRequestBody requestModel;
  final String requestKey;
  final String apiURL;
  final bool forceRefresh;

  const GetDropdownDataFromMasterData({
    required this.requestModel,
    required this.requestKey,
    required this.apiURL,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [requestModel, requestKey, apiURL];
} 