import 'package:eapprove/models/pyc/ticket_task_model.dart';

class ApproveContent {
  final int? id;
  final int? ticketId;
  final String? taskId;
  final String? procInstId;
  final String? ticketEndActId;
  final String? ticketStartActId;
  final String? createdUser;
  final String? taskDefKey;
  final String? taskStatus;
  final String? procTitle;
  String? get ticketTitle => procTitle;
  final String? taskName;
  final String? serviceName;
  final int? taskPriority;
  final String? startUser;
  final String? requestCode;
  final int? taskCreatedTime;
  final double? slaFinish;
  final double? slaResponse;
  final String? ticketProcDefId;
  final int? slaFinishTime;
  final int? remainingTime;
  final String? priorityName;
  final String? companyCode;
  final double? ticketRating;
  final int? ticketCreatedTime;
  final int? serviceId;
  final String? ticketStatus;
  final String? chartNodeName;
  final List<TicketTask>? ticketTaskDtoList;

  ApproveContent({
    this.id,
    this.ticketId,
    this.taskId,
    this.procInstId,
    this.ticketEndActId,
    this.ticketStartActId,
    this.createdUser,
    this.taskDefKey,
    this.taskStatus,
    this.procTitle,
    this.taskName,
    this.serviceName,
    this.taskPriority,
    this.startUser,
    this.requestCode,
    this.taskCreatedTime,
    this.slaFinish,
    this.slaResponse,
    this.ticketProcDefId,
    this.slaFinishTime,
    this.remainingTime,
    this.priorityName,
    this.companyCode,
    this.ticketRating,
    this.ticketCreatedTime,
    this.serviceId,
    this.ticketStatus,
    this.chartNodeName,
    this.ticketTaskDtoList,
  });

  factory ApproveContent.fromJson(Map<String, dynamic> json) => ApproveContent(
        id: json['id'],
        ticketId: json['ticketId'],
        taskId: json['taskId'],
        procInstId: json['procInstId'],
        ticketEndActId: json['ticketEndActId'],
        ticketStartActId: json['ticketStartActId'],
        createdUser: json['createdUser'],
        taskDefKey: json['taskDefKey'],
        taskStatus: json['taskStatus'],
        procTitle: json['procTitle'],
        taskName: json['taskName'],
        serviceName: json['serviceName'],
        taskPriority: json['taskPriority'],
        startUser: json['startUser'],
        taskCreatedTime: json['taskCreatedTime'],
        slaFinish: json['slaFinish'],
        slaResponse: json['slaResponse'],
        requestCode: json['requestCode'],
        ticketProcDefId: json['ticketProcDefId'],
        slaFinishTime: json['slaFinishTime'],
        remainingTime: json['remainingTime'],
        priorityName: json['priorityName'],
        companyCode: json['companyCode'],
        ticketRating: json['ticketRating'],
        ticketCreatedTime: json['ticketCreatedTime'],
        serviceId: json['serviceId'],
        ticketStatus: json['ticketStatus'],
        chartNodeName: json['chartNodeName'],
        ticketTaskDtoList: (json['ticketTaskDtoList'] is List)
            ? (json['ticketTaskDtoList'] as List<dynamic>)
                .map((item) => TicketTask.fromJson(item as Map<String, dynamic>))
                .toList()
            : null,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'ticketId': ticketId,
        'taskId': taskId,
        'procInstId': procInstId,
        'ticketEndActId': ticketEndActId,
        'ticketStartActId': ticketStartActId,
        'createdUser': createdUser,
        'taskDefKey': taskDefKey,
        'taskStatus': taskStatus,
        'procTitle': procTitle,
        'taskName': taskName,
        'serviceName': serviceName,
        'taskPriority': taskPriority,
        'startUser': startUser,
        'taskCreatedTime': taskCreatedTime,
        'slaFinish': slaFinish,
        'slaResponse': slaResponse,
        'requestCode': requestCode,
        'ticketProcDefId': ticketProcDefId,
        'slaFinishTime': slaFinishTime,
        'remainingTime': remainingTime,
        'priorityName': priorityName,
        'companyCode': companyCode,
        'ticketRating': ticketRating,
        'ticketCreatedTime': ticketCreatedTime,
        'serviceId': serviceId,
        'ticketStatus': ticketStatus,
        'chartNodeName': chartNodeName,
        'ticketTaskDtoList': ticketTaskDtoList?.map((e) => e.toJson()).toList(),
      };
}
