import 'dart:convert';

import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/models/form/form_model.dart';
import 'package:flutter/foundation.dart';
import 'dart:math';

class FormParser {
  static FormItemType stringToFormItemType(String? typeString) {
    switch (typeString) {
      case 'text':
        return FormItemType.input;
      case 'textarea':
        return FormItemType.textArea;
      case 'checkbox':
        return FormItemType.checkbox;
      case 'radio':
        return FormItemType.radioButton;
      case 'select':
        return FormItemType.dropdown;
      case 'date':
        return FormItemType.datePicker;
      case 'splitter':
        return FormItemType.splitter;
      case 'row':
        return FormItemType.row;
      case 'column':
        return FormItemType.column;
      case 'step':
        return FormItemType.step;
      case 'tab':
        return FormItemType.tab;
      case 'file':
        return FormItemType.fileUpload;
      case 'table':
        return FormItemType.table;
      case 'matrix':
        return FormItemType.matrix;
      case 'divider':
        return FormItemType.divider;
      case 'label':
        return FormItemType.label;
      case 'url':
        return FormItemType.url;
      default:
        return FormItemType.input;
    }
  }

  static FormModel parseFormJson(Map<String, dynamic> jsonData) {
    final formItems = <FormItemInfo>[];
    // debugPrint('Starting FormParser.parseFormJson...');

    try {
      final template = jsonData['template'] as Map<String, dynamic>? ?? {};
      // debugPrint('Template keys: ${template.keys.toList()}');
      
      final formData = template['form'] as List? ?? [];
      // debugPrint('Form data length: ${formData.length}');

      for (var i = 0; i < formData.length; i++) {
        try {
          final formItem = formData[i];
          // debugPrint('Processing form item $i: ${formItem.toString().substring(0, min(100, formItem.toString().length))}...');
          
          // Safely convert to Map<String, dynamic>
          final Map<String, dynamic> formItemMap = _safeMapCast(formItem);
          // debugPrint('Form item $i converted to map successfully');

          // Extract ID safely
          String? itemId = _safeGetString(formItemMap, 'id');
          String? itemName = _safeGetString(formItemMap, 'name');
          // debugPrint('Form item $i - ID: $itemId, Name: $itemName');

          final itemType = stringToFormItemType(_safeGetString(formItemMap, 'type'));
          // debugPrint('Form item $i - Type: $itemType');

          var options = _safeGetListOfMaps(formItemMap, 'options');
          var option = _safeGetListOfMaps(formItemMap, 'option');
          // debugPrint('Form item $i - Options count: ${options?.length}, Option count: ${option?.length}');

          // Create FormItemInfo
          final formItemInfo = FormItemInfo(
            id: itemId,
            step: _safeGetString(formItemMap, 'step'),
            row: _safeGetString(formItemMap, 'row'),
            col: _safeGetString(formItemMap, 'col'),
            rowSortOrder: _safeGetDouble(formItemMap, 'rowSortOrder'),
            colSortOrder: _safeGetDouble(formItemMap, 'colSortOrder'),
            fieldSortOrder: _safeGetDouble(formItemMap, 'fieldSortOrder'),
            tab: _safeGetString(formItemMap, 'tab'),
            tabItem: _safeGetString(formItemMap, 'tabItem'),
            parentName: _safeGetString(formItemMap, 'parentName'),
            splitter: _safeGetString(formItemMap, 'splitter'),
            validations: _safeGetMap(formItemMap, 'validations'),
            type: itemType,
            label: _safeGetString(formItemMap, 'label'),
            name: itemName,
            placeholder: _safeGetString(formItemMap, 'placeholder'),
            eventExpression: _safeGetListOfMaps(formItemMap, 'eventExpression'),
            isHorizontal: _safeGetDouble(formItemMap, 'isHorizontal'),
            fontWeight: _safeGetString(formItemMap, 'fontWeight'),
            display: formItemMap['display'],
            suggestText: _safeGetString(formItemMap, 'suggestText'),
            isCloneInSpecFlow: formItemMap['isCloneInSpecFlow'],
            displayName: _safeGetString(formItemMap, 'displayName'),
            isClearOnClone: formItemMap['isClearOnClone'],
            isConditionAuth: formItemMap['isConditionAuth'],
            value: formItemMap['value'],
            readonly: formItemMap['readonly'],
            forceView: formItemMap['forceView'],
            optionConfig: _safeGetMap(formItemMap, 'optionConfig'),
            autoGenId: formItemMap['autoGenId'],
            maskInput: formItemMap['maskInput'],
            showLimitInput: formItemMap['showLimitInput'],
            useTimeDefault: formItemMap['useTimeDefault'],
            tooltip: _safeGetString(formItemMap, 'tooltip'),
            maskInputValue: _safeGetListOfMaps(formItemMap, 'maskInputValue'),
            typeOption: _safeGetString(formItemMap, 'typeOption'),
            optionType: _safeGetString(formItemMap, 'optionType'),
            inputType: _safeGetString(formItemMap, 'inputType'),
            maxLength: _safeGetValidationDouble(formItemMap, 'maxlength'),
            minLength: _safeGetValidationDouble(formItemMap, 'minlength'),
            maxValue: _safeGetDouble(formItemMap, 'maxValue'),
            minValue: _safeGetDouble(formItemMap, 'minValue'),
            stepNumber: _safeGetDouble(formItemMap, 'stepNumber'),
            isDecimal: formItemMap['isDecimal'],
            currencyText: formItemMap['currencyText'],
            currencyType: _safeGetString(formItemMap, 'currencyType'),

            // For checkbox and radio
            isCheckDifference: formItemMap['isCheckDifference'],
            isCheckAll: formItemMap['isCheckAll'],
            isHorizontalItem: _safeGetInt(formItemMap, 'isHorizontalItem'),

            // For splitter
            splitterOpen: _safeGetInt(formItemMap, 'splitterOpen'),
            splitterDesc: _safeGetString(formItemMap, 'splitterDesc'),

            // For options (checkbox, radio, dropdown)
            options: options,
            option: option,

            dataValue: formItemMap['dataValue'],
            error: '',
          );

          formItems.add(formItemInfo);
          // debugPrint('Form item $i added successfully');
        } catch (e, stackTrace) {
          debugPrint('Error processing form item $i: $e');
          debugPrint('Stack trace: $stackTrace');
          // Continue to next item
        }
      }
      
      debugPrint('Successfully processed ${formItems.length} form items');
    } catch (e, stackTrace) {
      debugPrint('Error in parseFormJson: $e');
      debugPrint('Stack trace: $stackTrace');
      // Return an empty form model in case of error
      return FormModel(
        key: jsonData['id']?.toString() ?? 'default',
        formItems: [],
      );
    }

    return FormModel(
      key: jsonData['id']?.toString() ?? 'default',
      formItems: formItems,
    );
  }

  // Helper methods for safe data extraction

  static Map<String, dynamic> _safeMapCast(dynamic item) {
    if (item is Map) {
      return Map<String, dynamic>.from(item);
    }
    return {};
  }

  static String? _safeGetString(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return null;
    return value.toString();
  }

  static double? _safeGetDouble(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    
    try {
      final strValue = value.toString().trim();
      if (strValue.isEmpty) return null;
      
      // Handle special cases
      if (strValue.toLowerCase() == 'null' || strValue.toLowerCase() == 'undefined') {
        return null;
      }
      
      // Remove any non-numeric characters except decimal point and minus sign
      final cleanedValue = strValue.replaceAll(RegExp(r'[^0-9\-\.]'), '');
      if (cleanedValue.isEmpty) return null;
      
      return double.tryParse(cleanedValue);
    } catch (e) {
      print('Error parsing double for key "$key": $e. Original value: $value');
      return null;
    }
  }

  static int? _safeGetInt(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.round();
    
    try {
      final strValue = value.toString().trim();
      if (strValue.isEmpty) return null;
      
      // Handle special cases
      if (strValue.toLowerCase() == 'null' || strValue.toLowerCase() == 'undefined') {
        return null;
      }
      
      // Try parsing as double first, then round to int
      final doubleValue = double.tryParse(strValue);
      if (doubleValue != null) {
        return doubleValue.round();
      }
      
      // If double parsing fails, try parsing as int
      return int.tryParse(strValue);
    } catch (e) {
      print('Error parsing int for key "$key": $e. Original value: $value');
      return null;
    }
  }

  static double? _safeGetValidationDouble(Map<String, dynamic> map, String validationKey) {
    if (map['validations'] == null) return null;
    if (map['validations'] is! Map) return null;

    final validations = map['validations'] as Map;
    if (!validations.containsKey(validationKey)) return null;

    final value = validations[validationKey];
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    
    try {
      final strValue = value.toString().trim();
      if (strValue.isEmpty) return null;
      
      // Handle special cases
      if (strValue.toLowerCase() == 'null' || strValue.toLowerCase() == 'undefined') {
        return null;
      }
      
      // Remove any non-numeric characters except decimal point and minus sign
      final cleanedValue = strValue.replaceAll(RegExp(r'[^0-9\-\.]'), '');
      if (cleanedValue.isEmpty) return null;
      
      return double.tryParse(cleanedValue);
    } catch (e) {
      print('Error parsing validation double for key "$validationKey": $e. Original value: $value');
      return null;
    }
  }

  static Map<String, dynamic>? _safeGetMap(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return null;
    if (value is Map) {
      return Map<String, dynamic>.from(value);
    }
    return null;
  }

  static List<Map<String, dynamic>>? _safeGetListOfMaps(Map<String, dynamic> map, String key) {
    final value = map[key];
    if (value == null) return null;

    if (value is List) {
      try {
        return List<Map<String, dynamic>>.from(value);
      } catch (e) {
        final resultList = <Map<String, dynamic>>[];
        for (var item in value) {
          if (item is Map) {
            final convertedItem = <String, dynamic>{};
            item.forEach((k, v) {
              convertedItem[k.toString()] = v;
            });
            resultList.add(convertedItem);
          }
        }
        if (resultList.isNotEmpty) {
          return resultList;
        }
        return null;
      }
    }
    return null;
  }
}
