import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:eapprove/blocs/account/user_list_state.dart';
import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/blocs/user/user_state.dart';
import 'package:eapprove/models/account/account_model.dart';
import 'package:eapprove/repositories/switch_account_repository.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/home_screen.dart';
import 'package:eapprove/services/token.dart';
import 'package:eapprove/widgets/custom_bottom_switch_acc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_button.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'dart:async';
import 'dart:developer' as developer;

class _AccountSelectionState {
  int selectedAccountIndex = 0;
  List<UserList> userList = [];
  List<UserList> filteredUserList = [];
  String searchQuery = '';
}

void showAccountSelectionSheet(
    BuildContext context, SwitchAccountRepository switchAccountRepository) {
  final state = _AccountSelectionState();

  // Remove the FetchUserList call since we'll reuse existing data
  // context.read<UserListBloc>().add(FetchUserList());

  CustomBottomSheet.show(
    context: context,
    title: 'Đổi tài khoản',
    closeIcon: IconButton(
      icon: const Icon(Icons.close, color: Colors.black),
      onPressed: () => Navigator.pop(context),
    ),
    children: BlocBuilder<UserListBloc, UserListState>(
      builder: (context, blocState) {
        if (blocState is UserListLoading) {
          return Center(child: AppConstraint.buildLoading(context));
        } else if (blocState is UserListLoaded) {
          state.userList = blocState.users;
          state.filteredUserList = blocState.users;
          if (state.userList.isEmpty) {
            return const Center(
              child: Text(
                "Chưa có danh sách người dùng",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey),
              ),
            );
          }

          return Expanded(
            child: StatefulBuilder(
              builder: (context, setState) =>
                  _buildUserList(context, state, setState),
            ),
          );
        } else if (blocState is UserListError) {
          return Center(
            child:
                Text("Lỗi khi tải danh sách tài khoản: ${blocState.message}"),
          );
        }
        return const Center(child: Text("Không có dữ liệu"));
      },
    ),
    btnBottom: _buildBottomButtons(context, state, switchAccountRepository),
  );
}

Widget _buildUserList(
    BuildContext context, _AccountSelectionState state, StateSetter setState) {
  return Column(
    children: [
      Padding(
        padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 10.h),
        child: TextField(
          style: getTypoSkin()
              .body2Regular
              .copyWith(color: getColorSkin().primaryText),
          decoration: InputDecoration(
            hintText: 'Tìm công ty, tên tài khoản',
            hintStyle: getTypoSkin()
                .body2Regular
                .copyWith(color: getColorSkin().secondaryText),
            prefixIcon: Icon(Icons.search,
                color: getColorSkin().secondaryText, size: 20),
            prefixIconConstraints: BoxConstraints(minWidth: 40),
            contentPadding: EdgeInsets.symmetric(vertical: 8.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: getColorSkin().grey3Background),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: getColorSkin().grey3Background),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: getColorSkin().secondaryColor1),
            ),
          ),
          onChanged: (value) {
            setState(() {
              state.searchQuery = value.toLowerCase();
              state.filteredUserList = state.userList.where((user) {
                final fullName = user.fullName?.toLowerCase() ?? '';
                final userName = user.userName?.toLowerCase() ?? '';
                final email = user.email?.toLowerCase() ?? '';
                final phone = user.phone?.toLowerCase() ?? '';

                return fullName.contains(state.searchQuery) ||
                    userName.contains(state.searchQuery) ||
                    email.contains(state.searchQuery) ||
                    phone.contains(state.searchQuery);
              }).toList();
            });
          },
        ),
      ),
      Expanded(
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: state.searchQuery.isEmpty
              ? state.userList.length
              : state.filteredUserList.length,
          itemBuilder: (context, index) {
            final user = state.searchQuery.isEmpty
                ? state.userList[index]
                : state.filteredUserList[index];
            final actualIndex = state.searchQuery.isEmpty
                ? index
                : state.userList.indexOf(user);
            return _buildUserCard(context, state, actualIndex, setState);
          },
        ),
      ),
    ],
  );
}

Widget _buildUserCard(BuildContext context, _AccountSelectionState state,
    int index, StateSetter setState) {
  final user = state.userList[index];
  final isSelected = state.selectedAccountIndex == index;

  return GestureDetector(
    onTap: () => setState(() => state.selectedAccountIndex = index),
    child: Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: BorderSide(
            color: isSelected
                ? getColorSkin().secondaryColor1BorderColor
                : Colors.transparent,
            width: 1,
          ),
        ),
        color: isSelected
            ? getColorSkin().secondaryColor1TagBackground
            : Colors.white,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildAvatar(index, isSelected),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildUserInfo(user),
                    ),
                    SizedBox(
                      height: 25.h,
                      width: 25.w,
                      child: Transform.scale(
                        scale: 1.2, // Adjust the scale as needed
                        child: Radio<int>(
                          value: index,
                          groupValue: state.selectedAccountIndex,
                          onChanged: (value) => setState(
                              () => state.selectedAccountIndex = value!),
                          activeColor: Colors.blue,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

Widget _buildAvatar(int index, bool isSelected) {
  return Padding(
    padding: EdgeInsets.symmetric(vertical: 5.h),
    child: SizedBox(
      width: 48.w,
      height: 48.h,
      child: SvgPicture.asset(StringImage.avatar_default),
    ),
  );
}

Widget _buildUserInfo(UserList user) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        '${user.fullName ?? "Chưa có giá trị"} - ${user.userName ?? "Chưa có giá trị"}',
        style:
            getTypoSkin().title4Regular.copyWith(color: getColorSkin().title),
      ),
      const SizedBox(height: 4),
      Text(
        "${user.email ?? "Chưa có giá trị"} • ${user.phone ?? "Chưa có giá trị"}",
        style: getTypoSkin()
            .subtitle3Regular
            .copyWith(color: getColorSkin().secondaryText),
      ),
      const SizedBox(height: 8),
      _buildJobInfo(user.jobTitles),
    ],
  );
}

Widget _buildJobInfo(List<JobTitles>? jobTitles) {
  if (jobTitles == null || jobTitles.isEmpty) return const SizedBox.shrink();

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Text(
              "•",
              style: getTypoSkin()
                  .body2Regular
                  .copyWith(color: getColorSkin().primaryText),
            ),
          ),
          Expanded(
            child: Text(
              _formatJobTitles(jobTitles, (job) => "${job.parentCode ?? "Chưa có giá trị"} - ${job. parentName?? "Chưa có giá trị"}"),
              style: getTypoSkin()
                  .body2Regular
                  .copyWith(color: getColorSkin().primaryText),
              softWrap: true,
            ),
          ),
        ],
      ),
      SizedBox(height: 4.h),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Text("•",
                style: getTypoSkin()
                    .body2Regular
                    .copyWith(color: getColorSkin().primaryText)),
          ),
          Expanded(
            child: Text(
              "Chức danh: ${_formatJobTitles(jobTitles, (job) => "${job.jobTitleCode ?? "Chưa có giá trị"} - ${job.jobTilteName ?? "Chưa có giá trị"}")}",
              style: getTypoSkin()
                  .body2Regular
                  .copyWith(color: getColorSkin().primaryText),
              softWrap: true,
            ),
          )
        ],
      ),
      SizedBox(height: 4.h),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Text("•",
                style: getTypoSkin()
                    .body2Regular
                    .copyWith(color: getColorSkin().primaryText)),
          ),
          Expanded(
            child: Text(
              "Chức vụ: ${_formatJobTitles(jobTitles, (job) => "${job.positionCode ?? "Chưa có giá trị"} - ${job.positionName ?? "Chưa có giá trị"}")}",
              style: getTypoSkin()
                  .body2Regular
                  .copyWith(color: getColorSkin().primaryText),
              softWrap: true,
            ),
          )
        ],
      ),
    ],
  );
}

String _formatJobTitles(
    List<JobTitles> jobTitles, String Function(JobTitles) formatter) {
  return jobTitles.map(formatter).join(', ');
}

Widget _buildBottomButtons(BuildContext context, _AccountSelectionState state,
    SwitchAccountRepository switchAccountRepository) {
  return Row(
    children: [
      // Expanded(
      //   child: _buildCancelButton(context),
      // ),
      // SizedBox(width: 12.w),
      Expanded(
        child: _buildSwitchButton(context, state, switchAccountRepository),
      ),
    ],
  );
}

Widget _buildCancelButton(BuildContext context) {
  return CustomButton(
    label: 'Huỷ',
    textStyle: getTypoSkin().buttonText2Regular.copyWith(
          color: getColorSkin().secondaryText,
        ),
    textAlign: TextAlign.center,
    btnStyle: ElevatedButton.styleFrom(
      padding: EdgeInsets.symmetric(
        vertical: 12.h,
        horizontal: 24.w,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      backgroundColor: getColorSkin().grey3Background,
    ),
    onPressed: () => Navigator.pop(context),
  );
}

Widget _buildSwitchButton(BuildContext context, _AccountSelectionState state,
    SwitchAccountRepository switchAccountRepository) {
  return CustomButton(
    label: 'Chuyển',
    textStyle: getTypoSkin().buttonText1Medium.copyWith(
          color: getColorSkin().white,
        ),
    textAlign: TextAlign.center,
    btnStyle: ElevatedButton.styleFrom(
      padding: EdgeInsets.symmetric(
        vertical: 12.h,
        horizontal: 24.w,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      backgroundColor: getColorSkin().secondaryColor1,
    ),
    onPressed: () =>
        _handleAccountSwitch(context, state, switchAccountRepository),
  );
}

Future<void> _handleAccountSwitch(
    BuildContext context,
    _AccountSelectionState state,
    SwitchAccountRepository switchAccountRepository) async {
  if (state.userList.isEmpty ||
      state.selectedAccountIndex < 0 ||
      state.selectedAccountIndex >= state.userList.length) {
    print("Chưa chọn tài khoản nào!");
    return;
  }

  final username = state.userList[state.selectedAccountIndex].userName;
  if (username == null) return;

  try {
    _showLoadingDialog(context);
    final tokenManager = TokenManager();
    final token = await tokenManager.getTokenData();
    developer.log('Before switch account: ${token!['accessToken']}');
    final response = await switchAccountRepository.switchAccount(username);
    if (!context.mounted) {
      Navigator.pop(context); // Close loading dialog
      return;
    }
    developer.log('After switch account: ${response!.data!.accessToken}');

    // Check if response is successful and has data
    if (response?.meta.code == 200 && response?.data != null) {
      // Clear old token first
      await tokenManager.clearTokenData();

      // Save new token and wait for completion
      final completer = Completer<void>();
      final subscription =
          context.read<AuthenticationBloc>().stream.listen((state) {
        if (!completer.isCompleted) {
          if (state is TokenSuccess) {
            print("Token saved successfully in AuthenticationBloc");
            completer.complete();
          } else if (state is AuthenticationFailure) {
            print("Token save failed in AuthenticationBloc: ${state.error}");
            completer.completeError(state.error);
          }
        }
      });

      context
          .read<AuthenticationBloc>()
          .add(SaveSwitchAccountToken(response!.data!));

      try {
        await completer.future;
        print("Token saving process completed successfully");
      } catch (e) {
        print("Error during token saving process: $e");
        subscription.cancel();
        if (context.mounted) {
          Navigator.pop(context); // Close loading dialog
          _showErrorDialog(context, null,
              error: "Lỗi khi lưu token: ${e.toString()}");
        }
        return;
      }

      subscription.cancel();

      // Verify token is saved with exponential backoff
      bool tokenSaved = false;
      int attempts = 0;
      int maxAttempts = 5;
      int delay = 200; // Start with 200ms delay

      while (!tokenSaved && attempts < maxAttempts) {
        await Future.delayed(Duration(milliseconds: delay));

        // Get token data directly from storage
        final tokenData = await tokenManager.getTokenData();
        if (tokenData != null &&
            tokenData['accessToken'] == response.data!.accessToken) {
          tokenSaved = true;
          print("Token verified successfully on attempt $attempts");
        } else {
          print(
              "Token verification attempt $attempts: No token yet or mismatch (delay: ${delay}ms)");
          attempts++;
          if (attempts < maxAttempts) {
            delay *= 2; // Exponential backoff
          }
        }
      }

      if (!tokenSaved) {
        print("Failed to verify token after $attempts attempts");
        if (context.mounted) {
          Navigator.pop(context); // Close loading dialog
          _showErrorDialog(context, null,
              error:
                  "Đã xảy ra lỗi trong quá trình chuyển tài khoản. Vui lòng thử lại sau.");
        }
        return;
      }

      print("Token verified successfully");

      if (!context.mounted) {
        Navigator.pop(context); // Close loading dialog
        return;
      }

      // Clear user data cache first
      await context.read<UserBloc>().clearUserInfo();

      // Reset and fetch user data with new token
      context.read<UserBloc>().add(FetchUserInfo());
      context.read<UserListBloc>().add(ResetUserList());

      // Wait for user info to be fetched first
      delay = 500;
      bool userInfoFetched = false;
      attempts = 0;
      maxAttempts = 3;

      while (!userInfoFetched && attempts < maxAttempts) {
        await Future.delayed(Duration(milliseconds: delay));

        final userState = context.read<UserBloc>().state;
        if (userState is UserLoaded) {
          userInfoFetched = true;
          print("User info fetched successfully on attempt $attempts");
        } else {
          print(
              "User info fetch attempt $attempts: Data not ready yet (delay: ${delay}ms)");
          attempts++;
          if (attempts < maxAttempts) {
            delay *= 2;
          }
        }
      }

      if (!userInfoFetched) {
        print("Failed to fetch user info after $attempts attempts");
        if (context.mounted) {
          Navigator.pop(context); // Close loading dialog
          _showErrorDialog(context, null,
              error:
                  "Không thể tải thông tin người dùng. Vui lòng thử lại sau.");
        }
        return;
      }

      // Now fetch user list
      context.read<UserListBloc>().add(FetchUserList());

      // Wait for user list to be fetched
      delay = 500;
      bool userListFetched = false;
      attempts = 0;
      maxAttempts = 3;

      while (!userListFetched && attempts < maxAttempts) {
        await Future.delayed(Duration(milliseconds: delay));

        final userListState = context.read<UserListBloc>().state;
        if (userListState is UserListLoaded) {
          userListFetched = true;
          print("User list fetched successfully on attempt $attempts");

          // Force rebuild of home screen with new data
          if (context.mounted) {
            final userState = context.read<UserBloc>().state;
            if (userState is UserLoaded) {
              context.read<UserBloc>().emit(UserLoaded(userState.userInfo));
              context.read<UserListBloc>().emit(
                  UserListLoaded(userListState.users, userListState.userADM));
            }
          }
        } else {
          print(
              "User list fetch attempt $attempts: Data not ready yet (delay: ${delay}ms)");
          attempts++;
          if (attempts < maxAttempts) {
            delay *= 2;
          }
        }
      }

      if (!userListFetched) {
        print("Failed to fetch user list after $attempts attempts");
        if (context.mounted) {
          Navigator.pop(context); // Close loading dialog
          _showErrorDialog(context, null,
              error:
                  "Không thể tải danh sách người dùng. Vui lòng thử lại sau.");
        }
        return;
      }

      if (!context.mounted) {
        Navigator.pop(context); // Close loading dialog
        return;
      }

      debugPrint('BottomNavScreen');
      Navigator.pop(context);
      // Navigate to home screen
      context.go(BottomNavScreen.routeName);
    } else {
      if (context.mounted) {
        Navigator.pop(context); // Close loading dialog
        _showErrorDialog(context, response);
      }
    }
  } catch (e) {
    print("Error during account switch: $e");
    if (context.mounted) {
      Navigator.pop(context); // Close loading dialog
      _showErrorDialog(context, null, error: e);
    }
  }
}

void _showLoadingDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) =>
        Center(child: AppConstraint.buildLoading(context)),
  );
}

void _showErrorDialog(BuildContext context, dynamic response, {Object? error}) {
  final errorMessages = {
    ********: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
    ********: "Tài khoản không được liên kết!",
    ********: "Tài khoản chưa đủ thông tin!",
    401: "Bạn không có quyền thực hiện hành động này.",
    403: "Tài khoản của bạn bị hạn chế truy cập.",
    404: "Không tìm thấy tài khoản.",
    500: "Lỗi hệ thống, vui lòng thử lại sau."
  };

  final message = error != null
      ? "Đã xảy ra lỗi trong quá trình chuyển tài khoản. Vui lòng thử lại sau."
      : errorMessages[response?.meta.code] ??
          response?.meta.message ??
          "Đã xảy ra lỗi.";

  showDialog(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      backgroundColor: Colors.white,
      title: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 28),
          const SizedBox(width: 8),
          Text(
            error != null ? 'Lỗi' : 'Thông báo',
            style: const TextStyle(
                fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red),
          ),
        ],
      ),
      content: Text(
        message,
        style: const TextStyle(fontSize: 16, color: Colors.black87),
      ),
      actions: [
        Align(
          alignment: Alignment.center,
          child: TextButton(
            style: TextButton.styleFrom(
              backgroundColor: getColorSkin().secondaryColor1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            onPressed: () => Navigator.pop(context),
            child: const Text(
              "OK",
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white),
            ),
          ),
        ),
      ],
    ),
  );
}
