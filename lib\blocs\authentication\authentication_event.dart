import 'package:eapprove/models/switch_account/switch_account_model.dart'
    as sw_token;
import 'package:equatable/equatable.dart';

abstract class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object?> get props => [];
}

class LoginSubmitted extends AuthenticationEvent {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginSubmitted({
    required this.username,
    required this.password,
    required this.rememberMe,
  });

  @override
  List<Object?> get props => [username, password, rememberMe];
}

class LoginReset extends AuthenticationEvent {}

class LoadSavedLogin extends AuthenticationEvent {}

class LogoutRequested extends AuthenticationEvent {}

class ResetPassSubmitted extends AuthenticationEvent {
  final String username;

  const ResetPassSubmitted({
    required this.username,
  });

  @override
  List<Object?> get props => [username];
}

class FirstTimeLoginSubmitted extends AuthenticationEvent {
  final String username;
  final String password;
  final String rePassword;
  final String hash;

  const FirstTimeLoginSubmitted({
    required this.username,
    required this.password,
    required this.rePassword,
    required this.hash,
  });
}

class VerifyOtpSubmitted extends AuthenticationEvent {
  final String otp;
  final String sessionState;
  final String? hash;

  const VerifyOtpSubmitted({
    required this.otp,
    required this.sessionState,
    this.hash,
  });

  @override
  List<Object?> get props => [otp, sessionState, hash];
}

class ResendOtpRequested extends AuthenticationEvent {
  final String sessionState;
  final String? hash;

  const ResendOtpRequested({
    required this.sessionState,
    this.hash,
  });

  @override
  List<Object?> get props => [sessionState, hash];
}

class GetTokenRequested extends AuthenticationEvent {
  final String code;
  final bool is2FA;

  const GetTokenRequested({
    required this.code,
    this.is2FA = false,
  });

  @override
  List<Object?> get props => [code, is2FA];
}

class RefreshTokenRequested extends AuthenticationEvent {
  const RefreshTokenRequested();
}

class CheckAuthenticationStatus extends AuthenticationEvent {}

class SaveLoginInfo extends AuthenticationEvent {
  final String username;
  // final String password;
  final bool rememberMe;

  const SaveLoginInfo({
    required this.username,
    // required this.password,
    required this.rememberMe,
  });

  @override
  List<Object> get props => [username, rememberMe];
}

class SaveSwitchAccountToken extends AuthenticationEvent {
  final sw_token.TokenData tokenData;

  const SaveSwitchAccountToken(this.tokenData);

  @override
  List<Object?> get props => [tokenData];
}
