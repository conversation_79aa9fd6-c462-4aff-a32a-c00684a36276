import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/svg.dart';

class CollapseView extends StatefulWidget {
  final String title;
  final List<Widget> children;
  final bool isExpanded;
  final Color? backgroundColor;
  final String? expandedSvgIconPath;
  final String? titleSvgIconPath;
  final bool lockExpanded;

  const CollapseView({
    super.key,
    required this.title,
    required this.children,
    this.isExpanded = false,
    this.expandedSvgIconPath,
    this.titleSvgIconPath,
    this.lockExpanded = false,
    this.backgroundColor,
  });

  @override
  State<CollapseView> createState() => _CollapseViewState();
}

class _CollapseViewState extends State<CollapseView> {
  bool isCollapsed = false;

  @override
  void initState() {
    super.initState();
    isCollapsed = widget.isExpanded;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? getColorSkin().white,
          ),
          child: GestureDetector(
            onTap: () {
              setState(() {
                isCollapsed = !isCollapsed;
              });
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Row(
                children: [
                  if (widget.titleSvgIconPath != null)
                    SvgPicture.asset(
                      widget.titleSvgIconPath!,
                      width: 20.w,
                      height: 20.h,
                      colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
                    ),
                  if (widget.titleSvgIconPath != null) SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                      overflow: TextOverflow.visible,
                      softWrap: true,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  SvgPicture.asset(
                    isCollapsed ? StringImage.ic_arrow_down : StringImage.ic_arrow_up,
                    width: 10.w,
                    height: 10.h,
                    colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (isCollapsed)
          Container(
            width: double.infinity,
            padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 8.h),
            child: Column(
              children: widget.children,
            ),
          ),
        // SizedBox(height: 12.h),
      ],
    );
  }
}
