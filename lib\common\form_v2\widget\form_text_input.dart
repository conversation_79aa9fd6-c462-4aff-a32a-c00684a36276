import 'dart:async';

import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'dart:developer' as developer;
import 'package:eapprove/utils/utils.dart';

class TextInputForm extends StatefulWidget {
  final FormItemInfo formWidget;
  final FormStateManager stateManager;
  final FocusNode? focusNode; //
  final bool autoFocus;
  final int? rowIndex;
  final bool? isShowLabel;

  const TextInputForm({
    Key? key,
    required this.formWidget,
    required this.stateManager,
    this.focusNode,
    this.autoFocus = false,
    this.rowIndex,
    this.isShowLabel = true,
  }) : super(key: key);

  @override
  State<TextInputForm> createState() => _TextInputFormState();
}

class _TextInputFormState extends State<TextInputForm> {
  late final TextEditingController controller;
  FocusNode? _focusNode;

  @override
  void initState() {
    super.initState();
    final name = widget.formWidget.name;
    final parentName = widget.formWidget.parentName;
    final type = widget.formWidget.type;

    developer.log('type: ${widget.formWidget.type}', name: 'TextInputForm');

    controller = widget.stateManager.getController(
      name.toString(),
      parentName: parentName,
      rowIndex: widget.rowIndex,
    );

    // Initial value chỉ set 1 lần, không lặp lại nếu controller đã có text
    if (controller.text.isEmpty) {
      final initialValue = widget.stateManager.getFieldValueByName(
        name.toString(),
        parentName,
        rowIndex: widget.rowIndex,
      );
      if (initialValue != null) {
        controller.text =
            widget.formWidget.autoGenId == true ? '' : initialValue.toString();
      } else if (type == FormItemType.currency) {
        // Set default value to 0 for currency type
        controller.text = '0';
        widget.stateManager.setFieldValue(
          name.toString(),
          0,
          parentName: parentName,
          rowIndex: widget.rowIndex,
        );
      }
    }

    // Nếu widget truyền focusNode vào thì dùng, không thì tự tạo
    _focusNode = widget.focusNode ?? FocusNode();
  }

  void _onTextChanged(String? value) {
    if (value == null) return;

    String formattedValue = value;
    if (widget.formWidget.type == FormItemType.currency) {
      // Remove commas before parsing
      String cleanValue = value.replaceAll(',', '');
      double? numericValue = double.tryParse(cleanValue);
      // Store the raw number value
      widget.stateManager.setFieldValue(
        widget.formWidget.name.toString(),
        numericValue,
        parentName: widget.formWidget.parentName,
        rowIndex: widget.rowIndex,
        skipEvaluateEvent: true,
      );
      // Format for display
      formattedValue = Utils.formatCurrency(numericValue);
      // Update the controller with formatted value
      controller.text = formattedValue;
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: formattedValue.length),
      );
    } else {
      widget.stateManager.setFieldValue(
        widget.formWidget.name.toString(),
        formattedValue,
        parentName: widget.formWidget.parentName,
        rowIndex: widget.rowIndex,
        skipEvaluateEvent: true,
      );
    }
  }

  void _resetInput() {
    controller.clear();
    widget.stateManager.setFieldValue(
      widget.formWidget.name.toString(),
      '',
      parentName: widget.formWidget.parentName,
      rowIndex: widget.rowIndex,
    );
  }

  @override
  void dispose() {
    // Nếu tự tạo focusNode thì phải dispose
    if (widget.focusNode == null) {
      _focusNode?.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formWidget = widget.formWidget;
    final isReadonly = formWidget.readonly == true;

    void handleUnfocus() {
      if (_focusNode?.hasFocus == true) {
        _focusNode?.unfocus();
      }
      FocusScope.of(context).unfocus();
    }

    if (formWidget.display == false) {
      return const SizedBox.shrink();
    }
    
    final bool enable = !isReadonly && formWidget.autoGenId != true;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isShowLabel == true)
          FormLabel(
            displayName: formWidget.displayName,
            label: formWidget.label,
            suggestText: formWidget.suggestText,
            isRequired: formWidget.validations?['required'] == true,
          ),
        Container(
          margin: EdgeInsets.only(top: 8.h),
          child: CustomTextForm(
            edtController: controller,
            focusNode: _focusNode,
            maxLines: formWidget.type == FormItemType.textArea ? 5 : 1,
            hintText: formWidget.placeholder,
            onChanged: _onTextChanged,
            onSubmitted: (value) {
              handleUnfocus();
              widget.stateManager.setFieldValue(
                formWidget.name.toString(),
                value,
                parentName: formWidget.parentName,
                rowIndex: widget.rowIndex,
                skipEvaluateEvent: false,
              );
            },
            filled: true,
            fillColor: !enable ? getColorSkin().grey4Background : Colors.white,
            textInputAction: TextInputAction.done,
            onTapOutside: !enable ? (_) {} : (_) => handleUnfocus(),
            autoFocus: widget.autoFocus,
            showDeleteButton: true,
            enabled: enable,
            keyboardType: formWidget.type == FormItemType.number ||
                    formWidget.type == FormItemType.currency
                ? TextInputType.number
                : TextInputType.text,
            currency: formWidget.type == FormItemType.currency ? "VND" : null,
          ),
        ),
        if (formWidget.type == FormItemType.currency &&
            controller.text.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Text(
              'Bằng chữ: ${Utils.readNumber(double.tryParse(controller.text.replaceAll(',', '')))}',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }
}
