import 'package:flutter/material.dart';
import 'package:eapprove/models/helper_model/select_item.dart';

class CustomSelect extends StatefulWidget {
  final String label;
  final String? placeholder;
  final List<SelectItem> options;
  final Function(SelectItem?) onSelected;

  const CustomSelect({
    super.key,
    required this.label,
    this.placeholder,
    required this.options,
    required this.onSelected,
  });

  @override
  // ignore: library_private_types_in_public_api
  _CustomSelectState createState() => _CustomSelectState();
}

class _CustomSelectState extends State<CustomSelect> {
  SelectItem? selectedValue;

  void _onRemoveSelect() {
    setState(() => selectedValue = null);
    widget.onSelected(null);
  }

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                widget.placeholder ?? "Chọn ${widget.label}",
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
            Divider(height: 1, color: Colors.grey.shade300),
            ...widget.options.map((item) => ListTile(
                  title: Text(item.label),
                  onTap: () {
                    // ignore: unnecessary_null_comparison
                    if (item != null) {
                      setState(() => selectedValue = item);
                      widget.onSelected(item);
                      Navigator.pop(context);
                    }
                  },
                )),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showBottomSheet,
      child: Container(
        height: 56,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if ((selectedValue?.value ?? "").isNotEmpty)
                  Text(
                    widget.label,
                    style: const TextStyle(fontSize: 12, color: Colors.black),
                  ),
                Text(
                  (selectedValue?.value ?? "").isNotEmpty
                      ? selectedValue!.label
                      : widget.placeholder!.isNotEmpty
                          ? widget.placeholder.toString()
                          : widget.label,
                  style: TextStyle(
                    fontSize: 16,
                    color: (selectedValue?.value ?? "").isEmpty
                        ? Colors.grey
                        : Colors.black,
                  ),
                ),
              ],
            ),
            GestureDetector(
                onTap: () {
                  if (!(selectedValue?.value ?? "").isNotEmpty) {
                    _showBottomSheet();
                  } else {
                    _onRemoveSelect();
                  }
                },
                child: Icon(
                  !(selectedValue?.value ?? "").isNotEmpty
                      ? Icons.keyboard_arrow_down
                      : Icons.highlight_remove_outlined,
                  color: Colors.grey,
                )),
          ],
        ),
      ),
    );
  }
}
