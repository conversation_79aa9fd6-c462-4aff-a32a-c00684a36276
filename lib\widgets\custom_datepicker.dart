import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:intl/intl.dart';

class CustomDatePicker {
  /// Shows a date picker with customized styling
  ///
  /// [context] - The build context
  /// [initialDate] - The initially selected date
  /// [firstDate] - The earliest selectable date
  /// [lastDate] - The latest selectable date
  /// [locale] - Optional locale for the date picker, defaults to Vietnamese
  static Future<DateTime?> show({
    required BuildContext context,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    Locale locale = const Locale('vi', 'VN'),
  }) async {
    return await showDatePicker(
      locale: locale,
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      selectableDayPredicate: (DateTime day) {
        return !day.isBefore(firstDate);
      },
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: getColorSkin().blue2,
              onPrimary: Colors.black,
              onSurface: getColorSkin().ink1,
              surface: Colors.white,
            ),
            textSelectionTheme: TextSelectionThemeData(
              selectionColor: Colors.grey.withOpacity(0.4),
              cursorColor: getColorSkin().black,
              selectionHandleColor: Colors.grey,
            ),
            dialogBackgroundColor: Colors.white,
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: getColorSkin().primaryBlue,
              ),
            ),
            inputDecorationTheme: InputDecorationTheme(
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              hintStyle: TextStyle(color: Colors.black),
              labelStyle: TextStyle(color: Colors.black),
              filled: false,
              contentPadding: EdgeInsets.symmetric(vertical: 10.0, horizontal: 12.0),
            ),
            datePickerTheme: DatePickerThemeData(
              backgroundColor: Colors.white,
              todayBorder: BorderSide.none,
              todayForegroundColor: WidgetStateProperty.resolveWith((states) {
                return getColorSkin().ink1;
              }),
              todayBackgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return getColorSkin().blue2;
                }
                return Colors.transparent;
              }),
              dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return getColorSkin().blue2;
                }
                return Colors.transparent;
              }),
              dayForegroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return getColorSkin().ink1.withOpacity(0.3);
                }
                if (states.contains(WidgetState.selected)) {
                  return getColorSkin().ink1;
                }
                return getColorSkin().ink1;
              }),
            ),
          ),
          child: child!,
        );
      },
    );
  }

  static String formatDate(DateTime date, {String format = 'dd/MM/yyyy'}) {
    return DateFormat(format).format(date);
  }

  static Future<DateTime?> showAndUpdateController({
    required BuildContext context,
    required TextEditingController controller,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    String format = 'dd/MM/yyyy',
    Locale locale = const Locale('vi', 'VN'),
  }) async {
    final DateTime? picked = await show(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      locale: locale,
    );

    if (picked != null) {
      controller.text = formatDate(picked, format: format);
    }

    return picked;
  }
}
