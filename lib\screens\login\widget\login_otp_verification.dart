import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/widgets/otp_input_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:go_router/go_router.dart';

class Login2FA extends StatefulWidget {
  final String sessionState;
  final String? email;

  const Login2FA({
    super.key,
    required this.sessionState,
    this.email,
  });

  @override
  State<Login2FA> createState() => _Login2FAState();
}

class _Login2FAState extends State<Login2FA> {
  String _otpValue = '';
  bool _isLoading = false;
  String? _maskedEmail;

  @override
  void initState() {
    super.initState();
    if (widget.email != null && widget.email!.isNotEmpty) {
      _maskedEmail = _maskEmail(widget.email!);
    }
  }

  String _maskEmail(String email) {
    if (email.isEmpty) return "";
    try {
      final parts = email.split('@');
      if (parts.length != 2) return email;
      String username = parts[0];
      String domain = parts[1];
      if (username.length <= 4) {
        if (username.length <= 2) {
          return "${username.substring(0, 1)}${'*' * (username.length - 1)}@$domain";
        }
        return "${username.substring(0, 2)}${'*' * (username.length - 2)}@$domain";
      } else {
        return "${username.substring(0, 4)}${'*' * (username.length - 4)}@$domain";
      }
    } catch (e) {
      return email;
    }
  }

  void _handleOtpVerification() {
    if (_otpValue.length != 6) {
      SnackbarCore.error('Vui lòng nhập đủ 6 số OTP');
      return;
    }

    context.read<AuthenticationBloc>().add(
          VerifyOtpSubmitted(
            otp: _otpValue,
            sessionState: widget.sessionState,
          ),
        );
  }

  void _handleOtpComplete(String otp) {
    setState(() {
      _otpValue = otp;
    });
  }

  void _showLoadingDialog() {
    setState(() {
      _isLoading = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: Center(
              child: Container(
                padding: EdgeInsets.all(20.r),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AppConstraint.buildLoading(context),
                    SizedBox(height: 20.h),
                    Text(
                      'Đang xử lý...',
                      style: getTypoSkin().body2Regular.copyWith(
                            color: getColorSkin().primaryText,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _hideLoadingDialog() {
    setState(() {
      _isLoading = false;
    });
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is AuthenticationLoading) {
          _showLoadingDialog();
        } else {
          _hideLoadingDialog();

          if (state is OtpVerificationSuccess) {
            // Không cần làm gì ở đây vì sẽ chạy getToken
          } else if (state is TokenSuccess) {
            context.read<UserBloc>().add(FetchUserInfo());
            SnackbarCore.success('Đăng nhập thành công');
            final router = GoRouter.of(context);
            Future.delayed(const Duration(milliseconds: 300), () {
              context.go(BottomNavScreen.routeName);
            });
          } else if (state is AuthenticationFailure) {
            SnackbarCore.error(
                state.error.isEmpty ? 'Lỗi xác thực OTP' : state.error);
          }
        }
      },
      child: GradientBackground(
        showUpperImage: true,
        child: Scaffold(
          backgroundColor: getColorSkin().transparent,
          appBar: CustomAppBar(
            leading: IconButton(
              icon: Icon(Icons.clear, color: getColorSkin().title, size: 24),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            titleTextStyle: getTypoSkin().title3Medium.copyWith(
                  color: getColorSkin().title,
                ),
            title: 'Nhập OTP',
            centerTitle: true,
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
            child: Column(
              children: [
                SizedBox(height: 20.h),
                Text(
                  _maskedEmail != null && _maskedEmail!.isNotEmpty
                      ? 'Nhập mã OTP bạn đã nhận từ email $_maskedEmail'
                      : 'Nhập mã OTP bạn đã nhận',
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  softWrap: true,
                  style: getTypoSkin().buttonText2Regular.copyWith(
                        color: getColorSkin().secondaryText,
                      ),
                ),
                SizedBox(height: 20.h),
                OtpField(
                  onOtpComplete: _handleOtpComplete,
                  sessionState: widget.sessionState,
                ),
                SizedBox(height: 20.h),
              ],
            ),
          ),
          bottomNavigationBar: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: FFilledButton(
                  onPressed: _isLoading ? null : _handleOtpVerification,
                  isMaxWith: true,
                  child: const Text('Xác thực')),
            ),
          ),
        ),
      ),
    );
  }
}
