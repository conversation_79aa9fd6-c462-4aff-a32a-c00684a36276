class SelectItem {
  final String label;
  final String value;

  SelectItem({
    required this.label,
    required this.value,
  });

  @override
  String toString() => 'SelectItem(label: $label, value: $value)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SelectItem && other.value == value;
  }

  @override
  int get hashCode => value.hashCode;
}
