import 'package:equatable/equatable.dart';

enum RecoverRequestStatus { initial, loading, success, failure }

class RecoverRequestState extends Equatable {
  final RecoverRequestStatus status;
  final List<String> filePaths;
  final String? error;
  final String? actionType;

  const RecoverRequestState({
    required this.status,
    required this.filePaths,
    this.error,
    this.actionType,
  });

  factory RecoverRequestState.initial() =>
      RecoverRequestState(status: RecoverRequestStatus.initial, filePaths: [], error: null, actionType: null);

  RecoverRequestState copyWith(
      {RecoverRequestStatus? status, List<String>? filePaths, String? error, String? actionType}) {
    return RecoverRequestState(
        status: status ?? this.status,
        filePaths: filePaths ?? this.filePaths,
        error: error,
        actionType: actionType ?? this.actionType);
  }

  @override
  List<Object?> get props => [status, filePaths, error];
}
