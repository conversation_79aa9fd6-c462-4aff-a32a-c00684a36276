import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:eapprove/assets/StringIcon.dart';

class FileAttachmentPreview extends StatelessWidget {
  final List files;
  final Function(Map<String, dynamic>)? onFileTap;

  const FileAttachmentPreview({
    super.key,
    required this.files,
    this.onFileTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: files.map<Widget>((file) {
        return GestureDetector(
          onTap: () {
            if (onFileTap != null) {
              onFileTap!(file);
            }
          },
          child: Container(
            margin: EdgeInsets.only(bottom: 4.h),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              border: Border.all(color: getColorSkin().grey4Background),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Row(
              children: [
                Icon(Icons.insert_drive_file, color: getColorSkin().ink3, size: 20.r),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    file['fileName'] ?? 'Tệp đính kèm',
                    style: getTypoSkin().bodyRegular14,
                    maxLines: 1,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}

class FileOptionsDialog extends StatelessWidget {
  final Map<String, dynamic> file;
  final VoidCallback onView;
  final VoidCallback onDownload;

  const FileOptionsDialog({
    super.key,
    required this.file,
    required this.onView,
    required this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: getColorSkin().white,
      title: Text('Tùy chọn tệp', style: getTypoSkin().title5Medium),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(file['fileName'] ?? 'Tệp đính kèm', style: getTypoSkin().bodyRegular14),
          SizedBox(height: 20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildOptionButton(
                context: context,
                svgPath: StringImage.ic_eye,
                label: 'Xem trước',
                onTap: onView,
              ),
              _buildOptionButton(
                context: context,
                svgPath: StringImage.ic_download,
                label: 'Tải xuống',
                onTap: onDownload,
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          child: Text('Đóng', style: TextStyle(color: getColorSkin().primaryBlue)),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }

  Widget _buildOptionButton({
    required BuildContext context,
    required String svgPath,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: getColorSkin().grey4Background,
              shape: BoxShape.circle,
            ),
            child: SvgPicture.asset(
              svgPath,
              width: 24.w,
              height: 24.h,
            ),
          ),
          SizedBox(height: 8.h),
          Text(label, style: getTypoSkin().bodyRegular14),
        ],
      ),
    );
  }
}
