import 'dart:convert';

import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/models/authorize_management/authorize_management_request_model.dart';
import 'package:eapprove/models/authorize_management/generic_data_model.dart';
import 'package:eapprove/models/generic_respone_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:flutter/material.dart';

class AuthorizeManagementRepository {
  final ApiService _apiService;

  AuthorizeManagementRepository({required ApiService apiService})
      : _apiService = apiService;

  Future<GenericResponseModel<GenericDataAuthorize<AuthorizeItemData>>>
      getFilterAuthorizeManagement(
          {required AuthorizeManagementRequestModel requestBody}) async {
    try {
      final response = await _apiService.post(
          'business-process/assign/searchAssign', requestBody.toJson());

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        final result = GenericResponseModel<
            GenericDataAuthorize<AuthorizeItemData>>.fromJson(jsonData, (json) {
          if (json == null || json.isEmpty) {
            return GenericDataAuthorize<AuthorizeItemData>.fromJson({}, (jsn) {
              return AuthorizeItemData.fromJson(jsn);
            });
          }
          return GenericDataAuthorize<AuthorizeItemData>.fromJson(json,
              (jsonChild) {
            return AuthorizeItemData.fromJson(jsonChild);
          });
        });
        return result;
      } else {
        debugPrint(
            'getMyPyc - Error status code: ${response.statusCode}, Body: ${response.body}');
        throw Exception(
            "Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getMyPyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching MyPyc: $e");
    }
  }
}
