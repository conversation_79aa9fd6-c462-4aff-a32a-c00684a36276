import 'package:flutter/material.dart';

enum ActionCode {
//'CREATE_TICKET': oldProcInstId ?  '<PERSON><PERSON> trình lại' :  '<PERSON><PERSON> trình'
//  'CREATE_TASK': 'Tiếp nhận phiếu'
//  'COMPLETE_TASK': taskType == 'EXECUTION'    ?  'Hoàn thành'    :  'Phê duyệt'
//  'REQUEST_UPDATE': 'Trả về'
//  'GET_REQUEST_UPDATE': 'Bị trả về'
//  'AFFECTED_BY_RU': 'Bị trả về liên đới'
//  'RATING': 'Đánh giá'
//  'COMPLETE_TICKET': 'Hoàn thành phiếu'
//  'CANCEL_TICKET': 'Hủy phiếu'
//  'CHANGE_IMPLEMENTER': 'Ủy quyền'
//  'ADDITIONAL_REQUEST': 'Cần bổ sung'
//  'ADDITIONAL': 'Yêu cầu bổ sung'
//  'RE_CREATED_BY_RU': 'Đệ trình lại'
//  'UPDATE_TICKET': 'Cập nhật'
//  'CREATE_DRAFT_TICKET': 'Tạo phiếu nháp'
//  'RECALLING': 'Yêu cầu thu hồi'
//  'RECALLED': 'Đồng ý thu hồi'
//  'HANDOVER_MY_TICKET':
//  'HANDOVER_MY_TASK': 'Bàn giao công việc'

  createTicket('CREATE_TICKET', 'Đệ trình'),
  createTicketOldProcInstId('CREATE_TICKET', 'Đệ trình lại'),

  createTask('CREATE_TASK', 'Tiếp nhận phiếu'),
  completeTask('COMPLETE_TASK', 'Phê duyệt'),
  requestUpdate('REQUEST_UPDATE', 'Trả về'),
  getRequestUpdate('GET_REQUEST_UPDATE', 'Bị trả về'),
  affectedByRu('AFFECTED_BY_RU', 'Bị trả về liên đới'),
  rating('RATING', 'Đánh giá'),
  completeTicket('COMPLETE_TICKET', 'Hoàn thành phiếu'),
  cancelTicket('CANCEL_TICKET', 'Hủy phiếu'),
  changeImplementer('CHANGE_IMPLEMENTER', 'Ủy quyền'),
  additionalRequest('ADDITIONAL_REQUEST', 'Cần bổ sung'),
  additional('ADDITIONAL', 'Yêu cầu bổ sung'),
  reCreatedByRu('RE_CREATED_BY_RU', 'Đệ trình lại'),
  updateTicket('UPDATE_TICKET', 'Cập nhật'),
  createDraftTicket('CREATE_DRAFT_TICKET', 'Tạo phiếu nháp'),
  recalling('RECALLING', 'Yêu cầu thu hồi'),
  recalled('RECALLED', 'Đồng ý thu hồi'),
  handoverMyTicket('HANDOVER_MY_TICKET', 'Bàn giao công việc'),
  handoverMyTask('HANDOVER_MY_TASK', 'Bàn giao công việc'),
  defaulted('DEFAULT', '');

  final String code;
  final String name;

  const ActionCode(this.code, this.name);

  static ActionCode fromCode(String code,
      {String? oldProcInstId, String? taskType}) {
    if (code == 'CREATE_TICKET') {
      return oldProcInstId == 'true'
          ? ActionCode.createTicketOldProcInstId
          : ActionCode.createTicket;
    }
    if (code == 'CREATE_TASK') {
      return taskType == 'EXECUTION'
          ? ActionCode.completeTask
          : ActionCode.createTask;
    }
    return ActionCode.values.firstWhere(
      (action) => action.code == code,
      orElse: () => ActionCode.defaulted,
    );
  }

  static ActionCode fromName(String name,
      {String? oldProcInstId, String? taskType}) {
    return ActionCode.values.firstWhere(
      (action) => action.name == name,
      orElse: () => ActionCode.defaulted,
    );
  }
}
