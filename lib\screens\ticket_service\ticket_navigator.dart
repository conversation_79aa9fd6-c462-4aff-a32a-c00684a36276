import 'package:flutter/material.dart';
import 'package:eapprove/screens/setting_screen.dart';
import 'create_ticket_screen.dart';

class CreateTicketNavigator extends StatefulWidget {
  final String title;
  final VoidCallback? onBack;
  final GlobalKey<NavigatorState>? navigatorKey;

  const CreateTicketNavigator({
    Key? key,
    required this.title,
    this.onBack,
    this.navigatorKey,
  }) : super(key: key);

  @override
  State<CreateTicketNavigator> createState() => _CreateTicketNavigatorState();
}

class _CreateTicketNavigatorState extends State<CreateTicketNavigator> {
  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: widget.navigatorKey,
      initialRoute: 'create_ticket',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case 'create_ticket':
            return MaterialPageRoute(
              settings: const RouteSettings(name: 'create_ticket'),
              builder: (_) => CreateTicketScreen(
                title: widget.title,
              ),
            );
          case 'setting':
            return MaterialPageRoute(
              settings: const RouteSettings(name: 'setting'),
              builder: (_) => const EAppSettingScreen(),
            );
          default:
            return MaterialPageRoute(
              settings: const RouteSettings(name: 'create_ticket'),
              builder: (_) => CreateTicketScreen(
                title: widget.title,
              ),
            );
        }
      },
    );
  }
} 