import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/models/form/md_service_request_model.dart';
import 'package:hive/hive.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/common/form/common_form_widget.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_state.dart';
import 'dart:developer' as developer;

/// Constants for matrix styling
class MatrixConstants {
  static const double cellWidth = 250.0;
  static const double cellHeight = 40.0;
  static const double cellPadding = 8.0;
  static const double fontSize = 12.0;
  static const double borderRadius = 4.0;
  static const double bottomPadding = 16.0;
  static const double errorBottomPadding = 8.0;

  static const MaterialColor borderColor = Colors.grey;
  static const MaterialColor headerBackgroundColor = Colors.grey;
  static const Color errorColor = Colors.red;
}

/// A widget that displays data in a matrix format
class MatrixWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;
  final bool? isReadOnly;

  const MatrixWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
    this.isReadOnly,
  }) : super(key: key);

  @override
  State<MatrixWidget> createState() => _MatrixWidgetState();
}

class _MatrixWidgetState extends State<MatrixWidget> {
  late List<List<MatrixCell>> _matrixData;
  late int _maxRow;
  late int _maxCol;

  @override
  void initState() {
    super.initState();
    _initializeMatrixData();
  }

  /// Initializes the matrix data structure and fills it with column data
  void _initializeMatrixData() {
    final columns = widget.data.columns ?? [];

    // Find max row and column indices in a single pass
    _maxRow = 0;
    _maxCol = 0;
    for (var column in columns) {
      final rowMatrix = column.rowMatrix ?? 0;
      final colMatrix = column.colMatrix ?? 0;
      _maxRow = rowMatrix > _maxRow ? rowMatrix : _maxRow;
      _maxCol = colMatrix > _maxCol ? colMatrix : _maxCol;
    }

    // Initialize matrix with empty cells using List.filled for better performance
    _matrixData = List.generate(
      _maxRow + 1,
      (row) => List.filled(
        _maxCol + 1,
        MatrixCell.empty(row: row, col: 0),
      ),
    );

    // Fill matrix with column data
    for (var column in columns) {
      final rowMatrix = column.rowMatrix ?? 0;
      final colMatrix = column.colMatrix ?? 0;

      if (rowMatrix <= _maxRow && colMatrix <= _maxCol) {
        _matrixData[rowMatrix][colMatrix] = MatrixCell.fromColumn(
          row: rowMatrix,
          col: colMatrix,
          column: column,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.display != true) { 
      return const SizedBox.shrink();
    }

    return BlocListener<DropdownBloc, DropdownState>(
      listener: (context, state) {
        final key = widget.data.parentName?.isNotEmpty == true
            ? '${widget.data.parentName}.${widget.data.name}'
            : widget.data.name ?? "";

        final isLoading = state.isLoading[key] ?? false;

        developer.log('matrixxxisLoading: ${widget.data.name}',
            name: 'isLoading');
        if (!isLoading) {
          final response = state.dropdownResponses[key];

          developer.log('matrixxxresponse: ${response}', name: 'response');
          if (response != null && response.isSuccess) {
            // Handle successful dropdown response here
          }
        }
      },
      child: _buildMatrixTable(),
    );
  }

  Widget _buildErrorWidget() {
    return Padding(
      padding: EdgeInsets.only(bottom: MatrixConstants.errorBottomPadding.h),
      child: Text(
        widget.data.error ?? '',
        style: TextStyle(
          color: MatrixConstants.errorColor,
          fontSize: MatrixConstants.fontSize.sp,
        ),
      ),
    );
  }

  Widget _buildMatrixTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: MatrixConstants.borderColor.shade300),
        borderRadius: BorderRadius.circular(MatrixConstants.borderRadius.r),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Table(
          defaultColumnWidth: FixedColumnWidth(MatrixConstants.cellWidth.w),
          border: TableBorder(
            horizontalInside:
                BorderSide(color: MatrixConstants.borderColor.shade300),
            verticalInside:
                BorderSide(color: MatrixConstants.borderColor.shade300),
            bottom: BorderSide(color: MatrixConstants.borderColor.shade300),
          ),
          children: [
            // Header row
            TableRow(
              decoration: BoxDecoration(
                color: MatrixConstants.headerBackgroundColor.shade100,
              ),
              children: _matrixData[0]
                  .map(
                    (cell) => Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: MatrixConstants.cellPadding.w,
                        vertical: MatrixConstants.cellPadding.h,
                      ),
                      child: Center(
                        child: Text(
                          cell.label,
                          style: TextStyle(
                            fontSize: MatrixConstants.fontSize.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
            // Data rows
            ...List.generate(
              _maxRow,
              (row) => TableRow(
                children: _matrixData[row + 1]
                    .map(
                      (cell) => cell.column != null
                          ? Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: MatrixConstants.cellPadding.w,
                                vertical: MatrixConstants.cellPadding.h,
                              ),
                              child: FormItemWidget(
                                data: cell.column!,
                                stateManager: widget.stateManager,
                                isReadOnly: widget.isReadOnly,
                                isShowLabel: false,
                              ),
                            )
                          : Container(
                              height: MatrixConstants.cellHeight.h,
                            ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Represents a cell in the matrix
class MatrixCell {
  final int row;
  final int col;
  final String label;
  final String value;
  final String type;
  final bool readonly;
  final FormItemInfo? column;

  const MatrixCell({
    required this.row,
    required this.col,
    required this.label,
    required this.value,
    required this.type,
    required this.readonly,
    required this.column,
  });

  /// Creates an empty cell
  const factory MatrixCell.empty({required int row, required int col}) =
      _EmptyMatrixCell;

  /// Creates a cell from a FormItemInfo column
  factory MatrixCell.fromColumn({
    required int row,
    required int col,
    required FormItemInfo column,
  }) {
    return MatrixCell(
      row: row,
      col: col,
      label: column.label ?? '',
      value: column.value?.toString() ?? '',
      type: column.type?.toString() ?? '',
      readonly: column.readonly ?? false,
      column: column,
    );
  }
}

/// Implementation of an empty matrix cell
class _EmptyMatrixCell extends MatrixCell {
  const _EmptyMatrixCell({required int row, required int col})
      : super(
          row: row,
          col: col,
          label: '',
          value: '',
          type: '',
          readonly: false,
          column: null,
        );
}
