import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';


class FormulaWidget extends StatefulWidget {
  final FormItemInfo data;
  final FormStateManager stateManager;
  final int? rowIndex;
  final bool? isShowLabel;

  const FormulaWidget(
      {Key? key,
      required this.data,
      required this.stateManager,
      this.rowIndex,
      this.isShowLabel = true})
      : super(key: key);

  @override
  State<FormulaWidget> createState() => _FormulaWidgetState();
}

class _FormulaWidgetState extends State<FormulaWidget> {
  String? _formulaResult;
  final controller = TextEditingController();
  List<String> _referencedFields = [];
  bool _isUpdating = false; // Thêm dòng này

  @override
  void initState() {
    super.initState();
    _extractReferencedFields();
    widget.stateManager.addListener(_onStateChanged);
    _calculateFormula();
  }

  @override
  void dispose() {
    controller.dispose();
    widget.stateManager.removeListener(_onStateChanged);
    super.dispose();
  }

  void _extractReferencedFields() {
    _referencedFields = [];
    if (widget.data.condition != null) {
      String? currentScope;
      for (final token in widget.data.condition!) {
        final text = token['text'] as String? ?? '';
        if (text.startsWith("tbl_") || text.startsWith("mtx_")) {
          if (text.endsWith(":")) {
            currentScope = text.substring(0, text.length - 1);
            continue;
          }
        }
        final fullFieldMatch =
            RegExp(r'^([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)$').firstMatch(text);
        if (fullFieldMatch != null) {
          _referencedFields.add(text);
          continue;
        }
        if (token['type'] == 'condition' && !token['expression']) {
          final field = text;
          final fieldRef =
              currentScope != null ? '$currentScope.$field' : field;
          _referencedFields.add(fieldRef);
        }
      }
      // debugPrint('Extracted fields: $_referencedFields');
    }
  }

  void _onStateChanged() {
    // Chỉ tính toán lại khi các field được tham chiếu thay đổi
    bool shouldRecalculate = false;
    for (var field in _referencedFields) {
      final oldValue = widget.stateManager
          .getFieldValueByName(field, widget.data.parentName);
      final newValue = widget.stateManager
          .getFieldValueByName(field, widget.data.parentName);
      if (oldValue != newValue) {
        shouldRecalculate = true;
        break;
      }
    }

    if (shouldRecalculate) {
      _calculateFormula();
    }
  }

  @override
  void didUpdateWidget(FormulaWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      // debugPrint(
      //     'Widget data changed. Old: ${oldWidget.data.name}, New: ${widget.data.name}');
      _extractReferencedFields();
      _calculateFormula();
    }
  }

  void _calculateFormula() {
    if (widget.data.strCondition == null) return;
    if (_isUpdating) return;

    try {
      _isUpdating = true;
      widget.stateManager.removeListener(_onStateChanged);

      double result = 0;
      if (widget.data.condition != null && widget.data.condition!.isNotEmpty) {
        // debugPrint(
        //     'Calculating from condition array: ${widget.data.condition}');
        result = _calculateFromConditionArray();
      } else {
        final formula = widget.data.strCondition ?? '';
        // debugPrint('Calculating from string formula: $formula');
        if (formula.contains('sum(')) {
          result = _calculateSum(formula);
        } else {
          result = _calculateBasicOperation(formula);
        }
      }

      final formattedResult = result.truncateToDouble() == result
          ? result.toInt().toString()
          : result.toString();
      if (_formulaResult != formattedResult) {
        // debugPrint('Formula result: $formattedResult');
        setState(() {
          _formulaResult = formattedResult;
          controller.text =
              Utils.formatCurrency(double.parse(_formulaResult ?? '0')) ?? '';
        });

        widget.stateManager.setFieldValue(
          widget.data.name ?? '',
          formattedResult,
          rowIndex: widget.rowIndex,
          parentName: widget.data.parentName,
        );
      }
    } catch (e) {
      // debugPrint('Error calculating formula: $e');
      if (_formulaResult != '0') {
        setState(() {
          _formulaResult = '0';
          controller.text = _formulaResult ?? '';
        });
      }
    } finally {
      _isUpdating = false;
      widget.stateManager.addListener(_onStateChanged);
    }
  }

  double _calculateSpecialFunction(
      String functionName, List<Map<String, dynamic>> conditions) {
    try {
      // debugPrint(
      //     'Calculating special function: $functionName with conditions: $conditions');

      // Extract parameters from conditions
      final parameters = <String>[];
      bool isCollectingParams = false;

      for (var condition in conditions) {
        if (condition['text'] == '(') {
          isCollectingParams = true;
          continue;
        }
        if (condition['text'] == ')') {
          isCollectingParams = false;
          continue;
        }
        if (isCollectingParams && condition['type'] == 'condition') {
          parameters.add(condition['text']);
        }
      }

      // debugPrint('Function parameters: $parameters');

      // Calculate values for each parameter
      final values = <double>[];
      for (var param in parameters) {
        if (param.contains('.')) {
          // Table field
          values.add(_getTableFieldValue(param));
        } else {
          // Regular field
          final value = widget.stateManager
                  .getFieldValueByName(param, widget.data.parentName) ??
              '0';
          values.add(double.tryParse(value) ?? 0);
        }
      }

      // debugPrint('Calculated values: $values');

      // Apply the function
      switch (functionName.toLowerCase()) {
        case 'sum':
          return values.fold(0.0, (sum, value) => sum + value);
        case 'avg':
        case 'average':
          return values.isEmpty
              ? 0
              : values.reduce((a, b) => a + b) / values.length;
        case 'min':
          return values.isEmpty ? 0 : values.reduce((a, b) => a < b ? a : b);
        case 'max':
          return values.isEmpty ? 0 : values.reduce((a, b) => a > b ? a : b);
        case 'count':
          return values.length.toDouble();
        default:
          // debugPrint('Unknown function: $functionName');
          return 0;
      }
    } catch (e) {
      // debugPrint('Error calculating special function: $e');
      return 0;
    }
  }

  double _calculateFromConditionArray() {
    if (widget.data.condition == null || widget.data.condition!.isEmpty)
      return 0;

    final conditions = widget.data.condition!;
    // debugPrint('Condition array: $conditions');

    // Check if this is a special function
    if (conditions.isNotEmpty &&
        conditions[0]['type'] == 'condition' &&
        conditions[0]['expression'] == true) {
      return _calculateSpecialFunction(conditions[0]['text'], conditions);
    }

    // Check if this is a table formula
    if (_isTableFormula(conditions)) {
      return _calculateTableFormula(conditions);
    }

    // Check if this is a matrix formula
    if (_isMatrixFormula(conditions)) {
      return _calculateMatrixFormula(conditions);
    }

    // Handle basic formula
    return _calculateBasicFormula(conditions);
  }

  bool _isTableFormula(List<Map<String, dynamic>> conditions) {
    return conditions.isNotEmpty &&
        conditions[0]['type'] == 'condition' &&
        conditions[0]['text'].toString().startsWith("tbl_") &&
        conditions[0]['text'].toString().endsWith(":");
  }

  bool _isMatrixFormula(List<Map<String, dynamic>> conditions) {
    return conditions.isNotEmpty &&
        conditions[0]['type'] == 'condition' &&
        conditions[0]['text'].toString().startsWith("mtx_");
  }

  double _calculateTableFormula(List<Map<String, dynamic>> conditions) {
    final table = conditions[0]['text'].toString();
    final tableName = table.substring(0, table.length - 1);
    final tableData =
        widget.stateManager.getFieldValue(tableName, rowIndex: widget.rowIndex);

    if (tableData == null || widget.rowIndex == null) {
      // debugPrint('Invalid table data or row index');
      return 0;
    }

    try {
      final rowData = tableData[widget.rowIndex];
      if (rowData == null) {
        // debugPrint('Row data not found for index ${widget.rowIndex}');
        return 0;
      }

      String expr = "";
      for (var ele in conditions) {
        if (ele['type'] == 'condition') {
          if (ele['text'].toString().startsWith("tbl_") &&
              ele['text'].toString().endsWith(":")) {
            continue;
          }
          final fieldName = ele['text'].toString();
          final value = rowData[fieldName] ?? 0;
          expr += value.toString();
        } else {
          expr += ele['text'].toString();
        }
      }

      // debugPrint('Table formula expression: ${widget.data.name} $expr');
      return _evaluateExpression(expr);
    } catch (e) {
      // debugPrint('Error calculating table formula: $e');
      return 0;
    }
  }

  double _calculateMatrixFormula(List<Map<String, dynamic>> conditions) {
    final matrixName = conditions[0]['text'].toString();
    final matrixData = widget.stateManager
        .getFieldValue(matrixName, rowIndex: widget.rowIndex);

    if (matrixData is! Map) {
      // debugPrint('Invalid matrix data');
      return 0;
    }

    final values = <double>[];
    matrixData.forEach((key, value) {
      if (value is Map) {
        for (var i = 1; i < conditions.length; i++) {
          final fieldName = conditions[i]['text'].toString();
          if (value.containsKey(fieldName)) {
            values
                .add(double.tryParse(value[fieldName]?.toString() ?? '0') ?? 0);
          }
        }
      }
    });

    return _calculateFunctionResult(conditions[0]['text'].toString(), values);
  }

  double _calculateFunctionResult(String functionName, List<double> values) {
    if (values.isEmpty) return 0;

    switch (functionName) {
      case 'sum':
        return values.reduce((a, b) => a + b);
      case 'count':
        return values.length.toDouble();
      case 'avg':
      case 'average':
        return values.reduce((a, b) => a + b) / values.length;
      case 'min':
        return values.reduce((a, b) => a < b ? a : b);
      case 'max':
        return values.reduce((a, b) => a > b ? a : b);
      case 'quotient':
        return values.length >= 2
            ? (values[0] / values[1]).truncateToDouble()
            : 0;
      case 'mod':
        return values.length >= 2 ? values[0] % values[1] : 0;
      case 'percentage':
        return values.length >= 2 ? (values[0] / values[1]) * 100 : 0;
      default:
        return 0;
    }
  }

  double _calculateBasicFormula(List<Map<String, dynamic>> conditions) {
    double result = 0;
    String? currentOperator;
    // debugPrint("_calculateBasicFormula ${widget.data.name}");
    for (var condition in conditions) {
      if (condition['type'] == 'condition') {
        final fieldName = condition['text'] as String;
        double value = 0;

        if (fieldName.contains('.')) {
          value = _getTableFieldValue(fieldName);
          // debugPrint("fieldNamefieldName:: ${fieldName} $value");
        } else {
          value = double.tryParse(widget.stateManager
                      .getFieldValueByName(fieldName, widget.data.parentName) ??
                  '0') ??
              0;
        }

        if (currentOperator == null) {
          result = value;
        } else {
          result = _applyOperator(result, value, currentOperator);
        }
      } else if (condition['type'] == '') {
        currentOperator = condition['text'] as String;
      }
    }
    return result;
  }

  double _getTableFieldValue(String fieldName) {
    try {
      final parts = fieldName.split('.');
      if (parts.length != 2) {
        // debugPrint('Invalid field name format: $fieldName');
        return 0;
      }

      final tableName = parts[0];
      final columnName = parts[1];

      // debugPrint(
      //     'Getting table value - Table: $tableName, Column: $columnName, Row: ${widget.rowIndex}');

      final tableData = widget.stateManager
          .getFieldValue(tableName, rowIndex: widget.rowIndex);
      if (tableData == null) {
        // debugPrint('Table data is null for table: $tableName');
        return 0;
      }

      if (tableData is! List) {
        // debugPrint('Table data is not a List: $tableData');
        return 0;
      }

      // If rowIndex is null, we need to sum all values in the column
      if (widget.rowIndex == null) {
        double sum = 0;
        for (var rowData in tableData) {
          if (rowData is Map && rowData.containsKey(columnName)) {
            final value = rowData[columnName]?.toString() ?? '0';
            sum += double.tryParse(value) ?? 0;
          }
        }
        // debugPrint('Sum of column $columnName: $sum');
        return sum;
      }

      // If rowIndex is provided, get value from specific row
      final rowIndex = widget.rowIndex!;
      if (rowIndex >= tableData.length) {
        // debugPrint(
        //     'Row index $rowIndex is out of bounds for table with ${tableData.length} rows');
        return 0;
      }

      final rowData = tableData[rowIndex];
      if (rowData == null) {
        // debugPrint('Row data not found for index: $rowIndex');
        return 0;
      }

      if (rowData is! Map) {
        // debugPrint('Row data is not a Map: $rowData');
        return 0;
      }

      final value = rowData[columnName];
      // debugPrint('Found value: $value for column: $columnName');

      return double.tryParse(value?.toString() ?? '0') ?? 0;
    } catch (e) {
      // debugPrint('Error getting table field value: $e');
      return 0;
    }
  }

  double _applyOperator(double left, double right, String operator) {
    switch (operator) {
      case '+':
        return left + right;
      case '-':
        return left - right;
      case '*':
        return left * right;
      case '/':
        return right != 0 ? left / right : 0;
      default:
        return left;
    }
  }

  double _evaluateExpression(String expr) {
    try {
      // debugPrint('Evaluating expression: $expr');

      // Handle parentheses first
      while (expr.contains('(')) {
        final openIndex = expr.lastIndexOf('(');
        final closeIndex = expr.indexOf(')', openIndex);
        if (closeIndex == -1) break;

        final subExpr = expr.substring(openIndex + 1, closeIndex);
        final subResult = _evaluateExpression(subExpr);
        expr =
            expr.replaceRange(openIndex, closeIndex + 1, subResult.toString());
      }

      // Split by operators while preserving them
      final parts = <String>[];
      final operators = <String>[];
      String currentNumber = '';

      for (int i = 0; i < expr.length; i++) {
        final char = expr[i];
        if ('+-*/'.contains(char)) {
          if (currentNumber.isNotEmpty) {
            parts.add(currentNumber);
            currentNumber = '';
          }
          operators.add(char);
        } else {
          currentNumber += char;
        }
      }
      if (currentNumber.isNotEmpty) {
        parts.add(currentNumber);
      }

      // debugPrint('Parts: $parts');
      // debugPrint('Operators: $operators');

      if (parts.isEmpty) return 0;

      // First pass: multiplication and division
      for (int i = 0; i < operators.length; i++) {
        if (operators[i] == '*' || operators[i] == '/') {
          final left = double.tryParse(parts[i]) ?? 0;
          final right = double.tryParse(parts[i + 1]) ?? 0;
          final result = _applyOperator(left, right, operators[i]);
          parts[i] = result.toString();
          parts.removeAt(i + 1);
          operators.removeAt(i);
          i--;
        }
      }

      // Second pass: addition and subtraction
      double result = double.tryParse(parts[0]) ?? 0;
      for (int i = 0; i < operators.length; i++) {
        final nextValue = double.tryParse(parts[i + 1]) ?? 0;
        result = _applyOperator(result, nextValue, operators[i]);
      }

      // debugPrint('Expression result: $result');
      return result;
    } catch (e) {
      // debugPrint('Error evaluating expression: $e');
      return 0;
    }
  }

  /// Calculates the sum of values in a table
  double _calculateSum(String formula) {
    double result = 0;
    final startIndex = formula.indexOf('(') + 1;
    final endIndex = formula.lastIndexOf(')');

    if (startIndex > 0 && endIndex > startIndex) {
      final fieldName =
          formula.substring(startIndex, endIndex).replaceAll('@', '').trim();

      final tableData = widget.stateManager
          .getFieldValue(fieldName.split('.')[0], rowIndex: widget.rowIndex);
      if (tableData is List) {
        for (var row in tableData) {
          if (row is Map) {
            final value = row[fieldName.split('.')[1]]?.toString() ?? '0';
            result += double.tryParse(value) ?? 0;
          }
        }
      }
    }
    return result;
  }

  /// Calculates basic arithmetic operations (+, -, *, /)
  double _calculateBasicOperation(String formula) {
    final values = <String, double>{};
    final parts = formula.split('@');

    if (parts.length < 2) return 0;

    // Extract field values
    for (var i = 1; i < parts.length; i++) {
      final fieldName = parts[i].trim();
      final value = widget.stateManager
              .getFieldValueByName(fieldName, widget.data.parentName) ??
          '0';
      // debugPrint(
      //     '_calculateBasicOperation fieldName: $fieldName, value: $value');
      values[fieldName] = double.tryParse(value) ?? 0;
    }

    // Handle subtraction
    if (formula.contains('-')) {
      final operands = formula.split('-');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        return (values[firstField] ?? 0) - (values[secondField] ?? 0);
      }
    }
    // Handle addition
    else if (formula.contains('+')) {
      final operands = formula.split('+');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        return (values[firstField] ?? 0) + (values[secondField] ?? 0);
      }
    }
    // Handle multiplication
    else if (formula.contains('*')) {
      final operands = formula.split('*');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        return (values[firstField] ?? 0) * (values[secondField] ?? 0);
      }
    }
    // Handle division
    else if (formula.contains('/')) {
      final operands = formula.split('/');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        final divisor = values[secondField] ?? 0;
        return divisor != 0 ? (values[firstField] ?? 0) / divisor : 0;
      }
    }

    return 0;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.isShowLabel == true)
          FormLabel(
            displayName: widget.data.displayName,
            label: widget.data.label,
            suggestText: widget.data.suggestText,
            isRequired: widget.data.validations?['required'] == true,
          ),
        Container(
          margin: EdgeInsets.only(top: 8.h),
          child: CustomTextForm(
          edtController: controller,
          hintText: _formulaResult ?? "",
          onChanged: (value) {},
          initialValue: _formulaResult,
          filled: true,
          fillColor: getColorSkin().grey4Background,
          textInputAction: TextInputAction.done,
          onTapOutside: (_) {},
          autoFocus: false,
          showDeleteButton: false,
          enabled: false,
          keyboardType: widget.data.type == FormItemType.number ||
                  widget.data.type == FormItemType.currency
              ? TextInputType.number
              : TextInputType.text,
        ),
        )
      ],
    );
  }
}
