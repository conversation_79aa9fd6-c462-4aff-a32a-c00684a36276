class ProcAppCountRequestModel {
  final String? type;
  final bool? filterChangeAssignee;
  final String? search;

  ProcAppCountRequestModel({
    this.type,
    this.filterChangeAssignee,
    this.search,
  });

  factory ProcAppCountRequestModel.fromJson(Map<String, dynamic> json) {
    return ProcAppCountRequestModel(
      type: json['type'],
      filterChangeAssignee: json['filterChangeAssignee'],
      search: json['search'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'filterChangeAssignee': false,
      'search': search,
    };
  }
} 

class ProcAppCountDataModel {
  final int? shared;
  final int? completed;
  final int? cancel;
  final int? ongoing;
  final int? recalled;

  ProcAppCountDataModel({
    this.shared,
    this.completed,
    this.cancel,
    this.ongoing,
    this.recalled,
  });

  factory ProcAppCountDataModel.fromJson(Map<String, dynamic> json) {
    return ProcAppCountDataModel(
      shared: json['SHARED'],
      completed: json['COMPLETED'],
      cancel: json['CANCEL'],
      ongoing: json['ONGOING'],
      recalled: json['RECALLED'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'SHARED': shared,
      'COMPLETED': completed,
      'CANCEL': cancel,
      'ONGOING': ongoing,
      'RECALLED': recalled,
    };
  }
}
