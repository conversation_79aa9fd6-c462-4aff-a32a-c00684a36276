import 'package:eapprove/blocs/ticket_other_action/event/recover_request_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/recover_request_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/cancel_ticket_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/cancel_ticket_state.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';

class RecoverRequestBloc extends Bloc<RecoverRequestEvent, RecoverRequestState> {
  final TicketOtherActionRepository repository;

  RecoverRequestBloc({required this.repository}) : super(RecoverRequestState.initial()) {
    on<UploadRecoverRequestEventFile>(_onUploadFileRecoverRequest);
    on<SubmitRecoverRequestEvent>(_onSubmitRecoverRequest);
  }

  Future<void> _onUploadFileRecoverRequest(
      UploadRecoverRequestEventFile event, Emitter<RecoverRequestState> emit) async {
    emit(state.copyWith(status: RecoverRequestStatus.loading));
    try {
      final filePaths = await repository.uploadlFile(event.files);
      emit(state.copyWith(status: RecoverRequestStatus.success, filePaths: filePaths));
    } catch (e) {
      emit(state.copyWith(status: RecoverRequestStatus.failure, error: e.toString()));
    }
  }

  Future<void> _onSubmitRecoverRequest(SubmitRecoverRequestEvent event, Emitter<RecoverRequestState> emit) async {
    emit(state.copyWith(status: RecoverRequestStatus.loading));
    try {
      await repository.submitRecoverRequest(
        taskDefKey: event.taskDefKey,
        ticketId: event.ticketId,
        reason: event.reason,
        filePaths: event.filePaths,
      );
      emit(state.copyWith(status: RecoverRequestStatus.success));
    } catch (e) {
      emit(state.copyWith(status: RecoverRequestStatus.failure, error: e.toString()));
    }
  }
}
