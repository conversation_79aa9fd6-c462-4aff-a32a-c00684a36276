import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';

abstract class RecoverRequestEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class UploadRecoverRequestEventFile extends RecoverRequestEvent {
  final List<PlatformFile> files;

  UploadRecoverRequestEventFile(this.files);

  @override
  List<Object?> get props => [files];
}

class SubmitRecoverRequestEvent extends RecoverRequestEvent {
  final String ticketProcId;
  final String taskDefKey;
  final int ticketId;
  final String reason;
  final List<String> filePaths; // Đ<PERSON>i từ String? thành List<String>

  SubmitRecoverRequestEvent({
    required this.ticketProcId,
    required this.taskDefKey,
    required this.ticketId,
    required this.reason,
    required this.filePaths,
  });

  @override
  List<Object?> get props => [ticketProcId, taskDefKey, ticketId, reason, filePaths];

}
