import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/sign_print_repository.dart';
import 'sign_print_event.dart';
import 'sign_print_state.dart';

class SignPrintBloc extends Bloc<SignPrintEvent, SignPrintState> {
  final SignPrintRepository _repository;

  SignPrintBloc({required SignPrintRepository repository})
      : _repository = repository,
        super(SignPrintInitial()) {
    on<LoadSignPrintDataEvent>(_onLoadSignPrintData);
  }

  Future<void> _onLoadSignPrintData(
    LoadSignPrintDataEvent event,
    Emitter<SignPrintState> emit,
  ) async {
    try {
      emit(SignPrintLoading());
      
      final response = await _repository.getSignPrintData(
        event.bpmTpSignZone,
        event.procInstId,
      );

      emit(SignPrintLoaded(response));
    } catch (e) {
      emit(SignPrintError(e.toString()));
    }
  }
} 