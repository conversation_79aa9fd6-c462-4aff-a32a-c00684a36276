import 'package:eapprove/screens/ticket_other_action/widget/mention_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';

class MentionSuggestionList extends StatelessWidget {
  final PycState pycState;
  final String currentQuery;
  final Function(MentionUserModel) onSelectUser;

  const MentionSuggestionList({
    super.key,
    required this.pycState,
    required this.currentQuery,
    required this.onSelectUser,
  });

  @override
  Widget build(BuildContext context) {
    if (pycState.orgChartListStatus == PycStatus.loaded && pycState.orgChartList.isNotEmpty) {
      final users = _getMentionUsers(pycState);

      if (users.isEmpty) {
        return SizedBox(
          height: 100.h,
          child: Center(
            child: Text('Không tìm thấy người dùng', style: getTypoSkin().bodyRegular14),
          ),
        );
      }

      final filteredUsers = currentQuery.isEmpty
          ? users
          : users
              .where((user) =>
                  user.username.toLowerCase().contains(currentQuery.toLowerCase()) ||
                  user.fullname.toLowerCase().contains(currentQuery.toLowerCase()))
              .toList();

      if (filteredUsers.isEmpty) {
        return SizedBox(
          height: 100.h,
          child: Center(
            child: Text('Không tìm thấy người dùng phù hợp', style: getTypoSkin().bodyRegular14),
          ),
        );
      }

      return Material(
        child: Container(
          constraints: BoxConstraints(maxHeight: 200.h),
          decoration: BoxDecoration(
            color: getColorSkin().white,
            border: Border(top: BorderSide(color: getColorSkin().grey4Background)),
            boxShadow: [
              BoxShadow(
                color: getColorSkin().ink5.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.h),
            itemCount: filteredUsers.length,
            separatorBuilder: (context, index) => Divider(height: 1, color: getColorSkin().grey4Background),
            itemBuilder: (context, index) {
              final user = filteredUsers[index];
              return InkWell(
                onTap: () => onSelectUser(user),
                child: ListTile(
                  dense: true,
                  title: Text(
                    user.getDisplayText(),
                    style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink1),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            },
          ),
        ),
      );
    } else if (pycState.orgChartListStatus == PycStatus.error) {
      return SizedBox(
        height: 100.h,
        child: Center(
          child: Text('Lỗi: ${pycState.orgChartErrorMessage ?? "Không thể tải danh sách người dùng"}',
              style: getTypoSkin().bodyRegular14),
        ),
      );
    }

    return SizedBox(
      height: 100.h,
      child: Center(
        child: CircularProgressIndicator(color: getColorSkin().primaryBlue),
      ),
    );
  }

  List<MentionUserModel> _getMentionUsers(PycState state) {
    List<MentionUserModel> users = [];

    for (final orgData in state.orgChartList) {
      if (orgData.username != null && orgData.userId != null) {
        users.add(MentionUserModel(
          userId: orgData.userId,
          username: orgData.username ?? '',
          fullname: orgData.fullname ?? '',
          title: orgData.position ?? '',
          chartId: orgData.chartId?.toString(),
          shortNameChart: orgData.shortNameChart,
          chartNodeId: orgData.chartNodeId?.toString(),
          name: orgData.name,
          shortNameChartNode: orgData.shortNameChartNode,
        ));
      }
    }

    return users;
  }
}
