import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/models/form/md_service_response.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';

class InputTextWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;

  const InputTextWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
  }) : super(key: key);

  @override
  State<InputTextWidget> createState() => _InputTextWidgetState();
}

class _InputTextWidgetState extends State<InputTextWidget> {
  String? _displayValue;
  bool _isRequired = false;
  bool _isReadOnly = false;
  bool _isVisible = true;
  String? _errorMessage;
  String? _customLabel;
  String? _tooltip;

  @override
  void initState() {
    super.initState();
    // _evaluateEventExpression();
  }

  void _evaluateEventExpression() {
    // Xử lý các event expression từ data
    setState(() {
      _isRequired = widget.data.validations?['required'] == true;
      _isReadOnly = widget.data.readonly == true;
      _isVisible = widget.data.display != false;
      _tooltip = widget.data.tooltip;
      _customLabel = widget.data.label;
      _errorMessage = widget.data.error;
    });
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('InputTextWidget - build');
    debugPrint('Current display value: $_displayValue');

    // Check if widget should be displayed
    if (!_isVisible) {
      debugPrint('Widget is not visible - not showing');
      return const SizedBox.shrink();
    }

    final TextInputType keyboardType =
        widget.data.inputType == 'number' || widget.data.inputType == 'currency'
            ? TextInputType.number
            : TextInputType.text;
    debugPrint('Keyboard type: $keyboardType');

    return BlocListener<FormBloc, FormInfoState>(
      listenWhen: (previous, current) {
        return previous.mdServiceResponse != current.mdServiceResponse;
      },
      listener: (context, state) {
        debugPrint('FormBloc state changed - mdServiceResponse updated');
        if (state.mdServiceResponse != null &&
            state.mdServiceResponse!.isSuccess) {
          debugPrint('Master data loaded successfully');
          debugPrint('Data length: ${state.mdServiceResponse!.data.length}');

          if (state.mdServiceResponse!.data.isNotEmpty) {
            final mdService = state.mdServiceResponse!.data.firstWhere(
              (item) => item.isValid,
              orElse: () => MdService(),
            );

            String? newDisplayValue;
            if (widget.data.name == 'txt_soTaiKhoanNganHang') {
              newDisplayValue = mdService.soTaiKhoanNH;
            } else if (widget.data.name == 'txt_tenNguoiThuHuong') {
              newDisplayValue = mdService.tenNguoiThuHuong;
            } else if (widget.data.name == 'txt_nganHangChiNhanhMtk') {
              newDisplayValue = mdService.nganHangChiNhanh;
            }

            if (newDisplayValue != _displayValue) {
              setState(() {
                _displayValue = newDisplayValue;
              });
              debugPrint('Updated display value to: $newDisplayValue');
            }
          }
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: 12.h),
        child: _buildCustomTextForm(keyboardType),
      ),
    );
  }

  Widget _buildCustomTextForm(TextInputType keyboardType) {
    debugPrint('Building CustomTextForm');
    debugPrint('Current value: ${widget.data.value}');
    debugPrint('Display value: $_displayValue');
    debugPrint('Placeholder: ${widget.data.placeholder}');
    debugPrint('Error state: $_errorMessage');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_customLabel != null) ...[
          Row(
            children: [
              Text(
                _customLabel!,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (_isRequired)
                const Text(
                  ' *',
                  style: TextStyle(color: Colors.red),
                ),
              if (_tooltip != null)
                IconButton(
                  icon: const Icon(Icons.help_outline, size: 16),
                  onPressed: () {
                    // Show tooltip
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(_tooltip!)),
                    );
                  },
                ),
            ],
          ),
          SizedBox(height: 8.h),
        ],
        CustomTextForm(
          initialValue: _displayValue ?? widget.data.value?.toString() ?? '',
          maxLines: 1,
          hintText: widget.data.placeholder,
          validator:
              _errorMessage?.isNotEmpty == true ? (_) => _errorMessage : null,
          onChanged: (value) {
            debugPrint('Value changed to: $value ${widget.data.name}' );
            if (value != null) {
              widget.onChange?.call(widget.data.name.toString(), value);
            }
          },
          filled: true,
          fillColor: _isReadOnly ? getColorSkin().grey4Background : Colors.white,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 10.w,
            vertical: 8.h,
          ),
          showDeleteButton: !_isReadOnly,
          textInputAction: TextInputAction.done,
          // enabled: !_isReadOnly,
        ),
      ],
    );
  }
}
