class UserTaskInfoResponse {
  final int? code;
  final String? message;
  final List<TaskInfo>? data;

  UserTaskInfoResponse({this.code, this.message, this.data});

  factory UserTaskInfoResponse.fromJson(Map<String, dynamic> json) {
    return UserTaskInfoResponse(
      code: json['code'],
      message: json['message'],
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => TaskInfo.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

class TaskInfo {
  final String? taskDefKey;
  final String? taskName;
  final String? taskType;
  final List<AssigneeInfo>? lstAssigneeInfo;
  final String? formKey;

  TaskInfo({
    this.taskDefKey,
    this.taskName,
    this.taskType,
    this.lstAssigneeInfo,
    this.formKey,
  });

  factory TaskInfo.fromJson(Map<String, dynamic> json) {
    return TaskInfo(
      taskDefKey: json['taskDefKey'] ?? '',
      taskName: json['taskName'] ?? '',
      taskType: json['taskType'] ?? '',
      lstAssigneeInfo: (json['lstAssigneeInfo'] as List?)
              ?.map((item) => AssigneeInfo.fromJson(item))
              .toList() ??
          [],
      formKey: json['formKey'] ?? '',
    );
  }
}

class AssigneeInfo {
  final String? username;
  final String? fullName;
  final String? title;

  final String? completedTime;
  final String? taskStatus;
  final int? taskId;
  final String? companyCode;
  final String? chartShortName;
  final String? chartName;

  AssigneeInfo({
    this.username,
    this.fullName,
    this.title,
    this.completedTime,
    this.taskStatus,
    this.taskId,
    this.companyCode,
    this.chartShortName,
    this.chartName,
  });

  factory AssigneeInfo.fromJson(Map<String, dynamic> json) {
    return AssigneeInfo(
      username: json['username'],
      fullName: json['fullName'],
      title: json['title'],
      completedTime: json['completedTime'],
      taskStatus: json['taskStatus'],
      taskId: json['taskId'],
      companyCode: json['companyCode'],
      chartShortName: json['chartShortName'],
      chartName: json['chartName'],
    );
  }

  String getFormattedInfo() {
    return '$username - $fullName - $title';
  }
}
