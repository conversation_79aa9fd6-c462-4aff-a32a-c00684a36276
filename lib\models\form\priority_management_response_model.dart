class PriorityManagementResponseModel {
  final int code;
  final String message;
  final PriorityManagementData? data;

  PriorityManagementResponseModel({
    required this.code,
    required this.message,
    this.data,
  });

  factory PriorityManagementResponseModel.fromJson(Map<String, dynamic> json) {
    return PriorityManagementResponseModel(
      code: json['code'] as int,
      message: json['message'] as String,
      data: json['data'] != null ? PriorityManagementData.fromJson(json['data'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class PriorityManagementData {
  final int page;
  final int limit;
  final String? search;
  final String sortBy;
  final String sortType;
  final List<PriorityManagementContent> content;

  PriorityManagementData({
    required this.page,
    required this.limit,
    this.search,
    required this.sortBy,
    required this.sortType,
    required this.content,
  });

  factory PriorityManagementData.fromJson(Map<String, dynamic> json) {
    return PriorityManagementData(
      page: json['page'] as int,
      limit: json['limit'] as int,
      search: json['search'] as String?,
      sortBy: json['sortBy'] as String,
      sortType: json['sortType'] as String,
      content: (json['content'] as List)
          .map((e) => PriorityManagementContent.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'content': content.map((e) => e.toJson()).toList(),
    };
  }
}

class PriorityManagementContent {
  final int? id;
  final String? code;
  final String? name;
  final String? color;
  final String? alertTimeComplete;
  final List<String>? shareWith;
  final int? status;
  final double? slaValue;
  final String? createdUser;
  final String? createdDate;
  final String? modifiedUser;
  final String? modifiedDate;
  final String? description;
  final int? reminderBeingTime;
  final int? reminderTime;
  final int? reminderBeingValue;
  final int? reminderValue;
  final String? reminderBeingType;
  final String? reminderType;
  final bool? configReminder;
  final List<String>? applyFor;
  final String? companyName;
  final String? companyCode;
  final int? activeStatus;

  PriorityManagementContent({
    this.id,
    this.code,
    this.name,
    this.color,
    this.alertTimeComplete,
    this.shareWith,
    this.status,
    this.slaValue,
    this.createdUser,
    this.createdDate,
    this.modifiedUser,
    this.modifiedDate,
    this.description,
    this.reminderBeingTime,
    this.reminderTime,
    this.reminderBeingValue,
    this.reminderValue,
    this.reminderBeingType,
    this.reminderType,
    this.configReminder,
    this.applyFor,
    this.companyName,
    this.companyCode,
    this.activeStatus,
  });

  factory PriorityManagementContent.fromJson(Map<String, dynamic> json) {
    return PriorityManagementContent(
      id: json['id'] as int?,
      code: json['code'] as String?,
      name: json['name'] as String?,
      color: json['color'] as String?,
      alertTimeComplete: json['alertTimeComplete'] as String?,
      shareWith: (json['shareWith'] as List?)?.map((e) => e as String).toList(),
      status: json['status'] as int?,
      slaValue: json['slaValue'] != null ? (json['slaValue'] as num).toDouble() : null,
      createdUser: json['createdUser'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedUser: json['modifiedUser'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      description: json['description'] as String?,
      reminderBeingTime: json['reminderBeingTime'] as int?,
      reminderTime: json['reminderTime'] as int?,
      reminderBeingValue: json['reminderBeingValue'] as int?,
      reminderValue: json['reminderValue'] as int?,
      reminderBeingType: json['reminderBeingType'] as String?,
      reminderType: json['reminderType'] as String?,
      configReminder: json['configReminder'] as bool?,
      applyFor: (json['applyFor'] as List?)?.map((e) => e as String).toList(),
      companyName: json['companyName'] as String?,
      companyCode: json['companyCode'] as String?,
      activeStatus: json['activeStatus'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'color': color,
      'alertTimeComplete': alertTimeComplete,
      'shareWith': shareWith,
      'status': status,
      'slaValue': slaValue,
      'createdUser': createdUser,
      'createdDate': createdDate,
      'modifiedUser': modifiedUser,
      'modifiedDate': modifiedDate,
      'description': description,
      'reminderBeingTime': reminderBeingTime,
      'reminderTime': reminderTime,
      'reminderBeingValue': reminderBeingValue,
      'reminderValue': reminderValue,
      'reminderBeingType': reminderBeingType,
      'reminderType': reminderType,
      'configReminder': configReminder,
      'applyFor': applyFor,
      'companyName': companyName,
      'companyCode': companyCode,
      'activeStatus': activeStatus,
    };
  }
} 