import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/models/form/tao_to_trinh_request_body.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/models/form/bpmProcInst_create_request_model.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:go_router/go_router.dart';
import 'dart:developer' as developer;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';

class FormSubmitService {
  static Future<bool> submitForm({
    required BuildContext context,
    required Map<String, dynamic> formData,
    required String procDefId,
    required int ticketId,
    required String requestSubject,
    TaoToTrinhRequestBody? printSignZoneRequest,
  }) async {
    final formState = context.read<FormBloc>().state;

    final submitData = BpmProcInstCreateRequestModel(
      variables: formData,
      linkProcInstId: [],
      receiveMail: true,
      notifyUsers: [],
      priorityId: 0,
      isDraft: false,
      serviceId: ticketId,
      chartId: formState.chartData?.data.first.id ?? 0,
      isAssistant: false,
      submissionType: null,
      companyCode: formState.chartData?.data.first.companyCode ?? "",
      chartNodeName: formState.chartNodeData?.data.first.chartNodeName ?? "",
      chartNodeCode: formState.chartNodeData?.data.first.chartNodeCode ?? "",
      chartNodeId: formState.chartNodeData?.data.first.chartNodeId ?? 0,
      chartName: formState.chartData?.data.first.name ?? "",
      forceViewUrl: "",
      cancelTicketId: "",
      approvedBudgetIds: [],
    );

    context.read<FormBloc>().add(
          CreateBpmProcInstRequested(
            procDefId: procDefId,
            ticketId: ticketId,
            requestBody: submitData,
            printSignZoneRequest: printSignZoneRequest,
          ),
        );

    return true;
  }

  static void handleSubmitResponse(
    BuildContext context,
    FormInfoState formState,
    VoidCallback? onSuccess,
    VoidCallback? onError,
  ) {
    developer.log("formState.isSubmitting ${formState.isSubmitting}");

    if (formState.hasBpmProcInstCreateResponse) {
      if (formState.bpmProcInstCreateResponse?.code == 1) {
        SnackbarCore.success('Đệ trình thành công, vui lòng chờ duyệt');

        // Clear all states
        clearUploadState(context);
        onSuccess?.call();
        // Handle navigation and bottom nav state
        // WidgetsBinding.instance.addPostFrameCallback((_) {
        //   if (!context.mounted) return;
          
        //   // Check if there are signed files
        //   final hasSignedFiles = formState.uploadFileResponse?.data != null &&
        //       formState.fileType == 'signature';

        //   if (DeviceUtils.isTablet) {
        //     // For tablet mode, find the TicketFormScreen widget and call onClose
        //     final ticketFormScreen = context.findAncestorWidgetOfExactType<TicketFormScreen>();
        //     if (ticketFormScreen != null) {
        //       ticketFormScreen.onClose?.call();
        //     }
        //     return;
        //   }

        //   // For phone mode
        //   if (hasSignedFiles || formState.hasPrintSignZone) {
        //     context.go('/bottom_nav');
        //   } else {
        //     context.go('/bottom_nav');
        //   }

        //   switchToTab(0);
        //   context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
        // });
        return;
      } else {
        SnackbarCore.error(
            'Đệ trình thất bại, vui lòng kiểm tra lại thông tin');
        // Clear all states on failure
        clearUploadState(context);
        onError?.call();
      }
    }

    // if (formState.hasError) {
    //   // Clear all states on error
    //   clearUploadState(context);
    //   onError?.call();
    //   return;
    // }
  }

  static void clearUploadState(BuildContext context) {
    context.read<FormBloc>().add(FormReset());
    context.read<CheckTypeBloc>().add(ClearCheckTypeState());
    context.read<PrintFileTemplateBloc>().add(ClearPrintFileState());
  }
}
