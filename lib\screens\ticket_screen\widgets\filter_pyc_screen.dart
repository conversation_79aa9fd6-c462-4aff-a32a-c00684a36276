import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_data_model.dart';
import 'package:eapprove/widgets/custom_dropdown_multi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/widgets/custom_bottomsheet.dart';
import 'package:flutter_sdk/widgets/custom_check_box.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_request_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_request_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_request_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_request_model.dart';
import 'package:intl/intl.dart';

import 'package:eapprove/widgets/custom_datepicker.dart';

const listAssignStatusOption = [
  {"value": "-1", "label": "Chọn tất cả"},
  {"value": "active", "label": "Đang hiệu lực"},
  {"value": "expired", "label": "Hết hiệu lực"},
  {"value": "none", "label": "Không có ủy quyền"},
];

const listApprovalByStatusOption = [
  {"value": "assignUser", "label": "Người ủy quyền"},
  {"value": "assignedUser", "label": "Người được ủy quyền"},
];

class FilterPycBottomSheet extends StatefulWidget {
  final Function(Map<String, dynamic>)? onApplyFilterSilent;
  final Function(Map<String, dynamic>)? onApplyFilter;
  final int filterIndex;
  final bool isFilterTab;
  final Map<String, dynamic>? initialFilters;
  final VoidCallback? onResetFilter;

  const FilterPycBottomSheet(
      {super.key,
      this.onApplyFilter,
      this.filterIndex = 0,
      this.initialFilters,
      this.onResetFilter,
      this.onApplyFilterSilent,
      required this.isFilterTab});

  static Future<void> show({
    required BuildContext context,
    required int filterIndex,
    required bool isFilterTab,
    Function(Map<String, dynamic>)? onApplyFilter,
    Map<String, dynamic>? initialFilters,
    VoidCallback? onResetFilter,
  }) {
    final pycBloc = BlocProvider.of<PycBloc>(context, listen: false);

    if (DeviceUtils.isTablet) {
      return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext dialogContext) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            insetPadding:
                EdgeInsets.symmetric(horizontal: 20.w, vertical: 24.h),
            child: BlocProvider.value(
              value: pycBloc,
              child: FilterPycBottomSheet(
                isFilterTab: isFilterTab,
                filterIndex: filterIndex,
                onApplyFilter: onApplyFilter,
                initialFilters: initialFilters,
                onResetFilter: onResetFilter,
              ),
            ),
          );
        },
      );
    } else {
      return CustomBottomSheet.show(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
        titlePadding: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.zero,
        titleAlign: TextAlign.left,
        context: context,
        closeIconAfterTitle: true,
        title: 'Bộ lọc',
        closeIcon: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
        ),
        children: BlocProvider.value(
          value: pycBloc,
          child: FilterPycBottomSheet(
            isFilterTab: isFilterTab,
            onApplyFilter: onApplyFilter,
            filterIndex: filterIndex,
            initialFilters: initialFilters,
            onResetFilter: onResetFilter,
          ),
        ),
      );
    }
  }

  static applyFilter(Map<String, dynamic> filterValues, dynamic requestModel) {
    if (requestModel is MyPycRequestModel) {
      return requestModel.copyWith(
        search: filterValues['ticketTitle'],
        listStatus: filterValues['listTicketStatus'] != null
            ? (filterValues['listTicketStatus'] as List).cast<String>()
            : null,
        listService: filterValues['listService'] != null
            ? (filterValues['listService'] as List).cast<String>()
            : null,
        listUser: filterValues['listAssignee'] != null
            ? (filterValues['listAssignee'] as List).cast<String>()
            : null,
        listChartNodeName: filterValues['listChartNodeId'] != null
            ? (filterValues['listChartNodeId'] as List).cast<String>()
            : null,
        fromDate: filterValues['fromDate'] as String?,
        toDate: filterValues['toDate'] as String?,
      );
    } else if (requestModel is ProcRequestModel) {
      return requestModel.copyWith(
        search: filterValues['ticketTitle'],
        listStatus: filterValues['listTicketStatus'] != null
            ? (filterValues['listTicketStatus'] as List).cast<String>()
            : null,
        listUser: filterValues['listAssignee'] != null
            ? (filterValues['listAssignee'] as List).cast<String>()
            : null,
        listChartNodeName: filterValues['listChartNodeName'] != null
            ? (filterValues['listChartNodeName'] as List).cast<String>()
            : null,
        fromDate: filterValues['fromDate'] as String?,
        toDate: filterValues['toDate'] as String?,
      );
    } else if (requestModel is ApproveRequestModel) {
      return requestModel.copyWith(
        search: filterValues['ticketTitle'],
        listStatus: filterValues['listTicketStatus'] != null
            ? (filterValues['listTicketStatus'] as List).cast<String>()
            : null,
        listService: filterValues['listService'] != null
            ? (filterValues['listService'] as List).cast<String>()
            : null,
        listUser: filterValues['listAssignee'] != null
            ? (filterValues['listAssignee'] as List).cast<String>()
            : null,
        listChartNodeName: filterValues['listChartNodeId'] != null
            ? (filterValues['listChartNodeId'] as List).cast<String>()
            : null,
        fromDate: filterValues['fromDate'] as String?,
        toDate: filterValues['toDate'] as String?,
      );
    } else if (requestModel is AssisRequestModel) {
      return requestModel.copyWith(
        search: filterValues['ticketTitle'],
        listStatus: filterValues['listTicketStatus'] != null
            ? (filterValues['listTicketStatus'] as List).cast<String>()
            : null,
        listUser: filterValues['listAssignee'] != null
            ? (filterValues['listAssignee'] as List).cast<String>()
            : null,
        listChartNodeName: filterValues['listChartNodeId'] != null
            ? [filterValues['listChartNodeId']]
            : null,
        fromDate: filterValues['fromDate'] as String?,
        toDate: filterValues['toDate'] as String?,
      );
    }
    return requestModel;
  }

  @override
  State<FilterPycBottomSheet> createState() => _FilterPycBottomSheetState();
}

class _FilterPycBottomSheetState extends State<FilterPycBottomSheet> {
  final Map<String, dynamic> _filterValues = {};
  final Map<String, TextEditingController> _controllers = {};
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _filterValues.addAll(widget.initialFilters ?? {});
    _filterValues.putIfAbsent('startDate', () => null);
    _filterValues.putIfAbsent('endDate', () => null);

    final pycBloc = context.read<PycBloc>();

    pycBloc.add(FetchAssigneeList(
        requestModel: ListAssigneeRequestModel(isLoadAll: true)));
    pycBloc.add(FetchListChartId());
    pycBloc.add(FetchPriorityList(ListPriorityRequestModel(
        sortBy: 'name', sortType: 'ASC', limit: 100, size: 100, page: 1)));

    final chartIds =
        (_filterValues['listChartId'] as List?)?.cast<String>() ?? [];
    if (chartIds.isNotEmpty) {
      final intChartIds =
          chartIds.map((e) => int.tryParse(e)).whereType<int>().toList();
      pycBloc.add(FetchListCreatedUser(
          requestModel: ListCreatedUserRequestModel(
              isLoadAll: true, orgchart: intChartIds)));
      pycBloc.add(FetchOrgChartList(
        requestModel: ChartFilterRequestModel(
            orgchart: intChartIds, infoType: "department", condition: []),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: DeviceUtils.isTablet ? 375.w : double.infinity,
      constraints: DeviceUtils.isTablet
          ? BoxConstraints(maxHeight: 762.h)
          : BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: getColorSkin().white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (DeviceUtils.isTablet) _buildHeader(),
          if (DeviceUtils.isTablet)
            CustomDivider(color: getColorSkin().grey4Background),
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _buildFiltersByIndex(widget.filterIndex),
              ),
            ),
          ),
          if (DeviceUtils.isTablet)
            CustomDivider(color: getColorSkin().grey4Background),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 16.0.w),
          child: Text(
            'Bộ lọc',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
              color: getColorSkin().ink1,
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  List<Widget> _buildFiltersByIndex(int index) {
    switch (index) {
      case 0:
        return _buildMyPycFilters();
      case 1:
        return _buildProcPycFilters();
      case 2:
        return _buildAppPycFilters();
      case 3:
        return _buildAssisPycFilters();
      default:
        return [];
    }
  }

  List<Widget> _buildMyPycFilters() {
    final showChartFields = _filterValues['shareStatus'] == 'SHARED' ||
        _filterValues['shareStatus'] == 'FOLLOWED';
    return [
      _buildTextInput('Tên tờ trình', 'ticketTitle'),
      _buildFilterItem('Trạng thái phiếu', 'listTicketStatus'),
      _buildFilterItem('Tên dịch vụ', 'listService'),
      _buildFilterItem('Người thực hiện/phê duyệt', 'listAssignee'),
      _buildFilterItem('Lọc theo phiếu Chia sẻ/Theo dõi', 'shareStatus'),
      if (showChartFields)
        _buildFilterItem('Công ty người trình', 'listChartId'),
      if (showChartFields)
        _buildFilterItem('Phòng ban người trình', 'listChartNodeId'),
      if (showChartFields) _buildFilterItem('Người trình', 'listCreatedUser'),
      _buildFilterItem('Chọn loại thời gian cần lọc phiếu', 'dateType'),
      _buildDateRangePicker(),
      _buildMultiRowCheckboxes([
        {'key': 'isAssistant', 'label': 'Có ý kiến trợ lý'},
        {'key': 'isDiscussion', 'label': 'Cần tham vấn'},
      ], [
        {'key': 'isNotification', 'label': 'Được thông báo'},
      ]),
    ];
  }

  List<Widget> _buildAssisPycFilters() {
    return [
      _buildTextInput('Tên tờ trình', 'ticketTitle'),
      _buildFilterItem('Trạng thái phiếu', 'listTicketStatus'),
      _buildFilterItem('Tên dịch vụ', 'listService'),
      _buildFilterItem('Người thực hiện/phê duyệt', 'listAssignee'),
      _buildFilterItem('Công ty người trình', 'listChartId'),
      _buildFilterItem('Phòng ban người trình', 'listChartNodeId'),
      _buildFilterItem('Người trình', 'listCreatedUser'),
      _buildFilterItem('Độ ưu tiên', 'listTaskPriority'),
      _buildFilterItem('Yêu cầu bổ sung', 'listAdditionalStatus'),
      _buildFilterItem('Chọn loại thời gian cần lọc phiếu', 'dateType'),
      _buildDateRangePicker(),
      _buildMultiRowCheckboxes([
        {'key': 'hasDiscussion', 'label': 'Phiếu có tham vấn'},
      ]),
    ];
  }

  List<Widget> _buildProcPycFilters() {
    return [
      _buildTextInput('Tên tờ trình', 'ticketTitle'),
      _buildFilterItem('Trạng thái phiếu', 'listTicketStatus'),
      _buildFilterItem('Tên dịch vụ', 'listService'),
      _buildFilterItem('Bước thực hiện trong một dịch vụ', 'listTaskDefKey'),
      _buildFilterItem('Trạng thái bước', 'listTaskStatus'),
      _buildFilterItem('Người thực hiện/phê duyệt', 'listAssignee'),
      _buildFilterItem('Công ty người trình', 'listChartId'),
      _buildFilterItem('Phòng ban người trình', 'listChartNodeId'),
      _buildFilterItem('Người trình', 'listCreatedUser'),
      _buildFilterItem('Độ ưu tiên', 'listTaskPriority'),
      _buildFilterItem('Trạng thái ủy quyền', 'listAssignStatus'),
      _buildFilterItem('Trạng thái được ủy quyền', 'listAssignedStatus'),
      _buildFilterItem(
          'Phê duyệt bởi người UQ hoặc người được UQ', 'listApprovalByStatus'),
      _buildFilterItem('Yêu cầu bổ sung', 'listAdditionalStatus'),
      _buildFilterItem('Chọn loại thời gian cần lọc phiếu', 'dateType'),
      _buildDateRangePicker(),
      _buildMultiRowCheckboxes([
        {'key': 'isAssistant', 'label': 'Có ý kiến trợ lý'},
        {'key': 'isDiscussion', 'label': 'Cần tham vấn'},
      ]),
    ];
  }

  List<Widget> _buildAppPycFilters() {
    return [
      _buildTextInput('Tên tờ trình', 'ticketTitle'),
      _buildFilterItem('Trạng thái phiếu', 'listTicketStatus'),
      _buildFilterItem('Tên dịch vụ', 'listService'),
      _buildFilterItem('Bước thực hiện trong một dịch vụ', 'listTaskDefKey'),
      _buildFilterItem('Trạng thái bước', 'listTaskStatus'),
      _buildFilterItem('Người thực hiện/phê duyệt', 'listAssignee'),
      _buildFilterItem('Công ty người trình', 'listChartId'),
      _buildFilterItem('Phòng ban người trình', 'listChartNodeId'),
      _buildFilterItem('Người trình', 'listCreatedUser'),
      _buildFilterItem('Độ ưu tiên', 'listTaskPriority'),
      _buildFilterItem('Trạng thái ủy quyền', 'listAssignStatus'),
      _buildFilterItem('Trạng thái được ủy quyền', 'listAssignedStatus'),
      _buildFilterItem(
          'Phê duyệt bởi người UQ hoặc người được UQ', 'listApprovalByStatus'),
      _buildFilterItem('Yêu cầu bổ sung', 'listAdditionalStatus'),
      _buildFilterItem('Chọn loại thời gian cần lọc phiếu', 'dateType'),
      _buildDateRangePicker(),
      _buildMultiRowCheckboxes([
        {'key': 'isAssistant', 'label': 'Có ý kiến trợ lý'},
        {'key': 'isDiscussion', 'label': 'Cần tham vấn'},
      ]),
    ];
  }

  Widget _buildTextInput(String label, String key) {
    if (!_controllers.containsKey(key)) {
      _controllers[key] = TextEditingController(text: _filterValues[key] ?? '');
    }
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          ),
          SizedBox(height: 8.h),
          TextFormField(
            controller: _controllers[key],
            onChanged: (value) => _filterValues[key] = value,
            cursorColor: getColorSkin().black,
            maxLength: 100,
            decoration: InputDecoration(
              hintText: 'Nhập $label',
              hintStyle: TextStyle(fontSize: 14.sp, color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: getColorSkin().ink5,
                  style: BorderStyle.solid,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: getColorSkin().ink5,
                  style: BorderStyle.solid,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: getColorSkin().ink5,
                  style: BorderStyle.solid,
                  width: 1,
                ),
              ),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            ),
            style: DeviceUtils.isTablet
                ? TextStyle(fontSize: 14.sp)
                : getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterItem(String label, String key) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDropdown(label, key),
        ],
      ),
    );
  }

  Widget _buildDropdown(String label, String key) {
    List<SelectItem> options = [
      SelectItem(label: 'Chọn tất cả', value: "-1"),
    ];

    if (key == 'listTicketStatus') {
      options = [
        SelectItem(label: 'Chọn tất cả', value: "-1"),
        SelectItem(label: 'Thu hồi', value: 'RECALLED'),
        SelectItem(label: 'Yêu cầu Thu hồi', value: 'RECALLING'),
        SelectItem(label: 'Trả về', value: 'DELETED_BY_RU'),
        SelectItem(label: 'Đang phê duyệt', value: 'PROCESSING'),
        SelectItem(label: 'Hoàn thành', value: 'COMPLETED'),
        SelectItem(label: 'Hủy', value: 'CANCEL'),
        SelectItem(label: 'Cần bổ sung', value: 'ADDITIONAL_REQUEST'),
        SelectItem(label: 'Nháp', value: 'DRAFT'),
      ];
      final raw = _filterValues.putIfAbsent(key, () => ['-1']);
      final selectedValues = (raw as List).cast<String>();
      return CustomDropdownMulti(
        key: ValueKey('${key}_${selectedValues.join(',')}'),
        label: label,
        showBottomSheetLabel: false,
        options: options,
        enableSearch: true,
        imgSearchPath: StringImage.ic_search,
        titleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        defaultValues:
            options.where((o) => selectedValues.contains(o.value)).toList(),
        dropdownHeight: 48.h,
        onSelected: (selected) {
          setState(() {
            _filterValues[key] = selected.map((e) => e.value).toList();
          });
        },
      );
    } else if (key == 'listTaskStatus') {
      options = [
        SelectItem(label: 'Chọn tất cả', value: '-1'),
        SelectItem(label: 'Thu hồi', value: 'AGREE_TO_RECALL'),
        SelectItem(label: 'Trả về', value: 'DELETED_BY_RU'),
        SelectItem(label: 'Yêu cầu Thu hồi', value: 'RECALLING'),
        SelectItem(label: 'Đang phê duyệt', value: 'PROCESSING'),
        SelectItem(label: 'Hoàn thành', value: 'COMPLETED'),
        SelectItem(label: 'Hủy', value: 'CANCEL'),
      ];
      final raw = _filterValues.putIfAbsent(key, () => ['-1']);
      final selectedValues = (raw as List).cast<String>();

      return CustomDropdownMulti(
        key: ValueKey('${key}_${_filterValues[key] ?? ''}'),
        label: label,
        enableSearch: true,
        showBottomSheetLabel: false,
        imgSearchPath: StringImage.ic_search,
        titleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        options: options,
        defaultValues:
            options.where((o) => selectedValues.contains(o.value)).toList(),
        dropdownHeight: 48.h,
        onSelected: (selected) {
          setState(() {
            _filterValues[key] = selected.map((e) => e.value).toList();
          });
        },
      );
    } else if (key == 'shareStatus') {
      options = [
        SelectItem(label: 'Theo dõi', value: 'FOLLOWED'),
        SelectItem(label: 'Được chia sẻ', value: 'SHARED'),
        SelectItem(label: 'Đã chia sẻ', value: 'SHARE'),
      ];

      final selectedValue = (_filterValues[key] ?? '').toString();
      final selectedItem = options.firstWhere(
        (item) => item.value == selectedValue,
        orElse: () => SelectItem(label: '', value: ''),
      );

      //Không cần multi select
      return CustomDropdownMenu(
        key: ValueKey('${key}_${_filterValues[key] ?? ''}'),
        label: label,
        titleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        options: options,
        showDeleteIcon: selectedItem.value.isNotEmpty == true,
        defaultValue: selectedItem,
        dropdownHeight: 48.h,
        onSelected: (value) {
          setState(() {
            _filterValues[key] = value?.value;
          });
        },
      );
    } else if (key == 'listService') {
      return BlocBuilder<PycBloc, PycState>(
        builder: (context, state) {
          // final scrollController = ScrollController();
          // scrollController.addListener(() {
          //   if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 50) {
          //     final hasMore = state.hasMore;
          //     final isPaginating = state.isPaginating;
          //     final currentRequest = state.serviceListRequestModel;

          //     if (hasMore && !isPaginating) {
          //       context.read<PycBloc>().add(FetchServiceList(
          //           isLoadMore: true,
          //           requestModel: currentRequest!.copyWith(
          //             page: (currentRequest.page ?? 1) + 1,
          //           )));
          //     }
          //   }
          // });
          if (state.serviceListStatus == PycStatus.loading) {
            return Container(
              height: 48.h,
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  SizedBox(width: 12.w),
                  Text(
                    'Đang tải danh sách dịch vụ...',
                    style:
                        TextStyle(color: Colors.grey.shade600, fontSize: 14.sp),
                  ),
                ],
              ),
            );
          }
          if (state.serviceListStatus == PycStatus.initial) {
            context.read<PycBloc>().add(
                  FetchServiceList(
                    requestModel: RequestListService(
                      serviceType: [2],
                      sortBy: 'listService',
                      sortType: 'ASC',
                    ),
                  ),
                );
          }

          if (state.serviceListStatus == PycStatus.error) {
            return Container(
              height: 48.h,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: Text(
                'Lỗi tải dữ liệu dịch vụ',
                style: TextStyle(color: Colors.red, fontSize: 14.sp),
              ),
            );
          }

          final List<SelectItem> serviceOptions = [
            SelectItem(label: 'Chọn tất cả', value: '-1'),
            ...state.serviceList.map(
              (service) => SelectItem(
                label: service.serviceName,
                value: service.id.toString(),
              ),
            ),
          ];

          final raw = _filterValues.putIfAbsent(key, () => ['-1']);
          final selectedValues = (raw as List).cast<String>();
          return CustomDropdownMulti(
            key: ValueKey('${key}_${selectedValues.join(',')}'),
            label: label,
            showBottomSheetLabel: false,
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            options: serviceOptions,
            defaultValues: selectedValues.isEmpty
                ? []
                : serviceOptions
                    .where((o) => selectedValues.contains(o.value))
                    .toList(),
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            dropdownHeight: 48.h,
            onSelected: (selected) {
              setState(() {
                final selectedIds = selected.map((e) => e.value).toList();
                _filterValues[key] = selectedIds;

                _filterValues['listTaskDefKey'] = null;

                final validIds = selectedIds.where((id) => id != '-1').toList();

                if (validIds.isNotEmpty) {
                  context.read<PycBloc>().add(
                        FetchTaskDefKeyList(
                          ListTaskDefKeyRequestModel(
                            validIds.map((e) => int.parse(e)).toList(),
                          ),
                        ),
                      );
                }
              });
            },
          );
        },
      );
    } else if (key == 'listTaskDefKey') {
      return BlocBuilder<PycBloc, PycState>(
        builder: (context, state) {
          final selectedServiceIds =
              (_filterValues['listService'] as List?)?.cast<String>() ?? [];

          final bool isDisabled =
              selectedServiceIds.isEmpty || selectedServiceIds.contains('-1');

          if (isDisabled) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Bước thực hiện trong một dịch vụ',
                    style: getTypoSkin()
                        .medium16
                        .copyWith(color: getColorSkin().ink1)),
                SizedBox(height: 6.h),
                Container(
                  height: 48.h,
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade100,
                  ),
                  child: Text(
                    'Vui lòng chọn dịch vụ trước',
                    style:
                        TextStyle(fontSize: 14.sp, color: Colors.grey.shade600),
                  ),
                ),
              ],
            );
          }

          final shouldReload = state.taskDefKeyStatus != PycStatus.loading &&
              !(state.taskDefKeyRequestModel?.listServiceId
                      ?.map((e) => e.toString())
                      .toSet()
                      .containsAll(selectedServiceIds) ??
                  false);

          if (shouldReload) {
            context.read<PycBloc>().add(
                  FetchTaskDefKeyList(
                    ListTaskDefKeyRequestModel(
                      selectedServiceIds
                          .map((e) => int.tryParse(e))
                          .whereType<int>()
                          .toList(),
                    ),
                  ),
                );
          }

          if (state.taskDefKeyStatus == PycStatus.error) {
            return Text(
              'Lỗi tải bước xử lý',
              style: TextStyle(color: Colors.red, fontSize: 14.sp),
            );
          }

          final options = state.taskDefKeyList.asMap().entries.map((entry) {
            final index = entry.key;
            final task = entry.value;
            return SelectItem(
              label: task.taskName ?? '',
              value: '${task.taskDefKey}_$index',
            );
          }).toList();

          final selectedValues =
              (_filterValues[key] as List?)?.cast<String>() ?? [];

          return CustomDropdownMulti(
            key: ValueKey('${key}_${selectedValues.join(',')}'),
            label: label,
            options: options,
            showBottomSheetLabel: false,
            bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            defaultValues: selectedValues.isEmpty
                ? []
                : options
                    .where((o) => selectedValues.contains(o.value))
                    .toList(),
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            dropdownHeight: 48.h,
            onSelected: (selected) {
              setState(() {
                _filterValues[key] = selected.map((e) => e.value).toList();
              });
            },
          );
        },
      );
    } else if (key == 'listAssignee') {
      return BlocBuilder<PycBloc, PycState>(
        builder: (context, state) {
          if (state.assigneeListStatus != PycStatus.loading &&
              state.assigneeListStatus != PycStatus.loaded) {
            context.read<PycBloc>().add(
                  FetchAssigneeList(
                    requestModel: ListAssigneeRequestModel(isLoadAll: false),
                  ),
                );
          }
          final allAssignees = state.assigneeList
              .expand((assigneeDataModel) => assigneeDataModel.data)
              .toList();
          final options = [
            SelectItem(label: 'Chọn tất cả', value: '-1'),
            ...allAssignees.map(
              (assignee) => SelectItem(
                label:
                    '${assignee.fullName ?? ''} ${assignee.username ?? ''} - ${assignee.title ?? ''}',
                value: assignee.username ?? '',
              ),
            ),
          ];

          final raw = _filterValues.putIfAbsent(key, () => ['-1']);
          final selectedValues = (raw as List).cast<String>();

          return CustomDropdownMulti(
            key: ValueKey('${key}_${_filterValues[key] ?? ''}'),
            label: label,
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            showBottomSheetLabel: false,
            bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            options: options,
            defaultValues:
                options.where((o) => selectedValues.contains(o.value)).toList(),
            dropdownHeight: 48.h,
            onSelected: (selected) {
              setState(() {
                _filterValues[key] = selected.map((e) => e.value).toList();
              });
            },
          );
        },
      );
    } else if (key == 'listChartId') {
      return BlocBuilder<PycBloc, PycState>(
        builder: (context, state) {
          if (state.chartIdListStatus == PycStatus.initial) {
            context.read<PycBloc>().add(FetchListChartId());
          }

          final options = [
            SelectItem(label: 'Chọn tất cả', value: '-1'),
            ...state.chartIdList.map((chartId) => SelectItem(
                  label: '${chartId.code ?? ''} ${chartId.name ?? ''}',
                  value: chartId.id?.toString() ?? '',
                )),
          ];

          final raw = _filterValues.putIfAbsent(key, () => ['-1']);
          final selectedValues = (raw as List).cast<String>();

          return CustomDropdownMulti(
            key: ValueKey('listChartId_${_filterValues['listChartId'] ?? ''}'),
            label: label,
            options: options,
            showBottomSheetLabel: false,
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            defaultValues: selectedValues.isEmpty
                ? []
                : options
                    .where((o) => selectedValues.contains(o.value))
                    .toList(),
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            dropdownHeight: 48.h,
            onSelected: (selected) {
              final selectedChartIds = selected.map((e) => e.value).toList();
              setState(() {
                _filterValues['listChartId'] = selectedChartIds;
                _filterValues.remove('listChartNodeId');
                _filterValues.remove('listCreatedUser');

                final validIds = selectedChartIds
                    .where((id) => id != '-1')
                    .map((id) => int.tryParse(id))
                    .whereType<int>()
                    .toList();

                context.read<PycBloc>().add(
                      FetchListCreatedUser(
                        requestModel: ListCreatedUserRequestModel(
                          isLoadAll: true,
                          orgchart: validIds,
                        ),
                        chartId: validIds.isNotEmpty ? validIds.first : 0,
                      ),
                    );
                final orgchartIds = selectedChartIds.contains('-1')
                    ? []
                    : selectedChartIds
                        .where((id) => id != '-1')
                        .map((id) => int.tryParse(id))
                        .whereType<int>()
                        .toList();

                context.read<PycBloc>().add(
                      FetchOrgChartList(
                        requestModel: ChartFilterRequestModel(
                          orgchart: orgchartIds,
                          infoType: "department",
                          condition: [],
                        ),
                      ),
                    );
              });
            },
          );
        },
      );
    } else if (key == 'listChartNodeId') {
      return BlocBuilder<PycBloc, PycState>(
        buildWhen: (previous, current) =>
            previous.orgChartListStatus != current.orgChartListStatus ||
            previous.orgChartList != current.orgChartList,
        builder: (context, state) {
          final selectedChartIds =
              (_filterValues['listChartId'] as List?)?.cast<String>() ?? [];
          final validChartIds = selectedChartIds
              .where((id) => id != '-1')
              .map((id) => int.tryParse(id))
              .whereType<int>()
              .toList();

          if (state.orgChartListStatus == PycStatus.initial) {
            context.read<PycBloc>().add(
                  FetchOrgChartList(
                    requestModel: ChartFilterRequestModel(
                      orgchart:
                          selectedChartIds.contains('-1') ? [] : validChartIds,
                      infoType: "department",
                      condition: [],
                    ),
                  ),
                );
          }

          List<SelectItem> convertToSelectItems(List<OrgData> orgData) {
            List<SelectItem> convertOrgChildrenToSelectItems(
                List<OrgChild> children) {
              return children.map((child) {
                return SelectItem(
                  label: '${child.code} - ${child.title}',
                  value: child.code ?? '',
                  children: child.children != null
                      ? convertOrgChildrenToSelectItems(child.children!)
                      : [],
                );
              }).toList();
            }

            return orgData.map((node) {
              return SelectItem(
                label: '${node.code} - ${node.title}',
                value: node.id?.toString() ?? node.code ?? '',
                children: node.children != null
                    ? convertOrgChildrenToSelectItems(node.children!)
                    : [],
              );
            }).toList();
          }

          List<SelectItem> getSelectedItems(
              List<String> values, List<SelectItem> options) {
            if (values.contains('-1')) {
              return [SelectItem(label: 'Chọn tất cả', value: '-1')];
            }

            List<SelectItem> selected = [];
            for (var value in values) {
              void findSelectedItem(String value, List<SelectItem> options,
                  List<SelectItem> result) {
                for (var option in options) {
                  if (option.value == value) {
                    result.add(option);
                    return;
                  }
                  if (option.children.isNotEmpty) {
                    findSelectedItem(value, option.children, result);
                  }
                }
              }

              findSelectedItem(value, options, selected);
            }
            return selected;
          }

          List<SelectItem> optionsList = [
            SelectItem(label: 'Chọn tất cả', value: '-1'),
          ];

          if (state.orgChartListStatus == PycStatus.loaded) {
            optionsList.addAll(convertToSelectItems(state.orgChartList));
          }

          final raw = _filterValues.putIfAbsent(key, () => ['-1']);
          final selectedValues = (raw as List).cast<String>();

          return CustomDropdownMulti(
            key: ValueKey('${key}_${selectedValues.join(',')}'),
            label: label,
            options: optionsList,
            showBottomSheetLabel: false,
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            defaultValues: getSelectedItems(selectedValues, optionsList),
            dropdownHeight: 48.h,
            onSelected: (selected) {
              setState(() {
                _filterValues[key] = selected.map((e) => e.value).toList();
              });
            },
          );
        },
      );
    } else if (key == 'listCreatedUser') {
      return BlocBuilder<PycBloc, PycState>(
        builder: (context, state) {
          final selectedChartIds =
              (_filterValues['listChartId'] as List?)?.cast<String>() ?? [];
          final validIds = selectedChartIds
              .where((id) => id != '-1')
              .map((id) => int.tryParse(id))
              .whereType<int>()
              .toList();
          final bool isFetchAll = selectedChartIds.isEmpty;

          if (state.listCreatedUserStatus == PycStatus.initial) {
            context.read<PycBloc>().add(
                  FetchListCreatedUser(
                    requestModel: ListCreatedUserRequestModel(
                      isLoadAll: true,
                      orgchart: isFetchAll ? [] : validIds,
                    ),
                    chartId: isFetchAll
                        ? null
                        : int.tryParse(selectedChartIds.first),
                  ),
                );
          }

          final List<ListUserInfoModel> users =
              state.listCreatedUser.expand((wrapper) => wrapper.data).toList();

          final options = [
            SelectItem(label: 'Chọn tất cả', value: '-1'),
            ...users.map(
              (user) => SelectItem(
                label:
                    '${user.username ?? ''} - ${user.fullName ?? ''} - ${user.title ?? ''}',
                value: user.username ?? '',
              ),
            ),
          ];

          final raw = _filterValues.putIfAbsent(key, () => ['-1']);
          final selectedValues = (raw as List).cast<String>();
          return CustomDropdownMulti(
            key: ValueKey('listCreatedUser_${selectedValues.join(',')}'),
            label: label,
            showBottomSheetLabel: false,
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            options: options,
            defaultValues:
                options.where((o) => selectedValues.contains(o.value)).toList(),
            dropdownHeight: 48.h,
            onSelected: (selected) {
              setState(() {
                _filterValues[key] = selected.map((e) => e.value).toList();
              });
            },
          );
        },
      );
    } else if (key == 'listTaskPriority') {
      return BlocBuilder<PycBloc, PycState>(
        builder: (context, state) {
          if (state.priorityListStatus == PycStatus.initial) {
            context.read<PycBloc>().add(
                  FetchPriorityList(
                    ListPriorityRequestModel(
                      sortBy: 'name',
                      sortType: 'ASC',
                      limit: 100,
                      size: 100,
                      page: 1,
                    ),
                  ),
                );
          }

          if (state.priorityListStatus == PycStatus.error) {
            return Container(
              height: 48,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                'Lỗi tải dữ liệu',
                style: TextStyle(color: Colors.red, fontSize: 14.sp),
              ),
            );
          }

          final List<SelectItem> priorityOptions = [
            SelectItem(label: 'Chọn tất cả', value: '-1'),
            ...state.priorityListResponseModel?.data.content?.map(
                  (priority) => SelectItem(
                    label: priority.name ?? '',
                    value: priority.id?.toString() ?? '',
                  ),
                ) ??
                [],
          ];

          final raw = _filterValues.putIfAbsent(key, () => ['-1']);
          final selectedValues = (raw as List).cast<String>();

          return CustomDropdownMulti(
            key: ValueKey('${key}_${selectedValues.join(',')}'),
            label: label,
            showBottomSheetLabel: false,
            titleStyle: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
            enableSearch: true,
            imgSearchPath: StringImage.ic_search,
            options: priorityOptions,
            placeholder: "Độ ưu tiên",
            dropdownHeight: 48.h,
            defaultValues: priorityOptions
                .where((o) => selectedValues.contains(o.value))
                .toList(),
            onSelected: (selected) {
              setState(() {
                _filterValues[key] = selected.map((e) => e.value).toList();
              });
            },
          );
        },
      );
    } else if (key == 'dateType') {
      final List<SelectItem> timeFilterOptions = [
        SelectItem(label: 'Thời gian tạo phiếu', value: 'ticketCreatedTime'),
        SelectItem(
            label: 'Thời gian hoàn thành phiếu', value: 'ticketFinishTime'),
        SelectItem(label: 'Thời gian hủy phiếu', value: 'ticketCanceledTime'),
      ];

      final selectedValue = (_filterValues[key] ?? '').toString();
      final selectedItem = timeFilterOptions.firstWhere(
        (item) => item.value == selectedValue,
        orElse: () => SelectItem(label: '', value: ''),
      );

      //không cần multi select
      return CustomDropdownMenu(
          key: ValueKey('${key}_${_filterValues[key] ?? ''}'),
          label: label,
          titleStyle: getTypoSkin().medium16.copyWith(
                color: getColorSkin().ink1,
              ),
          // enableSearch: true,
          // imgSearchPath: StringImage.ic_search,
          options: timeFilterOptions,
          showDeleteIcon: selectedItem.value.isNotEmpty == true,
          defaultValue: selectedItem,
          dropdownHeight: 48.h,
          onSelected: (value) {
            setState(() {
              _filterValues[key] = value?.value;

              if (value?.value != null && value!.value.isNotEmpty) {
                _filterValues['startDate'] = null;
                _filterValues['endDate'] = null;
              } else {
                _filterValues['startDate'] = null;
                _filterValues['endDate'] = null;
              }
            });
          });
    } else if (key == 'listAssignStatus' || key == 'listAssignedStatus') {
      final List<SelectItem> assignStatusOptions = listAssignStatusOption
          .map(
            (status) => SelectItem(
              label: status['label'] as String,
              value: status['value'] as String,
            ),
          )
          .toList();

      final raw = _filterValues.putIfAbsent(
        key,
        () {
          if (key == 'listAssignStatus' || key == 'listAssignedStatus') {
            return <String>[];
          }
          return <String>['-1'];
        },
      );
      final selectedValues = (raw as List).cast<String>();

      return CustomDropdownMulti(
        key: ValueKey('${key}_${selectedValues.join(',')}'),
        label: key == 'listAssignStatus'
            ? "Trạng thái ủy quyền"
            : "Trạng thái được ủy quyền",
        options: assignStatusOptions,
        titleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        showBottomSheetLabel: false,
        bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        enableSearch: true,
        imgSearchPath: StringImage.ic_search,
        dropdownHeight: 48.h,
        defaultValues: assignStatusOptions
            .where((o) => selectedValues.contains(o.value))
            .toList(),
        onSelected: (selected) {
          setState(() {
            _filterValues[key] = selected.map((e) => e.value).toList();
          });
        },
      );
    } else if (key == 'listApprovalByStatus') {
      final List<SelectItem> approvalByStatusOptions =
          listApprovalByStatusOption
              .map(
                (status) => SelectItem(
                  label: status['label'] as String,
                  value: status['value'] as String,
                ),
              )
              .toList();

      final selectedValue = (_filterValues[key] ?? '').toString();
      final selectedItem = approvalByStatusOptions.firstWhere(
        (item) => item.value == selectedValue,
        orElse: () => SelectItem(label: '', value: ''),
      );

      //không cần multi select
      return CustomDropdownMenu(
        key: ValueKey('${key}_$selectedValue'),
        label: label,
        titleStyle: getTypoSkin().medium16.copyWith(
              color: getColorSkin().ink1,
            ),
        options: approvalByStatusOptions,
        showDeleteIcon: selectedItem.value.isNotEmpty == true,
        defaultValue: selectedItem,
        dropdownHeight: 48.h,
        onSelected: (value) {
          setState(() {
            _filterValues[key] = value?.value;
          });
        },
      );
    } else if (key == 'listAdditionalStatus') {
      options = [
        SelectItem(label: 'Chọn tất cả', value: '-1'),
        SelectItem(label: 'Cần bổ sung', value: 'additional'),
        SelectItem(label: 'Đã bổ sung', value: 'additionalCompleted'),
        SelectItem(label: 'Không yêu cầu bổ sung', value: 'none'),
      ];
    }

    final raw = _filterValues.putIfAbsent(
      key,
      () {
        if (key == 'listAdditionalStatus') {
          return <String>[];
        }
        return <String>['-1'];
      },
    );
    final selectedValues = (raw as List).cast<String>();
    return CustomDropdownMulti(
      key: ValueKey('${key}_${_filterValues[key] ?? ''}'),
      label: label,
      titleStyle: getTypoSkin().medium16.copyWith(
            color: getColorSkin().ink1,
          ),
      showBottomSheetLabel: false,
      bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
            color: getColorSkin().ink1,
          ),
      enableSearch: true,
      imgSearchPath: StringImage.ic_search,
      options: options,
      defaultValues:
          options.where((o) => selectedValues.contains(o.value)).toList(),
      dropdownHeight: 48.h,
      onSelected: (selected) {
        setState(() {
          _filterValues[key] = selected.map((e) => e.value).toList();
        });
      },
    );
  }

  Widget _buildDateRangePicker() {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        children: [
          Expanded(child: _buildDatePicker('startDate')),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Text('-', style: TextStyle(fontSize: 16.sp)),
          ),
          Expanded(child: _buildDatePicker('endDate')),
        ],
      ),
    );
  }

  Widget _buildDatePicker(String key) {
    final bool isEnabled = _filterValues['dateType'] != null &&
        _filterValues['dateType'].toString().isNotEmpty;

    return InkWell(
      onTap: isEnabled ? () => _selectDate(context, key) : null,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          color: isEnabled ? Colors.white : Colors.grey.shade100,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _formatDate(_filterValues[key] as DateTime?, enabled: isEnabled),
              style: TextStyle(
                fontSize: 14.sp,
                color: isEnabled ? Colors.black : Colors.grey,
              ),
            ),
            Icon(Icons.calendar_today,
                size: 18.sp, color: isEnabled ? Colors.black : Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildMultiRowCheckboxes(List<Map<String, String>> row1,
      [List<Map<String, String>>? row2]) {
    return Column(
      children: [
        Row(children: row1.map(_buildCheckboxItem).toList()),
        if (row2 != null) SizedBox(height: 8.h),
        if (row2 != null) Row(children: row2.map(_buildCheckboxItem).toList()),
      ],
    );
  }

  Widget _buildCheckboxItem(Map<String, String> item) {
    final key = item['key']!;
    final label = item['label']!;
    return Expanded(
      child: Row(
        children: [
          CustomCheckbox(
            borderRadius: 5.r,
            activeColor: getColorSkin().colorPrimary,
            value: _filterValues[key] ?? false,
            onChanged: (val) {
              setState(() {
                _filterValues[key] = val;
              });
            },
          ),
          SizedBox(width: 6.w),
          Flexible(
            child: Text(
              label,
              style: TextStyle(fontSize: 14.sp),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, String key) async {
    final DateTime? picked = await CustomDatePicker.show(
      context: context,
      initialDate: _filterValues[key] ?? DateTime.now(),
      firstDate: key == 'endDate'
          ? (_filterValues['startDate'] ?? DateTime(2000))
          : DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      setState(() {
        if (key == 'startDate') {
          _filterValues['startDate'] = picked;
          _filterValues['fromDate'] = DateFormat('dd/MM/yyyy').format(picked);

          final end = _filterValues['endDate'];
          if (end != null && picked.isAfter(end)) {
            _filterValues['endDate'] = null;
            _filterValues['toDate'] = null;
          }
        } else if (key == 'endDate') {
          final start = _filterValues['startDate'];
          if (start != null && picked.isBefore(start)) {
            return;
          }
          _filterValues['endDate'] = picked;
          _filterValues['toDate'] = DateFormat('dd/MM/yyyy').format(picked);
        }
      });
    }
  }

  String _formatDate(DateTime? date, {bool enabled = true}) {
    if (!enabled || date == null) return '';
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildActionButtons() {
    return DeviceUtils.isTablet
        ? _buildDialogStyleButtons()
        : _buildBottomSheetStyleButtons();
  }

  Widget _buildDialogStyleButtons() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(width: 16.w),
            SizedBox(
              width: 65.w,
              height: 32.h,
              child: _buildActionBtnTablet(
                  'Đóng', getColorSkin().red, getColorSkin().white, () {
                if (widget.isFilterTab) {
                  widget.onApplyFilter?.call({
                    ..._filterValues,
                    'silentClose': true,
                  });
                }
                Navigator.pop(context);
              }, isDanger: true),
            ),
            SizedBox(width: 7.w),
            SizedBox(
              width: 133.w,
              height: 32.h,
              child: _buildActionBtnTablet('Đặt về mặc định',
                  getColorSkin().primaryBlue, getColorSkin().white, () {
                setState(() {
                  _resetFilters();
                });
              }),
            ),
            SizedBox(width: 7.w),
            SizedBox(
              width: 89.w,
              height: 32.h,
              child: _buildActionBtnTablet(
                  'Lọc', getColorSkin().white, getColorSkin().primaryBlue, () {
                final startDate = _filterValues['startDate'] as DateTime?;
                final endDate = _filterValues['endDate'] as DateTime?;
                final dateFormatter = DateFormat('dd/MM/yyyy');

                final filterMap = {
                  ..._filterValues,
                  'fromDate': startDate != null
                      ? dateFormatter.format(startDate)
                      : null,
                  'toDate':
                      endDate != null ? dateFormatter.format(endDate) : null,
                };

                widget.onApplyFilter?.call(filterMap);
                Navigator.pop(context);
              }, isPrimary: true),
            ),
            SizedBox(width: 16.w),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetStyleButtons() {
    return SafeArea(
      child: Column(
        children: [
          CustomDivider(color: getColorSkin().ink4),
          Padding(
            padding: EdgeInsets.only(top: 8.h, bottom: 8.h, right: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildActionBtn(
                    'Đóng', getColorSkin().white, getColorSkin().red, () {
                  Navigator.pop(context);
                }, isDanger: true),
                SizedBox(width: 8.w),
                _buildActionBtn('Đặt về mặc định', getColorSkin().white,
                    getColorSkin().primaryBlue, () {
                  setState(() {
                    _resetFilters();
                  });
                  if (widget.isFilterTab) {
                    widget.onApplyFilter?.call(_filterValues);
                  }
                }),
                SizedBox(width: 8.w),
                _buildActionBtn('Lọc', Colors.blue, Colors.white, () {
                  final startDate = _filterValues['startDate'] as DateTime?;
                  final endDate = _filterValues['endDate'] as DateTime?;
                  final dateFormatter = DateFormat('dd/MM/yyyy');

                  final filterMap = {
                    ..._filterValues,
                    'fromDate': startDate != null
                        ? dateFormatter.format(startDate)
                        : null,
                    'toDate':
                        endDate != null ? dateFormatter.format(endDate) : null,
                  };

                  widget.onApplyFilter?.call(filterMap);
                  Navigator.pop(context);
                }, isPrimary: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionBtnTablet(
      String label, Color borderColor, Color textColor, VoidCallback onPressed,
      {bool isPrimary = false, bool isDanger = false}) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor:
            isPrimary ? getColorSkin().primaryBlue : getColorSkin().white,
        foregroundColor: isPrimary
            ? getColorSkin().white
            : (isDanger ? getColorSkin().red : getColorSkin().primaryBlue),
        elevation: 0,
        padding: EdgeInsets.zero,
        minimumSize: Size.fromHeight(32.h),
        side: BorderSide(
          color: isDanger ? getColorSkin().red : getColorSkin().primaryBlue,
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.r),
        ),
      ),
      child: Center(
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildActionBtn(
      String label, Color bg, Color fg, VoidCallback onPressed,
      {bool isPrimary = false, bool isDanger = false}) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: bg,
        foregroundColor: fg,
        elevation: 0,
        side: isPrimary
            ? BorderSide.none
            : BorderSide(
                color:
                    isDanger ? getColorSkin().red : getColorSkin().primaryBlue,
                width: 1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.r),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  void _resetFilters() {
    _filterValues.clear();

    _filterValues['isAssistant'] = false;
    _filterValues['isDiscussion'] = false;
    _filterValues['isNotification'] = false;
    _filterValues['hasDiscussion'] = false;

    _filterValues['startDate'] = null;
    _filterValues['endDate'] = null;

    final dropdownKeys = [
      'listTicketStatus',
      'listService',
      'listAssignee',
      'shareStatus',
      'listChartId',
      'listChartNodeId',
      'listCreatedUser',
      'listTaskPriority',
      'listTaskStatus',
      'listTaskDefKey',
      'listAssignStatus',
      'listAssignedStatus',
      'listApprovalByStatus',
      'listAdditionalStatus',
      'dateType',
    ];
    for (final key in dropdownKeys) {
      _filterValues.remove(key);

      // _filterValues[key] = ['-1'];
    }

    for (final controller in _controllers.values) {
      controller.clear();
    }
    widget.onResetFilter?.call();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    if (maxScroll - currentScroll <= 200) {
      final bloc = context.read<PycBloc>();
      final state = bloc.state;

      if (!state.isPaginating && state.hasMore) {
        final currentPage = (state.serviceListRequestModel?.page ?? 1);
        final nextPage = currentPage + 1;

        bloc.add(
          FetchServiceList(
            requestModel:
                state.serviceListRequestModel?.copyWith(page: nextPage) ??
                    RequestListService(page: nextPage, limit: 10),
            isLoadMore: true,
          ),
        );
      }
    }
  }
}
