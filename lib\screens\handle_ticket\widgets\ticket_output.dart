import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/models/pyc/ticket_process_detail_model.dart';
import 'package:eapprove/screens/common/shimmer_form_loading.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/common/form_v2/form_builder_v2.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';

class TicketOutputBlock extends StatefulWidget {
  final String title;
  final dynamic ticketId;
  final String? taskDefKey;
  final String? user;
  final String status;
  final String? procDefId;
  final dynamic ticket;

  const TicketOutputBlock({
    super.key,
    required this.title,
    required this.ticketId,
    this.taskDefKey,
    this.user,
    required this.status,
    this.procDefId,
    this.ticket,
  });

  @override
  State<TicketOutputBlock> createState() => _TicketOutputBlockState();
}

class _TicketOutputBlockState extends State<TicketOutputBlock> {
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Remove all API calls from here
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasInitialized) {
      _hasInitialized = true;
      context.read<TicketProcessDetailBloc>().add(
            LoadTicketProcessDetail(
              procInstId: TicketUtils.getCorrectTicketId(widget.ticketId),
              taskDefKey: widget.taskDefKey ?? "",
              user: widget.user ?? "",
              status: widget.status,
            ),
          );

      // context.read<FormBloc>().add(
      //   LoadFormRequested(
      //     procDefId: widget.procDefId ?? "",
      //     isCreateTicket: false,
      //   ),
      // );
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
        'FormState: ${context.read<FormBloc>().state.runtimeType}, TicketState: ${context.read<TicketProcessDetailBloc>().state.runtimeType}');
    return _buildFormContent();
  }

  Widget _buildFormContent() {
    return BlocBuilder<FormBloc, FormInfoState>(
      buildWhen: (previous, current) {
        return previous.isLoading != current.isLoading ||
            previous.formResponse != current.formResponse ||
            previous.errorMessage != current.errorMessage;
      },
      builder: (context, formState) {
        debugPrint('data formState: ${formState.formResponse?.data.template.form}');
        return BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
          builder: (context, ticketState) {
            debugPrint('FormState loading: ${formState.isLoading}, TicketState: ${ticketState.runtimeType}');

            if (formState.isLoading || ticketState is TicketProcessDetailLoading) {
              return buildFormShimmerLoading();
            }

            if (formState.errorMessage != null || ticketState is TicketProcessDetailError) {
              return Center(
                child: Text(
                  formState.errorMessage ?? (ticketState as TicketProcessDetailError).message,
                ),
              );
            }

            if (formState.formResponse != null && ticketState is TicketProcessDetailLoaded) {
              return _buildFormWithData(
                formState.formResponse!,
                ticketState.ticketProcessDetail,
              );
            }

            // return const Center(child: Text('No data available'));
            return SizedBox.shrink();
          },
        );
      },
    );
  }

  Widget _buildFormWithData(
    FormResponse formResponse,
    TicketProcessDetailModel ticketProcessDetail,
  ) {
    final formItems = formResponse.data.template.form; // List<FormItemInfo>
    final variables = ticketProcessDetail.data?.listVariables ?? [];

    debugPrint('Form items count: ${formItems.length}');
    debugPrint('Variables count: ${variables.length}');

    final filteredItems = formItems.where((item) {
      final itemName = item.name ?? '';
      if (itemName.isEmpty) return false;

      return variables.any((varItem) => (varItem.name ?? '').contains(itemName));
    }).toList();

    final variableMap = <String, dynamic>{};
    for (var variable in variables) {
      variableMap[variable.name] = variable.value;
    }

    final stateManager = FormStateManager();

    final initialValues = <String, dynamic>{};
    for (var item in filteredItems) {
      if (item.name != null) {
        final matchingVar = variables.firstWhere(
          (varItem) => (varItem.name ?? '').contains(item.name!),
        );

        if (matchingVar.value != null) {
          dynamic processedValue = matchingVar.value;

          if (item.type == 'checkbox' || item.type == 'switch' || item.type == 'boolean') {
            if (processedValue is List) {
              processedValue = processedValue.any((v) => v == true);
            } else if (processedValue is String) {
              processedValue = processedValue.toLowerCase() == 'true';
            } else {
              processedValue = processedValue == true;
            }
          }

          initialValues[item.name!] = processedValue;
        }
      }
    }

    debugPrint('Filtered items count: ${filteredItems.length}');
    debugPrint('Initial values: $initialValues');

    stateManager.setAllFields(initialValues);
    stateManager.setWidgets(filteredItems);

    if (filteredItems.isEmpty) {
      // return const Center(child: Text('No matching form items found'));
      return SizedBox.shrink();
    }

    return FormBuilderV2(
      widgets: filteredItems,
      stateManager: stateManager,
      // onFieldChanged: (fieldName, value) {
      //   debugPrint('Field $fieldName changed to $value');
      //   stateManager.setFieldValue(fieldName, value);
      // },
    );
  }
}
