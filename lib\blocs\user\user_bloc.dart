import 'dart:async';
import 'dart:developer' as developer;
import 'package:bloc/bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/blocs/user/user_state.dart';
import 'package:eapprove/repositories/user_repository.dart';
import 'package:hive_flutter/adapters.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final UserRepository userRepository;
  static const String _boxName = 'user_box';
  static const String _userKeysField = 'user_keys';
  late Box _box;

  UserBloc({required this.userRepository}) : super(UserInitial()) {
    _initBox();
    on<FetchUserInfo>(_onFetchUserInfo);
    on<GetUserInfoByUsername>(_onGetUserInfoByUsernameRequested);
  }

  Future<void> _initBox() async {
    if (!Hive.isBoxOpen(_boxName)) {
      _box = await Hive.openBox(_boxName);
    } else {
      _box = Hive.box(_boxName);
    }
  }

  Future<void> _onFetchUserInfo(FetchUserInfo event, Emitter<UserState> emit) async {
    emit(UserLoading());
    try {
      if (!Hive.isBoxOpen(_boxName)) {
        await _initBox();
      }
      final userInfo = await userRepository.fetchUserInfo();

      await _box.put('userId', userInfo.data.userId ?? "");
      await _box.put('username', userInfo.data.username ?? "");
      await _box.put('positionCode', userInfo.data.positionCode ?? "");
      await _box.put('positionName', userInfo.data.positionName ?? "");
      await _box.put('orgCode', userInfo.data.orgCode ?? "");
      await _box.put('orgName', userInfo.data.orgName ?? "");
      await _box.put('companyCode', userInfo.data.companyCode ?? "");
      await _box.put('companyName', userInfo.data.companyName ?? "");
      await _box.put('logoCompany', userInfo.data.logoCompany ?? "");
      await _box.put('logoCode', userInfo.data.companyCode ?? "");
      await _saveUserKeys();

      developer.log('User info saved to Hive');
      emit(UserLoaded(userInfo));
    } catch (e) {
      developer.log('Error fetching user info: ${e.toString()}');
      emit(UserError(e.toString()));
    }
  }

  Future<void> _saveUserKeys() async {
    if (!_box.isOpen) {
      await _initBox();
    }

    final userKeys = [
      'userId',
      'username',
      'positionCode',
      'positionName',
      'orgCode',
      'orgName',
      'companyCode',
      'companyName',
      'logoCompany',
    ];

    await _box.put(_userKeysField, userKeys);
  }

  Future<Map<String, String>> getUserInfo() async {
    try {
      if (!_box.isOpen) {
        await _initBox();
      }

      final List<dynamic>? userKeys = _box.get(_userKeysField);

      if (userKeys == null || userKeys.isEmpty) {
        return {};
      }

      final Map<String, String> userInfo = {};
      for (final key in userKeys) {
        final value = _box.get(key);
        if (value != null) {
          userInfo[key] = value.toString();
        }
      }

      return userInfo;
    } catch (e) {
      developer.log('Error getting user info: ${e.toString()}');
      return {};
    }
  }

  Future<void> clearUserInfo() async {
    try {
      if (!_box.isOpen) {
        await _initBox();
      }

      final List<dynamic>? userKeys = _box.get(_userKeysField);

      if (userKeys == null || userKeys.isEmpty) {
        return;
      }

      for (final key in userKeys) {
        await _box.delete(key);
      }

      await _box.delete(_userKeysField);
      developer.log('User info cleared from Hive');
    } catch (e) {
      developer.log('Error clearing user info: ${e.toString()}');
    }
  }

  @override
  Future<void> close() {
    if (_box.isOpen) {
      _box.close();
    }
    return super.close();
  }

  Future<void> _onGetUserInfoByUsernameRequested(GetUserInfoByUsername event, Emitter<UserState> emit) async {
    try {
      emit(UserLoading());

      final response = await userRepository.getUserInfoByUserName(
        username: event.username,
      );

      emit(UserInfoByUsername(response));
    } catch (e) {
      developer.log('Error getting user info by username: $e');
      emit(UserError('_onGetUserInfoByUsernameRequested: ${e.toString()}'));
    }
  }
}
