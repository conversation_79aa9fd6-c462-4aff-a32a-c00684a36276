import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_request_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_request_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_request_model.dart';
import 'package:eapprove/screens/ticket_screen/widgets/filter_pyc_screen.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_data_model.dart';

class TabContent extends StatefulWidget {
  final String tabTitle;
  final int filterIndex;
  final Map<String, dynamic> currentFilters;
  final bool useSearchFilterResponse;
  final bool forceReload;
  final Function(dynamic)? onTicketTap;
  final dynamic selectedTicket;
  final VoidCallback? onResetToInitialState;

  const TabContent({
    super.key,
    required this.tabTitle,
    required this.filterIndex,
    this.currentFilters = const {},
    required this.useSearchFilterResponse,
    this.forceReload = false,
    this.onTicketTap,
    this.selectedTicket,
    this.onResetToInitialState,

  });

  @override
  TabContentState createState() => TabContentState();
}

class TabContentState extends State<TabContent> {
  Future<void> refreshTickets() async {
    _currentPage = 1;
    _fetchTicketsForTab();
  }

  late ScrollController _scrollController;
  int _currentPage = 1;
  final int _limit = 10;
  bool _isFetchingMore = false;
  Map<String, dynamic> _previousFilters = {};
  bool _hasReloaded = false;
  final isTablet = DeviceUtils.isTablet;
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController()..addListener(_onScroll);
    _fetchTicketsForTab();

    _previousFilters = Map.from(widget.currentFilters);
  }

  @override
  void didUpdateWidget(TabContent oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.forceReload != oldWidget.forceReload) {
      _hasReloaded = false;
    }

    final shouldReload = widget.forceReload && !_hasReloaded;
    final filterChanged = _previousFilters.toString() != widget.currentFilters.toString();
    final tabChanged = oldWidget.tabTitle != widget.tabTitle || oldWidget.filterIndex != widget.filterIndex;

    if (shouldReload || filterChanged || tabChanged) {
      _previousFilters = Map.from(widget.currentFilters);
      _currentPage = 1;
      _fetchTicketsForTab();
      if (shouldReload) _hasReloaded = true;
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 300) {
      if (!_isFetchingMore && context.read<PycBloc>().state.hasMore) {
        _isFetchingMore = true;
        _currentPage++;
        _fetchTicketsForTab(isLoadMore: true);
      }
    }
  }

  void _fetchTicketsForTab({bool isLoadMore = false}) {
    final isFilterTab = widget.tabTitle.trim().toLowerCase() == 'bộ lọc';
    final searchKeyword = widget.currentFilters['search'] as String? ?? '';

    if (isFilterTab) {
      if (!widget.useSearchFilterResponse && widget.currentFilters['filterId'] == null) {
        return;
      }

      if (widget.useSearchFilterResponse) {
        SearchFilterRequestModel request = SearchFilterRequestModel.fromMap(widget.currentFilters);
        request.page = _currentPage;
        request.limit = _limit;
        request.sortBy = "id";
        request.sortType = "DESC";
        request.type = _mapFilterIndexToType(widget.filterIndex);

        context.read<PycBloc>().add(FetchSearchFilterPyc(request, isLoadMore: isLoadMore));
      } else {
        final request = FilterTicketRequestModel(
          search: "",
          page: _currentPage,
          limit: _limit,
          sortBy: "id",
          sortType: "DESC",
          type: _mapFilterIndexToType(widget.filterIndex),
          filterId: widget.currentFilters['filterId'] as int?,
        );
        context.read<PycBloc>().add(FetchTicketFilter(request, isLoadMore: isLoadMore));
      }
      return;
    }

    final myCategory = _getMyPycCategoryFromTitle(widget.tabTitle);
    final procCategory = _getProcCategoryFromTitle(widget.tabTitle);
    final appCategory = _getAppPycCategoryFromTitle(widget.tabTitle);
    final assisCategory = _getAssisCategoryFromTitle(widget.tabTitle);

    if (widget.filterIndex == 1) {
      var model = ProcRequestModel.fromCategory(procCategory, page: _currentPage, limit: _limit);
      if (widget.currentFilters.isNotEmpty) {
        model = FilterPycBottomSheet.applyFilter(widget.currentFilters, model);
      }
      context.read<PycBloc>().add(FetchProcPycList(requestModel: model, isLoadMore: isLoadMore));
    } else if (widget.filterIndex == 2) {
      var model = ApproveRequestModel.fromCategory(appCategory, page: _currentPage, limit: _limit);
      if (widget.currentFilters.isNotEmpty) {
        model = FilterPycBottomSheet.applyFilter(widget.currentFilters, model);
        debugPrint("Current filters: ${widget.currentFilters}");
      }
      context.read<PycBloc>().add(FetchAppPycList(requestModel: model, isLoadMore: isLoadMore));
    } else if (widget.filterIndex == 3) {
      var model = AssisRequestModel.fromCategory(assisCategory, page: _currentPage, limit: _limit);
      if (widget.currentFilters.isNotEmpty) {
        model = FilterPycBottomSheet.applyFilter(widget.currentFilters, model);
      }
      context.read<PycBloc>().add(FetchAssisPycList(requestModel: model, isLoadMore: isLoadMore));
    } else {
      var model = MyPycRequestModel.fromCategory(myCategory,
          page: _currentPage, limit: _limit, search: searchKeyword);
      if (widget.useSearchFilterResponse && searchKeyword.isNotEmpty) {
        model = model.copyWith(search: searchKeyword); 
      }
      if (widget.currentFilters.isNotEmpty) {
        model = FilterPycBottomSheet.applyFilter(widget.currentFilters, model);
      }
      context.read<PycBloc>().add(FetchMyPycList(requestModel: model, isLoadMore: isLoadMore));
    }
  }

  String? _mapFilterIndexToType(int index) {
    switch (index) {
      case 0:
        return 'ticket';
      case 1:
        return 'execution';
      case 2:
        return 'approval';
      case 3:
        return 'assistant';
      default:
        return null;
    }
  }

  MyPycTicketCategory _getMyPycCategoryFromTitle(String tabTitle) {
    switch (tabTitle.trim().toLowerCase()) {
      case 'đang phê duyệt':
        return MyPycTicketCategory.approving;
      case 'hoàn thành':
        return MyPycTicketCategory.completed;
      case 'trả về/thu hồi':
        return MyPycTicketCategory.returned;
      case 'hủy':
        return MyPycTicketCategory.canceled;
      case 'nháp':
        return MyPycTicketCategory.draft;
      case 'được chia sẻ/theo dõi':
        return MyPycTicketCategory.sharedFollowing;
      case 'đã chia sẻ':
        return MyPycTicketCategory.shared;
      default:
        return MyPycTicketCategory.approving;
    }
  }

  ProcPycTicketCategory _getProcCategoryFromTitle(String tabTitle) {
    switch (tabTitle.trim().toLowerCase()) {
      case 'xử lý':
        return ProcPycTicketCategory.execution;
      case 'hoàn thành':
        return ProcPycTicketCategory.completed;
      case 'trả về/thu hồi':
        return ProcPycTicketCategory.returned;
      case 'hủy':
        return ProcPycTicketCategory.canceled;
      default:
        return ProcPycTicketCategory.execution;
    }
  }

  ApproveTicketCategory _getAppPycCategoryFromTitle(String tabTitle) {
    switch (tabTitle.trim().toLowerCase()) {
      case 'đang phê duyệt':
      case 'đang chờ phê duyệt':
        return ApproveTicketCategory.approval;
      case 'đã phê duyệt':
        return ApproveTicketCategory.approved;
      case 'trả về/thu hồi':
        return ApproveTicketCategory.returned;
      case 'hủy':
        return ApproveTicketCategory.canceled;
      default:
        return ApproveTicketCategory.approval;
    }
  }

  AssisPycTicketCategory _getAssisCategoryFromTitle(String tabTitle) {
    switch (tabTitle.trim().toLowerCase()) {
      case 'xử lý':
        return AssisPycTicketCategory.execution;
      case 'hoàn thành':
        return AssisPycTicketCategory.completed;
      case 'hủy':
        return AssisPycTicketCategory.canceled;
      case 'theo dõi':
        return AssisPycTicketCategory.sharedFollowing;
      default:
        return AssisPycTicketCategory.execution;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PycBloc, PycState>(
      listener: (context, state) {
        if (!state.isPaginating) {
          _isFetchingMore = false;
        }
      },
      builder: (context, state) {
        final isFilterTab = widget.tabTitle.trim().toLowerCase() == 'bộ lọc';
        final isFirstPage = _currentPage == 1;

        if (isFilterTab && isFirstPage) {
          final isApiInitial = widget.useSearchFilterResponse
              ? state.searchFilterStatus == PycStatus.initial
              : state.ticketFilterStatus == PycStatus.initial;
          if (isApiInitial && widget.currentFilters['filterId'] != null) {
            return _buildShimmer();
          }
        }

        if (isFilterTab && !widget.useSearchFilterResponse && widget.currentFilters['filterId'] == null) {
          return _buildEmptyResult();
        }

        final isFirstLoad = _currentPage == 1;
        final isLoading = isFilterTab
            ? (widget.useSearchFilterResponse
                ? state.searchFilterStatus == PycStatus.loading && isFirstLoad
                : state.ticketFilterStatus == PycStatus.loading && isFirstLoad)
            : state.status == PycStatus.loading && isFirstLoad;

        if (isLoading) {
          return _buildShimmer();
        }

        List<dynamic> tickets;
        if (isFilterTab) {
          tickets = widget.useSearchFilterResponse
              ? state.searchFilterResponseModel?.data.content ?? []
              : state.ticketFilterResponseModel?.data.content ?? [];
        } else if (widget.useSearchFilterResponse && widget.filterIndex == 0) {
          tickets = state.myPycResponseModel?.data.content ?? []; // Sử dụng myPycResponseModel cho tìm kiếm MyPyc
        } else {
          tickets = widget.filterIndex == 1
              ? _filterProcTicketsByStatus(state)
              : widget.filterIndex == 2
                  ? _filterAppTicketsByStatus(state)
                  : widget.filterIndex == 3
                      ? _filterAssisTicketsByStatus(state)
                      : _filterMyPycTicketsByStatus(state);
        }

        if (tickets.isEmpty && !state.isPaginating) {
          return _buildEmptyResult(isFilterTab);
        }

        return RefreshIndicator(
          backgroundColor: getColorSkin().white,
          color: getColorSkin().primaryBlue,
          onRefresh: () async {
            if (widget.onResetToInitialState != null && (widget.currentFilters['search']?.toString().isNotEmpty ?? false)) {
              widget.onResetToInitialState!();
            } else {
              _currentPage = 1;
              _fetchTicketsForTab();
            }
          },
          child: ListView.builder(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: tickets.length + (state.isPaginating ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == tickets.length && state.isPaginating) {
                return Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(child: AppConstraint.buildLoading(context)),
                );
              }

              final item = tickets[index];
              if (item is SearchFilterContentModel) {
                return _buildSearchFilterTicketCard(item);
              }
              if (item is ProcContent) return _buildTicketCard(item);
              if (item is ApproveContent) return _buildTicketCard(item);
              if (item is AssisContent) return _buildTicketCard(item);
              if (item is MyPycContent) return _buildTicketCard(item);
              if (item is TicketContentModel) return _buildTicketCard(item);
              return Container();
            },
          ),
        );
      },
    );
  }

  Widget _buildShimmer() {
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (_, __) => AppConstraint.buildShimmer(
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 20.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(height: 4.h),
              Container(
                width: 150.w,
                height: 14.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Container(
                    width: 16.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(width: 6.w),
                  Container(
                    width: 80.w,
                    height: 12.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: 80.w,
                    height: 24.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<MyPycContent> _filterMyPycTicketsByStatus(PycState state) {
    return state.myPycResponseModel?.data.content ?? [];
  }

  List<ProcContent> _filterProcTicketsByStatus(PycState state) {
    return state.procPycResponseModel?.data.content ?? [];
  }

  List<ApproveContent> _filterAppTicketsByStatus(PycState state) {
    return state.appPycResponseModel?.data.content ?? [];
  }

  List<AssisContent> _filterAssisTicketsByStatus(PycState state) {
    return state.assisPycResponseModel?.data.content ?? [];
  }

  Widget _buildTicketCard(dynamic item) {
    String title;
    String serviceName;
    int? createdTime;
    String? status;
    String? sharedUser;

    if (item is MyPycContent) {
      title = item.ticketTitle ?? "Không có tiêu đề";
      serviceName = item.procServiceName ?? "Không có dịch vụ";
      createdTime = item.ticketCreatedTime;
      status = item.ticketStatus;
      sharedUser = item.sharedUser ?? "Không có người chia sẻ";
    } else if (item is ProcContent) {
      title = item.procTitle ?? "Không có tiêu đề";
      serviceName = item.serviceName ?? "Không có dịch vụ";
      createdTime = item.ticketCreatedTime;
      status = item.ticketStatus;
    } else if (item is ApproveContent) {
      title = item.procTitle ?? "Không có tiêu đề";
      serviceName = item.serviceName ?? "Không có dịch vụ";
      createdTime = item.ticketCreatedTime;
      status = item.ticketStatus;
    } else if (item is AssisContent) {
      title = item.ticketTitle ?? "Không có tiêu đề";
      serviceName = item.procServiceName ?? "Không có dịch vụ";
      createdTime = item.ticketCreatedTime;
      status = item.ticketStatus;
    } else if (item is TicketContentModel) {
      title = item.ticketTitle ?? "Không có tiêu đề";
      serviceName = item.serviceName ?? "Không có dịch vụ";
      createdTime = item.ticketCreatedTime;
      status = item.ticketStatus;
    } else {
      return Container();
    }
    final bool isSelected = widget.selectedTicket != null && item.id == widget.selectedTicket.id;
    final card = GestureDetector(
      onTap: () {
        if (widget.onTicketTap != null) {
          widget.onTicketTap!(item);
        }
      },
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
        padding:
            isTablet ? EdgeInsets.fromLTRB(8.w, 8.h, 8.w, 8.h) : EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        width: 343.w,
        decoration: BoxDecoration(
          color: isSelected ? getColorSkin().blue2 : getColorSkin().white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w700,
                color: const Color(0xff323232),
              ),
            ),
            if (isTablet) SizedBox(height: 8.h),
            Text(serviceName,
                overflow: TextOverflow.ellipsis,
                style: getTypoSkin().buttonText3Regular.copyWith(
                      color: Color(0xff777B7E),
                    )),
            if (widget.tabTitle.trim().toLowerCase() == 'được chia sẻ/theo dõi' && sharedUser != null)
              Padding(
                padding: EdgeInsets.only(top: isTablet ? 8.h : 4.h),
                child: Row(
                  children: [
                    SvgPicture.asset(
                      StringImage.ic_shared_user,
                      width: 16.w,
                      height: 16.h,
                    ),
                    SizedBox(width: 6.w),
                    Expanded(
                      child: Text(
                        sharedUser,
                        overflow: TextOverflow.ellipsis,
                        style: getTypoSkin().buttonText3Regular.copyWith(
                              color: const Color(0xff777B7E),
                            ),
                        maxLines: 2,
                      ),
                    ),
                  ],
                ),
              ),
            if (isTablet) SizedBox(height: 8.h) else SizedBox(height: 4.h),
            if (createdTime != null || status != null)
              LayoutBuilder(
                builder: (context, constraints) {
                  return Row(
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            StringImage.ic_calendar,
                            width: 16.w,
                            height: 16.h,
                          ),
                          SizedBox(width: 6.w),
                          ConstrainedBox(
                            constraints: BoxConstraints(
                              maxWidth: constraints.maxWidth * 0.5,
                            ),
                            child: Text(
                              _formatDate(createdTime),
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w400,
                                color: const Color(0xff8C8C8C),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(width: 6.w),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: _buildStatusBadge(status),
                        ),
                      ),
                    ],
                  );
                },
              ),
          ],
        ),
      ),
    );

    if (widget.tabTitle.trim().toLowerCase() == 'nháp') {
      return Slidable(
        key: ValueKey(title),
        closeOnScroll: true,
        endActionPane: ActionPane(
          motion: const DrawerMotion(),
          extentRatio: 0.15,
          children: [
            CustomSlidableAction(
              onPressed: (_) async {
                final id = item.id;
                debugPrint("🧾 TICKET TO DELETE: $id");
                if (id == null) return;
                if (!mounted) return;

                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (dialogContext) => BlocProvider.value(
                    value: BlocProvider.of<PycBloc>(context),
                    child: CustomDialog(
                      title: "Xác nhận xóa",
                      content: "Bạn có chắc chắn muốn xóa nháp này không?",
                      onConfirm: () {
                        if (!mounted) return;
                        context.read<PycBloc>().add(DeleteDraftTicket(id, context));
                        Navigator.of(dialogContext).pop();
                      },
                      onCancel: () {
                        Navigator.of(dialogContext).pop();
                      },
                      cancelButtonText: "Hủy",
                      confirmButtonText: "Xóa",
                      confirmButtonColor: getColorSkin().colorError,
                      confirmButtonTextStyle: getTypoSkin().buttonText3Regular.copyWith(
                            color: getColorSkin().white,
                          ),
                      cancelButtonColor: getColorSkin().secondaryText,
                      cancelButtonTextStyle: getTypoSkin().buttonText3Regular.copyWith(
                            color: getColorSkin().white,
                          ),
                    ),
                  ),
                );
              },
              backgroundColor: Colors.transparent,
              padding: EdgeInsets.zero,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return ConstrainedBox(
                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                    child: Container(
                      margin: EdgeInsets.only(right: 12.w),
                      width: 54.w,
                      decoration: BoxDecoration(
                        color: getColorSkin().red2,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        StringImage.ic_red_trash,
                        width: 24.w,
                        height: 24.h,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            if (widget.onTicketTap != null) {
              widget.onTicketTap!(item);
            }
          },
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xff323232),
                      ),
                    ),
                    Text(
                      serviceName,
                      overflow: TextOverflow.ellipsis,
                      style: getTypoSkin().buttonText3Regular.copyWith(
                            color: const Color(0xff777B7E),
                          ),
                    ),
                    Row(
                      children: [
                        SvgPicture.asset(
                          StringImage.ic_calendar,
                          width: 16.w,
                          height: 16.h,
                        ),
                        SizedBox(width: 6.w),
                        Text(
                          _formatDate(createdTime),
                          style: getTypoSkin().buttonText3Regular.copyWith(
                                color: getColorSkin().secondaryText,
                              ),
                        ),
                        const Spacer(),
                        _buildStatusBadge(status),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );
    }

    return card;
  }

  String _formatDate(int? timestamp) {
    if (timestamp == null) return "Không có ngày";
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat("dd-MM-yyyy").format(date);
  }

  Widget _buildStatusBadge(String? status) {
    if (status == null) return Container();

    final blocState = context.read<PycBloc>().state;
    final statusText = blocState.getStatusLabel(status) ?? "Không xác định";
    final textColor = blocState.getStatusColor(status);
    final bgColor = blocState.getStatusBackgroundColor(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      // constraints: isTablet
      //     ? null
      //     : BoxConstraints(
      //         maxWidth: 230.w,
      //       ),
      child: Text(
        maxLines: 1,
        overflow: isTablet ? TextOverflow.visible : TextOverflow.ellipsis,
        statusText,
        style: getTypoSkin().buttonText3Regular.copyWith(
              color: textColor,
            ),
      ),
    );
  }

  Widget _buildSearchFilterTicketCard(SearchFilterContentModel item) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.ticketTitle ?? "Không có tiêu đề",
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w700,
              color: const Color(0xff323232),
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            item.serviceName ?? "Không có dịch vụ",
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: const Color(0xff777B7E),
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              SvgPicture.asset(
                StringImage.ic_calendar,
                width: 16.w,
                height: 16.h,
              ),
              SizedBox(width: 6.w),
              Text(
                Utils.formatDate(item.ticketCreatedTime),
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xff8C8C8C),
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: Align(
                  alignment: Alignment.centerRight,
                  child: _buildStatusBadge(item.ticketStatus),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyResult([bool isFilterTab = false]) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(StringImage.ic_no_result),
          SizedBox(height: 8),
          Text(
            isFilterTab ? 'Không có dữ liệu phù hợp với bộ lọc' : 'Không có phiếu',
            style: TextStyle(fontSize: 16.sp, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
