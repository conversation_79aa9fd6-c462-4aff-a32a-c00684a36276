class FormRequestBody {
  final String procInstId;
  final String taskDefinitionKey;
  final String urlName;

  FormRequestBody({
    required this.procInstId,
    required this.taskDefinitionKey,
    required this.urlName,
  });

  factory FormRequestBody.fromJson(Map<String, dynamic> json) {
    return FormRequestBody(
      procInstId: json['procInstId'] ?? '',
      taskDefinitionKey: json['taskDefinitionKey'] ?? '',
      urlName: json['urlName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'procInstId': procInstId,
      'taskDefinitionKey': taskDefinitionKey,
      'urlName': urlName,
    };
  }
}
