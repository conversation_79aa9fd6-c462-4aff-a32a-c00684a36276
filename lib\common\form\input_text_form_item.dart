import 'package:eapprove/common/form/base_form_item.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InputTextFormItem extends BaseFormItem {
  const InputTextFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.h),
      child: TextFormField(
        initialValue: data.value?.toString(),
        decoration: InputDecoration(
          labelText: data.label,
          hintText: data.placeholder,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        onChanged: (value) => onChange(data.name ?? '', value),
        enabled: data.readonly != true,
        maxLength: data.maxLength?.toInt(),
        keyboardType: _getKeyboardType(),
        textInputAction: TextInputAction.next,
      ),
    );
  }

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() {
    if (data.validations == null) return true;
    final value = data.value?.toString() ?? '';
    // Check required validation
    if (data.validations!['required'] == true && value.isEmpty) {
      return false;
    }

    // Check min length
    if (data.validations!['minlength'] != null &&
        value.length < data.validations!['minlength']) {
      return false;
    }
    // Check max length
    if (data.validations!['maxlength'] != null &&
        value.length > data.validations!['maxlength']) {
      return false;
    }
    return true;
  }

  @override
  void reset() {
    onChange(data.name ?? '', null);
  }

  @override
  void setValue(dynamic value) {
    onChange(data.name ?? '', value);
  }

  TextInputType _getKeyboardType() {
    switch (data.inputType) {
      case 'number':
        return TextInputType.number;
      case 'email':
        return TextInputType.emailAddress;
      case 'phone':
        return TextInputType.phone;
      case 'url':
        return TextInputType.url;
      default:
        return TextInputType.text;
    }
  }
}
