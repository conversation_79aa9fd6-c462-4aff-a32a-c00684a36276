class TicketTask {
  final int? id;
  final String? taskId;
  final String? taskDefKey;
  final String? taskName;
  final String? taskAssignee;
  final int? taskCreatedTime;
  final int? taskStartedTime;
  final String? taskStatus;
  final String? procDefId;
  final String? taskType;
  final String? taskProcDefId;

  TicketTask(
      {this.id,
      this.taskId,
      this.taskDefKey,
      this.taskName,
      this.taskAssignee,
      this.taskCreatedTime,
      this.taskStartedTime,
      this.taskStatus,
      this.procDefId,
      this.taskType,
      this.taskProcDefId});
  factory TicketTask.fromJson(Map<String, dynamic> json) {
    return TicketTask(
      id: json['id'],
      taskId: json['taskId'],
      taskDefKey: json['taskDefKey'],
      taskName: json['taskName'],
      taskAssignee: json['taskAssignee'],
      taskCreatedTime: json['taskCreatedTime'],
      taskStartedTime: json['taskStartedTime'],
      taskStatus: json['taskStatus'],
      procDefId: json['procDefId'],
      taskType: json['taskType'],
      taskProcDefId: json['taskProcDefId'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'taskDefKey': taskDefKey,
      'taskName': taskName,
      'taskAssignee': taskAssignee,
      'taskCreatedTime': taskCreatedTime,
      'taskStartedTime': taskStartedTime,
      'taskStatus': taskStatus,
      'procDefId': procDefId,
      'taskType': taskType,
      'taskProcDefId': taskProcDefId,
    };
  }
}
