import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/screens/authorize_management/widgets/authorize_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SearchResults extends StatefulWidget {
  final String searchQuery;
  final Function(String) onResultSelected;

  const SearchResults(
      {super.key, required this.searchQuery, required this.onResultSelected});

  @override
  State<SearchResults> createState() => _SearchResultsState();
}

class _SearchResultsState extends State<SearchResults> {
  List<AuthorizeItemData> allRequests = [
    // {
    //   'id': 'TTR-24110988 - Ủy quyền đề nghị thanh toán',
    //   'title': 'EEPT04 -  Đề nghị thanh toán',
    //   'date': '24/12/2024',
    //   'director': '<PERSON><PERSON><PERSON><PERSON>',
    //   'directorTitle': '<PERSON><PERSON><PERSON><PERSON> đốc phân tích',
    //   'analyst': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    //   'analystTitle': 'Chuyên viên phân tích',
    //   'status': 'in_progress'
    // },
    // {
    //   'id': 'TTR-24110989 - Ủy quyền đề nghị thanh toán',
    //   'title': 'EEPT04 -  Đề nghị thanh toán',
    //   'date': '24/12/2024',
    //   'director': 'Nguyễn Thanh Phong',
    //   'directorTitle': 'Giám đốc phân tích',
    //   'analyst': 'Trương Đức Duy',
    //   'analystTitle': 'Chuyên viên phân tích',
    //   'status': 'in_progress'
    // },
    // {
    //   'id': 'TTR-24110990 - Ủy quyền đề nghị thanh toán',
    //   'title': 'EEPT04 -  Đề nghị thanh toán',
    //   'date': '24/12/2024',
    //   'director': 'Nguyễn Thanh Phong',
    //   'directorTitle': 'Giám đốc phân tích',
    //   'analyst': 'Trương Đức Duy',
    //   'analystTitle': 'Chuyên viên phân tích',
    //   'status': 'completed'
    // },
  ];

  void _handleResultSelection(AuthorizeItemData request) {
    final String fullInfo = '${request.assignName}';

    String processedInfo = fullInfo;

    if (fullInfo.contains('-')) {
      final parts = fullInfo.split('-');
      if (parts.length > 1) {
        processedInfo = fullInfo;
      }
    }
    widget.onResultSelected(processedInfo);
  }

  List<AuthorizeItemData> get filteredRequests {
    if (widget.searchQuery.isEmpty) {
      return [];
    }

    final query = widget.searchQuery.toLowerCase();

    return allRequests.where((request) {
      final id = '${request.assignName}'.toLowerCase();
      final title = '${request.assignName}'.toLowerCase();
      final director = '${request.assignUser}'.toLowerCase();
      final analyst = '${request.assignedUser}'.toLowerCase();

      bool directMatch = id.contains(query) ||
          title.contains(query) ||
          director.contains(query) ||
          analyst.contains(query);

      bool hyphenMatch = false;

      if (id.contains('-')) {
        final parts = id.split('-');
        if (parts.length > 1) {
          final afterHyphen = parts[1].trim().toLowerCase();
          hyphenMatch = afterHyphen.contains(query);
        }
      }

      if (title.contains('-')) {
        final parts = title.split('-');
        if (parts.length > 1) {
          final afterHyphen = parts[1].trim().toLowerCase();
          hyphenMatch = hyphenMatch || afterHyphen.contains(query);
        }
      }

      return directMatch || hyphenMatch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.searchQuery.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Text(
          'Vui lòng nhập từ khóa để tìm kiếm',
          style: getTypoSkin()
              .medium16
              .copyWith(color: getColorSkin().secondaryText),
        ),
      );
    }

    if (filteredRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: 40.h),
            Icon(
              Icons.search_off_outlined,
              size: 48,
              color: getColorSkin().secondaryText,
            ),
            SizedBox(height: 16.h),
            Text(
              'Không tìm thấy kết quả phù hợp',
              style: getTypoSkin()
                  .medium16
                  .copyWith(color: getColorSkin().secondaryText),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Kết quả tìm kiếm',
            style: getTypoSkin()
                .medium16
                .copyWith(color: getColorSkin().primaryText),
          ),
          ...filteredRequests.map((request) => Container(
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: getColorSkin().ink5, width: 0.5),
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: PaymentRequestCard(
                  request: request,
                  onTap: (p0) => {_handleResultSelection(p0)},
                ),
              )),
        ],
      ),
    );
  }
}
