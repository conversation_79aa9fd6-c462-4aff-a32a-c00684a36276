import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Events
abstract class AppThemeEvent extends Equatable {
  const AppThemeEvent();

  @override
  List<Object> get props => [];
}

class ToggleTheme extends AppThemeEvent {}

// State
class AppThemeState extends Equatable {
  final bool isDarkMode;

  const AppThemeState({this.isDarkMode = false});

  AppThemeState copyWith({bool? isDarkMode}) {
    return AppThemeState(
      isDarkMode: isDarkMode ?? this.isDarkMode,
    );
  }

  @override
  List<Object> get props => [isDarkMode];
}

// Bloc
class AppThemeBloc extends Bloc<AppThemeEvent, AppThemeState> {
  AppThemeBloc() : super(const AppThemeState()) {
    on<ToggleTheme>((event, emit) {
      emit(state.copyWith(isDarkMode: !state.isDarkMode));
    });
  }
} 