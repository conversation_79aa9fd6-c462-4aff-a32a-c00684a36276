import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/sign_print/sign_print_bloc.dart';
import 'package:eapprove/blocs/sign_print/sign_print_event.dart';
import 'package:eapprove/blocs/sign_print/sign_print_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/common/form_v2/form_builder_v2.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';
import 'package:eapprove/models/form/upload_file_response_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/screens/payment_step/widgets/handle_modal.dart';
import 'package:eapprove/utils/inherit_dialog_helper.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:async';
import 'dart:developer' as developer;
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:flutter_sdk/utils/device_utils.dart';

class UploadFileWithField {
  final UploadSingleFileResponseModel response;
  final String fieldName;

  UploadFileWithField(this.response, this.fieldName);
}

class ApproveTab extends StatefulWidget {
  final bool isDone;
  final bool isSigned;
  final bool isInPerformPhase;
  final bool? isNotForHandleModal;
  final bool hideButtonsOnTablet;
  final dynamic ticket;
  final VoidCallback? onApproveSuccess;
  final Function(VoidCallback? saveDraft, VoidCallback? approve)? onCallbacksReady;
  final Function(bool isProcessing)? onProcessingStateChanged;
  const ApproveTab({
    super.key,
    required this.isDone,
    required this.isSigned,
    required this.isInPerformPhase,
    this.isNotForHandleModal,
    this.hideButtonsOnTablet = false,
    required this.ticket,
    this.onApproveSuccess,
    this.onCallbacksReady,
    this.onProcessingStateChanged,
  });

  @override
  State<ApproveTab> createState() => _ApproveTabState();
}

class _ApproveTabState extends State<ApproveTab> {
  late Box box;
  late String? username;
  List<dynamic> listSignForm = [];
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  dynamic id;
  dynamic taskId;
  List<int> startPermission = [];
  TextEditingController approveContent = TextEditingController();
  String? _approveContentError;
  bool _isFormInitialized = false;
  Map<String, dynamic>? _lastApproveBody;

  bool _isProcessingApproval = false;
  bool _requiresSignature = false;
  bool _isExecutionMode = false;

  // Add form management properties
  late FormStateManager _formStateManager;
  FormResponse? _formResponse;
  bool _isLoading = false;
  bool _hasInitializedForm = false;

  // Store draft variables để map sau khi form ready
  List<dynamic>? _pendingDraftVariables;

  @override
  void initState() {
    _isExecutionMode = widget.ticket is ProcContent;

    box = Hive.box('authentication');
    username = box.get('username', defaultValue: 'employee');

    // Initialize form state manager
    _formStateManager = FormStateManager();
    _formStateManager.initBloc(
      context.read<DropdownBloc>(),
      context.read<FormBloc>(),
    );

    context.read<TicketProcessDetailBloc>().add(LoadTicketProcessDetail(
          procInstId: TicketUtils.getProcInstId(widget.ticket) ?? '',
          taskDefKey: TicketUtils.getTaskDefKey(widget.ticket) ?? '',
          user: username ?? 'employee',
        ));
    context.read<UserBloc>().add(FetchUserInfo());
    context.read<TicketProcessDetailBloc>().add(LoadTicketProcessDetail(
        procInstId: TicketUtils.getProcInstId(widget.ticket) ?? '',
        taskDefKey: TicketUtils.getTaskDefKey(widget.ticket) ?? '',
        user: username ?? 'employee'));

    // Setup callbacks cho HandleModal nếu có
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onCallbacksReady != null) {
        widget.onCallbacksReady!(_handleSaveDraft, _handleApprove);
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    _formStateManager.dispose();
    super.dispose();
  }

  Future<void> _loadFormTemplate() async {
    if (_isFormInitialized || _hasInitializedForm) return;
    _hasInitializedForm = true;
    setState(() => _isLoading = true);

    try {
      final processId = int.tryParse(widget.ticket.ticketId?.toString() ?? '') ?? 0;
      context.read<FormBloc>().add(LoadFormRequested(
            procDefId: widget.ticket.ticketProcDefId ?? '',
            processId: processId,
            isCreateTicket: false,
          ));

      context.read<FormBloc>().stream.firstWhere((state) => !state.isLoading).then((state) {
        if (state.formResponse != null && state.formResponse!.code == 1) {
          setState(() {
            _formResponse = state.formResponse;
            _isFormInitialized = true;
            _isLoading = false;
          });
          _initializeForm(state);
        }
      });
    } catch (e) {
      setState(() => _isLoading = false);
      SnackbarCore.error('Đã xảy ra lỗi: ${e.toString()}');
    }
  }

  void _initializeForm(FormInfoState formState) {
    if (_isFormInitialized && _formResponse != null) return;

    final formResponse = _formResponse ?? formState.formResponse;
    if (formResponse == null || formResponse.data == null) {
      return;
    }

    final widgets = formResponse.data.template.form;
    final chartData = formState.chartData;
    final chartNodeCustomData = formState.chartNodeCustomData;
    final chartNodeData = formState.chartNodeData;

    final initialValues = <String, dynamic>{
      'system_chartId_service': chartData?.data.first.id,
      'system_chartNodeCustomId': chartNodeCustomData?.data.data.first.id,
      'system_chartCustomId': chartNodeCustomData?.data.data.first.chartId,
      'system_chartNodeId_ticket': chartNodeData?.data.first.chartNodeId,
    };

    final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket) ?? '';
    _formStateManager.setTaskDefKey(taskDefKey);

    for (var formWidget in widgets) {
      if (formWidget.name == 'dtm_ngayLapPhieu') {
        initialValues[formWidget.name.toString()] = Utils.convertToUtcIso(DateTime.now().toIso8601String());
      } else if (formWidget.name != 'slt_logo') {
        initialValues[formWidget.name.toString()] = formWidget.value;
      }
      if (formWidget.autoGenId == true) {
        initialValues[formWidget.name.toString()] = formWidget.autoGenIdValue;
      }
    }

    _formStateManager.setAllFields(initialValues);
    _formStateManager.setWidgets(widgets);
    setState(() => _isFormInitialized = true);

    // Map pending draft variables nếu có
    if (_pendingDraftVariables != null) {
      developer.log('Mapping pending draft variables after form initialization');
      _mapDraftVariablesToForm(_pendingDraftVariables!);
      _pendingDraftVariables = null; // Clear sau khi đã map
    }
  }

  bool _validateApproveContent() {
    final value = approveContent.text;
    if (value.trim().isEmpty) {
      setState(() {
        _approveContentError = 'Vui lòng nhập nội dung phê duyệt';
      });
      return false;
    }
    setState(() {
      _approveContentError = null;
    });
    return true;
  }

  void _showInheritDialog() async {
    try {
      final result = await InheritDialogHelper.showInheritDialog(
        context: context,
        ticket: widget.ticket,
      );

      if (result == true && mounted) {
        SnackbarCore.success('Kế thừa thành công');

        // Có thể reload form hoặc refresh data nếu cần
        // _loadFormTemplate();
      }
    } catch (e) {
      developer.log('Error showing inherit dialog: $e');
      if (mounted) {
        SnackbarCore.error('Lỗi khi mở dialog kế thừa: $e');
      }
    }
  }

  void _handleSaveDraft() async {
    try {
      if (!formKey.currentState!.validate()) {
        SnackbarCore.error('Vui lòng nhập đầy đủ thông tin');
        return;
      }

      await _handleFileUploads();

      final variables = _transformFormValuesToVariables();

      final approveComment = approveContent.text;
      if (approveComment.isNotEmpty) {
        final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket);
        final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
        variables['$prefix${taskDefKey}_attachmentSignComment'] = {
          "value": approveComment,
          "type": "String",
          "valueInfo": {}
        };
      }

      final currentTaskId = taskId?.toString() ?? TicketUtils.getTaskId(widget.ticket)?.toString();
      final currentProcInstId =
          widget.ticket.procInstId?.toString() ?? TicketUtils.getProcInstId(widget.ticket)?.toString();

      if (currentTaskId == null || currentProcInstId == null) {
        SnackbarCore.error('Không thể lấy thông tin phiếu');
        return;
      }

      context.read<TicketProcessDetailBloc>().add(SaveDraft(
            taskId: currentTaskId,
            procInstId: currentProcInstId,
            variables: variables,
          ));
    } catch (e) {
      developer.log('Error saving draft: $e');
      if (mounted) {
        SnackbarCore.error('Lỗi khi lưu nháp: $e');
      }
    }
  }

  void _mapDraftVariablesToForm(List<dynamic> draftVariables) {
    try {
      final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket) ?? '';
      final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';

      for (var draftVar in draftVariables) {
        if (draftVar is Map<String, dynamic>) {
          final String varName = draftVar['name'] ?? '';
          final String varValue = draftVar['value'] ?? '';
          final String varType = draftVar['type'] ?? 'STRING';
          final String? additionalVal = draftVar['additionalVal'];


          String fieldName = '';

          if (varName.startsWith('$prefix$taskDefKey\_')) {
            fieldName = varName.replaceFirst('$prefix$taskDefKey\_', '');
          } else if (varName.startsWith('system_')) {
            fieldName = varName;
          } else if (varName.startsWith('${taskDefKey}_')) {
            fieldName = varName.replaceFirst('${taskDefKey}_', '');
          } else if (varName.contains('_')) {
            final parts = varName.split('_');
            if (parts.length > 1) {
              fieldName = varName;
            }
          }

          if (fieldName.isNotEmpty) {
            if (fieldName.endsWith('attachmentSignComment')) {
              setState(() {
                approveContent.text = varValue;
              });
            } else {
              // Map vào form field
              _handleDraftFormField(fieldName, varValue, varType, additionalVal);
            }
          } else {
            developer.log('❌ Could not map field: $varName (no matching pattern)');
          }
        }
      }

      setState(() {});
      developer.log('=== DRAFT MAPPING COMPLETED ===');
    } catch (e) {
      developer.log('Error mapping draft variables to form: $e');
    }
  }

  void _handleDraftFormField(String fieldName, dynamic value, String? type, String? additionalVal) {
    try {

      switch (type?.toUpperCase()) {
        case "FILE":
          _handleDraftFileField(fieldName, additionalVal);
          break;
        case "JSON":
          if (value is String && value.isNotEmpty) {
            try {
              final jsonValue = json.decode(value);
              _formStateManager.setFieldValue(fieldName, jsonValue);
            } catch (e) {
              _formStateManager.setFieldValue(fieldName, value);
            }
          }
          break;
        default:
          _formStateManager.setFieldValue(fieldName, value);
          break;
      }
    } catch (e) {
      developer.log('❌ Error handling draft form field $fieldName: $e');
    }
  }

  void _handleDraftFileField(String fieldName, String? additionalVal) {
    try {
      if (additionalVal != null && additionalVal.isNotEmpty) {
        final additionalValMap = json.decode(additionalVal) as Map<String, dynamic>;
        if (additionalValMap['data'] != null) {
          final fileInfo = additionalValMap['data'];
          final file = {
            'fileName': fileInfo['displayName'] ?? fileInfo['filename'],
            'path': additionalValMap['filename'],
            'fileSize': fileInfo['fileSize'] ?? 0,
          };

          _formStateManager.setUploadedFiles({
            'name': fieldName,
            'files': [file],
            'fileType': "",
            'isDefault': true,
          });

          developer.log('Mapped draft file field: $fieldName');
        }
      }
    } catch (e) {
      developer.log('Error handling draft file field $fieldName: $e');
    }
  }

  Future<UploadSingleFileResponseModel?> _uploadSingleFile(Map fileData) async {
    final completer = Completer<UploadSingleFileResponseModel?>();
    late final StreamSubscription sub;
    sub = context.read<FormBloc>().stream.listen((state) {
      if (state.uploadFileResponse != null && state.name == fileData['name']) {
        completer.complete(state.uploadFileResponse);
        sub.cancel();
      }
    });
    context.read<FormBloc>().add(UploadFileRequested(
          fileData: fileData['files'].first,
          name: fileData['name'],
          fileType: fileData['fileType'],
        ));
    return completer.future;
  }

  Future<List<UploadFileWithField>> _uploadAttachments(List<Map> attachmentFiles) async {
    List<UploadFileWithField> responses = [];
    for (final fileData in attachmentFiles) {
      final resp = await _uploadSingleFile(fileData);
      if (resp != null) {
        responses.add(UploadFileWithField(resp, fileData['name']));
      }
    }
    return responses;
  }

  Future<void> _handleFileUploads() async {
    final uploadedFiles = _formStateManager.getUploadedFiles().where((e) => e['isDefault'] != true).toList();

    if (uploadedFiles.isNotEmpty) {
      try {
        final responses = await _uploadAttachments(uploadedFiles);

        for (final wrapper in responses) {
          final fileIndex = uploadedFiles.indexWhere((file) => file['name'] == wrapper.fieldName);
          if (fileIndex >= 0) {
            uploadedFiles[fileIndex]['uploadResponse'] = wrapper.response;
          }
        }

        developer.log('Đã upload ${responses.length} files thành công');
      } catch (e) {
        developer.log('Lỗi khi upload files: $e');
        SnackbarCore.error('Lỗi khi upload files: $e');
        rethrow;
      }
    }
  }

  Map<String, dynamic> _transformFormValuesToVariables() {
    final saveData = _formStateManager.getSaveFormState();
    developer.log('saveData: $saveData', name: 'transformFormValuesToVariables');
    final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket);
    final Map<String, dynamic> prefixedData = {};

    // danh sách các trường hệ thống không cần prefix khi isNotForHandleModal = true
    final systemFields = [
      'system_chartId_service',
      'system_chartNodeCustomId',
      'system_chartCustomId',
      'system_chartNodeId_ticket'
    ];

    saveData.forEach((key, value) {
      if (key != 'requestSubject' && !key.endsWith('_iso')) {
        final variable = {
          "value": value['value']?.toString() ?? "",
          "type": value['type'] ?? "String",
          "valueInfo": value['valueInfo'] ?? {}
        };

        if (systemFields.contains(key) && (widget.isNotForHandleModal ?? false)) {
          prefixedData[key] = variable;
        } else {
          final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
          prefixedData['$prefix${taskDefKey}_$key'] = variable;
        }

        if (key == 'slt_logo') {
          final formWidget = _formStateManager.findWidgetByName(key);
          if (key == 'slt_logo') {
            prefixedData['${key}_text'] = {
              "value": formWidget?.option?.firstOrNull?['label']?.toString() ?? "",
              "type": "String",
              "valueInfo": {}
            };
            if (formWidget?.optionType == "normal") {
              if (systemFields.contains(key) && (widget.isNotForHandleModal ?? false)) {
                prefixedData[key] = {
                  "value": value['value']?.toString() ?? "",
                  "type": value['type'] ?? "String",
                  "valueInfo": {"data": formWidget?.option}
                };
              } else {
                final logoPrefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
                prefixedData['$logoPrefix${taskDefKey}_$key'] = {
                  "value": value['value']?.toString() ?? "",
                  "type": value['type'] ?? "String",
                  "valueInfo": {"data": formWidget?.option}
                };
              }
            }
          }
        }
      } else {
        prefixedData[key] = value;
        if (key == "${taskDefKey}_attachmentSignComment") {
          final requestSubject = approveContent.text;
          prefixedData[key] = {"value": requestSubject, "type": "String", "valueInfo": {}};
        }
      }
    });

    final uploadedFiles = _formStateManager.getUploadedFiles().where((e) => e['isDefault'] != true).toList();

    for (final fileData in uploadedFiles) {
      final uploadResponse = fileData['uploadResponse'] as UploadSingleFileResponseModel?;
      if (uploadResponse != null) {
        final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
        final uploadData = transformUploadResponseToFormData(
          '$prefix$taskDefKey',
          fileData['name'],
          uploadResponse,
          _formStateManager,
        );
        prefixedData.addAll(uploadData);
      }
    }

    final fileUpload = _formStateManager.getUploadedFiles();
    final fileDefault = fileUpload.where((element) => element['isDefault'] == true).toList();
    if (fileDefault.isNotEmpty) {
      final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
      final transFileDefault = transformUploadResponseToFormData(
        '$prefix$taskDefKey',
        fileDefault.first['name'],
        null,
        _formStateManager,
      );
      prefixedData.addAll(transFileDefault);
    }
    Box boxUser = Hive.box('user_box');
    String? companyCode = boxUser.get('companyCode');
    String? logoCode = boxUser.get('logoCode');
    if (widget.isNotForHandleModal ?? false) {
      prefixedData['system_completeTicketId'] = {"value": [], "type": "String", "valueInfo": {}};

      prefixedData['system_companyCode'] = {
        "value": companyCode ?? logoCode ?? '1000',
        "type": "String",
        "valueInfo": {}
      };
    }

    return prefixedData;
  }

  Map<String, dynamic> _buildApproveBodyWithPermissions({
    required dynamic signPrintData,
    required String procInstId,
    required int templatePrintId,
    required dynamic inputTaskInformation,
    required String userTitle,
    required String chartNodeLevel,
  }) {
    final variables = _transformFormValuesToVariables();

    final approveComment = approveContent.text;
    final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket);
    final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
    variables['$prefix${taskDefKey}_attachmentSignComment'] = {
      "value": approveComment,
      "type": "String",
      "valueInfo": {}
    };

    final List<Map<String, dynamic>> bpmSignRequests = [];
    final currentTaskDefKey = TicketUtils.getTaskDefKey(widget.ticket);

    if (signPrintData?.data?.signs != null) {
      developer.log('Processing ${signPrintData.data.signs.length} signs');

      for (int i = 0; i < signPrintData.data.signs.length; i++) {
        final sign = signPrintData.data.signs[i];
        final taskDefKey = sign.taskDefKey;

        developer.log('=== Processing Sign $i ===');
        developer.log('TaskDefKey: $taskDefKey');
        developer.log('CurrentTaskDefKey: $currentTaskDefKey');

        // Check isSign
        bool isSign = false;
        if (taskDefKey == currentTaskDefKey && (startPermission.contains(2) || startPermission.contains(3))) {
          isSign = true;
        }

        // Set comment cho taskDefKey hiện tại
        final commentForThisTask = (taskDefKey == currentTaskDefKey) ? approveComment : "";

        // Set signImg: Nếu có dữ liệu trong BPM thì map cho cả taskDefKey có dữ liệu và taskDefKey có isSign=true
        String? signImg;

        if (sign.bpmSignResponse?.img != null) {
          // Có dữ liệu signature trong BPM -> gán cho tất cả (cả taskDefKey có dữ liệu và isSign=true)
          signImg = sign.bpmSignResponse?.img;
        }

        developer.log('Sign img from model: $signImg');
        developer.log('Final: TaskDefKey=$taskDefKey, isSign=$isSign, hasImg=${signImg != null}');

        // Set title và chartNodeLevel chỉ cho taskDefKey hiện tại
        final titleForThisTask = (taskDefKey == currentTaskDefKey) ? userTitle : "";
        final chartNodeLevelForThisTask = (taskDefKey == currentTaskDefKey) ? chartNodeLevel : "";

        bpmSignRequests.add({
          "tpSignZoneId": sign.id,
          "fileType": "png",
          "taskDefKey": sign.taskDefKey,
          "procInstId": procInstId,
          "sign": signImg,
          "title": titleForThisTask,
          "comment": commentForThisTask,
          "chartNodeLevel": chartNodeLevelForThisTask,
          "isSign": isSign,
        });
      }
    }

    // 4. Tạo body hoàn chỉnh
    return {
      "variables": variables,
      "businessKey": null,
      "signZoneRequest": {
        "templatePrintId": templatePrintId,
        "bpmSignRequest": bpmSignRequests,
        "ticketId": widget.ticket.ticketId ?? 0,
        "bpmTaskId": inputTaskInformation?['id'],
        "taskId": inputTaskInformation?['taskId'],
      }
    };
  }

  void _handleApprove() async {
    if (_isProcessingApproval) {
      developer.log('Đang xử lý phê duyệt, bỏ qua request này');
      return;
    }

    try {
      developer.log(_isExecutionMode ? 'Bắt đầu xử lý thực hiện' : 'Bắt đầu xử lý phê duyệt');

      _isProcessingApproval = true;
      widget.onProcessingStateChanged?.call(true);

      bool isApproveContentValid = _validateApproveContent();
      bool isFormValid = formKey.currentState?.validate() == true;

      if (!isApproveContentValid || !isFormValid) {
        SnackbarCore.error('Vui lòng nhập đầy đủ thông tin');
        _isProcessingApproval = false;
        widget.onProcessingStateChanged?.call(false);
        return;
      }

      final confirmed = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return CustomDialog(
            title: _isExecutionMode ? 'Xác nhận thực hiện' : 'Xác nhận phê duyệt',
            content: _isExecutionMode
                ? 'Bạn có chắc chắn muốn thực hiện phiếu này không?'
                : 'Bạn có chắc chắn muốn phê duyệt phiếu này không?',
            onConfirm: () => Navigator.pop(dialogContext, true),
            onCancel: () => Navigator.pop(dialogContext, false),
            confirmButtonText: _isExecutionMode ? 'Thực hiện' : 'Phê duyệt',
            cancelButtonText: 'Hủy',
          );
        },
      );

      if (confirmed != true) {
        setState(() {
          _isProcessingApproval = false;
        });
        widget.onProcessingStateChanged?.call(false);
        return;
      }

      await _handleFileUploads();

      if (listSignForm.isNotEmpty) {
        final signFormItem = listSignForm.first;

        final dynamic idValue = signFormItem.id;
        final int bpmTpSignZone;
        if (idValue is int) {
          bpmTpSignZone = idValue;
        } else if (idValue is String) {
          bpmTpSignZone = int.parse(idValue);
        } else {
          throw Exception('Invalid id type: ${idValue.runtimeType}');
        }

        final dynamic procInstIdValue = widget.ticket.procInstId;
        final String procInstId;
        if (procInstIdValue is String) {
          procInstId = procInstIdValue;
        } else if (procInstIdValue is int) {
          procInstId = procInstIdValue.toString();
        } else {
          procInstId = '';
        }

        developer.log('ticket phe duyet: ${widget.ticket.toJson()}');
        developer.log('ticket keys: ${widget.ticket.toJson().keys.toList()}');

        developer.log('Tìm thấy listSignForm với id: $bpmTpSignZone');
        developer.log('procInstId: $procInstId');

        if (mounted) {
          context.read<SignPrintBloc>().add(LoadSignPrintDataEvent(
                bpmTpSignZone: bpmTpSignZone,
                procInstId: procInstId,
              ));
        }
      } else {
        developer.log('Không có listSignForm, tiếp tục phê duyệt bình thường');
        _proceedWithNormalApproval();
      }
    } catch (e) {
      developer.log('Lỗi khi xử lý phê duyệt: $e');
      _isProcessingApproval = false;
      widget.onProcessingStateChanged?.call(false);
      if (mounted) {
        SnackbarCore.error('Lỗi khi xử lý phê duyệt: $e');
      }
    }
  }

  void _proceedWithNormalApproval({dynamic signPrintData}) async {
    developer.log('Thực hiện logic phê duyệt bình thường');

    if (formKey.currentState?.validate() != true) {
      SnackbarCore.error('Vui lòng nhập đầy đủ thông tin');
      _isProcessingApproval = false;
      widget.onProcessingStateChanged?.call(false);
      return;
    }

    try {
      if (signPrintData != null) {
        _requiresSignature = true;
        final procInstId = widget.ticket.procInstId;
        final templatePrintId = listSignForm.first.id;

        context.read<FormBloc>().add(GetFinalTitleByListUser(usernames: [username ?? '']));

        _waitForTitleDataAndBuildBody(signPrintData, procInstId, templatePrintId);
      } else {
        developer.log('Phê duyệt không có sign zone');
        _requiresSignature = false;

        final variables = _transformFormValuesToVariables();

        final approveComment = approveContent.text;
        final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket);
        final prefix = !(widget.isNotForHandleModal ?? false) ? 'zoom_${username}_' : '';
        variables['$prefix${taskDefKey}_attachmentSignComment'] = {
          "value": approveComment,
          "type": "String",
          "valueInfo": {}
        };

        final approveBody = {
          "variables": variables,
          "businessKey": null,
        };

        developer.log('Approve body without signZoneRequest: $approveBody');

        if (mounted) {
          context.read<TicketProcessDetailBloc>().add(ApproveTicket(
                taskId: taskId,
                procInstId: widget.ticket.procInstId,
                signComplete: false,
                body: approveBody,
              ));
        }
      }
    } catch (e) {
      developer.log('Lỗi khi phê duyệt: $e');
      SnackbarCore.error('Lỗi khi phê duyệt: $e');
      _isProcessingApproval = false;
      widget.onProcessingStateChanged?.call(false);
    }
  }

  void _waitForTitleDataAndBuildBody(dynamic signPrintData, dynamic procInstId, int templatePrintId) async {
    try {
      context.read<FormBloc>()
        ..add(GetFinalTitleByListUser(usernames: [username ?? '']))
        ..add(GetDefaultSignatureRequested(username: username ?? ''));

      developer.log('Đang chờ title data và signature...');

      final formState = await context
          .read<FormBloc>()
          .stream
          .where((state) => state.titleByUserResponse != null && state.defaultSignatureResponse != null)
          .first
          .timeout(
        Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('Timeout khi chờ title data');
        },
      );

      developer.log('Đã nhận được title data và signature');

      if (!_isProcessingApproval) {
        developer.log('Approval đã bị hủy, không tiếp tục');
        return;
      }

      final titleData = formState.titleByUserResponse!.data.first;
      final userTitle = titleData.title ?? '';
      final chartNodeLevel = titleData.chartNodeLevel ?? '';

      _buildFinalApproveBody(signPrintData, procInstId, templatePrintId, userTitle, chartNodeLevel);
    } on TimeoutException catch (e) {
      developer.log('Timeout khi chờ title data: $e');
      _isProcessingApproval = false;
    } catch (e) {
      developer.log('Lỗi khi chờ title data: $e');
      _isProcessingApproval = false;
    }
  }

  void _buildFinalApproveBody(
    dynamic signPrintData,
    dynamic procInstId,
    int templatePrintId,
    String userTitle,
    String chartNodeLevel,
  ) {
    if (!_isProcessingApproval) {
      developer.log('Approval đã bị hủy, không build body');
      return;
    }

    final inputTaskInformation = {
      'id': id,
      'taskId': taskId,
    };

    final approveBody = _buildApproveBodyWithPermissions(
      signPrintData: signPrintData,
      procInstId: procInstId,
      templatePrintId: templatePrintId,
      inputTaskInformation: inputTaskInformation,
      userTitle: userTitle,
      chartNodeLevel: chartNodeLevel,
    );

    developer.log('Final Approve body: $approveBody');

    _lastApproveBody = approveBody;

    if (mounted) {
      context.read<TicketProcessDetailBloc>().add(ApproveTicket(
            taskId: taskId,
            procInstId: procInstId,
            signComplete: false,
            body: approveBody,
          ));
    }
  }

  Widget _buildButtonSection() {
    final isTablet = DeviceUtils.isTablet;

    Widget buttonRow;

    if (_isExecutionMode && !widget.isDone) {
      buttonRow = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: OutlinedButton(
              onPressed: () => _handleSaveDraft(),
              style: FormUtils.outlinedBtnStyle(),
              child: Text(
                'Lưu nháp',
                style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isProcessingApproval ? null : _handleApprove,
              style: FormUtils.elevatedBtnStyle(),
              child: _isProcessingApproval
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
                      ),
                    )
                  : Text(
                      'Hoàn thành',
                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                    ),
            ),
          ),
        ],
      );
    } else if (!_isExecutionMode && !widget.isSigned && !widget.isInPerformPhase && !widget.isDone) {
      buttonRow = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 1,
            child: OutlinedButton(
              onPressed: () => _handleSaveDraft(),
              style: FormUtils.outlinedBtnStyle(),
              child: Text(
                'Lưu nháp',
                style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            flex: 1,
            child: OutlinedButton(
              onPressed: () => _showInheritDialog(),
              style: FormUtils.outlinedBtnStyle(),
              child: Text(
                'Kế thừa',
                style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isProcessingApproval ? null : _handleApprove,
              style: FormUtils.elevatedBtnStyle(),
              child: _isProcessingApproval
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
                      ),
                    )
                  : Text(
                      'Phê duyệt',
                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                    ),
            ),
          ),
        ],
      );
    } else if (!_isExecutionMode && (widget.isSigned || widget.isInPerformPhase) && !widget.isDone) {
      buttonRow = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: OutlinedButton(
              onPressed: () => _handleSaveDraft(),
              style: FormUtils.outlinedBtnStyle(),
              child: Text(
                'Lưu nháp',
                style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isProcessingApproval ? null : _handleApprove,
              style: FormUtils.elevatedBtnStyle(),
              child: _isProcessingApproval
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
                      ),
                    )
                  : Text(
                      'Hoàn thành',
                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                    ),
            ),
          ),
        ],
      );
    } else {
      return SizedBox.shrink();
    }

    // Nếu là tablet, đẩy button sang phải và làm cho chúng nhỏ lại
    if (isTablet) {
      return Align(
        alignment: Alignment.bottomRight,
        child: _buildCompactButtonRow(),
      );
    }

    return buttonRow;
  }

  Widget _buildCompactButtonRow() {
    // Button row compact cho tablet - không dùng Expanded
    if (_isExecutionMode && !widget.isDone) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 100.w,
            child: OutlinedButton(
              onPressed: () => _handleSaveDraft(),
              style: FormUtils.outlinedBtnStyle(),
              child: Text(
                'Lưu nháp',
                style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          SizedBox(
            width: 120.w,
            child: ElevatedButton(
              onPressed: _isProcessingApproval ? null : _handleApprove,
              style: FormUtils.elevatedBtnStyle(),
              child: _isProcessingApproval
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
                      ),
                    )
                  : Text(
                      'Hoàn thành',
                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                    ),
            ),
          ),
        ],
      );
    } else if (!_isExecutionMode && !widget.isSigned && !widget.isInPerformPhase && !widget.isDone) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          OutlinedButton(
            onPressed: () => _handleSaveDraft(),
            style: FormUtils.outlinedBtnStyle(),
            child: Text(
              'Lưu nháp',
              style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 8.w),
          OutlinedButton(
            onPressed: () => _showInheritDialog(),
            style: FormUtils.outlinedBtnStyle(),
            child: Text(
              'Kế thừa',
              style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 8.w),
          ElevatedButton(
            onPressed: _isProcessingApproval ? null : _handleApprove,
            style: FormUtils.elevatedBtnStyle(),
            child: _isProcessingApproval
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
                    ),
                  )
                : Text(
                    'Phê duyệt',
                    style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                  ),
          ),
        ],
      );
    } else if (!_isExecutionMode && (widget.isSigned || widget.isInPerformPhase) && !widget.isDone) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 100.w,
            child: OutlinedButton(
              onPressed: () => _handleSaveDraft(),
              style: FormUtils.outlinedBtnStyle(),
              child: Text(
                'Lưu nháp',
                style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          SizedBox(
            width: 120.w,
            child: ElevatedButton(
              onPressed: _isProcessingApproval ? null : _handleApprove,
              style: FormUtils.elevatedBtnStyle(),
              child: _isProcessingApproval
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
                      ),
                    )
                  : Text(
                      'Hoàn thành',
                      style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
                    ),
            ),
          ),
        ],
      );
    }

    return SizedBox.shrink();
  }

  Widget _buildFormShimmer() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 80.h,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: AppConstraint.buildShimmer(),
        ),
        SizedBox(height: 16.h),
        for (int i = 0; i < 4; i++) ...[
          Container(
            width: double.infinity,
            height: 60.h,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: AppConstraint.buildShimmer(),
          ),
          SizedBox(height: 12.h),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasInitializedForm) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadFormTemplate();
      });
    }

    return MultiBlocListener(
      listeners: [
        BlocListener<FormBloc, FormInfoState>(
          listener: (context, state) {
            if (state.formResponse?.code == 1 && !_isFormInitialized) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _initializeForm(state);
              });
            }
          },
        ),
        BlocListener<SignPrintBloc, SignPrintState>(
          listener: (context, state) {
            if (state is SignPrintLoaded) {
              developer.log('Sign print data loaded: ${state.signPrintData.data.name}');
              developer.log('Signed file: ${state.signPrintData.data.signedFile}');

              _proceedWithNormalApproval(signPrintData: state.signPrintData);
            } else if (state is SignPrintError) {
              developer.log('Sign print error: ${state.message}');
              _isProcessingApproval = false;
            } else if (state is SignPrintLoading) {
              developer.log('Đang tải thông tin ký...');
            }
          },
        ),
        BlocListener<TicketProcessDetailBloc, TicketProcessDetailState>(
          listener: (context, state) {
            if (state is TicketProcessDetailLoaded) {
              listSignForm = state.ticketProcessDetail.data?.listSignForm ?? [];
              id = state.ticketProcessDetail.data?.id;
              taskId = state.ticketProcessDetail.data?.taskId;

              developer.log('listSignForm length: ${listSignForm.length}');

              final startPermissionData = state.ticketProcessDetail.data?.startPermission;
              developer.log('startPermissionData: $startPermissionData');
              if (startPermissionData != null) {
                if (startPermissionData is int) {
                  startPermission = [startPermissionData];
                } else if (startPermissionData is String) {
                  try {
                    if (startPermissionData.contains(',')) {
                      startPermission = startPermissionData.split(',').map((e) => int.parse(e.trim())).toList();
                    } else {
                      startPermission = [int.parse(startPermissionData)];
                    }
                  } catch (e) {
                    developer.log('Error parsing startPermission string: $e');
                    startPermission = [];
                  }
                } else if (startPermissionData is List) {
                  startPermission = List<int>.from(startPermissionData);
                }
              }

              developer.log('StartPermission loaded: $startPermission');

              // Check và map draftVariables nếu có
              final draftVariables = state.ticketProcessDetail.data?.draftVariables;
              developer.log('draftVariables122345: $draftVariables');
              if (draftVariables != null && draftVariables is List && draftVariables.isNotEmpty) {
                developer.log('Found ${draftVariables.length} draft variables');
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _mapDraftVariablesToForm(List<dynamic>.from(draftVariables));
                });
              }
            } else if (state is ApproveTicketSuccess) {
              developer.log('Approve ticket success: ${state.message}');

              if (_requiresSignature &&
                  _lastApproveBody != null &&
                  _lastApproveBody!.containsKey('signZoneRequest') &&
                  mounted) {
                developer.log('Calling sign API after approval success');

                final signRequestBody = {
                  "templatePrintId": _lastApproveBody!['signZoneRequest']['templatePrintId'],
                  "bpmSignRequest": _lastApproveBody!['signZoneRequest']['bpmSignRequest'],
                  "ticketId": _lastApproveBody!['signZoneRequest']['ticketId'],
                  "bmpTaskId": _lastApproveBody!['signZoneRequest']['bmpTaskId'],
                  "taskId": _lastApproveBody!['signZoneRequest']['taskId'],
                };

                context.read<TicketProcessDetailBloc>().add(SignPrintZone(
                      requestBody: signRequestBody,
                    ));
              } else {
                _isProcessingApproval = false;
                widget.onProcessingStateChanged?.call(false);

                if (mounted) {
                  SnackbarCore.success(_isExecutionMode ? 'Thực hiện thành công' : 'Phê duyệt thành công');

                  // Nếu là tablet và ở trong modal, điều hướng về service tab
                  if (DeviceUtils.isTablet && (widget.isNotForHandleModal ?? false)) {
                    try {
                      context.read<BottomNavBloc>().add(const NavigateToTabletTab(0));
                      developer.log('Navigated to service tab on tablet after approval success', name: 'ApproveTab');
                    } catch (e) {
                      developer.log('Error navigating to service tab: $e', name: 'ApproveTab');
                    }
                  }

                  if (widget.onApproveSuccess != null) {
                    try {
                      widget.onApproveSuccess!();
                    } catch (e) {
                      developer.log('Error calling onApproveSuccess callback: $e', name: 'ApproveTab');
                    }
                  }
                }
              }
            } else if (state is SignPrintZoneSuccess) {
              developer.log('Sign print zone success: ${state.message}');
              _isProcessingApproval = false;
              widget.onProcessingStateChanged?.call(false);

              if (mounted) {
                SnackbarCore.success(_isExecutionMode ? 'Thực hiện thành công' : 'Phê duyệt thành công');

                // Nếu là tablet và ở trong modal, điều hướng về service tab
                if (DeviceUtils.isTablet) {
                  try {
                    context.read<BottomNavBloc>().add(const NavigateToTabletTab(0));
                    developer.log('Navigated to service tab on tablet after sign success', name: 'ApproveTab');
                  } catch (e) {
                    developer.log('Error navigating to service tab: $e', name: 'ApproveTab');
                  }
                }

                if (widget.onApproveSuccess != null) {
                  try {
                    widget.onApproveSuccess!();
                  } catch (e) {
                    developer.log('Error calling onApproveSuccess callback: $e', name: 'ApproveTab');
                  }
                }
              }
            } else if (state is SignPrintZoneError) {
              developer.log('Sign print zone error: ${state.message}');
              _isProcessingApproval = false;
              widget.onProcessingStateChanged?.call(false);
            } else if (state is SaveDraftSuccess) {
              SnackbarCore.success('Lưu nháp thành công');
            } else if (state is SaveDraftError) {
              developer.log('Save draft error: ${state.message}');
            }
          },
        ),
      ],
      child: Form(
        key: formKey,
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: SizedBox(
            height: 530.h,
            child: _isLoading
                ? _buildFormShimmer()
                : Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          padding: widget.isNotForHandleModal == true
                              ? null
                              : EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              FormUtils.buildLabel(_isExecutionMode ? 'Nội dung thực hiện' : 'Nội dung phê duyệt',
                                  required: true),
                              SizedBox(height: 8.h),
                              CustomTextForm(
                                edtController: approveContent,
                                maxLines: 3,
                                enabled: !widget.isDone,
                                filled: true,
                                fillColor: widget.isDone ? getColorSkin().grey4Background : getColorSkin().white,
                                validator: _approveContentError != null ? (_) => _approveContentError : null,
                                onChanged: (value) {
                                  if (_approveContentError != null && value?.isNotEmpty == true) {
                                    setState(() {
                                      _approveContentError = null;
                                    });
                                  }
                                },
                              ),
                              SizedBox(height: 16.h),
                              if (_formResponse != null)
                                FormBuilderV2(
                                  widgets: _formResponse!.data.template.form,
                                  stateManager: _formStateManager,
                                  row: _formResponse!.data.template.row,
                                  step: _formResponse!.data.template.step,
                                  col: _formResponse!.data.template.column,
                                  tab: _formResponse!.data.template.tab,
                                  onFieldChanged: (id, name, value) {
                                    _formStateManager.setFieldValue(id, value);
                                  },
                                ),
                              SizedBox(height: 16.h),
                            ],
                          ),
                        ),
                      ),
                      if (!widget.hideButtonsOnTablet) _buildButtonSection(),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}

Map<String, dynamic> transformUploadResponseToFormData(
  String taskDefKey,
  String name,
  UploadSingleFileResponseModel? uploadResponse,
  FormStateManager stateManager,
) {
  final result = <String, dynamic>{};
  final now = DateTime.now();
  final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(now);
  final Box authBox = Hive.box('authentication');
  final String? username = authBox.get('username');

  if (uploadResponse?.data == null) {
    final widget = stateManager.findWidgetByName(name);
    if (widget?.attachFileObjDefault != null) {
      final defaultFile = widget!.attachFileObjDefault!;

      final historyEntry = {
        "createdUser": defaultFile['createdUser'] ?? username ?? "",
        "createdTime": now.toIso8601String(),
        "uploadTime": formattedDate,
        "filename": defaultFile['filename'] ?? defaultFile['fileName'] ?? "",
        "downloadUrl": defaultFile['downloadUrl'] ?? defaultFile['orgFilePath'] ?? "",
        "idHistory": defaultFile['idHistory'] ?? "",
        "displayName": defaultFile['filename'] ?? defaultFile['fileName'] ?? "",
        "fileSize": defaultFile['fileSize'] ?? 0,
      };

      result['${taskDefKey}_${name}_history'] = {
        "value": jsonEncode([historyEntry]),
        "type": "String"
      };

      result['${taskDefKey}_${name}_multiName'] = {"value": "", "type": "String"};

      result['${taskDefKey}_$name'] = {
        "value": "",
        "type": "File",
        "valueInfo": {
          "filename": defaultFile['downloadUrl'] ?? defaultFile['orgFilePath'] ?? "",
          "mimeType": defaultFile['fileType'] ?? "",
          "data": historyEntry
        }
      };

      return result;
    }
  }

  final historyEntry = {
    "createdUser": username ?? "",
    "createdTime": now.toIso8601String(),
    "uploadTime": formattedDate,
    "filename": uploadResponse?.data?.fileName?.split('/').last ?? "",
    "downloadUrl": uploadResponse?.data?.downloadUrl ?? "",
    "idHistory": "",
    "displayName": uploadResponse?.data?.fileName?.split('/').last ?? "",
    "fileSize": uploadResponse?.data?.size ?? 0,
  };

  result['${taskDefKey}_${name}_history'] = {
    "value": jsonEncode([historyEntry]),
    "type": "String"
  };

  result['${taskDefKey}_${name}_multiName'] = {"value": "", "type": "String"};

  result['${taskDefKey}_$name'] = {
    "value": "",
    "type": "File",
    "valueInfo": {
      "filename": uploadResponse?.data?.downloadUrl ?? "",
      "mimeType": uploadResponse?.data?.contentType ?? "",
      "data": historyEntry
    }
  };

  return result;
}
