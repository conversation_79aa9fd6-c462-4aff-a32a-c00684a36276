import 'package:eapprove/models/pyc/ticket_task_model.dart';

class AssisContent {
  final String? priorityName;
  final String? actionUser;
  final String? ticketTitle;
  final String? ticketStartActId;
  final int? ticketStartedTime;
  final String? priorityColor;
  final int? ticketEndTime;
  final String? procServiceName;
  final String? endKey;
  final String? ticketStartUserId;
  final int? chartId;
  final String? chartNodeName;
  final String? requestCode;
  final int? id;
  final int? ticketCanceledTime;
  final int? serviceId;
  final String? companyCode;
  final int? ticketFinishTime;
  final int? ticketCreatedTime;
  final String? submissionTypeName;
  final List<TicketTask>? ticketTaskDtoList;
  final String? chartName;
  final String? ticketProcDefId;
  final String? ticketPriority;
  final String? ticketStatus;
  final double? ticketRating;
  final int? ticketEditTime;
  final int? ticketClosedTime;
  final String? comment;
  final double? slaFinish;
  final String? ticketId;
  final double? slaResponse;

  AssisContent({
    this.priorityName,
    this.actionUser,
    this.ticketTitle,
    this.ticketStartActId,
    this.ticketStartedTime,
    this.priorityColor,
    this.ticketEndTime,
    this.procServiceName,
    this.endKey,
    this.ticketStartUserId,
    this.chartId,
    this.chartNodeName,
    this.requestCode,
    this.id,
    this.ticketCanceledTime,
    this.serviceId,
    this.companyCode,
    this.ticketFinishTime,
    this.ticketCreatedTime,
    this.submissionTypeName,
    this.ticketTaskDtoList,
    this.chartName,
    this.ticketProcDefId,
    this.ticketPriority,
    this.ticketStatus,
    this.ticketRating,
    this.ticketEditTime,
    this.ticketClosedTime,
    this.comment,
    this.slaFinish,
    this.ticketId,
    this.slaResponse,
  });
  factory AssisContent.fromJson(Map<String, dynamic> json) {
    return AssisContent(
      priorityName: json['priorityName'],
      actionUser: json['actionUser'],
      ticketTitle: json['ticketTitle'],
      ticketStartActId: json['ticketStartActId'],
      ticketStartedTime: json['ticketStartedTime'],
      priorityColor: json['priorityColor'],
      ticketEndTime: json['ticketEndTime'],
      procServiceName: json['procServiceName'],
      endKey: json['endKey'],
      ticketStartUserId: json['ticketStartUserId'],
      chartId: json['chartId'],
      chartNodeName: json['chartNodeName'],
      requestCode: json['requestCode'],
      id: json['id'],
      ticketCanceledTime: json['ticketCanceledTime'],
      serviceId: json['serviceId'],
      companyCode: json['companyCode'],
      ticketFinishTime: json['ticketFinishTime'],
      ticketCreatedTime: json['ticketCreatedTime'],
      submissionTypeName: json['submissionTypeName'],
      ticketTaskDtoList: (json['ticketTaskDtoList'] is List)
          ? (json['ticketTaskDtoList'] as List<dynamic>)
              .map((item) => TicketTask.fromJson(item as Map<String, dynamic>))
              .toList()
          : null,
      chartName: json['chartName'],
      ticketProcDefId: json['ticketProcDefId'],
      ticketPriority: json['ticketPriority'],
      ticketStatus: json['ticketStatus'],
      ticketRating: json['ticketRating'],
      ticketEditTime: json['ticketEditTime'],
      ticketClosedTime: json['ticketClosedTime'],
      comment: json['comment'],
      slaFinish: json['slaFinish'],
      ticketId: json['ticketId'],
      slaResponse: json['slaResponse'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      "priorityName": priorityName,
      "actionUser": actionUser,
      "ticketTitle": ticketTitle,
      "ticketStartActId": ticketStartActId,
      "ticketStartedTime": ticketStartedTime,
      "priorityColor": priorityColor,
      "ticketEndTime": ticketEndTime,
      "procServiceName": procServiceName,
      "endKey": endKey,
      "ticketStartUserId": ticketStartUserId,
      "chartId": chartId,
      "chartNodeName": chartNodeName,
      "requestCode": requestCode,
      "id": id,
      "ticketCanceledTime": ticketCanceledTime,
      "serviceId": serviceId,
      "companyCode": companyCode,
      "ticketFinishTime": ticketFinishTime,
      "ticketCreatedTime": ticketCreatedTime,
      "submissionTypeName": submissionTypeName,
      "ticketTaskDtoList": ticketTaskDtoList?.map((e) => e.toJson()).toList(),
      "chartName": chartName,
      "ticketProcDefId": ticketProcDefId,
      "ticketPriority": ticketPriority,
      "ticketStatus": ticketStatus,
      "ticketRating": ticketRating,
      "ticketEditTime": ticketEditTime,
      "ticketClosedTime": ticketClosedTime,
      "comment": comment,
      "slaFinish": slaFinish,
      "ticketId": ticketId,
      "slaResponse": slaResponse,
    };
  }
}
