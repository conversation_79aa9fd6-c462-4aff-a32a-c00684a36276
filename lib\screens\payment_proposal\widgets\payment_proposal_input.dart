import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/form/form_content_response_model.dart' as form_models;
import 'package:eapprove/screens/payment_proposal/widgets/note_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';

class UploadedFile {
  final String name;
  final String type;

  UploadedFile({required this.name, required this.type});
}

class PaymentProposalInput extends StatefulWidget {
  final form_models.FormResponse? formData;

  const PaymentProposalInput({super.key, this.formData});

  @override
  State<PaymentProposalInput> createState() => _PaymentProposalInputState();
}

class _PaymentProposalInputState extends State<PaymentProposalInput> {
  SelectItem? phongSelected = SelectItem(label: 'DES', value: 'DES');
  SelectItem? giamSelected = SelectItem(label: 'Trần Hữu Thắng', value: 'thang');
  SelectItem? chucSelected = SelectItem(label: 'Lãnh đạo', value: 'lanhdao');
  SelectItem? viSelected = SelectItem(label: 'Nhân viên', value: 'nhanvien');

  String note = '';
  List<UploadedFile> uploadedFiles = [];

  final List<SelectItem> phongOptions = [
    SelectItem(label: 'DEX', value: 'dex'),
    SelectItem(label: 'QEX', value: 'qex'),
    SelectItem(label: 'GES', value: 'ges'),
  ];

  final List<SelectItem> giamOptions = [
    SelectItem(label: 'Trần Hữu Thắng', value: 'thang'),
    SelectItem(label: 'Nguyễn Văn A', value: 'a'),
    SelectItem(label: 'Trần Thị B', value: 'b'),
  ];

  final List<SelectItem> chucOptions = [
    SelectItem(label: 'Lãnh đạo', value: 'lanhdao'),
    SelectItem(label: 'Quản lý', value: 'quanly'),
    SelectItem(label: 'Nhân viên', value: 'nhanvien'),
  ];

  final List<SelectItem> viOptions = [
    SelectItem(label: 'Nhân viên', value: 'nhanvien'),
    SelectItem(label: 'Quản lý', value: 'quanly'),
    SelectItem(label: 'Giám đốc', value: 'giamdoc'),
  ];

  void _handleFileUpload(UploadedFile file) {
    setState(() {
      uploadedFiles.add(file);
    });
  }

  void _handleFileRemove(UploadedFile file) {
    setState(() {
      uploadedFiles.remove(file);
    });
  }

  void _handleNoteChange(String value) {
    setState(() {
      note = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomExpandedList<String>(
      defaultImage: StringImage.ic_ticket_group_EAP,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      childPadding: EdgeInsets.symmetric(horizontal: 16.w),
      title: "1. Đề nghị thanh toán",
      isExpanded: true,
      titleStyle: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
      child: _buildFormContent(),
    );
  }

  Widget _buildFormContent() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDropdown(
            label: "Phòng",
            options: phongOptions,
            defaultValue: phongSelected,
            placeholder: "Chọn phòng",
          ),
          SizedBox(height: 16.h),

          _buildDropdown(
            label: "Giám",
            options: giamOptions,
            defaultValue: giamSelected,
            placeholder: "Chọn người giám sát",
            isFilled: true,
          ),
          SizedBox(height: 16.h),

          _buildDropdown(
            label: "Chức",
            options: chucOptions,
            defaultValue: chucSelected,
            placeholder: "Chọn chức vụ",
            isFilled: true,
          ),
          SizedBox(height: 16.h),

          _buildDropdown(
            label: "Vị",
            options: viOptions,
            defaultValue: viSelected,
            placeholder: "Chọn vị trí",
            isFilled: false,
          ),
          SizedBox(height: 16.h),
          NoteTextField(
            initialValue: note,
            onChanged: _handleNoteChange,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown({
    required String label,
    required List<SelectItem> options,
    required SelectItem? defaultValue,
    required String placeholder,
    bool isFilled = false,
  }) {
    return CustomDropdownMenu(
      dropdownHeight: 40.h,
      label: label,
      options: options,
      onSelected: (value) {},
      placeholder: placeholder,
      defaultValue: defaultValue,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        // color: isFilled ? getColorSkin().lightGray : null,
        border: Border.all(color: getColorSkin().lightGray, width: 1),
      ),
      showDeleteIcon: false,
      isFilled: isFilled,
    );
  }

}