import 'dart:convert';
import 'dart:developer';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/service/service_bloc.dart';
import 'package:eapprove/blocs/service/service_event.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/service/service_data_model.dart';
import 'package:eapprove/screens/common/error_handler.dart';
import 'package:eapprove/screens/ticket_service/service_search_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/ticket_service/widget/pdf_download_dialog.dart';

class CreateTicketScreenTablet extends StatefulWidget {
  final String title;

  static bool _hasLoadedData = false;
  final Function(String, String, int?, int?)? onServiceSelected;

  static void resetLoadState() {
    _hasLoadedData = false;
  }

  const CreateTicketScreenTablet(
      {super.key, required this.title, this.onServiceSelected});

  @override
  State<CreateTicketScreenTablet> createState() =>
      _CreateTicketScreenTabletState();
}

class _CreateTicketScreenTabletState extends State<CreateTicketScreenTablet>
    with AutomaticKeepAliveClientMixin {
  bool isSearchVisible = false;
  String searchTerm = '';
  bool hasPerformedSearch = false;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  bool get wantKeepAlive => false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchRootServices();
    });
  }

  void _fetchRootServices() {
    if (!mounted) return;

    if (!CreateTicketScreenTablet._hasLoadedData) {
      CreateTicketScreenTablet._hasLoadedData = true;
      context.read<ServiceBloc>().add(const ServiceFetchRoot());
    } else {
      log('Services already loaded, skipping fetch');
    }
  }

  @override
  void dispose() {
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _toggleSearch() {
    final currentBloc = BlocProvider.of<ServiceBloc>(context);
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: currentBloc,
          child: const ServiceSearchScreen(),
        ),
      ),
    )
        .then((_) {
      if (!mounted) return;
      context.read<ServiceBloc>().add(const ServiceFetchRoot());
    });
  }

  Widget buildImageFromIcon(ServiceData parent) {
    final String? iconData = parent.icon;

    if (iconData == null || iconData.isEmpty) {
      return SizedBox(
        width: 48.w,
        height: 48.h,
      );
    }

    final bool isBase64 =
        iconData.isNotEmpty && RegExp(r'^[A-Za-z0-9+/=]+$').hasMatch(iconData);

    if (isBase64) {
      try {
        return SizedBox(
          width: 48.w,
          height: 48.h,
          child: Center(
            child: Image.memory(
              base64Decode(iconData),
              width: 48.w,
              height: 48.h,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return Icon(Icons.folder, color: Colors.white, size: 48.w);
              },
            ),
          ),
        );
      } catch (e) {
        return SizedBox(
          width: 48.w,
          height: 48.h,
          child: Center(
            child: Icon(Icons.folder, color: Colors.white, size: 48.w),
          ),
        );
      }
    } else {
      return SizedBox(
        width: 48.w,
        height: 48.h,
        child: Center(
          child: Icon(Icons.folder, color: Colors.white, size: 48.w),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocBuilder<ServiceBloc, ServiceState>(
      buildWhen: (previous, current) {
        return previous.status != current.status ||
            previous.serviceModels != current.serviceModels ||
            previous.errorMessage != current.errorMessage ||
            previous.pdfFilePath != current.pdfFilePath ||
            previous.pdfDownloadStatus != current.pdfDownloadStatus;
      },
      builder: (context, state) {
        if (state.pdfDownloadStatus == PdfDownloadStatus.success &&
            state.pdfFilePath != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            SnackbarCore.success('Tải PDF thành công');
            context.read<ServiceBloc>().add(const ClearPdfStatus());
          });
        } else if (state.pdfDownloadStatus == PdfDownloadStatus.failure &&
            state.errorMessage != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            SnackbarCore.error('Tải PDF thất bại');
            context.read<ServiceBloc>().add(const ClearPdfStatus());
          });
        }
        return Stack(children: [
          GradientBackground(
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Scaffold(
                  backgroundColor: const Color.fromRGBO(0, 0, 0, 0),
                  extendBodyBehindAppBar: false,
                  body: RefreshIndicator(
                    onRefresh: () async {
                      context.read<ServiceBloc>().add(const ServiceFetchRoot());
                    },
                    color: getColorSkin().primaryBlue,
                    child: _buildBody(context, state),
                  ),
                ),
              ),
            ),
          ),
          if (state.pdfDownloadStatus == PdfDownloadStatus.downloading)
            GestureDetector(
              onTap: () {
                context.read<ServiceBloc>().add(const CancelDownload());
                SnackbarCore.error('Tải PDF đã bị hủy');
              },
              child: Container(
                color: Colors.black.withOpacity(0.1),
                child: Center(
                  child: GestureDetector(
                    onTap: () {},
                    child: const PdfDownloadDialog(
                      message: 'Đang tải PDF...',
                    ),
                  ),
                ),
              ),
            ),
        ]);
      },
    );
  }

  Widget _buildBody(BuildContext context, ServiceState state) {
    if (state.status == ServiceStatus.failure) {
      // if (state.isNetworkError) {
      //   return NetworkErrorWrapper(
      //     isNetworkError: true,
      //     onRetry: () {
      //       context.read<ServiceBloc>().add(const ServiceFetchRoot());
      //     },
      //     child: const SizedBox.shrink(),
      //   );
      // }
      // return ErrorWrapper(
      //   hasError: true,
      //   onRetry: () {
      //     context.read<ServiceBloc>().add(const ServiceFetchRoot());
      //   },
      //   child: const SizedBox.shrink(),
      // );
    }

    if (state.status == ServiceStatus.loading) {
      return Center(child: AppConstraint.buildLoading(context));
    }

    final allData = state.serviceModels?.data ?? [];
    final parentItems = allData
        .where(
            (item) => item.parentId == null && item.notShowingMoblie == false)
        .toList();

// Sort parent items by positionPackage
    parentItems.sort(
        (a, b) => (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));

    if (parentItems.isEmpty &&
        state.searchKey != null &&
        state.searchKey!.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              StringImage.ic_no_result,
              width: 100.w,
              height: 100.h,
            ),
            SizedBox(height: 16.h),
            Text(
              "Không có dịch vụ nào",
              style: getTypoSkin().medium16.copyWith(
                    color: getColorSkin().ink1,
                  ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        Positioned(
          top: 20.h,
          left: 0,
          right: 0,
          child: Container(
            width: double.infinity,
            height: 40.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            margin: EdgeInsets.only(bottom: 12.h),
            decoration: BoxDecoration(
              color: getColorSkin().white,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: getTypoSkin()
                      .medium16
                      .copyWith(color: getColorSkin().black),
                ),
                IconButton(
                  iconSize: 24.w.h,
                  highlightColor: Colors.transparent,
                  icon: SvgPicture.asset(StringImage.ic_search),
                  onPressed: _toggleSearch,
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 40.h + 20.h + 12.h,
          left: 0,
          right: 0,
          bottom: 0,
          child: GridView.builder(
            padding: EdgeInsets.zero,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8.0,
              mainAxisSpacing: 8.0,
              childAspectRatio: 0.6,
            ),
            itemCount: parentItems.length,
            itemBuilder: (context, index) {
              final parent = parentItems[index];
              return ServiceGridItem(
                parent: parent,
                allData: allData,
              );
            },
          ),
        ),
      ],
    );
  }
}

// Tạo widget mới để quản lý trạng thái của mỗi ô lưới độc lập
class ServiceGridItem extends StatefulWidget {
  final ServiceData parent;
  final List<ServiceData> allData;

  const ServiceGridItem({
    super.key,
    required this.parent,
    required this.allData,
  });

  @override
  State<ServiceGridItem> createState() => _ServiceGridItemState();
}

class _ServiceGridItemState extends State<ServiceGridItem> {
  final Map<int?, bool> _expandedItems = {};

  bool isExpanded(int? itemId) {
    if (itemId == null) return false;
    return _expandedItems[itemId] ?? false;
  }

  // Helper method to find ancestor widget
  T? findAncestorWidgetOfExactType<T extends Widget>() {
    BuildContext? currentContext = context;
    T? result;

    while (currentContext != null) {
      if (currentContext.widget is T) {
        result = currentContext.widget as T;
        break;
      }
      currentContext = currentContext
          .findAncestorStateOfType<State<StatefulWidget>>()
          ?.context;
    }

    return result;
  }

  void toggleExpanded(ServiceData? serviceItem) async {
    if (serviceItem?.id == null) return;

    final int itemId = serviceItem!.id!;

    // Check if this item has children
    final childItems = widget.allData
        .where(
            (item) => item.parentId == itemId && item.notShowingMoblie == false)
        .toList();

    // If there are children, expand/collapse as normal
    if (childItems.isNotEmpty) {
      setState(() {
        _expandedItems[itemId] = !(_expandedItems[itemId] ?? false);
        debugPrint(
            'Toggled expansion for $itemId to ${_expandedItems[itemId]}');

        if (_expandedItems[itemId] == true) {
          debugPrint('Found ${childItems.length} children for $itemId');
          for (var i = 0; i < childItems.length; i++) {
            debugPrint(
                '  Child $i: ${childItems[i].serviceName} (ID: ${childItems[i].id})');
          }
        }
      });
    }
    // If no children, handle as a direct action based on service type
    else {
      if (serviceItem.specialFlow == true) {
        try {
          final parentWidget =
              findAncestorWidgetOfExactType<CreateTicketScreenTablet>();
          final specialService = await context
              .read<ServiceBloc>()
              .repository
              .getServiceSpecialByParentId(serviceItem.id!);
          debugPrint('specialService: ${specialService.data.procDefId}');
          if (parentWidget?.onServiceSelected != null) {
            parentWidget!.onServiceSelected!(
              serviceItem.procDefId!,
              serviceItem.serviceName ?? '',
              serviceItem.processId,
              serviceItem.id,
            );
          } else {
            if (specialService.data != null) {
              // If the special service has a procDefId, navigate to the form
              final formBloc = BlocProvider.of<FormBloc>(context);
              final dropdownBloc = BlocProvider.of<DropdownBloc>(context);
              final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
              final printFileTemplateBloc =
                  BlocProvider.of<PrintFileTemplateBloc>(context);
              final bpmProcInstBloc = BlocProvider.of<BpmProcInstBloc>(context);
              final chartBloc = BlocProvider.of<ChartBloc>(context);
              final userListBloc = BlocProvider.of<UserListBloc>(context);
              
              if (!mounted) return;
              
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MultiBlocProvider(
                    providers: [
                      BlocProvider.value(value: formBloc),
                      BlocProvider.value(value: dropdownBloc),
                      BlocProvider.value(value: checkTypeBloc),
                      BlocProvider.value(value: printFileTemplateBloc),
                      BlocProvider.value(value: bpmProcInstBloc),
                      BlocProvider.value(value: chartBloc),
                      BlocProvider.value(value: userListBloc),
                    ],
                    child: TicketFormScreen(
                      procDefId: serviceItem.procDefId!,
                      title: serviceItem.serviceName ?? '',
                      processId: serviceItem.processId,
                      ticketId: serviceItem.id,
                      onClose: () {
                        if (mounted) {
                          Navigator.of(context).pop();
                        }
                      },
                    ),
                  ),
                ),
              );
              return;
            }
          }
        } catch (e) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading special services: $e'),
              backgroundColor: getColorSkin().red,
            ),
          );
          return;
        }
      }

      switch (serviceItem.serviceType.toString()) {
        case "3": // URL type
          if (serviceItem.url != null && serviceItem.url!.isNotEmpty) {
            try {
              debugPrint('Opening URL: ${serviceItem.url}');
              final uri = Uri.parse(serviceItem.url!);
              launchUrl(
                uri,
                mode: LaunchMode.externalApplication,
              );
            } catch (e) {
              if (!mounted) return;
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Invalid URL format: ${serviceItem.url}'),
                  backgroundColor: getColorSkin().red,
                ),
              );
            }
          } else {
            debugPrint('No URL provided for this service');
            if (!mounted) return;
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('No URL provided for this service'),
                backgroundColor: getColorSkin().red,
              ),
            );
          }
          break;

        case "4": // PDF type
          if (serviceItem.url != null && serviceItem.url!.isNotEmpty) {
            debugPrint('Downloading PDF from URL: ${serviceItem.url}');
            context.read<ServiceBloc>().add(const ClearPdfStatus());
            context.read<ServiceBloc>().add(DownloadPdf(serviceItem.url!));
          } else {
            ErrorHandler.showErrorSnackBar(
                context, 'No PDF URL provided for this service');
          }
          break;

        default:
          if (serviceItem.procDefId != null) {
            final parentWidget =
                findAncestorWidgetOfExactType<CreateTicketScreenTablet>();

            if (parentWidget?.onServiceSelected != null) {
              parentWidget!.onServiceSelected!(
                serviceItem.procDefId!,
                serviceItem.serviceName ?? '',
                serviceItem.processId,
                serviceItem.id,
              );
            } else {
              final formBloc = BlocProvider.of<FormBloc>(context);
              final dropdownBloc = BlocProvider.of<DropdownBloc>(context);
              final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
              final printFileTemplateBloc =
                  BlocProvider.of<PrintFileTemplateBloc>(context);
              final bpmProcInstBloc = BlocProvider.of<BpmProcInstBloc>(context);
              final chartBloc = BlocProvider.of<ChartBloc>(context);
              final userListBloc = BlocProvider.of<UserListBloc>(context);
              
              if (!mounted) return;
              
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MultiBlocProvider(
                    providers: [
                      BlocProvider.value(value: formBloc),
                      BlocProvider.value(value: dropdownBloc),
                      BlocProvider.value(value: checkTypeBloc),
                      BlocProvider.value(value: printFileTemplateBloc),
                      BlocProvider.value(value: bpmProcInstBloc),
                      BlocProvider.value(value: chartBloc),
                      BlocProvider.value(value: userListBloc),
                    ],
                    child: TicketFormScreen(
                      procDefId: serviceItem.procDefId!,
                      title: serviceItem.serviceName ?? '',
                      processId: serviceItem.processId,
                      ticketId: serviceItem.id,
                      onClose: () {
                        if (mounted) {
                          Navigator.of(context).pop();
                        }
                      },
                    ),
                  ),
                ),
              );
            }
          }
      }
    }
  }

  Color getServiceColor(ServiceData service) {
    var colorValue = service.color;

    if (colorValue == null) {
      return getColorSkin().primaryBlue;
    }

    String hexString = colorValue.trim();

    if (hexString.isEmpty) {
      return getColorSkin().primaryBlue;
    }

    if (hexString.startsWith("#")) {
      hexString = hexString.substring(1);
    }

    try {
      if (hexString.length == 6) {
        return Color(int.parse("0xFF$hexString"));
      } else if (hexString.length == 8) {
        return Color(int.parse("0x$hexString"));
      }
    } catch (e) {
      debugPrint("Lỗi chuyển đổi mã màu: $hexString - $e");
    }

    return getColorSkin().secondaryColor1;
  }

  @override
  Widget build(BuildContext context) {
    final directChildren = widget.allData
        .where((child) =>
            child.parentId == widget.parent.id &&
            child.notShowingMoblie == false)
        .toList();

    directChildren.sort(
        (a, b) => (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));

    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      elevation: 2,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              width: double.infinity,
              height: 80.h,
              padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
              color: getServiceColor(widget.parent),
              child: Row(
                children: [
                  buildImageFromIcon(widget.parent),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      widget.parent.serviceName ?? '',
                      style: getTypoSkin()
                          .medium16
                          .copyWith(color: getColorSkin().white),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: Container(
                color: Colors.white,
                child: ListView.separated(
                  physics: const ClampingScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: directChildren.length,
                  separatorBuilder: (context, index) => Divider(
                    color: getColorSkin().grey4Background,
                    height: 1.h,
                  ),
                  itemBuilder: (context, index) {
                    final childData = directChildren[index];
                    return buildServiceItem(childData, padding: 16.w);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Phương thức đệ quy để xây dựng các mục dịch vụ ở bất kỳ cấp độ nào
  Widget buildServiceItem(ServiceData item, {required double padding}) {
    final hasChildren = widget.allData.any((child) =>
        child.parentId == item.id && child.notShowingMoblie == false);
    final isItemExpanded = isExpanded(item.id);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: () => toggleExpanded(item),
          child: Container(
            height: 40.h,
            width: double.infinity,
            color: Colors.white.withOpacity(0.9),
            child: Row(
              children: [
                SizedBox(width: padding),

                Expanded(
                  child: Text(
                    item.serviceName ?? '',
                    style: getTypoSkin().buttonRegular14.copyWith(
                          color: getColorSkin().ink1,
                          fontSize: 12,
                        ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),

                if (hasChildren)
                  Container(
                    width: 32.w,
                    height: 32.h,
                    alignment: Alignment.center,
                    child: Icon(
                      isItemExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.chevron_right,
                      color: getColorSkin().primaryBlue,
                      size: 24.w,
                    ),
                  ),

                if (!hasChildren &&
                    (item.serviceType == "3" || item.serviceType == "4"))
                  Container(
                    width: 32.w,
                    height: 32.h,
                    alignment: Alignment.center,
                    child: Icon(
                      item.serviceType == "3"
                          ? Icons.link
                          : Icons.picture_as_pdf,
                      color: getColorSkin().primaryBlue,
                      size: 20.w,
                    ),
                  ),

                // Right padding
                SizedBox(width: 8.w),
              ],
            ),
          ),
        ),

        // Children
        if (isItemExpanded && hasChildren)
          buildChildrenList(item.id, padding + 16.w),
      ],
    );
  }

  Widget buildChildrenList(int? parentId, double padding) {
    final children = widget.allData
        .where((item) =>
            item.parentId == parentId && item.notShowingMoblie == false)
        .toList();

    children.sort(
        (a, b) => (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));

    debugPrint(
        'Building children list for $parentId, found ${children.length} items');

    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: children.length,
      separatorBuilder: (context, index) => Divider(
        height: 1.h,
        color: getColorSkin().grey4Background,
      ),
      itemBuilder: (context, index) {
        final child = children[index];
        return buildServiceItem(child, padding: padding);
      },
    );
  }

  Widget buildImageFromIcon(ServiceData parent) {
    final String? iconData = parent.icon;

    if (iconData == null || iconData.isEmpty) {
      return SizedBox(
        width: 48.w,
        height: 48.h,
      );
    }

    final bool isBase64 =
        iconData.isNotEmpty && RegExp(r'^[A-Za-z0-9+/=]+$').hasMatch(iconData);

    if (isBase64) {
      try {
        return SizedBox(
          width: 48.w,
          height: 48.h,
          child: Center(
            child: Image.memory(
              base64Decode(iconData),
              width: 48.w,
              height: 48.h,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return Icon(Icons.folder, color: Colors.white, size: 48.w);
              },
            ),
          ),
        );
      } catch (e) {
        return SizedBox(
          width: 48.w,
          height: 48.h,
          child: Center(
            child: Icon(Icons.folder, color: Colors.white, size: 48.w),
          ),
        );
      }
    } else {
      return SizedBox(
        width: 48.w,
        height: 48.h,
        child: Center(
          child: Icon(Icons.folder, color: Colors.white, size: 48.w),
        ),
      );
    }
  }
}
