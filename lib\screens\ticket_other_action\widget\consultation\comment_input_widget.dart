import 'package:eapprove/screens/ticket_other_action/widget/mention_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_button.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:path/path.dart' as path;

class CommentInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final List<MentionRange> mentions;
  final bool isPrivate;
  final List<dynamic> attachments;
  final bool isEditing;
  final Function(bool) onPrivacyToggle;
  final VoidCallback onPickFiles;
  final VoidCallback onSend;
  final Function(int) onRemoveAttachment;
  final Function(int) onRemoveExistingFile;
  final bool showExistingAttachments;
  final List<Map<String, dynamic>>? existingFiles;
  // Added to accept a list of maps for user information
  final List<Map<String, dynamic>>? fullUserInfo;

  const CommentInputWidget({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.mentions,
    required this.isPrivate,
    required this.attachments,
    this.isEditing = false,
    required this.onPrivacyToggle,
    required this.onPickFiles,
    required this.onSend,
    required this.onRemoveAttachment,
    required this.onRemoveExistingFile,
    this.showExistingAttachments = false,
    this.existingFiles,
    this.fullUserInfo,
  });

  @override
  State<CommentInputWidget> createState() => _CommentInputWidgetState();
}

class _CommentInputWidgetState extends State<CommentInputWidget> {
  bool _isTextFieldFocused = false;
  late MentionSpecialTextSpanBuilder _specialTextSpanBuilder;
  int _previousTextLength = 0;

  @override
  void initState() {
    super.initState();
    widget.focusNode.addListener(_onFocusChange);
    _specialTextSpanBuilder = MentionSpecialTextSpanBuilder(
      fullUserInfo: widget.fullUserInfo,
      mentions: widget.mentions,
    );
    widget.controller.addListener(_handleTextChange);
  }

  @override
  void didUpdateWidget(CommentInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update the builder if fullUserInfo or mentions change
    if (oldWidget.fullUserInfo != widget.fullUserInfo || oldWidget.mentions != widget.mentions) {
      _specialTextSpanBuilder = MentionSpecialTextSpanBuilder(
        fullUserInfo: widget.fullUserInfo,
        mentions: widget.mentions,
      );
    }
  }

  @override
  void dispose() {
    widget.focusNode.removeListener(_onFocusChange);
    widget.controller.removeListener(_handleTextChange);
    super.dispose();
  }

  void _handleTextChange() {
    final text = widget.controller.text;
    final selection = widget.controller.selection;
    final cursorPosition = selection.baseOffset;

    // Check if text length decreased and cursor is at the end of a mention
    if (text.length < _previousTextLength && cursorPosition > 0) {
      for (var mention in widget.mentions) {
        // Check if cursor is exactly at the end of mention
        if (cursorPosition == mention.start + mention.length) {
          // Check if the text after mention is empty or starts with a space
          final textAfterMention = text.substring(mention.start + mention.length);
          if (textAfterMention.isEmpty || textAfterMention.startsWith(' ')) {
            // Delete the entire mention
            final beforeMention = text.substring(0, mention.start);
            final afterMention = text.substring(mention.start + mention.length);
            widget.controller.text = beforeMention + afterMention;
            widget.controller.selection = TextSelection.collapsed(offset: mention.start);
            widget.mentions.remove(mention);
            setState(() {});
            return;
          }
        }
      }
    }
    _previousTextLength = text.length;
  }

  void _onFocusChange() {
    setState(() {
      _isTextFieldFocused = widget.focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          decoration: BoxDecoration(
            border: Border(top: BorderSide(color: getColorSkin().ink5)),
            color: getColorSkin().white,
          ),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.r),
                    border: Border.all(
                      color: _isTextFieldFocused ? getColorSkin().primaryBlue : getColorSkin().grey4Background,
                    ),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
                  child: KeyboardListener(
                    focusNode: FocusNode(),
                    onKeyEvent: (event) {
                      if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.backspace) {
                        final text = widget.controller.text;
                        final selection = widget.controller.selection;
                        final cursorPosition = selection.baseOffset;

                        // Find if cursor is exactly at the end of mention
                        for (var mention in widget.mentions) {
                          if (cursorPosition == mention.start + mention.length) {
                            // Check if the text after mention is empty or starts with a space
                            final textAfterMention = text.substring(mention.start + mention.length);
                            if (textAfterMention.isEmpty || textAfterMention.startsWith(' ')) {
                              // Delete the entire mention
                              final beforeMention = text.substring(0, mention.start);
                              final afterMention = text.substring(mention.start + mention.length);
                              widget.controller.text = beforeMention + afterMention;
                              widget.controller.selection = TextSelection.collapsed(offset: mention.start);
                              widget.mentions.remove(mention);
                              setState(() {});
                              return;
                            }
                          }
                        }
                      }
                    },
                    child: ExtendedTextField(
                      controller: widget.controller,
                      focusNode: widget.focusNode,
                      specialTextSpanBuilder: _specialTextSpanBuilder,
                      maxLines: null,
                      textInputAction: TextInputAction.newline,
                      onEditingComplete: () {
                        // Handle backspace for mentions
                        final text = widget.controller.text;
                        final selection = widget.controller.selection;
                        final cursorPosition = selection.baseOffset;

                        // Find if cursor is at the end of a mention
                        for (var mention in widget.mentions) {
                          if (cursorPosition == mention.start + mention.length) {
                            // Delete the entire mention
                            final beforeMention = text.substring(0, mention.start);
                            final afterMention = text.substring(mention.start + mention.length);
                            widget.controller.text = beforeMention + afterMention;
                            widget.controller.selection = TextSelection.collapsed(offset: mention.start);
                            widget.mentions.remove(mention);
                            setState(() {});
                            return;
                          }
                        }
                      },
                      onTap: () {
                        setState(() {
                          _isTextFieldFocused = true;
                        });
                      },
                      onChanged: (val) {
                        // Clear mentions if text is empty
                        if (val.isEmpty && widget.mentions.isNotEmpty) {
                          widget.mentions.clear();
                          setState(() {});
                        }
                      },
                      decoration: InputDecoration(
                        hintText: widget.isEditing ? 'Sửa nội dung tham vấn' : 'Nhập nội dung',
                        hintStyle: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink3),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                        isDense: true,
                      ),
                      style: getTypoSkin().bodyRegular14.copyWith(
                            color: getColorSkin().ink1,
                          ),
                    ),
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () => widget.onPrivacyToggle(!widget.isPrivate),
                    icon: SvgPicture.asset(
                      widget.isPrivate ? StringImage.ic_unlock : StringImage.ic_globe,
                      width: 24.w,
                      height: 24.h,
                      colorFilter: ColorFilter.mode(getColorSkin().ink2, BlendMode.srcIn),
                    ),
                  ),
                  if (!DeviceUtils.isTablet)
                    IconButton(
                      onPressed: widget.onPickFiles,
                      icon: Image.asset(StringImage.upload, width: 24.w, height: 24.h),
                      padding: EdgeInsets.zero,
                    ),
                  IconButton(
                    onPressed: widget.controller.text.trim().isNotEmpty || widget.attachments.isNotEmpty
                        ? widget.onSend
                        : null,
                    icon: Icon(
                      Icons.send,
                      color: widget.controller.text.trim().isNotEmpty || widget.attachments.isNotEmpty
                          ? getColorSkin().primaryBlue
                          : getColorSkin().ink4,
                      size: 24,
                    ),
                    padding: EdgeInsets.zero,
                  ),
                ],
              )
            ],
          ),
        ),
        if (DeviceUtils.isTablet)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Chứng từ đính kèm:',
                  style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
                ),
                SizedBox(width: 8.w),
                Container(
                    decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(6.r))),
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: getColorSkin().white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.r),
                            side: BorderSide(color: getColorSkin().ink5, width: 1),
                          ),
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                        ),
                        onPressed: widget.onPickFiles,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(StringImage.ic_upload, width: 20.w, height: 20.h),
                            SizedBox(width: 8.w),
                            Text(
                              'Upload',
                              style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                            ),
                          ],
                        ))),
              ],
            ),
          ),
        if (widget.attachments.isNotEmpty ||
            (widget.showExistingAttachments && widget.existingFiles != null && widget.existingFiles!.isNotEmpty))
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: getColorSkin().ink5)),
              color: getColorSkin().white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.attachments.isNotEmpty)
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: widget.attachments.length,
                    itemBuilder: (context, index) {
                      final file = widget.attachments[index];
                      final name = file.name;
                      final ext = path.extension(name).toLowerCase();
                      return Container(
                        margin: EdgeInsets.only(bottom: 8.h),
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: getColorSkin().grey4Background,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              StringImage.ic_paper_clip,
                              width: 22.w,
                              height: 22.h,
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      name,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().primaryBlue),
                                    ),
                                  ),
                                  Text(
                                    ext,
                                    style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().primaryBlue),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 8.w),
                            GestureDetector(
                              onTap: () => widget.onRemoveAttachment(index),
                              child: SvgPicture.asset(
                                StringImage.ic_delete,
                                width: 20.w,
                                height: 20.h,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                if (widget.showExistingAttachments && widget.existingFiles != null && widget.existingFiles!.isNotEmpty)
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: widget.existingFiles!.length,
                    itemBuilder: (context, index) {
                      final file = widget.existingFiles![index];
                      final name = file['fileName'] ?? '';
                      final ext = path.extension(name).toLowerCase();
                      return Container(
                        margin: EdgeInsets.only(bottom: 8.h),
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: getColorSkin().grey4Background,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              StringImage.ic_paper_clip,
                              width: 22.w,
                              height: 22.h,
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      name,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().primaryBlue),
                                    ),
                                  ),
                                  Text(
                                    ext,
                                    style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().primaryBlue),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 8.w),
                            GestureDetector(
                              onTap: () => widget.onRemoveExistingFile(file['id']),
                              child: SvgPicture.asset(
                                StringImage.ic_delete,
                                width: 20.w,
                                height: 20.h,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
      ],
    );
  }
}

class MentionSpecialTextSpanBuilder extends SpecialTextSpanBuilder {
  final List<Map<String, dynamic>>? fullUserInfo;
  final List<MentionRange> mentions;

  MentionSpecialTextSpanBuilder({
    this.fullUserInfo,
    this.mentions = const [],
  });

  @override
  SpecialText? createSpecialText(String flag,
      {TextStyle? textStyle, SpecialTextGestureTapCallback? onTap, required int index}) {
    return null;
  }

  @override
  TextSpan build(String data, {TextStyle? textStyle, onTap}) {
    List<InlineSpan> inlineList = [];
    int lastIndex = 0;

    if (mentions.isNotEmpty) {
      for (var mention in mentions) {
        if (mention.start > lastIndex) {
          inlineList.add(TextSpan(text: data.substring(lastIndex, mention.start), style: textStyle));
        }

        inlineList.add(TextSpan(
          text: mention.data,
          style: textStyle?.copyWith(color: getColorSkin().primaryBlue),
        ));

        lastIndex = mention.start + mention.length;
      }

      if (lastIndex < data.length) {
        inlineList.add(TextSpan(text: data.substring(lastIndex), style: textStyle));
      }
    } else {
      // Nếu không có mentions trong list, sử dụng regex để tìm @mentions
      final mentionReg = RegExp(r'@[^@\s]+(?: - [^@\s]+)*');
      int start = 0;

      for (final match in mentionReg.allMatches(data)) {
        if (match.start > start) {
          inlineList.add(TextSpan(text: data.substring(start, match.start), style: textStyle));
        }

        inlineList.add(TextSpan(
          text: match.group(0)!,
          style: textStyle?.copyWith(color: getColorSkin().primaryBlue),
        ));

        start = match.end;
      }

      if (start < data.length) {
        inlineList.add(TextSpan(text: data.substring(start), style: textStyle));
      }
    }

    return TextSpan(children: inlineList, style: textStyle);
  }
}
