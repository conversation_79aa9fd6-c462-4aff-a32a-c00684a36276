class FilterDataAuthorizeResponse {
  final int code;
  final String message;
  final FilterDataAuthorize data;

  FilterDataAuthorizeResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory FilterDataAuthorizeResponse.fromJson(Map<String, dynamic> json) {
    return FilterDataAuthorizeResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: FilterDataAuthorize.from<PERSON>son(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class FilterDataAuthorize {
  final List<String> companyCode;
  final List<String> fileName;
  final List<String> endDate;
  final List<String> companyName;
  final List<String> assignName;
  final List<String> description;
  final List<String> updatedUser;
  final List<String> authorityConditions;
  final List<int> effect;
  final List<String> requestCode;
  final List<String> assignUser;
  final List<String> newRequestCode;
  final List<int> serviceId;
  final List<String> newTicket;
  final List<String> assignedUser;
  final List<String> createdUser;
  final List<String> startDate;
  final List<ServiceRange> serviceRange;
  final List<int> ticketId;
  final List<int> status;

  FilterDataAuthorize({
    required this.companyCode,
    required this.fileName,
    required this.endDate,
    required this.companyName,
    required this.assignName,
    required this.description,
    required this.updatedUser,
    required this.authorityConditions,
    required this.effect,
    required this.requestCode,
    required this.assignUser,
    required this.newRequestCode,
    required this.serviceId,
    required this.newTicket,
    required this.assignedUser,
    required this.createdUser,
    required this.startDate,
    required this.serviceRange,
    required this.ticketId,
    required this.status,
  });

  factory FilterDataAuthorize.fromJson(Map<String, dynamic> json) {
    return FilterDataAuthorize(
      companyCode: List<String>.from(json['companyCode'] ?? []),
      fileName: List<String>.from(json['fileName'] ?? []),
      endDate: List<String>.from(json['endDate'] ?? []),
      companyName: List<String>.from(json['companyName'] ?? []),
      assignName: List<String>.from(json['assignName'] ?? []),
      description: List<String>.from(json['description'] ?? []),
      updatedUser: List<String>.from(json['updatedUser'] ?? []),
      authorityConditions: List<String>.from(json['authorityConditions'] ?? []),
      effect: List<int>.from(json['effect'] ?? []),
      requestCode: List<String>.from(json['requestCode'] ?? []),
      assignUser: List<String>.from(json['assignUser'] ?? []),
      newRequestCode: List<String>.from(json['newRequestCode'] ?? []),
      serviceId: List<int>.from(json['serviceId'] ?? []),
      newTicket: List<String>.from(json['newTicket'] ?? []),
      assignedUser: List<String>.from(json['assignedUser'] ?? []),
      createdUser: List<String>.from(json['createdUser'] ?? []),
      startDate: List<String>.from(json['startDate'] ?? []),
      serviceRange: (json['serviceRange'] as List?)
              ?.map((e) => ServiceRange.fromJson(e))
              .toList() ??
          [],
      ticketId: List<int>.from(json['ticketId'] ?? []),
      status: List<int>.from(json['status'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'companyCode': companyCode,
      'fileName': fileName,
      'endDate': endDate,
      'companyName': companyName,
      'assignName': assignName,
      'description': description,
      'updatedUser': updatedUser,
      'authorityConditions': authorityConditions,
      'effect': effect,
      'requestCode': requestCode,
      'assignUser': assignUser,
      'newRequestCode': newRequestCode,
      'serviceId': serviceId,
      'newTicket': newTicket,
      'assignedUser': assignedUser,
      'createdUser': createdUser,
      'startDate': startDate,
      'serviceRange': serviceRange.map((e) => e.toJson()).toList(),
      'ticketId': ticketId,
      'status': status,
    };
  }
}

class ServiceRange {
  final int id;
  final int parentId;
  final String serviceName;
  final String color;
  final String icon;
  final int serviceType;
  final int processId;
  final String description;
  final String? note;
  final String url;
  final int positionPackage;
  final int idOrgChart;
  final bool notShowingWebsite;
  final bool notShowingMoblie;
  final String? hideName;
  final String? visibleName;
  final bool deleted;
  final int masterParentId;
  final List<dynamic> lsChild;

  ServiceRange({
    required this.id,
    required this.parentId,
    required this.serviceName,
    required this.color,
    required this.icon,
    required this.serviceType,
    required this.processId,
    required this.description,
    this.note,
    required this.url,
    required this.positionPackage,
    required this.idOrgChart,
    required this.notShowingWebsite,
    required this.notShowingMoblie,
    this.hideName,
    this.visibleName,
    required this.deleted,
    required this.masterParentId,
    required this.lsChild,
  });

  factory ServiceRange.fromJson(Map<String, dynamic> json) {
    return ServiceRange(
      id: json['id'] ?? 0,
      parentId: json['parentId'] ?? 0,
      serviceName: json['serviceName'] ?? '',
      color: json['color'] ?? '',
      icon: json['icon'] ?? '',
      serviceType: json['serviceType'] ?? 0,
      processId: json['processId'] ?? 0,
      description: json['description'] ?? '',
      note: json['note'],
      url: json['url'] ?? '',
      positionPackage: json['positionPackage'] ?? 0,
      idOrgChart: json['idOrgChart'] ?? 0,
      notShowingWebsite: json['notShowingWebsite'] ?? false,
      notShowingMoblie: json['notShowingMoblie'] ?? false,
      hideName: json['hide_name'],
      visibleName: json['visible_name'],
      deleted: json['deleted'] ?? false,
      masterParentId: json['masterParentId'] ?? 0,
      lsChild: json['lsChild'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'serviceName': serviceName,
      'color': color,
      'icon': icon,
      'serviceType': serviceType,
      'processId': processId,
      'description': description,
      'note': note,
      'url': url,
      'positionPackage': positionPackage,
      'idOrgChart': idOrgChart,
      'notShowingWebsite': notShowingWebsite,
      'notShowingMoblie': notShowingMoblie,
      'hide_name': hideName,
      'visible_name': visibleName,
      'deleted': deleted,
      'masterParentId': masterParentId,
      'lsChild': lsChild,
    };
  }
} 