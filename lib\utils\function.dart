import 'package:eapprove/models/form/individual_info_response_model.dart';
import 'dart:developer' as developer;

List<Map<String, dynamic>> removeDuplicateElements(List<dynamic> items, String filterField) {
  if (items.isEmpty) return [];
  
  final uniqueMap = <String, Map<String, dynamic>>{};
  
  for (final item in items) {
    String? username;
    if (item is IndividualInfo) {
      username = item.username;
    } else if (item is Map) {
      username = item['username'] as String?;
    }
    
    if (username != null && !uniqueMap.containsKey(username)) {
      uniqueMap[username] = item is Map ? item : item.toJson();
    }
  }
  
  return uniqueMap.values.toList();
}
