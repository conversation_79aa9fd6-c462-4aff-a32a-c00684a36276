import 'package:eapprove/screens/login/login_screen.dart';
import 'package:eapprove/screens/ticket_screen/widgets/ticket_model.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/payment_step/widgets/payment_step_1.dart';
import 'package:eapprove/screens/pyc/overview_ticket_board_screen.dart';
import 'package:eapprove/screens/ticket_screen/ticket_view_screen.dart';
import 'package:eapprove/screens/user/user_screen.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/services/global_navigation.dart';

class HomeNavigator extends StatefulWidget {
  final GlobalKey<NavigatorState>? navigatorKey;
  final bool? isEmbedded;
  final Function(String)? onNavigateToSubScreen;

  const HomeNavigator({
    Key? key,
    this.navigatorKey,
    this.isEmbedded,
    this.onNavigateToSubScreen,
  }) : super(key: key);

  @override
  State<HomeNavigator> createState() => _HomeNavigatorState();
}

class _HomeNavigatorState extends State<HomeNavigator> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onNavigateToSubScreen?.call('home');
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          final navigator = widget.navigatorKey?.currentState;
          if (navigator != null && navigator.canPop()) {
            navigator.pop();
            return;
          }
          GlobalNavigation().handleBackNavigation(context);
        }
      },
      child: Navigator(
        key: widget.navigatorKey,
        initialRoute: 'home',
        observers: [
          _HomeNavigatorObserver(onNavigateToSubScreen: widget.onNavigateToSubScreen),
        ],
        onGenerateRoute: (settings) {
          debugPrint('settings: ${settings.name}');
          switch (settings.name) {
            case 'home':
              return MaterialPageRoute(
                settings: const RouteSettings(name: 'home'),
                builder: (_) => OverviewTicketScreen(
                  isEmbedded: widget.isEmbedded,
                ),
              );
            case 'user':
              return MaterialPageRoute(settings: const RouteSettings(name: 'user'), builder: (_) => const UserScreen());
            case 'listTicket':
              final args = settings.arguments as Map<String, dynamic>;
              final filterIndex = args['filterIndex'] as int;
              final tabIndex = args['tabIndex'] as int;
              return MaterialPageRoute(
                  settings: const RouteSettings(name: 'listTicket'),
                  builder: (_) => TicketViewScreen(
                        screenModel: ticketScreens[filterIndex],
                        initialTabIndex: tabIndex,
                        filterIndex: filterIndex,
                      ));
            case 'login':
              return MaterialPageRoute(
                  settings: const RouteSettings(name: 'login'), builder: (_) => const LoginScreen());
            case 'paymentProposal':
              return MaterialPageRoute(
                  settings: const RouteSettings(name: 'paymentProposal'), builder: (_) => const TicketFormScreen());
            case 'payment-step':
              return MaterialPageRoute(
                  settings: const RouteSettings(name: 'payment-step'), builder: (_) => const PaymentStep1Screen());
            // case 'setting':
            //   return MaterialPageRoute(
            //       settings: const RouteSettings(name: 'setting'),
            //       builder: (_) => SettingScreen(
            //         onHomeScreenBack: _onHomeScreenBack,
            //       ));
            default:
              return MaterialPageRoute(
                settings: const RouteSettings(name: 'home'),
                builder: (_) => OverviewTicketScreen(
                  isEmbedded: widget.isEmbedded,
                ),
              );
          }
        },
      ),
    );
  }
}

class _HomeNavigatorObserver extends NavigatorObserver {
  final Function(String)? onNavigateToSubScreen;

  _HomeNavigatorObserver({this.onNavigateToSubScreen});

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    onNavigateToSubScreen?.call(route.settings.name ?? 'home');
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    debugPrint('Popping from: ${route.settings.name}');
    debugPrint('Previous route: ${previousRoute?.settings.name}');
    onNavigateToSubScreen?.call(previousRoute?.settings.name ?? 'home');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute != null) {
      onNavigateToSubScreen?.call(newRoute.settings.name ?? 'home');
    }
  }
}
