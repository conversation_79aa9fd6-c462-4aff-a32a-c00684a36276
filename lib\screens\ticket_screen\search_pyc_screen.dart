import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/common/search_history_manager.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_request_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_request_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/screens/common/search_history.dart';
import 'package:eapprove/screens/handle_ticket/handle_ticket_screen.dart';
import 'package:eapprove/screens/ticket_screen/widgets/pyc_ticket_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:eapprove/assets/StringIcon.dart';

import 'package:eapprove/models/pyc/assistant_pyc/assistant_request_model.dart';

import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';

class PycSearchScreen extends StatefulWidget {
  final int filterIndex;
  final int tabIndex;

  const PycSearchScreen({super.key, this.filterIndex = 0, this.tabIndex = 0});

  @override
  State<PycSearchScreen> createState() => _PycSearchScreenState();
}

class _PycSearchScreenState extends State<PycSearchScreen> {
  static const String historyKey = 'pyc_history_search';

  String searchQuery = '';
  List<String> searchHistory = [];
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    _loadAllPycData();
  }

  Future<void> _loadSearchHistory() async {
    final history = await SearchHistoryManager.getSearchHistory(historyKey: historyKey);
    setState(() {
      searchHistory = history;
    });
  }

  String? _getFilterType(int index) {
    switch (index) {
      case 0:
        return 'ticket';
      case 1:
        return 'execution';
      case 2:
        return 'approval';
      case 3:
        return 'assistant';
      default:
        return null;
    }
  }

  void _onSearchSubmitted(String query) {
    if (DeviceUtils.isTablet) {
      final keyword = query.trim();
      if (keyword.isEmpty) return;
      _searchController.text = keyword;

      setState(() {
        searchQuery = keyword.toLowerCase();
      });
      SearchHistoryManager.addSearchTerm(keyword, historyKey: historyKey).then((_) => _loadSearchHistory());

      context.read<PycBloc>().add(FetchSearchFilterPyc(
            SearchFilterRequestModel(
              search: searchQuery,
              type: _getFilterType(widget.filterIndex),
              page: 1,
              limit: 1000,
            ),
          ));

      Navigator.pop(context, {'search': searchQuery});
    } else {
      final keyword = query.trim();
      _searchController.text = keyword;

      SearchHistoryManager.addSearchTerm(keyword, historyKey: historyKey).then((_) => _loadSearchHistory());

      setState(() {
        searchQuery = keyword.toLowerCase();
      });

      _loadAllPycData(keyword: keyword);
    }
  }

  void _onSearchHistorySelected(String keyword) {
    _searchController.text = keyword;
    _onSearchSubmitted(keyword);
  }

  void _loadAllPycData({String keyword = ''}) {
    switch (widget.filterIndex) {
      case 1:
        final category = _mapFilterIndexToCategory(widget.tabIndex);
        final request = ProcRequestModel.fromCategory(category).copyWith(limit: 50, search: keyword);
        context.read<PycBloc>().add(FetchProcPycList(requestModel: request));
        break;
      case 2:
        final category = _mapAppFilterIndexToCategory(widget.tabIndex);
        final request = ApproveRequestModel.fromCategory(category).copyWith(limit: 50, search: keyword);
        context.read<PycBloc>().add(FetchAppPycList(requestModel: request));
        break;
      case 3:
        final category = _mapAssisFilterIndexToCategory(widget.tabIndex);
        final request = AssisRequestModel.fromCategory(category).copyWith(limit: 50, search: keyword);
        context.read<PycBloc>().add(FetchAssisPycList(requestModel: request));
        break;
      default:
        final category = _mapMyFilterIndexToCategory(widget.tabIndex);
        final request = MyPycRequestModel.fromCategory(category).copyWith(limit: 50, search: keyword);
        context.read<PycBloc>().add(FetchMyPycList(requestModel: request));
    }
  }

  void _handleTicketTap(dynamic ticket) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => HandleTicketScreen(ticket: ticket)),
    );

    if (result == true) {
      context.read<PycBloc>().add(FetchStatusTicket(
            StatusTicketRequest(
              code: 'STATUS_TICKET',
              type: 'system',
              page: 1,
              limit: 9999,
              search: '',
              sortBy: 'id',
              sortType: 'DESC',
              chartId: '',
              status: ['active'],
            ),
          ));

      await Future.delayed(const Duration(milliseconds: 300));
      _loadAllPycData(keyword: searchQuery);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope<dynamic>(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          Navigator.pop(context, {'search': searchQuery});
        }
      },
      child: Scaffold(
        backgroundColor: getColorSkin().white,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(0),
          child: AppBar(
            backgroundColor: getColorSkin().white,
            elevation: 0,
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 8),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                child: Row(
                  children: [
                    IconButton(
                      icon: SvgPicture.asset(StringImage.ic_arrow_left, width: 9.w, height: 18.h),
                      onPressed: () => Navigator.pop(context, true),
                    ),
                    Expanded(
                      child: CustomTextField(
                        controller: _searchController,
                        onSubmitted: _onSearchSubmitted,
                        textInputAction: TextInputAction.search,
                        prefixIcon: SvgPicture.asset(StringImage.ic_search, width: 16.w, height: 16.h),
                        hintText: DeviceUtils.isTablet ? 'Mã tờ trình, Tên tờ trình' : 'Tìm kiếm phiếu yêu cầu',
                        showClearIcon: true,
                        autoFocus: false,
                        onClear: () {
                          setState(() {
                            searchQuery = '';
                            _searchController.clear();
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: searchQuery.isEmpty
                    ? FutureBuilder<List<String>>(
                        future: SearchHistoryManager.getSearchHistory(historyKey: historyKey),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting) {
                            return const Center(child: CircularProgressIndicator());
                          }
                          if (snapshot.hasError) {
                            return const Center(child: Text('Lỗi khi tải lịch sử tìm kiếm'));
                          }
                          searchHistory = snapshot.data ?? [];
                          return _buildSearchHistory();
                        },
                      )
                    : _buildSearchResultsBloc(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchHistory() {
    return SearchHistory(
      searchHistory: searchHistory,
      onSearchSelected: _onSearchHistorySelected,
      onClearAll: () {
        SearchHistoryManager.clearSearchHistory(historyKey: historyKey).then((_) => _loadSearchHistory());
      },
    );
  }

  Widget _buildSearchResultsBloc() {
    return BlocBuilder<PycBloc, PycState>(
      builder: (context, state) {
        if (state.status == PycStatus.loading) {
          return ListView.builder(
            padding: EdgeInsets.all(12.w),
            itemCount: 10,
            itemBuilder: (_, __) => AppConstraint.buildShimmer(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: 150.w,
                      height: 14.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Container(
                          width: 16.w,
                          height: 16.h,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        SizedBox(width: 6.w),
                        Container(
                          width: 80.w,
                          height: 12.h,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          width: 80.w,
                          height: 24.h,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        List<dynamic> allItems = [];
        if (widget.filterIndex == 1) {
          allItems = state.procPycResponseModel?.data.content ?? [];
        } else if (widget.filterIndex == 2) {
          allItems = state.appPycResponseModel?.data.content ?? [];
        } else if (widget.filterIndex == 3) {
          allItems = state.assisPycResponseModel?.data.content ?? [];
        } else {
          allItems = state.myPycResponseModel?.data.content ?? [];
        }

        if (allItems.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(StringImage.ic_no_result, width: 120.w, height: 120.h),
                SizedBox(height: 16.h),
                Text(
                  'Không tìm thấy phiếu nào phù hợp',
                  style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(12.w),
          itemCount: allItems.length,
          itemBuilder: (context, index) {
            final item = allItems[index];
            if (widget.filterIndex == 1) return _buildProcPycItem(item);
            if (widget.filterIndex == 2) return _buildApprovePycItem(item);
            if (widget.filterIndex == 3) return _buildAssistantPycItem(item);
            return _buildMyPycItem(item);
          },
        );
      },
    );
  }

  Widget _buildProcPycItem(ProcContent item) {
    return GestureDetector(
      onTap: () => _handleTicketTap(item),
      child: PycTicketCard(
        title: item.procTitle ?? 'Không có tiêu đề',
        serviceName: item.serviceName ?? '',
        createdTime: item.ticketCreatedTime,
        status: item.ticketStatus,
      ),
    );
  }

  Widget _buildApprovePycItem(ApproveContent item) {
    return GestureDetector(
      onTap: () => _handleTicketTap(item),
      child: PycTicketCard(
        title: item.procTitle ?? 'Không có tiêu đề',
        serviceName: item.serviceName ?? '',
        createdTime: item.ticketCreatedTime,
        status: item.ticketStatus,
      ),
    );
  }

  Widget _buildAssistantPycItem(AssisContent item) {
    return GestureDetector(
      onTap: () => _handleTicketTap(item),
      child: PycTicketCard(
        title: item.ticketTitle ?? 'Không có tiêu đề',
        serviceName: item.procServiceName ?? '',
        createdTime: item.ticketCreatedTime,
        status: item.ticketStatus,
      ),
    );
  }

  Widget _buildMyPycItem(MyPycContent item) {
    return GestureDetector(
      onTap: () => _handleTicketTap(item),
      child: PycTicketCard(
        title: item.ticketTitle ?? 'Không có tiêu đề',
        serviceName: item.procServiceName ?? '',
        createdTime: item.ticketCreatedTime,
        status: item.ticketStatus,
      ),
    );
  }

  ProcPycTicketCategory _mapFilterIndexToCategory(int index) {
    switch (index) {
      case 0:
        return ProcPycTicketCategory.execution;
      case 1:
        return ProcPycTicketCategory.completed;
      case 2:
        return ProcPycTicketCategory.returned;
      default:
        return ProcPycTicketCategory.canceled;
    }
  }

  ApproveTicketCategory _mapAppFilterIndexToCategory(int index) {
    switch (index) {
      case 0:
        return ApproveTicketCategory.approval;
      case 1:
        return ApproveTicketCategory.approved;
      case 2:
        return ApproveTicketCategory.returned;
      default:
        return ApproveTicketCategory.canceled;
    }
  }

  AssisPycTicketCategory _mapAssisFilterIndexToCategory(int index) {
    switch (index) {
      case 0:
        return AssisPycTicketCategory.execution;
      case 1:
        return AssisPycTicketCategory.completed;
      case 2:
        return AssisPycTicketCategory.sharedFollowing;
      default:
        return AssisPycTicketCategory.canceled;
    }
  }

  MyPycTicketCategory _mapMyFilterIndexToCategory(int index) {
    switch (index) {
      case 0:
        return MyPycTicketCategory.approving;
      case 1:
        return MyPycTicketCategory.completed;
      case 2:
        return MyPycTicketCategory.returned;
      case 3:
        return MyPycTicketCategory.canceled;
      case 4:
        return MyPycTicketCategory.draft;
      case 5:
        return MyPycTicketCategory.sharedFollowing;
      default:
        return MyPycTicketCategory.shared;
    }
  }
}
