import 'package:eapprove/common/form/form_builder.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/helper/data_form.dart';
import 'package:eapprove/helper/test_form_data.dart';
import 'package:eapprove/models/form/form_model.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/utils/form_parser.dart';
import 'package:flutter/material.dart';

class TestScreen extends StatefulWidget {
  // static const routeName = "/notification_screen";
  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => const TestScreen());
  }
  const TestScreen({super.key});

  @override
  State<TestScreen> createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  FormModel? _formModel;
  // late FormBuilder _formBuilder;
  

  @override
  void initState() {
    super.initState();
    _loadFormData();
  }

  void _loadFormData() {
    // final formData = dataForm["form"] as List;
    final formData = testFormData["form"] as List;
    _formModel = FormModel(
      key: DateTime.now().toString(),
      formItems: formData.map((item) => FormItemInfo.fromJson(item)).toList(),
    );
    // _formBuilder = FormBuilder();
  }

  @override
  Widget build(BuildContext context) {
    if (_formModel == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Form Test'),
      ),
      body: Expanded(
        child: SingleChildScrollView(
          // child: _formBuilder.buildFormFromModel(
          //   _formModel!,
          //   context,
          //   onChange: (name, value) {},
          //   stateManager: FormStateManager(),
          // ),
        ),
      ),
    );
  }
}