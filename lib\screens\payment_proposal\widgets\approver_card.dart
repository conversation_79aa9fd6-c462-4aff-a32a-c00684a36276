import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ApproverCard extends StatefulWidget {
  const ApproverCard({super.key});

  @override
  State<ApproverCard> createState() => _ApproverCardState();
}

class _ApproverCardState extends State<ApproverCard> {
  @override
  Widget build(BuildContext context) {
    return CustomExpandedList<String>(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        childPadding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
        title: "2. Người phê duyệt",
        isExpanded: true,
        titleStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        child: CustomDropdownMenu(
          dropdownHeight: 40.h,
          label: '<PERSON>ại tờ',
          options: [
            SelectItem(label: 'Đề nghị thanh toán', value: 'payment_request'),
            SelectItem(label: 'Đề nghị thanh toán', value: 'payment_request1'),
            SelectItem(label: 'Đề nghị thanh toán', value: 'payment_request2'),
          ],
          onSelected: (value) {},
          isFilled: true,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              border:Border.all(color: getColorSkin().lightGray, width: 1)
          ),
          defaultValue: SelectItem(label: 'Đề nghị thanh toán', value: 'payment_request'),
          showDeleteIcon: false,
        ),);
  }
}
