import 'package:eapprove/models/pyc/approve_pyc/approve_request_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_request_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:equatable/equatable.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_request_model.dart';
import 'package:flutter/material.dart';

class PycEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class FetchServiceList extends PycEvent {
  final RequestListService requestModel;
  final bool isLoadMore;
  FetchServiceList({required this.requestModel, this.isLoadMore = false});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchAssigneeList extends PycEvent {
  final ListAssigneeRequestModel requestModel;
  FetchAssigneeList({required this.requestModel});
}

class FetchListChartId extends PycEvent {
  @override
  List<Object?> get props => [];
}

class FetchOrgChartList extends PycEvent {
  final ChartFilterRequestModel requestModel;

  FetchOrgChartList({required this.requestModel});

  @override
  List<Object?> get props => [requestModel];
}

class FetchMyPycList extends PycEvent {
  final MyPycRequestModel? requestModel;
  final bool? isLoadMore;

  FetchMyPycList({this.requestModel, this.isLoadMore});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchProcPycList extends PycEvent {
  final ProcRequestModel? requestModel;
  final bool? isLoadMore;

  FetchProcPycList({this.requestModel, this.isLoadMore});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchAppPycList extends PycEvent {
  final ApproveRequestModel? requestModel;
  final bool? isLoadMore;

  FetchAppPycList({this.requestModel, this.isLoadMore});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchAssisPycList extends PycEvent {
  final AssisRequestModel? requestModel;
  final bool? isLoadMore;

  FetchAssisPycList({this.requestModel, this.isLoadMore});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchFilter extends PycEvent {
  final FilterRequestModel requestModel;

  FetchFilter(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchTicketFilter extends PycEvent {
  final FilterTicketRequestModel requestModel;
  final bool isLoadMore;

  FetchTicketFilter(this.requestModel, {this.isLoadMore = false});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchTaskDefKeyList extends PycEvent {
  final ListTaskDefKeyRequestModel requestModel;

  FetchTaskDefKeyList(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchListCreatedUser extends PycEvent {
  final ListCreatedUserRequestModel requestModel;
  final int? chartId;

  FetchListCreatedUser({
    required this.requestModel,
    this.chartId,
  });
}

class FetchPriorityList extends PycEvent {
  final ListPriorityRequestModel requestModel;

  FetchPriorityList(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchSearchFilterPyc extends PycEvent {
  final SearchFilterRequestModel requestModel;
  final bool isLoadMore;

  FetchSearchFilterPyc(this.requestModel, {this.isLoadMore = false});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchMyPycCount extends PycEvent {
  final MyPycCountRequestModel requestModel;

  FetchMyPycCount(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchProcAppCount extends PycEvent {
  final ProcAppCountRequestModel requestModel;

  FetchProcAppCount(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchAssisPycCount extends PycEvent {
  final AssisPycCountRequestModel requestModel;

  FetchAssisPycCount(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchStatusTicket extends PycEvent {
  final StatusTicketRequest requestModel;

  FetchStatusTicket(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class FetchStatusTask extends PycEvent {
  final StatusTicketRequest requestModel;

  FetchStatusTask(this.requestModel);

  @override
  List<Object?> get props => [requestModel];
}

class DeleteDraftTicket extends PycEvent {
  final int id;
  final BuildContext context;

  DeleteDraftTicket(this.id, this.context);

  @override
  List<Object?> get props => [id];
}
