import 'dart:ui';

class StyleParser {
  static Map<String, dynamic> parseInlineStyle(String? style) {
    final Map<String, dynamic> result = {};
    if (style == null || style.isEmpty) return result;

    final declarations = style.split(';').where((s) => s.trim().isNotEmpty);

    for (final declaration in declarations) {
      final parts = declaration.split(':');
      if (parts.length == 2) {
        final property = parts[0].trim().toLowerCase();
        final value = parts[1].trim();

        switch (property) {
          case 'color':
            result['color'] = _parseColor(value);
            break;
          case 'font-size':
            result['fontSize'] = _parseFontSize(value);
            break;
          case 'font-family':
            result['fontFamily'] = value.replaceAll('"', '').replaceAll("'", '');
            break;
          case 'font-weight':
            result['fontWeight'] = _parseFontWeight(value);
            break;
          case 'text-decoration':
            result['textDecoration'] = _parseTextDecoration(value);
            break;
        }
      }
    }

    return result;
  }

  static Color? _parseColor(String value) {
    final rgbMatch = RegExp(r'rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)').firstMatch(value);
    if (rgbMatch != null) {
      final r = int.parse(rgbMatch.group(1)!);
      final g = int.parse(rgbMatch.group(2)!);
      final b = int.parse(rgbMatch.group(3)!);
      return Color.fromRGBO(r, g, b, 1.0);
    }

    final rgbaMatch = RegExp(r'rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)').firstMatch(value);
    if (rgbaMatch != null) {
      final r = int.parse(rgbaMatch.group(1)!);
      final g = int.parse(rgbaMatch.group(2)!);
      final b = int.parse(rgbaMatch.group(3)!);
      final a = double.parse(rgbaMatch.group(4)!);
      return Color.fromRGBO(r, g, b, a);
    }

    if (value.startsWith('#')) {
      return Color(int.parse(value.substring(1), radix: 16) + 0xFF000000);
    }

    return null;
  }

  static double? _parseFontSize(String value) {
    final match = RegExp(r'(\d+(?:\.\d+)?)').firstMatch(value);
    if (match != null) {
      return double.tryParse(match.group(1)!);
    }
    return null;
  }

  static FontWeight? _parseFontWeight(String value) {
    switch (value.toLowerCase()) {
      case 'bold':
      case '700':
        return FontWeight.w700;
      case 'normal':
      case '400':
        return FontWeight.w400;
      case '500':
        return FontWeight.w500;
      case '600':
        return FontWeight.w600;
      default:
        return null;
    }
  }

  static TextDecoration? _parseTextDecoration(String value) {
    switch (value.toLowerCase()) {
      case 'underline':
        return TextDecoration.underline;
      case 'line-through':
        return TextDecoration.lineThrough;
      case 'none':
        return TextDecoration.none;
      default:
        return null;
    }
  }
}