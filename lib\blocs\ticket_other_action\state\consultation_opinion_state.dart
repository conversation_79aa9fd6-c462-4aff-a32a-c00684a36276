import 'package:equatable/equatable.dart';
import 'package:eapprove/models/ticket_other_action/consultation_opinion_model.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';

class ConsultationOpinionState extends Equatable {
  final ServiceStatus status;
  final List<ConsultationOpinionModel> opinions;
  final TicketOtherActionResponse opinionsResponse;
  final String? errorMessage;

  const ConsultationOpinionState({
    this.status = ServiceStatus.initial,
    this.opinions = const [],
    this.opinionsResponse = TicketOtherActionResponse.empty,
    this.errorMessage,
  });

  ConsultationOpinionState copyWith({
    ServiceStatus? status,
    List<ConsultationOpinionModel>? opinions,
    TicketOtherActionResponse? opinionsResponse,
    String? errorMessage,
  }) {
    return ConsultationOpinionState(
      status: status ?? this.status,
      opinions: opinions ?? this.opinions,
      opinionsResponse: opinionsResponse ?? this.opinionsResponse,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, opinions, opinionsResponse, errorMessage];
}
