import 'package:eapprove/repositories/notification_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'notification_event.dart';
import 'notification_state.dart';

class NotiBloc extends Bloc<NotiEvent, NotiState> {
  final NotiRepository repository;

  NotiBloc({required NotiRepository notiRepository})
      : repository = notiRepository,
        super(const NotiInitial()) {
    on<FetchUnreadAmount>(_onFetchUnreadAmount);
    on<ResetUnreadAmount>(_onResetUnreadAmount);
    on<GetNotification>(_onGetNotification);
    on<ReadNotification>(_onReadNotification);
    on<ShowLoadingBeforeFetch>(_onShowLoadingBeforeFetch);
    // on<ReadNoti>(_onGetNotification);
  }

  /// Fetch notification list from repository
  Future<void> _onGetNotification(
    GetNotification event,
    Emitter<NotiState> emit,
  ) async {
    emit(const NotiLoading());
    try {
      final notifications = await repository.getNotifications(
        page: event.page,
        size: event.size,
        systems: event.systems,
        search: event.search,
      );
      emit(NotiLoaded(notifications));

      // try {
      //   final unreadModel = await repository.fetchUnreadAmount();
      //   emit(UnreadLoaded((unreadModel.data?.count?['EAPP'] as num?)?.toInt() ?? 0));
      // } catch (_) {
      //   emit(const UnreadLoaded(0));
      // }
    } catch (_) {
      emit(const NotiError("Failed to fetch notifications"));
    }
  }

  /// Fetch unread notification count
// notification_bloc.dart
// notification_bloc.dart
  Future<void> _onFetchUnreadAmount(FetchUnreadAmount _, Emitter<NotiState> emit) async {
    try {
      final unread = await repository.fetchUnreadAmount();
      emit(UnreadLoaded((unread.data?.count?['EAPP'] ?? 0)));
    } catch (_) {
      emit(const UnreadLoaded(0));
    }
  }

  Future<bool> getNotificationStatus() async {
    try {
      return await repository.getNotificationSettings();
    } catch (e) {
      return false;
    }
  }

  Future<bool> updateNotificationStatus(bool enable) async {
    try {
      return await repository.updateNotificationSettings(enable);
    } catch (e) {
      return false;
    }
  }

  Future<void> _onResetUnreadAmount(ResetUnreadAmount event, Emitter<NotiState> emit) async {
    emit(const NotiLoading());
    try {
      final notifications = await repository.getNotifications();
            for (var notification in notifications) {
        if (notification.id != null) {
          try {
            await repository.markAsRead(notification.id!);
          } catch (e) {
            debugPrint('Error marking notification ${notification.id} as read: $e');
          }
        }
      }
      
      final updatedNotifications = await repository.getNotifications();
      emit(NotiLoaded(updatedNotifications));
      emit(const UnreadLoaded(0));
    } catch (e, stacktrace) {
      debugPrint('Error in _onResetUnreadAmount: $e\n$stacktrace');
      emit(const UnreadLoaded(0));
    }
  }

  Future<void> _onReadNotification(
    ReadNotification event,
    Emitter<NotiState> emit,
  ) async {
    try {
      await repository.markAsRead(event.id);
      // Optionally refresh notifications or unread count
      final notifications = await repository.getNotifications(
        page: 0,
        size: 20, // Adjust based on your pagination settings
        systems: ['EAPP'], // Adjust based on your app's systems
      );
      emit(NotiLoaded(notifications));

      try {
        final unreadModel = await repository.fetchUnreadAmount();
        emit(UnreadLoaded((unreadModel.data?.count as num?)?.toInt() ?? 0));
      } catch (_) {
        emit(const UnreadLoaded(0));
      }
    } catch (e) {
      emit(NotiError('Failed to mark notification as read: ${e.toString()}'));
    }
  }

  Future<void> _onShowLoadingBeforeFetch(
    ShowLoadingBeforeFetch event,
    Emitter<NotiState> emit,
  ) async {
    emit(const NotiLoading());
    try {
      final notifications = await repository.getNotifications(
        page: 0,
        size: 20,
        systems: ['EAPP'],
      );
      emit(NotiLoaded(notifications));

      try {
        final unreadModel = await repository.fetchUnreadAmount();
        emit(UnreadLoaded((unreadModel.data?.count?['EAPP'] as num?)?.toInt() ?? 0));
      } catch (_) {
        emit(const UnreadLoaded(0));
      }
    } catch (_) {
      emit(const NotiError("Failed to fetch notifications"));
    }
  }
}