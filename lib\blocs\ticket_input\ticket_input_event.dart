import 'package:equatable/equatable.dart';

abstract class TicketInputEvent extends Equatable {
  const TicketInputEvent();

  @override
  List<Object> get props => [];
}

class LoadTicketInput extends TicketInputEvent {
  final String taskId;
  final String type;
  final String? token;
  final String? chart;

  const LoadTicketInput({
    required this.taskId,
    required this.type,
    this.token = '',
    this.chart = '',
  });

  @override
  List<Object> get props => [taskId, type, token ?? '', chart ?? ''];
}