import 'package:eapprove/models/form/user_info_model.dart';
import 'package:eapprove/models/user/user_info_model.dart';
import 'package:equatable/equatable.dart';

abstract class UserState extends Equatable {
  @override
  List<Object?> get props => [];
}

class UserInitial extends UserState {}

class UserLoading extends UserState {}

class UserLoaded extends UserState {
  final UserInfoResponse userInfo;

  UserLoaded(this.userInfo);

  @override
  List<Object?> get props => [userInfo];
}

class UserError extends UserState {
  final String message;

  UserError(this.message);

  @override
  List<Object?> get props => [message];
}

class UserInfoByUsername extends UserState {
  final UserInfoByUserNameResponse userInfoByUserNameResponse;

  UserInfoByUsername(this.userInfoByUserNameResponse);

  @override
  List<Object?> get props => [userInfoByUserNameResponse];
}
