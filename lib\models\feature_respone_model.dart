class Feature {
  int? code;
  String? message;
  Data? data;

  Feature({this.code, this.message, this.data});

  Feature.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Features>? features;
  FeatureCheckData? featureCheckData;

  Data({this.features, this.featureCheckData});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['features'] != null) {
      features = <Features>[];
      json['features'].forEach((v) {
        features!.add(Features.fromJson(v));
      });
    }
    featureCheckData =
        json['featureCheckData'] != null ? FeatureCheckData.fromJson(json['featureCheckData']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (features != null) {
      data['features'] = features!.map((v) => v.toJson()).toList();
    }
    if (featureCheckData != null) {
      data['featureCheckData'] = featureCheckData!.toJson();
    }
    return data;
  }
}

class Features {
  int? id;
  String? name;
  String? link;
  Null parentId;
  Null description;
  String? icon;
  int? position;
  int? featurePosition;
  int? groupId;
  Null createdTime;
  Null createdUser;
  Null modifiedTime;
  Null modifiedUser;
  int? showType;
  int? featureType;
  bool? isPublic;
  Null isMobile;
  Null hasChild;
  bool? enableType;
  List<LsChild>? lsChild;

  Features(
      {this.id,
      this.name,
      this.link,
      this.parentId,
      this.description,
      this.icon,
      this.position,
      this.featurePosition,
      this.groupId,
      this.createdTime,
      this.createdUser,
      this.modifiedTime,
      this.modifiedUser,
      this.showType,
      this.featureType,
      this.isPublic,
      this.isMobile,
      this.hasChild,
      this.enableType,
      this.lsChild});

  Features.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    link = json['link'];
    parentId = json['parentId'];
    description = json['description'];
    icon = json['icon'];
    position = json['position'];
    featurePosition = json['featurePosition'];
    groupId = json['groupId'];
    createdTime = json['createdTime'];
    createdUser = json['createdUser'];
    modifiedTime = json['modifiedTime'];
    modifiedUser = json['modifiedUser'];
    showType = json['showType'];
    featureType = json['featureType'];
    isPublic = json['isPublic'];
    isMobile = json['isMobile'];
    hasChild = json['hasChild'];
    enableType = json['enableType'];
    if (json['lsChild'] != null) {
      lsChild = <LsChild>[];
      json['lsChild'].forEach((v) {
        lsChild!.add(LsChild.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['link'] = link;
    data['parentId'] = parentId;
    data['description'] = description;
    data['icon'] = icon;
    data['position'] = position;
    data['featurePosition'] = featurePosition;
    data['groupId'] = groupId;
    data['createdTime'] = createdTime;
    data['createdUser'] = createdUser;
    data['modifiedTime'] = modifiedTime;
    data['modifiedUser'] = modifiedUser;
    data['showType'] = showType;
    data['featureType'] = featureType;
    data['isPublic'] = isPublic;
    data['isMobile'] = isMobile;
    data['hasChild'] = hasChild;
    data['enableType'] = enableType;
    if (lsChild != null) {
      data['lsChild'] = lsChild!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LsChild {
  int? id;
  String? name;
  String? link;
  int? parentId;
  Null description;
  Null icon;
  int? position;
  int? featurePosition;
  int? groupId;
  Null createdTime;
  Null createdUser;
  Null modifiedTime;
  Null modifiedUser;
  int? showType;
  int? featureType;
  bool? isPublic;
  Null isMobile;
  Null hasChild;
  bool? enableType;
  List<Null>? lsChild;

  LsChild(
      {this.id,
      this.name,
      this.link,
      this.parentId,
      this.description,
      this.icon,
      this.position,
      this.featurePosition,
      this.groupId,
      this.createdTime,
      this.createdUser,
      this.modifiedTime,
      this.modifiedUser,
      this.showType,
      this.featureType,
      this.isPublic,
      this.isMobile,
      this.hasChild,
      this.enableType,
      this.lsChild});

  LsChild.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    link = json['link'];
    parentId = json['parentId'];
    description = json['description'];
    icon = json['icon'];
    position = json['position'];
    featurePosition = json['featurePosition'];
    groupId = json['groupId'];
    createdTime = json['createdTime'];
    createdUser = json['createdUser'];
    modifiedTime = json['modifiedTime'];
    modifiedUser = json['modifiedUser'];
    showType = json['showType'];
    featureType = json['featureType'];
    isPublic = json['isPublic'];
    isMobile = json['isMobile'];
    hasChild = json['hasChild'];
    enableType = json['enableType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['link'] = link;
    data['parentId'] = parentId;
    data['description'] = description;
    data['icon'] = icon;
    data['position'] = position;
    data['featurePosition'] = featurePosition;
    data['groupId'] = groupId;
    data['createdTime'] = createdTime;
    data['createdUser'] = createdUser;
    data['modifiedTime'] = modifiedTime;
    data['modifiedUser'] = modifiedUser;
    data['showType'] = showType;
    data['featureType'] = featureType;
    data['isPublic'] = isPublic;
    data['isMobile'] = isMobile;
    data['hasChild'] = hasChild;
    data['enableType'] = enableType;

    return data;
  }
}

class FeatureCheckData {
  int? countDataByBpmExecution;
  int? countDataByBpmAssistant;
  int? countDataByAssign;

  FeatureCheckData({this.countDataByBpmExecution, this.countDataByBpmAssistant, this.countDataByAssign});

  FeatureCheckData.fromJson(Map<String, dynamic> json) {
    countDataByBpmExecution = json['countDataByBpmExecution'];
    countDataByBpmAssistant = json['countDataByBpmAssistant'];
    countDataByAssign = json['countDataByAssign'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['countDataByBpmExecution'] = countDataByBpmExecution;
    data['countDataByBpmAssistant'] = countDataByBpmAssistant;
    data['countDataByAssign'] = countDataByAssign;
    return data;
  }
}
