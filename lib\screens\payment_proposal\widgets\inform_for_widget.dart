import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/models/account/chart_active_response_model.dart';
import 'package:eapprove/widgets/custom_dropdown_multi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:developer' as developer;

class InformForCard extends StatefulWidget {
  final List<ChartData> chartData;
  final Function(List<SelectItem>) onSelected;
  const InformForCard({super.key, required this.chartData, required this.onSelected});

  @override
  State<InformForCard> createState() => _InformForCardState();
}

class _InformForCardState extends State<InformForCard> with AutomaticKeepAliveClientMixin {
  bool isEmailNotification = false;
  bool isAssistantCheck = false;
  List<SelectItem> selectedPersons = [];
  bool _hasRequestedData = false;
  // Map to track users by username
  Map<String, List<SelectItem>> usersByUsername = {};

  // This ensures the widget's state is preserved
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    final Box authBox = Hive.box('authentication');
    final String? username = authBox.get('username');
    developer.log('username: $username');
    developer.log('chartData: ${widget.chartData.first.id}');
    context.read<FormBloc>().add(
      GetAccountMultiChartRequested(
        username: '',
        chartIds: widget.chartData.first.id.toString(),
      ),
    );
  }

  // Display text for selected users
  String getSelectedDisplayText() {
    if (selectedPersons.isEmpty) {
      return 'Chọn người nhận thông báo';
    }

    // If more than one user is selected, show count
    if (selectedPersons.length > 1) {
      return 'Đã chọn ${selectedPersons.length} người';
    }

    // Show the label of the single selected user
    return selectedPersons.first?.label ?? 'Chọn người nhận thông báo';
  }

  // Handle selection of all users with same username
  void handleUserSelection(SelectItem? selectedItem) {
    // if (selectedItem == null) {
    //   setState(() {
    //     selectedPersons = [];
    //   });
    //   return;
    // }

    // Extract username from the label (format: username_fullname_position_shortNameChart)
    // final parts = selectedItem.label.split('_');
    // if (parts.isEmpty) return;

    // final username = parts[0];

    // Call GetAccountMultiChartRequested
    // context.read<FormBloc>().add(
    //   GetAccountMultiChartRequested(
    //     username: username,
    //     chartIds: widget.chartData.first.id.toString(),
    //   ),
    // );
  }

  @override
  Widget build(BuildContext context) {
    // Call the super method for AutomaticKeepAliveClientMixin
    super.build(context);

    return BlocBuilder<FormBloc, FormInfoState>(
      builder: (context, formState) {
        // Handle accountMultiChartResponse
        if (formState.accountMultiChartResponse != null) {
          final response = formState.accountMultiChartResponse!;
          if (response.code == 200 && response.data.isNotEmpty) {
            // Update the selectedPersons list with the new data
            setState(() {
              selectedPersons = response.data.map((account) {
                final label = "${account.username}_${account.firstname} ${account.lastname}_${account.chartNodeName}_${account.shortName}";
                return SelectItem(
                  label: label,
                  value: account.id.toString(),
                );
              }).toList();
            });
          }
        }

        return Container(
          margin: EdgeInsets.only(top: 12.h),
          decoration: BoxDecoration(
            color: getColorSkin().white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: getColorSkin().lightGray, width: 1)
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomDropdownMulti(
                  label: 'Thông báo cho',
                  placeholder: 'Chọn người nhận thông báo',
                  options: formState.accountMultiChartResponse?.data.map((account) {
                    final label = "${account.username}_${account.firstname} ${account.lastname}_${account.chartNodeName}_${account.shortName}";
                    return SelectItem(
                      label: label,
                      value: account.id.toString(),
                    );
                  }).toList() ?? [],
                  onSelected: (selectedItems) {
                    setState(() {
                      selectedPersons = selectedItems;
                    });
                    widget.onSelected(selectedItems);
                  },
                  isFilled: true,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: getColorSkin().lightGray, width: 1),
                    color: getColorSkin().white,
                  ),
                  isDisabled: false,
                  dropdownHeight: 40.h,
                ),

                // If multiple users with same username are selected, show them
                if (selectedPersons.length > 1)
                  Padding(
                    padding: EdgeInsets.only(top: 8.h),
                    child: Container(
                      padding: EdgeInsets.all(8.w),
                      decoration: BoxDecoration(
                        color: getColorSkin().whiteSmoke,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Selected users:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 12.sp,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          ...selectedPersons.map((item) {
                            final parts = item.label.split('_');
                            return Padding(
                              padding: EdgeInsets.only(bottom: 2.h),
                              child: Text(
                                parts.length > 1 ? '${parts[1]} (${parts[0]})' : item.label,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}