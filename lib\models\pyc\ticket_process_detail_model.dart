
class TicketProcessDetailModel {
  final dynamic code;
  final TicketProcessDetailData? data;
  final String message;

  TicketProcessDetailModel({
    required this.code,
    this.data,
    required this.message,
  });

  factory TicketProcessDetailModel.fromJson(Map<dynamic, dynamic> json) {
    return TicketProcessDetailModel(
      code: json['code'],
      data: json['data'] != null ? TicketProcessDetailData.from<PERSON>son(json['data']) : null,
      message: json['message'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'code': code,
      'data': data?.toJson(),
      'message': message,
    };
  }
}
class TicketProcessDetailData {
  final dynamic id;
  final dynamic taskId;
  final dynamic taskDefKey;
  final dynamic taskName;
  final dynamic taskPriority;
  final dynamic taskAssignee;
  final dynamic taskCreatedTime;
  final dynamic taskStartedTime;
  final dynamic ticketStartUserId;
  final dynamic taskSla;
  final dynamic taskFinishedTime;
  final dynamic taskDoneTime;
  final dynamic taskStatus;
  final dynamic procInstId;
  final dynamic endKey;
  final dynamic procDefId;
  final dynamic taskType;
  final dynamic affected;
  final dynamic listStatus;
  final List<FormVariable>? listVariables;
  final ResponseTime? responseTime;
  final FinishTime? finishTime;
  final dynamic newTaskId;
  final dynamic startPermission;
  final dynamic newStatus;
  final dynamic printId;
  final dynamic ticketId;
  final List<SignForm>? listSignForm;
  final dynamic editPermission;
  final dynamic ticketProcDefId;
  final dynamic fullName;
  final List<dynamic>? taskUsers;
  final dynamic priorityId;
  final dynamic color;
  final List<SignedFile>? signedFiles;
  final dynamic newId;
  final dynamic draftVariables;
  final TaskActionUser? taskActionUser;
  final Map<dynamic, dynamic>? taskOrgAssignee;
  final dynamic taskProcDefId;
  final String? requestCode;
  final String? forceViewUrl;

  TicketProcessDetailData({
    this.id,
    this.taskId,
    this.taskDefKey,
    this.taskName,
    this.taskPriority,
    this.taskAssignee,
    this.taskCreatedTime,
    this.taskStartedTime,
    this.ticketStartUserId,
    this.taskSla,
    this.taskFinishedTime,
    this.taskDoneTime,
    this.taskStatus,
    this.procInstId,
    this.endKey,
    this.procDefId,
    this.taskType,
    this.affected,
    this.listStatus,
    this.listVariables,
    this.responseTime,
    this.finishTime,
    this.newTaskId,
    this.startPermission,
    this.newStatus,
    this.printId,
    this.ticketId,
    this.listSignForm,
    this.editPermission,
    this.ticketProcDefId,
    this.fullName,
    this.taskUsers,
    this.priorityId,
    this.color,
    this.signedFiles,
    this.newId,
    this.draftVariables,
    this.taskActionUser,
    this.taskOrgAssignee,
    this.taskProcDefId,
    this.requestCode,
    this.forceViewUrl,
  });

  factory TicketProcessDetailData.fromJson(dynamic json) {
    return TicketProcessDetailData(
      id: json['id'],
      taskId: json['taskId'],
      taskDefKey: json['taskDefKey'],
      taskName: json['taskName'],
      taskPriority: json['taskPriority'],
      taskAssignee: json['taskAssignee'],
      taskCreatedTime: json['taskCreatedTime'],
      taskStartedTime: json['taskStartedTime'],
      ticketStartUserId: json['ticketStartUserId'],
      taskSla: json['taskSla'],
      taskFinishedTime: json['taskFinishedTime'],
      taskDoneTime: json['taskDoneTime'],
      taskStatus: json['taskStatus'],
      procInstId: json['procInstId'],
      endKey: json['endKey'],
      procDefId: json['procDefId'],
      taskType: json['taskType'],
      affected: json['affected'],
      listStatus: json['listStatus'],
      listVariables: (json['listVariables'] as List<dynamic>?)
          ?.map((item) => FormVariable.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      responseTime: json['responseTime'] != null
          ? ResponseTime.fromJson(json['responseTime'] as Map<dynamic, dynamic>)
          : null,
      finishTime: json['finishTime'] != null
          ? FinishTime.fromJson(json['finishTime'] as Map<dynamic, dynamic>)
          : null,
      newTaskId: json['newTaskId'],
      startPermission: json['startPermission'],
      newStatus: json['newStatus'],
      printId: json['printId'],
      ticketId: json['ticketId'],
      listSignForm: json['listSignForm'] != null
          ? (json['listSignForm'] as List)
          .map((e) => SignForm.fromJson(e as Map<dynamic, dynamic>))
          .toList()
          : null,
      editPermission: json['editPermission'],
      ticketProcDefId: json['ticketProcDefId'],
      fullName: json['fullName'],
      taskUsers: json['taskUsers'],
      priorityId: json['priorityId'],
      color: json['color'],
      signedFiles: json['signedFiles'] != null
          ? (json['signedFiles'] as List)
          .map((e) => SignedFile.fromJson(e as Map<dynamic, dynamic>))
          .toList()
          : null,
      newId: json['newId'],
      draftVariables: json['draftVariables'],
      taskActionUser: json['taskActionUser'] != null
          ? TaskActionUser.fromJson(json['taskActionUser'] as Map<dynamic, dynamic>)
          : null,
      taskOrgAssignee: json['taskOrgAssignee'] as Map<dynamic, dynamic>?,
      taskProcDefId: json['taskProcDefId'],
      requestCode: json['requestCode'],
      forceViewUrl: json['forceViewUrl'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'taskDefKey': taskDefKey,
      'taskName': taskName,
      'taskPriority': taskPriority,
      'taskAssignee': taskAssignee,
      'taskCreatedTime': taskCreatedTime,
      'taskStartedTime': taskStartedTime,
      'ticketStartUserId': ticketStartUserId,
      'taskSla': taskSla,
      'taskFinishedTime': taskFinishedTime,
      'taskDoneTime': taskDoneTime,
      'taskStatus': taskStatus,
      'procInstId': procInstId,
      'endKey': endKey,
      'procDefId': procDefId,
      'taskType': taskType,
      'affected': affected,
      'listStatus': listStatus,
      'listVariables': listVariables?.map((variable) => variable.toJson()).toList(),
      'responseTime': responseTime?.toJson(),
      'finishTime': finishTime?.toJson(),
      'newTaskId': newTaskId,
      'startPermission': startPermission,
      'newStatus': newStatus,
      'printId': printId,
      'ticketId': ticketId,
      'listSignForm': listSignForm?.map((e) => e.toJson()).toList(),
      'editPermission': editPermission,
      'ticketProcDefId': ticketProcDefId,
      'fullName': fullName,
      'taskUsers': taskUsers,
      'priorityId': priorityId,
      'color': color,
      'signedFiles': signedFiles?.map((e) => e.toJson()).toList(),
      'newId': newId,
      'draftVariables': draftVariables,
      'taskActionUser': taskActionUser?.toJson(),
      'taskOrgAssignee': taskOrgAssignee,
      'taskProcDefId': taskProcDefId,
      'requestCode': requestCode,
      'forceViewUrl': forceViewUrl,
    };
  }
}

class ResponseTime {
  final dynamic actual;
  final dynamic expected;
  final double? sla;
  final dynamic status;

  ResponseTime({
    this.actual,
    this.expected,
    this.sla,
    this.status,
  });

  factory ResponseTime.fromJson(Map<dynamic, dynamic> json) {
    return ResponseTime(
      actual: json['actual'],
      expected: json['expected'],
      sla: (json['sla'] as num?)?.toDouble(),
      status: json['status'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'actual': actual,
      'expected': expected,
      'sla': sla,
      'status': status,
    };
  }
}

class FinishTime {
  final dynamic actual;
  final dynamic expected;
  final double? sla;
  final dynamic status;

  FinishTime({
    this.actual,
    this.expected,
    this.sla,
    this.status,
  });

  factory FinishTime.fromJson(Map<dynamic, dynamic> json) {
    return FinishTime(
      actual: json['actual'],
      expected: json['expected'],
      sla: (json['sla'] as num?)?.toDouble(),
      status: json['status'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'actual': actual,
      'expected': expected,
      'sla': sla,
      'status': status,
    };
  }
}

class SignForm {
  final dynamic taskDefKey;
  final dynamic pdfContent;
  final dynamic templateName;
  final dynamic uploadWordsChange;
  final dynamic id;
  final dynamic content;

  SignForm({
    this.taskDefKey,
    this.pdfContent,
    this.templateName,
    this.uploadWordsChange,
    this.id,
    this.content,
  });

  factory SignForm.fromJson(Map<dynamic, dynamic> json) {
    return SignForm(
      taskDefKey: json['taskDefKey'],
      pdfContent: json['pdfContent'],
      templateName: json['templateName'],
      uploadWordsChange: json['uploadWordsChange'],
      id: json['id'],
      content: json['content'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'taskDefKey': taskDefKey,
      'pdfContent': pdfContent,
      'templateName': templateName,
      'uploadWordsChange': uploadWordsChange,
      'id': id,
      'content': content,
    };
  }
}

class SignedFile {
  final dynamic taskDefKey;
  final dynamic signedFile;
  final dynamic fileName;
  final dynamic bpmTemplatePrintId;

  SignedFile({
    this.taskDefKey,
    this.signedFile,
    this.fileName,
    this.bpmTemplatePrintId,
  });

  factory SignedFile.fromJson(Map<dynamic, dynamic> json) {
    return SignedFile(
      taskDefKey: json['taskDefKey'],
      signedFile: json['signedFile'],
      fileName: json['fileName'],
      bpmTemplatePrintId: json['bpmTemplatePrintId'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'taskDefKey': taskDefKey,
      'signedFile': signedFile,
      'fileName': fileName,
      'bpmTemplatePrintId': bpmTemplatePrintId,
    };
  }
}

class TaskActionUser {
  final dynamic fullName;
  final dynamic account;

  TaskActionUser({
    this.fullName,
    this.account,
  });

  factory TaskActionUser.fromJson(Map<dynamic, dynamic> json) {
    return TaskActionUser(
      fullName: json['fullName'],
      account: json['account'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'fullName': fullName,
      'account': account,
    };
  }
}
class FormVariable {
  final String name;
  final String type;
  final dynamic value;

  FormVariable({
    required this.name,
    required this.type,
    this.value,
  });

  factory FormVariable.fromJson(Map<String, dynamic> json) {
    return FormVariable(
      name: json['name'],
      type: json['type'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'value': value,
    };
  }
}