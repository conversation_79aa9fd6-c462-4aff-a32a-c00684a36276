import 'dart:convert';
import 'package:eapprove/models/notification/notification_model.dart';
import 'package:eapprove/models/notification/notification_setting_model.dart';
import 'package:eapprove/models/notification/unread_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:flutter/material.dart';

class NotiRepository {
  static const String _unreadEndpoint = '/noti-socket/unread-amount/';
  static const String _readEndpoint = '/noti-socket/notifications/read?id=';

  static const String _resetEndpoint = '/noti-socket/unread-amount/reset';
  // static const String _notiEndpoint =
  //     '/noti-socket/notifications?types=socket&page=0&size=20&sort=receiveTime%2Cdesc&system=EAPP';
  static const String _notiEndpoint = '/noti-socket/notifications';
  static const String _notificationSettingsEndpoint = '/noti-socket/notification-setting';
  static const String _updateNotificationEndpoint = '/noti-socket/notification-setting/enable-mobile-notification';

  final ApiService _apiService;

  NotiRepository({required ApiService apiService}) : _apiService = apiService;
  Future<UnreadModel> fetchUnreadAmount({
    List<String> systems = const ['EAPP'],
    String type = 'mobile',
  }) async {
    final uri = Uri(path: _unreadEndpoint, queryParameters: {
      'system': systems,
      'type': [type],
    });
    try {
      final response = await _apiService.get(uri.toString(), customBaseUrl: 'noti');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return UnreadModel.fromJson(data);
      }
      throw Exception('Failed to load unread amount: Status ${response.statusCode}');
    } catch (e) {
      debugPrint('Error in fetchUnreadAmount: $e');
      return UnreadModel(code: null, message: null, data: UnreadData(object: null, count: {'EAPP': 0}));
    }
  }

  Future<void> resetUnreadAmount() async {
    try {
      final response = await _apiService.post(_resetEndpoint, {}, customBaseUrl: 'noti');

      if (response.statusCode != 200) {
        throw Exception('Failed to reset unread amount: Status ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error in resetUnreadAmount: $e');
      throw Exception('Error resetting unread amount: $e');
    }
  }

  Future<List<NotificationData>> getNotifications({
    List<String> systems = const ['EAPP'],
    int page = 0,
    int size = 20,
    String sort = 'receiveTime,desc',
    String search = '',
  }) async {
    final uri = Uri(path: _notiEndpoint, queryParameters: {
      'type': ['mobile'],
      'system': systems,
      'page': ['$page'],
      'size': ['$size'],
      'sort': [sort],
      'search': [search],
    });
    try {
      final response = await _apiService.get(uri.toString(), customBaseUrl: 'noti');

      if (response.statusCode == 200) {
        final decodedBody = utf8.decode(response.bodyBytes);
        final data = jsonDecode(decodedBody);
        final rawList = data['data']?['data']?['EAPP'];
        debugPrint(jsonEncode(data));
        if (rawList == null || rawList is! List) {
          return [];
        }

        return rawList.map<NotificationData>((e) => NotificationData.fromJson(e)).toList();
      }

      throw Exception('Failed to load notifications: Status ${response.statusCode}');
    } catch (e) {
      debugPrint('Error in getNotifications: $e');
      // Return empty list instead of throwing exception
      return [];
    }
  }

  Future<bool> getNotificationSettings() async {
    try {
      final response = await _apiService.get(_notificationSettingsEndpoint, customBaseUrl: 'noti');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final settings = NotificationSettingsModel.fromJson(data);
        return settings.data?.enableMobileNotification ?? false;
      }
      throw Exception('Failed to load notification settings: Status ${response.statusCode}');
    } catch (e) {
      debugPrint('Error in getNotificationSettings: $e');
      return false;
    }
  }

  Future<bool> updateNotificationSettings(bool enable) async {
    try {
      final body = {'enable': enable};

      final response = await _apiService.post(_updateNotificationEndpoint, body, customBaseUrl: 'noti');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final updateResponse = UpdateNotificationResponse.fromJson(data);
        return updateResponse.code == "SUCCESS";
      }
      return false;
    } catch (e) {
      debugPrint('Error in updateNotificationSettings: $e');
      return false;
    }
  }

  Future<void> markAsRead(String id) async {
    final url = '$_readEndpoint$id';
    final response = await _apiService.put(url, {}, customBaseUrl: 'noti');
    if (response.statusCode != 200) {
      throw Exception('Failed to mark notification $id as read: '
          'Status ${response.statusCode}');
    }
  }
}
