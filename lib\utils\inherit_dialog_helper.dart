import 'package:eapprove/widgets/step_inherit_selection_dialog.dart';
import 'package:flutter/material.dart';

class InheritDialogHelper {
  static Future<bool?> showInheritDialog({
    required BuildContext context,
    required dynamic ticket,
  }) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StepSelectionDialog(
          ticket: ticket,
        );
      },
    );
  }
}

extension InheritDialogExtension on BuildContext {
  Future<bool?> showInheritDialog(dynamic ticket) {
    return InheritDialogHelper.showInheritDialog(
      context: this,
      ticket: ticket,
    );
  }
}
