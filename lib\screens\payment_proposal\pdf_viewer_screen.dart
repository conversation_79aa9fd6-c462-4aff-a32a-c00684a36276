// import 'dart:async';
// import 'dart:developer' as developer;
// import 'package:eapprove/blocs/form/form_state.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'package:flutter_sdk/widgets/custom_app_bar.dart';
// import 'package:flutter_sdk/widgets/custom_background.dart';
// import 'package:flutter_pdfview/flutter_pdfview.dart';
// import 'package:hive_flutter/hive_flutter.dart';

// import 'package:eapprove/blocs/form/form_bloc.dart';
// import 'package:eapprove/blocs/form/form_event.dart';
// import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
// import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
// import 'package:eapprove/models/form/bpmProcInst_create_request_model.dart';
// import 'package:eapprove/models/form/form_xml_response_model.dart';
// import 'package:eapprove/services/form_submit_service.dart';

// class PdfViewerScreen extends StatefulWidget {
//   final String pdfUrl;
//   final String title;
//   final String? procDefId;
//   final int? ticketId;
//   final Map<String, dynamic> variables;
//   final Map<String, dynamic> formData;
//   // final VoidCallback onConfirm;

//   const PdfViewerScreen({
//     super.key,
//     required this.pdfUrl,
//     required this.title,
//     this.procDefId,
//     this.ticketId,
//     required this.variables,
//     required this.formData,
//     // required this.onConfirm,
//   });

//   @override
//   State<PdfViewerScreen> createState() => _PdfViewerScreenState();
// }

// class _PdfViewerScreenState extends State<PdfViewerScreen> {
//   late StreamSubscription _subscription;
//   late List<Map<String, dynamic>> _zones;
//   late List<Map<String, dynamic>> _elements;
//   late CheckTypeResponseModel? checkTypeResponse;
//   @override
//   void initState() {
//     super.initState();
//     _subscription = const Stream.empty().listen((_) {});
//     _zones = [];
//     _elements = [];
//     developer.log('initState: _elements initialized as empty',
//         name: 'PdfViewerScreen');
//     _initData();
//   }

//   @override
//   void dispose() {
//     _subscription.cancel();
//     super.dispose();
//   }

//   void _initData() {
//     final formBloc = context.read<FormBloc>();
//     final formState = formBloc.state;

//     final bpmnDefinition = formState.bpmnDefinition;
//     developer.log('_initData: bpmnDefinition = $bpmnDefinition',
//         name: 'PdfViewerScreen');

//     final process = bpmnDefinition?.process;
//     developer.log('_initData: process = $process', name: 'PdfViewerScreen');

//     _elements = process?.elements
//             .map((e) => {
//                   'id': e.id,
//                   'name': e.name,
//                   if (e is BpmnUserTask)
//                     'assignee':
//                         e.assignee?.contains('jsonArrayToString') == true
//                             ? e.assignee?.split('(').last.split(')').first
//                             : e.assignee,
//                   if (e is BpmnStartEvent) 'formKey': e.formKey,
//                 })
//             .toList() ??
//         [];

//     developer.log('_initData: _elements after mapping = $_elements',
//         name: 'PdfViewerScreen');

//     // Sort elements: startEvent first, then by id
//     _elements?.sort((a, b) {
//       if (a['formKey'] != null) return -1; // startEvent comes first
//       if (b['formKey'] != null) return 1;
//       final idA = a['id']?.toString() ?? '';
//       final idB = b['id']?.toString() ?? '';
//       return idA.compareTo(idB);
//     });

//     developer.log('All elements after sort: $_elements',
//         name: 'PdfViewerScreen');

//     // Create a list to store all user assignees
//     final userAssignees = <String>[];
//     final Box authBox = Hive.box('authentication');
//     final String? username = authBox.get('username');
//     for (final element in _elements ?? []) {
//       if (element['assignee'] != null || element['id'] == 'start') {
//         final userAssignee = element['id'] == 'start'
//             ? username
//             : widget.variables[element['assignee']] is Map
//                 ? widget.variables[element['assignee']]['value']
//                 : widget.variables[element['assignee']];

//         if (userAssignee != null) {
//           userAssignees.add(userAssignee);
//         }
//       }
//     }

//     // Trigger API calls for all users at once
//     if (userAssignees.isNotEmpty) {
//       // Get default signature for start user
//       formBloc.add(GetDefaultSignatureRequested(username: username ?? ''));
//       formBloc.add(GetUserInfoByUsername(username: userAssignees.first));
//       formBloc.add(GetFinalTitleByListUser(usernames: userAssignees));
//     }
//   }

//   void _submitForm() {
//     final checkTypeBloc = context.read<CheckTypeBloc>();
//     final checkTypeState = checkTypeBloc.state;
//     final checkTypeResponse =
//         checkTypeState is CheckTypeSuccess ? checkTypeState.response : null;

//     if (checkTypeResponse?.listSignForm.isNotEmpty == true) {
//       // _submitFormCoToTrinh();
//       return;
//     }

//     final formBloc = context.read<FormBloc>();
//     final formState = formBloc.state;

//     FormSubmitService.submitForm(
//       context: context,
//       formData: widget.variables,
//       procDefId: widget.procDefId ?? "",
//       ticketId: widget.ticketId ?? 0,
//       requestSubject: formState.submissionTypeName ?? "",
//     );

//     // developer.log('zoneszoneszoneszones: ${zones.map((zone) => zone.toString())}', name: 'PdfViewerScreen');

//     // try {
//       final bpmProcInstBloc = context.read<BpmProcInstBloc>();

//       bpmProcInstBloc.add(CreateInstRequested(
//         procDefId: widget.procDefId ?? '',
//         ticketId: widget.ticketId ?? 0,
//         requestBody: BpmProcInstCreateRequestModel(
//           oldProcInstId: '',
//           variables: widget.variables,
//           isDraft: false,
//           serviceId: widget.ticketId ?? 0,
//           linkProcInstId: [],
//           receiveMail: true,
//           isAssistant: true,
//           notifyUsers: [],
//           priorityId: formState.submitterInfoResponse?.data.first.priorityId,
//           chartId: formState.chartData?.data.first.id,
//           assistantEmail: [],
//           submissionType: formState.serviceByIdWithPermissionResponse?.data?.submissionType ?? 30,
//           companyCode: formState.chartData?.data.first.code,
//           submissionTypeName: formState.submissionTypeName ?? "Đề nghị thanh toán",
//           chartNodeName: formState.chartNodeData?.data.first.chartNodeName,
//           chartNodeCode: formState.chartNodeData?.data.first.chartNodeCode,
//           chartNodeId: formState.chartNodeData?.data.first.chartNodeId,
//           chartName: formState.chartData?.data.first.name,
//           forceViewUrl: "",
//           cancelTicketId: "",
//           approvedBudgetIds: [],
//           printSignZoneRequest: {
//             "zones": zones.map((zone) => Map<String, dynamic>.from(zone)).toList(),
//             "name": checkTypeResponse?.listSignForm.firstOrNull?.uploadWordsChange ?? "",
//             "procDefId": widget.procDefId ?? "",
//             "pdfContent": checkTypeResponse?.listSignForm.firstOrNull?.uploadWordsChange ?? "",
//             "procInstId": "",
//             "uploadWordsChange": checkTypeResponse?.listSignForm.firstOrNull?.uploadWordsChange ?? "",
//             "taskKey": [
//               {
//                 "taskType": 0
//               }
//             ]
//           },
//         ),
//       ));

//     //   // Add form submission
//     //   FormSubmitService.submitForm(
//     //     context: context,
//     //     formData: widget.variables,
//     //     procDefId: widget.procDefId ?? "",
//     //     ticketId: widget.ticketId ?? 0,
//     //     requestSubject: formState.submissionTypeName ?? "Đề nghị thanh toán",
//     //   );
//     // } catch (e, stack) {
//     //   print('Error: $e');
//     //   print('Stack: $stack');
//     // }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GradientBackground(
//       child: Scaffold(
//         backgroundColor: getColorSkin().transparent,
//         appBar: CustomAppBar(
//           automaticallyImplyLeading: true,
//           centerTitle: true,
//           title: widget.title,
//           titleTextStyle: getTypoSkin()
//               .title4Regular
//               .copyWith(color: getColorSkin().ink1),
//           textColor: getColorSkin().black,
//           actionsPadding: EdgeInsets.only(right: 18.w),
//           backgroundColor: getColorSkin().transparent,
//         ),
//         body: BlocListener<FormBloc, FormInfoState>(
//           listener: (context, formState) {
//             FormSubmitService.handleSubmitResponse(context, formState,() => {},() => {});
//           },
//           child: MultiBlocProvider(
//             providers: [
//               BlocProvider.value(value: context.read<FormBloc>()),
//               BlocProvider.value(value: context.read<CheckTypeBloc>()),
//             ],
//             child: SafeArea(child: PDFView(
//               filePath: widget.pdfUrl,
//               autoSpacing: true,
//               fitPolicy: FitPolicy.WIDTH,
//             ),),
//           ),
//         ),
//         bottomNavigationBar: Container(
//           padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
//           decoration: BoxDecoration(
//             color: getColorSkin().white,
//           ),
//           child: Row(
//             children: [
//               Expanded(
//                 child: ElevatedButton(
//                   onPressed: () {
//                     Navigator.pop(context);
//                   },
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: getColorSkin().transparent,
//                     padding: EdgeInsets.symmetric(vertical: 12.h),
//                     elevation: 0,
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(10.r),
//                       side: BorderSide(color: getColorSkin().primaryBlue),
//                     ),
//                   ),
//                   child: Text(
//                     'Quay về',
//                     style: getTypoSkin()
//                         .body2Regular
//                         .copyWith(color: getColorSkin().primaryBlue),
//                   ),
//                 ),
//               ),
//               SizedBox(width: 16.w),
//               Expanded(
//                 child: ElevatedButton(
//                   onPressed: _submitForm,
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: getColorSkin().primaryBlue,
//                     padding: EdgeInsets.symmetric(vertical: 12.h),
//                     elevation: 0,
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(10.r),
//                     ),
//                   ),
//                   child: Text(
//                     'Xác nhận',
//                     style: getTypoSkin()
//                         .body2Regular
//                         .copyWith(color: getColorSkin().white),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
