import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class PdfDownloadDialog extends StatelessWidget {
  final String message;

  const PdfDownloadDialog({
    Key? key,
    this.message = 'Đang xử lý...',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(20.r),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppConstraint.buildLoading(context),
              SizedBox(height: 20.h),
              Text(
                message,
                style: getTypoSkin().body2Regular.copyWith(
                  color: getColorSkin().primaryText,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}