import 'dart:developer' as developer;
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:flutter/foundation.dart';

class TicketUtils {
  static dynamic getCorrectTicketId(dynamic ticket) {
    if (ticket == null) return null;

    if (ticket is Map) {
      return ticket['ticketId'] ?? ticket['id'];
    }

    if (ticket is ApproveContent || ticket is ProcContent) return ticket.ticketId;
    if (ticket is MyPycContent) return ticket.id;
    if (ticket is AssisContent) return ticket.id;

    try {
      final dynamicTicket = ticket as dynamic;
      return dynamicTicket.ticketId ?? dynamicTicket.id;
    } catch (_) {
      return null;
    }
  }

  static dynamic getTicketId(dynamic ticket) {
    if (ticket == null) return null;

    if (ticket is Map) {
      return ticket['ticketId'] ?? ticket['taskId'] ?? ticket['id'];
    }

    if (ticket is ApproveContent || ticket is ProcContent) {
      debugPrint('taskid123:${ticket.taskId}');
      return ticket.taskId;
    }

    try {
      return (ticket as dynamic).ticketId ?? (ticket as dynamic).id ?? (ticket as dynamic).taskId;
    } catch (e) {
      debugPrint('Error in getTicketId: $e');
      return null;
    }
  }

  static dynamic getProcInstId(dynamic ticket) {
    if (ticket == null) return null;

    if (ticket is ApproveContent || ticket is ProcContent) {
      return ticket.ticketId;
    }

    return safeGetProperty(ticket, 'ticketId') ?? safeGetProperty(ticket, 'procInstId');
  }

  static String? getTaskDefKey(dynamic ticket) {
    if (ticket == null) return null;

    if (ticket is MyPycContent) {
      final myPyc = ticket;
      if (myPyc.ticketTaskDtoList != null &&
          myPyc.ticketTaskDtoList!.isNotEmpty &&
          myPyc.ticketStatus != 'COMPLETED' &&
          myPyc.ticketStatus != 'CLOSED') {
        return myPyc.ticketTaskDtoList!.first.taskDefKey;
      } else {
        return myPyc.startKey;
      }
    } else if (ticket is AssisContent) {
      final assisContent = ticket;
      return assisContent.ticketStartActId;
    } else if (ticket is ApproveContent) {
      final approveContent = ticket;
      if (approveContent.ticketTaskDtoList != null && approveContent.ticketTaskDtoList!.isNotEmpty) {
        return approveContent.ticketTaskDtoList!.first.taskDefKey;
      }
    } else if (ticket is ProcContent) {
      final procContent = ticket;
      if (procContent.ticketTaskDtoList != null && procContent.ticketTaskDtoList!.isNotEmpty) {
        return procContent.ticketTaskDtoList!.first.taskDefKey;
      }
    }

    final ticketTasks = safeGetListProperty(ticket, 'ticketTaskDtoList') ?? safeGetListProperty(ticket, 'tasks');
    if (ticketTasks != null && ticketTasks.isNotEmpty) {
      return safeGetPropertyFromObject(ticketTasks[0], 'taskDefKey')?.toString();
    }
    return null;
  }

  static String? getProcDefId(dynamic ticket) {
    if (ticket == null) return null;

    if (ticket is ApproveContent) {
      developer.log('> ApproveContent.ticketProcDefId = ${ticket.ticketProcDefId}');
      return ticket.ticketProcDefId;
    }
    if (ticket is AssisContent) {
      return ticket.ticketProcDefId;
    }
    if (ticket is ProcContent) {
      return ticket.ticketProcDefId;
    }
    return safeGetProperty(ticket, 'procDefId') ?? safeGetProperty(ticket, 'ticketProcDefId');
  }

  static String getTicketTitle(dynamic ticket) {
    if (ticket == null) return 'Chi tiết phiếu';

    try {
      if (ticket is Map) {
        return ticket['ticketTitle'] ?? ticket['title'] ?? ticket['name'] ?? 'Chi tiết phiếu';
      } else {
        final dynamic ticketDynamic = ticket;
        return ticketDynamic.ticketTitle ?? ticketDynamic.title ?? ticketDynamic.name ?? 'Chi tiết phiếu';
      }
    } catch (e) {
      developer.log('Error accessing ticket properties: $e');
    }

    return 'Chi tiết phiếu';
  }

  static dynamic safeGetProperty(dynamic ticket, String propertyName) {
    if (ticket == null) return null;

    try {
      if (ticket is Map) {
        return ticket[propertyName];
      } else if (ticket is MyPycContent) {
        final myPyc = ticket as MyPycContent;
        switch (propertyName) {
          case 'processId':
          case 'id':
            return myPyc.id;
          case 'ticketId':
            return myPyc.ticketId;
          case 'status':
            return myPyc.ticketStatus;
          case 'ticketProcDefId':
          case 'procDefId':
            return myPyc.ticketProcDefId;
          case 'serviceId':
            return myPyc.serviceId;
          case 'ticketStartUserId':
          case 'startUserId':
            return myPyc.ticketStartUserId;
          default:
            final json = myPyc.toJson();
            return json[propertyName];
        }
      } else if (ticket is ApproveContent) {
        final approve = ticket as ApproveContent;
        switch (propertyName) {
          case 'processId':
          case 'id':
          case 'ticketId':
            return approve.ticketId;
          case 'status':
            return approve.ticketStatus;
          case 'ticketProcDefId':
          case 'procDefId':
            return approve.ticketProcDefId;
          case 'serviceId':
            return approve.serviceId;
          case 'ticketStartUserId':
          case 'startUserId':
            return approve.startUser;
          default:
            final json = approve.toJson();
            return json[propertyName];
        }
      } else if (ticket is ProcContent) {
        final proc = ticket;
        switch (propertyName) {
          case 'processId':
          case 'id':
          case 'ticketId':
            return proc.ticketId;
          case 'status':
            return proc.ticketStatus;
          case 'ticketProcDefId':
          case 'procDefId':
            return proc.ticketProcDefId;
          case 'serviceId':
            return proc.serviceId;
          case 'ticketStartUserId':
          case 'startUserId':
            return proc.startUser;
          default:
            final json = proc.toJson();
            return json[propertyName];
        }
      } else {
        final json = (ticket as dynamic).toJson();
        return json[propertyName];
      }
    } catch (e) {
      debugPrint('[DEBUG] Error in safeGetProperty($propertyName): $e');
      return null;
    }
  }

  static String? getTaskId(dynamic ticket) {
    if (ticket == null) return null;

    try {
      if (ticket is MyPycContent || ticket is AssisContent) {
        final tasks = ticket.ticketTaskDtoList;
        if (tasks != null && tasks.isNotEmpty) {
          return tasks.first.taskId;
        }
      }

      if (ticket is ProcContent || ticket is ApproveContent) {
        return ticket.taskId;
      }

      final taskList = safeGetListProperty(ticket, 'ticketTaskDtoList');
      if (taskList != null && taskList.isNotEmpty) {
        return safeGetPropertyFromObject(taskList.first, 'taskId')?.toString();
      }

      return safeGetProperty(ticket, 'taskId')?.toString();
    } catch (e) {
      debugPrint('Error in getTaskId: $e');
      return null;
    }
  }

  static List<dynamic>? safeGetListProperty(dynamic ticket, String propertyName) {
    final value = safeGetProperty(ticket, propertyName);
    if (value is List) {
      return value;
    }
    return null;
  }

  static dynamic safeGetPropertyFromObject(dynamic object, String propertyName) {
    if (object == null) return null;

    try {
      if (object is Map) {
        return object[propertyName];
      } else {
        return (object as dynamic).$propertyName;
      }
    } catch (e) {
      return null;
    }
  }

  static String? safeGetTaskDefKey(dynamic ticket) {
    if (ticket == null) return null;

    if (ticket is Map) {
      final tasks = (ticket['ticketTaskDtoList'] as List?)?.cast<Map<String, dynamic>>();
      return tasks?.isNotEmpty == true ? tasks!.first['taskDefKey'] : null;
    }

    final ticketTasks = safeGetListProperty(ticket, 'ticketTaskDtoList');
    return ticketTasks?.isNotEmpty == true
        ? safeGetPropertyFromObject(ticketTasks!.first, 'taskDefKey')?.toString()
        : null;
  }

  static void logTicketDetails(dynamic ticket) {
    developer.log('ticket type: ${ticket?.runtimeType}');
    developer.log('ticket title: ${getTicketTitle(ticket)}');

    try {
      developer.log('ticket status: ${safeGetProperty(ticket, 'status')}', name: 'TicketUtils');
      developer.log('ticket procServiceName: ${safeGetProperty(ticket, 'procServiceName')}', name: 'TicketUtils');
      developer.log('ticket processId: ${safeGetProperty(ticket, 'processId') ?? safeGetProperty(ticket, 'id')}',
          name: 'TicketUtils');
      developer.log('ticket ticketId: ${safeGetProperty(ticket, 'ticketId')}', name: 'TicketUtils');
      developer.log('ticket id: ${safeGetProperty(ticket, 'id')}', name: 'TicketUtils');
      developer.log('ticket ticketProcDefId: ${safeGetProperty(ticket, 'ticketProcDefId')}', name: 'TicketUtils');
      developer.log('ticket ticketCreatedTime: ${safeGetProperty(ticket, 'ticketCreatedTime')}', name: 'TicketUtils');
      developer.log(
          'ticket ticketStartUserId: ${safeGetProperty(ticket, 'ticketStartUserId') ?? safeGetProperty(ticket, 'startUserId')}',
          name: 'TicketUtils');
      developer.log('ticket companyCode: ${safeGetProperty(ticket, 'companyCode')}', name: 'TicketUtils');

      final ticketTasks = safeGetListProperty(ticket, 'ticketTaskDtoList') ?? safeGetListProperty(ticket, 'tasks');
      if (ticketTasks != null && ticketTasks.isNotEmpty) {
        developer.log('ticket taskDefKey: ${safeGetPropertyFromObject(ticketTasks[0], 'taskDefKey')}',
            name: 'TicketUtils');
      } else {
        developer.log('ticket taskDefKey: Not available (empty task list or null ticket)', name: 'TicketUtils');
      }
    } catch (e) {
      developer.log('Error in logTicketDetails: $e', name: 'TicketUtils');
    }
  }
}
