import 'package:eapprove/models/meta.dart';

class SwitchAccountResponse {
  final Meta meta;
  final TokenData? data;

  SwitchAccountResponse({required this.meta, this.data});

  factory SwitchAccountResponse.fromJson(Map<String, dynamic> json) {
    return SwitchAccountResponse(
      meta: Meta.fromJson(json['meta']),
      data: json['data'] != null ? TokenData.fromJson(json['data']) : null,
    );
  }
}

class TokenData {
  final String accessToken;
  final String idToken;
  final int expiresIn;
  final int refreshExpiresIn;
  final String refreshToken;
  final String tokenType;
  final int notBeforePolicy;
  final String sessionState;
  final String scope;

  TokenData({
    required this.accessToken,
    required this.idToken,
    required this.expiresIn,
    required this.refreshExpiresIn,
    required this.refreshToken,
    required this.tokenType,
    required this.notBeforePolicy,
    required this.sessionState,
    required this.scope,
  });

  factory TokenData.from<PERSON>son(Map<String, dynamic> json) {
    return TokenData(
      accessToken: json['access_token'],
      idToken: json['id_token'],
      expiresIn: json['expires_in'],
      refreshExpiresIn: json['refresh_expires_in'],
      refreshToken: json['refresh_token'],
      tokenType: json['token_type'],
      notBeforePolicy: json['not_before_policy'],
      sessionState: json['session_state'],
      scope: json['scope'],
    );
  }
}
