import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/ticket_input_repository.dart';
import 'ticket_input_event.dart';
import 'ticket_input_state.dart';

class TicketInputBloc extends Bloc<TicketInputEvent, TicketInputState> {
  final TicketInputRepository _repository;

  TicketInputBloc({required TicketInputRepository repository})
      : _repository = repository,
        super(TicketInputInitial()) {
    on<LoadTicketInput>(_onLoadTicketInput);
  }

  Future<void> _onLoadTicketInput(
    LoadTicketInput event,
    Emitter<TicketInputState> emit,
  ) async {
    try {
      emit(TicketInputLoading());
      final ticketInput = await _repository.getTicketInputData(
        taskId: event.taskId,
        type: event.type,
        token: event.token ?? '',
        chart: event.chart ?? '',
      );
      emit(TicketInputLoaded(ticketInput));
    } catch (e) {
      emit(TicketInputError(e.toString()));
    }
  }
} 