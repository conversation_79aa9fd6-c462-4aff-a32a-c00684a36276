import 'dart:convert';
import 'package:eapprove/services/api_services.dart';
import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import 'dart:typed_data';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class BusinessProcessRepository {
  final ApiService _apiService;

  BusinessProcessRepository({required ApiService apiService}) : _apiService = apiService;

  Future<dynamic> checkProcessType(int serviceId, String procDefId) async {
    try {
      final response = await _apiService.get(
        'business-process/bpmProcdef/check-type/$serviceId/$procDefId/start',
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to check process type: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error checking process type: $e');
      rethrow;
    }
  }

  Future<Uint8List> getPrintFileTemplate(Map<String, dynamic> requestBody) async {
    try {
      // developer.log('requestBody: $requestBody', name: 'getPrintFileTemplate');
      final response = await _apiService.post(
        'business-process/bpmProcInst/getPrintFileTemplate/0',
        requestBody,
      );
      // developer.log('response: ${response.body}', name: 'getPrintFileTemplate');
      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        throw Exception('Failed to get print file template: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting print file template: $e');
      rethrow;
    }
  }

  Future<void> savePdf(Uint8List pdfBytes, String fileName) async {
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/$fileName');
    await file.writeAsBytes(pdfBytes);
  }
} 