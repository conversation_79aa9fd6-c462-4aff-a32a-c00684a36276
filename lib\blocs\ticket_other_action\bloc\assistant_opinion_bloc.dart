import 'package:eapprove/blocs/ticket_other_action/event/assistant_opinion_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/assistant_opinion_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';

class AssistantOpinionBloc
    extends Bloc<AssistantOpinionEvent, AssistantOpinionState> {
  final TicketOtherActionRepository repository;

  AssistantOpinionBloc({required this.repository})
      : super(AssistantOpinionState.initial()) {
    on<UploadAssistantOpinionlFileEvent>(_onUploadFiles);
    on<SubmitAssistantOpinionEvent>(_onSubmitAssistantOpinion);
  }

  Future<void> _onUploadFiles(UploadAssistantOpinionlFileEvent event,
      Emitter<AssistantOpinionState> emit) async {
    emit(state.copyWith(status: AssistantOpinionStatus.loading));
    try {
      final url = await repository.uploadlFileAssistantOpinion(event.files);
      emit(state.copyWith(status: AssistantOpinionStatus.success, url: url));
    } catch (e) {
      emit(state.copyWith(
          status: AssistantOpinionStatus.failure, error: e.toString()));
    }
  }

  Future<void> _onSubmitAssistantOpinion(SubmitAssistantOpinionEvent event,
      Emitter<AssistantOpinionState> emit) async {
    emit(state.copyWith(status: AssistantOpinionStatus.loading));
    try {
      await repository.submitAssistantOpinionTicket(
        ticketId: event.ticketId,
        opinion: event.opinion,
        assistantEmail: event.assistantEmail,
        status: event.status,
        url: event.url,
      );
      if (event.status == 1) {
        SnackbarCore.success("Gửi ý kiến thành công");
      }
      if (event.status == 0) {
        SnackbarCore.success("Lưu nháp ý kiến thành công");
      }
      emit(state.copyWith(status: AssistantOpinionStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: AssistantOpinionStatus.failure, error: e.toString()));
    }
  }
}
