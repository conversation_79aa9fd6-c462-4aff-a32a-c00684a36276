import 'dart:developer';

import 'package:eapprove/models/service/service_request_model.dart';
import 'package:eapprove/repositories/service_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/main.dart'; // Import for NoInternetException

import 'package:eapprove/repositories/account_repository.dart';
import 'service_event.dart';
import 'service_state.dart';

class ServiceBloc extends Bloc<ServiceEvent, ServiceState> {
  final ServiceRepository _repository;
  final AccountRepository _accountRepository;
  Future<void>? _downloadOperation;

  ServiceBloc(this._repository, this._accountRepository) : super(const ServiceState()) {
    on<ServiceFetchRoot>(_onFetchRootService);
    on<SearchService>(_onSearchService);
    on<ToggleSearchVisibility>(_onToggleSearchVisibility);
    on<DownloadPdf>(_onDownloadPdf);
    on<ClearPdfStatus>(_onClearPdfStatus);
    on<CancelDownload>(_onCancelDownload);
    on<ClearServiceState>(_onClearServiceState);
  }

  ServiceRepository get repository => _repository;

  void _onClearServiceState(
      ClearServiceState event, Emitter<ServiceState> emit) {
    emit(const ServiceState());
  }

  void _onCancelDownload(CancelDownload event, Emitter<ServiceState> emit) {
    // Cancel the ongoing download operation if it exists
    if (_downloadOperation != null) {
      _downloadOperation = null;
      emit(state.copyWith(
        pdfDownloadStatus: PdfDownloadStatus.initial,
        pdfFilePath: null,
        errorMessage: 'Download cancelled'
      ));
    }
  }

  void _onClearPdfStatus(ClearPdfStatus event, Emitter<ServiceState> emit) {
    _downloadOperation = null;

    // Reset the PDF download status
    emit(state.copyWith(
        pdfDownloadStatus: PdfDownloadStatus.initial,
        pdfFilePath: null
    ));
  }

  void _onToggleSearchVisibility(ToggleSearchVisibility event,
      Emitter<ServiceState> emit) {
    if (event.isVisible) {
      emit(state.copyWith(searchKey: ''));
    } else {
      emit(state.copyWith(searchKey: null));
    }
  }

  Future<void> _onFetchRootService(ServiceFetchRoot event,
      Emitter<ServiceState> emit) async {
    try {
      // First emit loading state
      emit(state.copyWith(
          status: ServiceStatus.loading,
          // isNetworkError: false,
          errorMessage: null
      ));

      final chartData = await _accountRepository.getChartData();

      final requestModel = ServiceRequestModel(
          serviceName: "",
          processName: "",
          serviceType: [],
          chartId: chartData.data.first.id,
          limit: 9999,
          size: 9999,
          page: 1,
          sortBy: "id",
          sortType: "ASC",
          sortCompanyCode: [],
          admin: false
      );

      emit(state.copyWith(
          serviceRequestModel: requestModel,
          searchKey: null
      ));

      final result = await _repository.getServices(requestBody: requestModel);
      log('result: $result');

      emit(state.copyWith(
          status: ServiceStatus.success,
          serviceModels: result,
          serviceRequestModel: requestModel,
          // isNetworkError: false,
          errorMessage: null
      ));
    } catch (error) {
      emit(state.copyWith(
          status: ServiceStatus.failure,
          errorMessage: 'Đã xảy ra lỗi, vui lòng thử lại sau.',
          serviceModels: null
      ));
    }
  }

  Future<void> _onSearchService(SearchService event,
      Emitter<ServiceState> emit) async {
    try {
      // First emit loading state
      emit(state.copyWith(
          status: ServiceStatus.loading,
          errorMessage: null
      ));

      final currentRequestModel = state.serviceRequestModel ??
          ServiceRequestModel();

      final requestModel = ServiceRequestModel(
          serviceName: event.searchKey,
          processName: currentRequestModel.processName,
          serviceType: currentRequestModel.serviceType,
          chartId: currentRequestModel.chartId,
          limit: currentRequestModel.limit,
          size: currentRequestModel.size,
          page: currentRequestModel.page,
          sortBy: currentRequestModel.sortBy,
          sortType: currentRequestModel.sortType,
          sortCompanyCode: currentRequestModel.sortCompanyCode,
          admin: currentRequestModel.admin
      );

      final result = await _repository.getServices(requestBody: requestModel);
      log('search result: $result');

      emit(state.copyWith(
          status: ServiceStatus.success,
          serviceModels: result,
          serviceRequestModel: requestModel,
          searchKey: event.searchKey,
          // isNetworkError: false,
          errorMessage: null
      ));
    } catch (error) {
      emit(state.copyWith(
          status: ServiceStatus.failure,
          errorMessage: 'Đã xảy ra lỗi, vui lòng thử lại sau.',
          serviceModels: null
      ));
    }
  }

  Future<void> _onDownloadPdf(DownloadPdf event, Emitter<ServiceState> emit) async {
    try {
      emit(state.copyWith(
        pdfDownloadStatus: PdfDownloadStatus.downloading,
        // isNetworkError: false,
        errorMessage: null
      ));

      _downloadOperation = _repository.downloadPdf(fileName: event.fileName);
      final String filePath = await _downloadOperation as String;

      if (!isClosed && _downloadOperation != null) {
        emit(state.copyWith(
          pdfFilePath: filePath,
          pdfDownloadStatus: PdfDownloadStatus.success
        ));
      }
    } catch (error) {
      if (!isClosed) {
        emit(state.copyWith(
          errorMessage: 'Đã xảy ra lỗi khi tải file PDF',
          pdfDownloadStatus: PdfDownloadStatus.failure
        ));
      }
    } finally {
      _downloadOperation = null;
    }
  }
}