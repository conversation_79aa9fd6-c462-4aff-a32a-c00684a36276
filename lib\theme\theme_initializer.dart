import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/theme/app_theme_bloc.dart';
import 'package:eapprove/theme/custom_color.dart';
import 'package:eapprove/theme/custom_text_style.dart';

class AppThemeInitializer extends StatelessWidget {
  final Widget child;

  const AppThemeInitializer({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppThemeBloc, AppThemeState>(
      builder: (context, state) {
        return Theme(
          data: ThemeData(
            brightness: state.isDarkMode ? Brightness.dark : Brightness.light,
            extensions: [
              CustomTextStyle.defaultInstance(),
              CustomColor.defaultInstance(),
            ],
          ),
          child: child,
        );
      },
    );
  }
} 