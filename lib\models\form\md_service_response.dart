import 'dart:convert';

class MdServiceResponseModel {
  final int code;
  final String message;
  final List<MdService> data;
  final String? requestKey;

  const MdServiceResponseModel({
    required this.code,
    required this.message,
    required this.data,
    this.requestKey,
  });

  factory MdServiceResponseModel.fromJson(Map<String, dynamic> json) {
    return MdServiceResponseModel(
      code: json['code'] is String ? int.tryParse(json['code']) ?? 0 : json['code'] as int,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((item) => MdService.fromJson(item as Map<String, dynamic>))
          .toList(),
      requestKey: json['requestKey'] as String?,
    );
  }

  factory MdServiceResponseModel.fromJsonString(String jsonString) {
    return MdServiceResponseModel.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((info) => info.toJson()).toList(),
      'requestKey': requestKey,
    };
  }

  bool get isSuccess => code == 1;
}

class MdService {
  final String? updatedTime;
  final String? sTT;
  final String? codeNS;
  final String? fundDN;
  final String? createdTime;
  final String? sD;
  final String? mdParentId;
  final String? id;
  final String? updatedUser;
  final String? codePB;
  final String? typeMd;
  final String? companyCode;
  final String? mien;
  final String? maNhanVien;
  final String? tenDayDuCty;
  final String? tenDayDuNhanVien;
  final String? tenVietTatVungMien;
  final String? truongLayDuLieu;
  final String? chucDanh;
  final String? shortCode;
  final String? tenNguoiThuHuong;
  final String? soTaiKhoanNH;
  final String? nganHangChiNhanh;

  const MdService({
    this.updatedTime,
    this.sTT,
    this.codeNS,
    this.fundDN,
    this.createdTime,
    this.sD,
    this.mdParentId,
    this.id,
    this.updatedUser,
    this.codePB,
    this.typeMd,
    this.companyCode,
    this.mien,
    this.maNhanVien,
    this.tenDayDuCty,
    this.tenDayDuNhanVien,
    this.tenVietTatVungMien,
    this.truongLayDuLieu,
    this.chucDanh,
    this.shortCode,
    this.tenNguoiThuHuong,
    this.soTaiKhoanNH,
    this.nganHangChiNhanh,
  });

  factory MdService.fromJson(Map<String, dynamic> json) {
    return MdService(
      updatedTime: json['updatedTime'] as String?,
      sTT: json['STT'] as String?,
      codeNS: json['Code NS'] as String?,
      fundDN: json['Fund/Dự án'] as String?,
      createdTime: json['createdTime'] as String?,
      sD: json['Số dư'] as String?,
      mdParentId: json['mdParentId'] as String?,
      id: json['id'] as String?,
      updatedUser: json['updatedUser'] as String?,
      codePB: json['Code PB'] as String?,
      typeMd: json['typeMd'] as String?,
      companyCode: json['companyCode'] as String?,
      mien: json['Mien'] as String?,
      maNhanVien: json['maNhanVien'] as String?,
      tenDayDuCty: json['tenDayDuCty'] as String?,
      tenDayDuNhanVien: json['tenDayDuNhanVien'] as String?,
      tenVietTatVungMien: json['tenVietTatVungMien'] as String?,
      truongLayDuLieu: json['truongLayDuLieu'] as String?,
      chucDanh: json['chucDanh'] as String?,
      shortCode: json['shortCode'] as String?,
      tenNguoiThuHuong: json['Tên người thụ hưởng'] as String?,
      soTaiKhoanNH: json['Số tài khoản NH'] as String?,
      nganHangChiNhanh: json['Ngân hàng - Chi nhánh'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'updatedTime': updatedTime,
      'STT': sTT,
      'Code NS': codeNS,
      'Fund/Dự án': fundDN,
      'createdTime': createdTime,
      'Số dư': sD,
      'mdParentId': mdParentId,
      'id': id,
      'updatedUser': updatedUser,
      'Code PB': codePB,
      'typeMd': typeMd,
      'companyCode': companyCode,
      'Mien': mien,
      'maNhanVien': maNhanVien,
      'tenDayDuCty': tenDayDuCty,
      'tenDayDuNhanVien': tenDayDuNhanVien,
      'tenVietTatVungMien': tenVietTatVungMien,
      'truongLayDuLieu': truongLayDuLieu,
      'chucDanh': chucDanh,
      'shortCode': shortCode,
      'Tên người thụ hưởng': tenNguoiThuHuong,
      'Số tài khoản NH': soTaiKhoanNH,
      'Ngân hàng - Chi nhánh': nganHangChiNhanh,
    };
  }

  // Convenience method to check if the item is valid
  bool get isValid =>
      updatedTime != null &&
          sTT != null &&
          codeNS != null &&
          id != null;
}