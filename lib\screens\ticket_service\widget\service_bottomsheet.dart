import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/service/service_bloc.dart';
import 'package:eapprove/blocs/service/service_event.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/service/service_data_model.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:eapprove/repositories/service_repository.dart';
import 'package:eapprove/screens/common/error_handler.dart';

class ServiceBottomSheet extends StatefulWidget {
  final ServiceData initialServiceData;
  final List<ServiceData> allServices;

  const ServiceBottomSheet({
    Key? key,
    required this.initialServiceData,
    required this.allServices,
  }) : super(key: key);

  static Future<void> show({
    required BuildContext context,
    required ServiceData serviceData,
    required List<ServiceData> allServices,
  }) async {
    await showModalBottomSheet(
    context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (bottomSheetContext) =>
      BlocProvider.value(
        value: context.read<ServiceBloc>(),
        child: ServiceBottomSheet(
          initialServiceData: serviceData,
          allServices: allServices,
        ),
      ),
    );
  }

  @override
  State<ServiceBottomSheet> createState() => _ServiceBottomSheetState();
}

class _ServiceBottomSheetState extends State<ServiceBottomSheet> {
  // Navigation stack to track history
  final List<_NavigationItem> _navigationStack = [];

  @override
  void initState() {
    super.initState();
    // Initialize with the root service
    _navigationStack.add(_NavigationItem(
      serviceData: widget.initialServiceData,
      children: _getDirectChildren(widget.initialServiceData),
    ));
  }

  List<ServiceData> _getDirectChildren(ServiceData parent) {
    return widget.allServices
        .where((item) => item.parentId == parent.id && item.notShowingMoblie == false)
        .toList()
      ..sort((a, b) => (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));
  }

  // Navigate to a child service (push to stack)
  void _navigateToChild(ServiceData childService) {
    setState(() {
      _navigationStack.add(_NavigationItem(
        serviceData: childService,
        children: _getDirectChildren(childService),
        parentTitle: _currentItem.serviceData.serviceName,
      ));
    });
  }

  // Go back to previous service (pop from stack)
  void _navigateBack() {
    if (_navigationStack.length > 1) {
      setState(() {
        _navigationStack.removeLast();
      });
    }
  }

  // Get the current item being displayed
  _NavigationItem get _currentItem => _navigationStack.last;

  // Check if a service has children
  bool _hasChildren(ServiceData item) {
    return widget.allServices.any((child) =>
    child.parentId == item.id && child.notShowingMoblie == false
    );
  }

  Future<void> _handleServiceTap(ServiceData item) async {
    final hasChildren = _hasChildren(item);
    
    if (hasChildren) {
      _navigateToChild(item);
      return;
    }

    switch (item.serviceType.toString()) {
      case "3": // URL type
        if (item.url != null && item.url!.isNotEmpty) {
          try {
            final uri = Uri.parse(item.url!);
            await launchUrl(
              uri,
              mode: LaunchMode.externalApplication,
            );
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Invalid URL format: ${item.url}'),
                  backgroundColor: getColorSkin().red,
                ),
              );
            }
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('No URL provided for this service'),
                backgroundColor: getColorSkin().red,
              ),
            );
          }
        }
        break;
      
      case "4": // PDF type
        if (item.url != null && item.url!.isNotEmpty) {
          context.read<ServiceBloc>().add(const ClearPdfStatus());
          context.read<ServiceBloc>().add(DownloadPdf(item.url!));
        } else {
          if (mounted) {
            ErrorHandler.showErrorSnackBar(context, 'No PDF URL provided for this service');
          }
        }
        break;
      
      default:
        // Handle normal form navigation
        // if (item.procDefId != null) {
        //   Navigator.push(
        //     context,
        //     MaterialPageRoute(
        //       builder: (context) => TicketFormScreen(
        //         procDefId: item.procDefId!,
        //         title: item.serviceName ?? '',
        //       ),
        //     ),
        //   );
        // }
    }
  }

  // Build a service list item
  Widget _buildServiceItem(ServiceData item) {
    final hasChildren = _hasChildren(item);
    final padding = 16.w + (_navigationStack.length > 1 ? 16.w : 0);

    return GestureDetector(
      onTap: () => _handleServiceTap(item),
      onHorizontalDragEnd: hasChildren ? (details) {
        // If swiped left with enough velocity, navigate to child
        if (details.primaryVelocity != null && details.primaryVelocity! < -300) {
          _navigateToChild(item);
        }
      } : null,
        child: Container(
        padding: EdgeInsets.only(left: padding, right: 16.w),
        height: 48.h,
        child: Row(
          children: [
            if (item.serviceType.toString() == "3")
              Icon(
                Icons.link,
                color: getColorSkin().primaryBlue,
                size: 16.w,
              ),
            if (item.serviceType.toString() == "4")
              Icon(
                Icons.picture_as_pdf,
                color: getColorSkin().primaryBlue,
                size: 16.w,
              ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                item.serviceName ?? '',
                style: getTypoSkin().buttonRegular14.copyWith(
                  color: getColorSkin().ink1,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            if (hasChildren)
              Icon(
                Icons.chevron_right,
                color: getColorSkin().primaryBlue,
                size: 24.w,
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ServiceBloc, ServiceState>(
      buildWhen: (previous, current) {
        return previous.status != current.status ||
               previous.errorMessage != current.errorMessage ||
               previous.pdfFilePath != current.pdfFilePath;
      },
      builder: (context, state) {
        // Show PDF download success/error message
        if (state.status == ServiceStatus.success && state.errorMessage == null && state.pdfFilePath != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('PDF downloaded successfully'),
                backgroundColor: getColorSkin().primaryBlue,
              ),
            );
          });
        } else if (state.status == ServiceStatus.failure && state.errorMessage != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: getColorSkin().red,
              ),
            );
          });
        }

        final screenHeight = MediaQuery.of(context).size.height;
        final isRoot = _navigationStack.length == 1;
        final currentChildren = _currentItem.children;

        return Container(
          height: screenHeight * 0.5,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.r),
              topRight: Radius.circular(12.r),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with title and navigation controls
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: getColorSkin().grey4Background,
                      width: 1.h,
                    ),
                  ),
              ),
              child: Row(
                children: [
                  // Close button
                  IconButton(
                    icon: SvgPicture.asset(StringImage.ic_black_close, width: 25.w, height: 25.h),
                    onPressed: () => Navigator.pop(context),
                  ),
                    // Back button (only shown when not at root)
                    if (!isRoot)
                      IconButton(
                        icon: SvgPicture.asset(StringImage.ic_arrow_left_2, width: 25.w, height: 25.h),
                        onPressed: _navigateBack,
                      ),
                    Expanded(
                    child: Text(
                        _currentItem.serviceData.serviceName ?? '',
                        style: getTypoSkin().title6Regular.copyWith(
                          color: getColorSkin().ink1,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                ],
              ),
            ),

              // Service list
              Expanded(
                child: ListView.separated(
                  key: ValueKey<String>(_currentItem.serviceData.id?.toString() ?? ''),
                  itemCount: currentChildren.length,
                  padding: EdgeInsets.zero,
                  separatorBuilder: (context, index) => Divider(
                    height: 1.h,
                    color: getColorSkin().grey4Background,
                  ),
                  itemBuilder: (context, index) {
                    final child = currentChildren[index];
                    return _buildServiceItem(child);
                  },
                ),
              ),
            ],
        ),
      );
    },
    );
  }
}

// Helper class to represent an item in the navigation stack
class _NavigationItem {
  final ServiceData serviceData;
  final List<ServiceData> children;
  final String? parentTitle;

  _NavigationItem({
    required this.serviceData,
    required this.children,
    this.parentTitle,
  });
}

// Helper function to show the bottom sheet
void showServiceBottomSheet(
    BuildContext context,
    ServiceData serviceData,
    List<ServiceData> allServices,
    ServiceRepository serviceRepository,
    ) {
  ServiceBottomSheet.show(
    context: context,
    serviceData: serviceData,
    allServices: allServices,
  );
}