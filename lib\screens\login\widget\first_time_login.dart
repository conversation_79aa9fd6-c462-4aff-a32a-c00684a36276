import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/login/widget/login_container.dart';
import 'package:eapprove/widgets/otp_input_field.dart';
import 'package:eapprove/widgets/password_validation.dart'
    as PasswordValidationResult;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_sdk/widgets/form.dart';
import 'package:go_router/go_router.dart';

class FirstTimeLogin extends StatefulWidget {
  final VoidCallback onBackToLogin;
  final Function(String password, String rePassword) onSelectLoginMethod;
  final String username;
  final String hash;
  final bool is2FA;
  final String? sessionState;
  final String? email;

  const FirstTimeLogin({
    super.key,
    required this.onBackToLogin,
    required this.onSelectLoginMethod,
    required this.username,
    required this.hash,
    required this.is2FA,
    this.sessionState,
    this.email,
  });

  @override
  State<FirstTimeLogin> createState() => _FirstTimeLoginState();
}

class _FirstTimeLoginState extends State<FirstTimeLogin> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _rePasswordController = TextEditingController();

  bool _passwordVisible = false;
  bool _rePasswordVisible = false;
  final _formKey = GlobalKey<FFormState>();

  void _handleContinue() {
    if (_formKey.currentState?.validate() == true) {
      // if (widget.is2FA) {
      //   widget.onSelectLoginMethod(_passwordController.text, _rePasswordController.text);
      // } else {
      context.read<AuthenticationBloc>().add(
            FirstTimeLoginSubmitted(
              username: widget.username,
              password: _passwordController.text,
              rePassword: _rePasswordController.text,
              hash: widget.hash,
            ),
          );
      // }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is FirstTimeLoginSuccess) {
          widget.onBackToLogin();
        }
      },
      child: LoginContainer(
        child: FForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 20.h),
              Row(
                children: [
                  IconButton(
                    onPressed: widget.onBackToLogin,
                    icon: FIcon(icon: FIconData.icBack),
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'Cập nhật mật khẩu',
                    style: getTypoSkin().title1Medium.copyWith(
                          color: getColorSkin().title,
                        ),
                  ),
                ],
              ),
              SizedBox(height: 20.h),
              Text(
                'Bạn cần thay đổi mật khẩu để kích hoạt lại tài khoản của bạn',
                style: getTypoSkin().buttonText2Regular.copyWith(
                      color: getColorSkin().secondaryText,
                    ),
                textAlign: TextAlign.center,
                maxLines: 2,
                softWrap: true,
              ),
              SizedBox(height: 20.h),
              CustomTextField(
                controller: _passwordController,
                backgroundColor: getColorSkin().white,
                obscureText: !_passwordVisible,
                showClearIcon: true,
                labelText: 'Nhập mật khẩu mới',
                maxLength: 50,
                suffixIcon: FFilledButton.icon(
                  onPressed: () {
                    setState(() {
                      _passwordVisible = !_passwordVisible;
                    });
                  },
                  child: _passwordVisible
                      ? Icon(Icons.visibility, color: getColorSkin().subtitle)
                      : Icon(Icons.visibility_off,
                          color: getColorSkin().subtitle),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return FTextFieldStatus(
                        status: TFStatus.error,
                        message: 'Vui lòng nhập mật khẩu mới');
                  }

                  final result =
                      PasswordValidationResult.validatePasswordWithDetails(
                    value!,
                    username: widget.username,
                    email: widget.email,
                  );

                  if (!result.isValid) {
                    return FTextFieldStatus(
                        status: TFStatus.error,
                        message:
                            result.errorMessage ?? 'Mật khẩu không hợp lệ');
                  }

                  return null;
                },
              ),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: _rePasswordController,
                backgroundColor: getColorSkin().white,
                obscureText: !_rePasswordVisible,
                showClearIcon: true,
                labelText: 'Xác nhận mật khẩu',
                maxLength: 50,
                suffixIcon: FFilledButton.icon(
                  onPressed: () {
                    setState(() {
                      _rePasswordVisible = !_rePasswordVisible;
                    });
                  },
                  child: _rePasswordVisible
                      ? Icon(Icons.visibility, color: getColorSkin().subtitle)
                      : Icon(Icons.visibility_off,
                          color: getColorSkin().subtitle),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return FTextFieldStatus(
                        status: TFStatus.error,
                        message: 'Vui lòng xác nhận mật khẩu');
                  }
                  if (value != _passwordController.text) {
                    return FTextFieldStatus(
                        status: TFStatus.error,
                        message: 'Mật khẩu xác nhận không khớp');
                  }
                  return null;
                },
              ),
              SizedBox(height: 24.h),
              FFilledButton(
                onPressed: _handleContinue,
                isMaxWith: true,
                child:
                    Text(widget.is2FA ? 'Tiếp tục' : 'Xác nhận mật khẩu mới'),
              ),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _rePasswordController.dispose();
    super.dispose();
  }
}

class SelectLoginMethod extends StatefulWidget {
  final VoidCallback onBackToLogin;
  final VoidCallback? onBackToFirstTimeLogin;
  final String sessionState;
  final String username;
  final String hash;
  final String? newPassword;
  final String? newRePassword;
  final bool isFirstTimeLogin;

  const SelectLoginMethod({
    super.key,
    required this.onBackToLogin,
    this.onBackToFirstTimeLogin,
    required this.sessionState,
    required this.username,
    required this.hash,
    this.newPassword,
    this.newRePassword,
    this.isFirstTimeLogin = true,
  });

  @override
  State<SelectLoginMethod> createState() => _SelectLoginMethodState();
}

class _SelectLoginMethodState extends State<SelectLoginMethod> {
  String _otpValue = '';

  void handleBack() {
    if (widget.isFirstTimeLogin && widget.onBackToFirstTimeLogin != null) {
      widget.onBackToFirstTimeLogin!();
    } else {
      widget.onBackToLogin();
    }
  }

  void _handleOtpVerification() {
    if (_otpValue.length != 6) {
      SnackbarCore.error('Vui lòng nhập đủ 6 số OTP');
      return;
    }

    context.read<AuthenticationBloc>().add(
          VerifyOtpSubmitted(
            otp: _otpValue,
            sessionState: widget.sessionState,
            hash: widget.hash,
          ),
        );
  }

  void _handleOtpComplete(String otp) {
    setState(() {
      _otpValue = otp;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is OtpVerificationSuccess) {
          if (widget.isFirstTimeLogin &&
              widget.hash.isNotEmpty &&
              widget.newPassword != null &&
              widget.newPassword!.isNotEmpty) {
            context.read<AuthenticationBloc>().add(
                  FirstTimeLoginSubmitted(
                    username: widget.username,
                    password: widget.newPassword!,
                    rePassword: widget.newRePassword!,
                    hash: widget.hash,
                  ),
                );
          }
        } else if (state is FirstTimeLoginSuccess) {
          widget.onBackToLogin();
        } else if (state is TokenSuccess) {
          context.go(BottomNavScreen.routeName);
        } else if (state is AuthenticationFailure) {
          SnackbarCore.error(state.error);
        }
      },
      child: LoginContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 20.h),
            Row(
              children: [
                IconButton(
                    onPressed: handleBack,
                    icon: FIcon(
                      icon: FIconData.icBack,
                      color: getColorSkin().title,
                    )),
                SizedBox(width: 12.w),
                Text(
                  'Cập nhật mật khẩu',
                  style: getTypoSkin().title1Medium.copyWith(
                        color: getColorSkin().title,
                      ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Text(
              'Nhập OTP',
              style: getTypoSkin().buttonText2Regular.copyWith(
                    color: getColorSkin().secondaryText,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              softWrap: true,
            ),
            SizedBox(height: 20.h),
            OtpField(
              sessionState: widget.sessionState,
              onOtpComplete: _handleOtpComplete,
              hash: widget.hash,
            ),
            SizedBox(height: 16.h),
            FFilledButton(
              onPressed: _handleOtpVerification,
              isMaxWith: true,
              child: const Text('Xác nhận mật khẩu mới'),
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}
