class DateFilter {
  final String? fromDate;
  final String? toDate;
  final String? type;

  DateFilter({
    this.fromDate,
    this.toDate,
    this.type,
  });

  factory DateFilter.fromJson(Map<String, dynamic> json) {
    return DateFilter(
      fromDate: json['fromDate'],
      toDate: json['toDate'],
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fromDate': fromDate,
      'toDate': toDate,
      'type': type,
    };
  }
}

class AuthorizeManagementRequestModel {
  final List<String>? listAssignUser;
  final List<String>? listAssignedUser;
  final List<int>? status;
  final List<String>? listCreatedUser;
  final List<String>? listUpdatedUser;
  final List<String>? listNewRequestCode;
  final String? search;
  final String? sortBy;
  final String? sortType;
  final int? limit;
  final int? page;
  final int? totalPages;
  final int? totalElements;
  final List<DateFilter>? listDateFilter;
  final String? userLogin;
  final List<String>? assignUser;
  final List<String>? assignedUser;

  AuthorizeManagementRequestModel({
    this.listAssignUser,
    this.listAssignedUser,
    this.status,
    this.listCreatedUser,
    this.listUpdatedUser,
    this.listNewRequestCode,
    this.search,
    this.sortBy,
    this.sortType,
    this.limit,
    this.page,
    this.totalPages,
    this.totalElements,
    this.listDateFilter,
    this.userLogin,
    this.assignUser,
    this.assignedUser,
  });

  factory AuthorizeManagementRequestModel.fromJson(Map<String, dynamic> json) {
    return AuthorizeManagementRequestModel(
      listAssignUser: List<String>.from(json['listAssignUser']),
      listAssignedUser: List<String>.from(json['listAssignedUser']),
      status: List<int>.from(json['status']),
      listCreatedUser: List<String>.from(json['listCreatedUser']),
      listUpdatedUser: List<String>.from(json['listUpdatedUser']),
      listNewRequestCode: List<String>.from(json['listNewRequestCode']),
      search: json['search'],
      sortBy: json['sortBy'],
      sortType: json['sortType'],
      limit: json['limit'],
      page: json['page'],
      totalPages: json['totalPages'],
      totalElements: json['totalElements'],
      listDateFilter: (json['listDateFilter'] as List)
          .map((item) => DateFilter.fromJson(item))
          .toList(),
      userLogin: json['userLogin'],
      assignUser: List<String>.from(json['assignUser']),
      assignedUser: List<String>.from(json['assignedUser']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'listAssignUser': listAssignUser,
      'listAssignedUser': listAssignedUser,
      'status': status,
      'listCreatedUser': listCreatedUser,
      'listUpdatedUser': listUpdatedUser,
      'listNewRequestCode': listNewRequestCode,
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'limit': limit,
      'page': page,
      'totalPages': totalPages,
      'totalElements': totalElements,
      'listDateFilter': listDateFilter?.map((item) => item.toJson()).toList(),
      'userLogin': userLogin,
      'assignUser': assignUser,
      'assignedUser': assignedUser,
    };
  }
}
