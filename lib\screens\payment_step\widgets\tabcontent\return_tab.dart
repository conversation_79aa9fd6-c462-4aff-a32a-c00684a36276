
import 'package:eapprove/screens/payment_step/widgets/handle_modal.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_sdk/widgets/form.dart';

class ReturnTab extends StatelessWidget {
  final GlobalKey<FFormState> formKey;
  final TextEditingController reasonReturn;
  final List<PlatformFile> pickedReturnFile;
  final VoidCallback onPick;
  final ValueChanged<PlatformFile> onRemove;
  final VoidCallback onSaveDraft;
  final VoidCallback onReturn;
  final bool hideButtonsOnTablet;

  const ReturnTab({
    Key? key,
    required this.formKey,
    required this.reasonReturn,
    required this.pickedReturnFile,
    required this.onPick,
    required this.onRemove,
    required this.onSaveDraft,
    required this.onReturn,
    this.hideButtonsOnTablet = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FForm(
      key: formK<PERSON>,
      child: SingleChildScrollView(
        padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 32.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FormUtils.buildLabel('Lý do', required: true),
            SizedBox(height: 8.h),
            CustomTextField(
              controller: reasonReturn,
              maxLines: 3,
              isRequired: true,
              hintText: 'Nhập lý do trả về',
              validator: (v) {
                if (v?.isEmpty ?? true) {
                  return FTextFieldStatus(
                    status: TFStatus.error,
                    message: 'Vui lòng nhập lý do',
                  );
                }
                return null;
              },
            ),
            SizedBox(height: 12.h),
            FormUtils.buildAttachmentPicker(
              files: pickedReturnFile,
              onPick: onPick,
              onRemove: onRemove,
            ),
            SizedBox(height: 16.h),
            if (!hideButtonsOnTablet)
              Row(
                children: [
                  Expanded(
                  child: OutlinedButton(
                    onPressed: onSaveDraft,
                    style: FormUtils.outlinedBtnStyle(),
                    child: Text('OK',
                    style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue)),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onReturn,
                    style: FormUtils.elevatedBtnStyle(),
                    child: Text('Trả về',
                    style: getTypoSkin().medium14.copyWith(color: getColorSkin().white)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}