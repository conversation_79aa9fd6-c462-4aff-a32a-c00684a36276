import 'package:eapprove/models/meta.dart';

class FirstTimeLoginRequest {
  final String clientId;
  final String redirectUri;
  final String username;
  final String password;
  final String rePassword;
  final String hash;

  FirstTimeLoginRequest({
    required this.clientId,
    required this.redirectUri,
    required this.username,
    required this.password,
    required this.rePassword,
    required this.hash,
  });

  Map<String, dynamic> toJson() => {
        'client_id': clientId,
        'redirect_uri': redirectUri,
        'username': username,
        'password': password,
        're_password': rePassword,
        'hash': hash,
      };
}

class FirstTimeLoginResponse {
  final Meta meta;
  final FirstTimeLoginData? data;

  FirstTimeLoginResponse({
    required this.meta,
    this.data,
  });

  factory FirstTimeLoginResponse.fromJson(Map<String, dynamic> json) {
    return FirstTimeLoginResponse(
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
      data: json['data'] != null
          ? FirstTimeLoginData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  factory FirstTimeLoginResponse.error(int code, String message) {
    return FirstTimeLoginResponse(
      meta: Meta(code: code, message: message),
      data: null,
    );
  }
}

class FirstTimeLoginData {
  final String email;
  final String? code;
  final String? sessionState;
  final bool? is2FA;
  final String? redirectUri;
  final String? clientId;
  final String? hash;

  FirstTimeLoginData({
    required this.email,
    this.code,
    this.sessionState,
    this.is2FA,
    this.redirectUri,
    this.clientId,
    this.hash,
  });

  factory FirstTimeLoginData.fromJson(Map<String, dynamic> json) {
    return FirstTimeLoginData(
      email: json['email'] as String? ?? '',
      code: json['code'] as String?,
      sessionState: json['session_state'] as String?,
      is2FA: json['is2FA'] as bool?,
      redirectUri: json['redirect_uri'] as String?,
      clientId: json['client_id'] as String?,
      hash: json['hash'] as String?,
    );
  }
}
