import 'dart:convert';
import 'package:eapprove/models/account/account_model.dart';
import 'package:eapprove/models/account/chart_active_response_model.dart';
import 'package:eapprove/models/account/chart_node_by_code_model.dart';
import 'package:eapprove/models/account/chart_node_response_model.dart';
import 'package:eapprove/models/account/user_info_response_model.dart';
import 'package:eapprove/models/form/user_info_model.dart';
import 'package:eapprove/models/role_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:logger/logger.dart';

class AccountRepository {
  final ApiService _apiService;
  final Logger _logger = LoggerConfig.logger;

  AccountRepository({required ApiService apiService}) : _apiService = apiService;

  Future<Data> fetchUserData() async {
    try {
      final response = await _apiService.post(
        '/api/admin-api/public/keycloak/get-account-linked',
        {},
        customBaseUrl: 'admin',
      );

      _logger.d('Response status: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body: $responseBody');

      if (response.statusCode != 200) {
        throw Exception('⚠️ API fetchUserData trả về mã lỗi: ${response.statusCode}');
      }

      final Map<String, dynamic> jsonData = jsonDecode(responseBody);

      if (jsonData.containsKey('data')) {
        final data = Data.fromJson(jsonData['data']);
        _logger.d(
            'Dữ liệu nhận được: userList (${data.userList?.length ?? 0} users), userADM: ${data.userADM != null ? '✅ Có dữ liệu' : '❌ Không có dữ liệu'}');
        return data;
      } else {
        throw Exception('❌ Dữ liệu trả về không hợp lệ: $jsonData');
      }
    } catch (e, stackTrace) {
      _logger.e('Lỗi khi fetch user list: $e', error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }

  //dùng để lấy chartId (data.id) cho những api khác cần đến chart
  Future<ChartResponseModel> getChartData() async {
    try {
      final response = await _apiService.get(
        'customer/chart/getCharActiveByUser',
      );

      _logger.d('Response status chart data: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body chart data: $responseBody');

      if (response.statusCode != 200) {
        throw Exception('⚠️ API getChartData trả về mã lỗi: ${response.statusCode}');
      }

      final Map<String, dynamic> jsonData = jsonDecode(responseBody);

      if (jsonData.containsKey('data')) {
        final chartData = ChartResponseModel.fromJson(jsonData);
        _logger.d('Dữ liệu nhận được chart data: ${chartData.data.length} charts');
        return chartData;
      } else {
        throw Exception('❌ Dữ liệu trả về không hợp lệ: $jsonData');
      }
    } catch (e, stackTrace) {
      _logger.e('Lỗi khi fetch chart data: $e', error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }

  Future<ChartNodeResponseModel> getNodeByUserAndChartId({
    required int chartId,
    required String username,
    required int concurrently,
  }) async {
    try {
      final response = await _apiService.get(
        'customer/chart-node/getNodeByUserAndChartId?username=$username&chartId=$chartId&concurrently=$concurrently',
      );

      _logger.d('Response status: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body: $responseBody');

      if (response.statusCode != 200) {
        throw Exception('⚠️ API getNodeByUserAndChartId trả về mã lỗi: ${response.statusCode}');
      }

      final Map<String, dynamic> jsonData = jsonDecode(responseBody);

      if (jsonData.containsKey('data')) {
        final chartNodeData = ChartNodeResponseModel.fromJson(jsonData);
        _logger.d('Dữ liệu nhận được: ${chartNodeData.data.length} nodes');
        return chartNodeData;
      } else {
        throw Exception('Dữ liệu trả về không hợp lệ: $jsonData');
      }
    } catch (e, stackTrace) {
      _logger.e('Lỗi khi fetch chart node data: $e', error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }

  Future<UserProfileResponseModel> getUserInfoByToken() async {
    try {
      final response = await _apiService.get(
        'v1/auth/userInfoByToken',
        customBaseUrl: 'https://uat-api-sso.datxanh.com.vn/',
      );

      _logger.d('Response status: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body: $responseBody');

      if (response.statusCode != 200) {
        throw Exception('⚠️ API getUserInfoByToken trả về mã lỗi: ${response.statusCode}');
      }

      final Map<String, dynamic> jsonData = jsonDecode(responseBody);

      if (jsonData.containsKey('data')) {
        final userInfo = UserProfileResponseModel.fromJson(jsonData);
        return userInfo;
      } else {
        throw Exception('❌ Dữ liệu trả về không hợp lệ: $jsonData');
      }
    } catch (e, stackTrace) {
      _logger.e('Lỗi khi fetch user info: $e', error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }

  Future<RoleResponseModel> getUserRoles() async {
    try {
      Box box = Hive.box('authentication');
      final username = box.get('username', defaultValue: '').toString().toLowerCase();

      final response = await _apiService.get('auth/roles/$username');
      _logger.d('Role response status: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Role response body: $responseBody');

      if (response.statusCode != 200) {
        throw Exception('⚠️ API getUserRoles trả về mã lỗi: ${response.statusCode}');
      }

      final List<dynamic> roleList = jsonDecode(responseBody);

      final roleModel = RoleResponseModel(
        data: roleList.map((role) => role.toString()).toList(),
      );

      box.put('roles', roleModel.data);
      box.put('isAdmin', roleModel.data.contains('ROLE_ADMIN'));

      _logger.d('📥 Fetched ${roleModel.data.length} roles. Is Admin: ${roleModel.data.contains('ROLE_ADMIN')}');

      return roleModel;
    } catch (e, stackTrace) {
      _logger.e('Error fetching user roles: $e', error: e, stackTrace: stackTrace);
      return RoleResponseModel(data: ['ROLE_DEFAULT_USER']);
    }
  }

  Future<UserInfoByUserNameResponse> getUserInfoByUserName({required String username}) async {
    try {
      final response = await _apiService.get('customer/userInfo/getByUsername?username=$username');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final UserInfoByUserNameResponse userInfoResponse = UserInfoByUserNameResponse.fromJson(jsonData);
        debugPrint('[FormRepository]: User Info Response: $userInfoResponse');
        return userInfoResponse;
      } else {
        throw Exception('Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data - getUserInfoByUserName: $e');
    }
  }

  Future<ChartNodeByCodeModel> getChartNodeCustomByCode({
    required String code,
  }) async {
    try {
      final response = await _apiService.get(
        'customer/chartNodeCustom/getChartNodeCustomByCode?code=$code',
      );

      _logger.d('Response status: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body: $responseBody');

      if (response.statusCode != 200) {
        throw Exception('⚠️ API getChartNodeCustomByCode trả về mã lỗi: ${response.statusCode}');
      }

      final Map<String, dynamic> jsonData = jsonDecode(responseBody);

      if (jsonData.containsKey('data')) {
        final chartNodeData = ChartNodeByCodeModel.fromJson(jsonData);
        return chartNodeData;
      } else {
        throw Exception('❌ Dữ liệu trả về không hợp lệ: $jsonData');
      }
    } catch (e, stackTrace) {
      _logger.e('Lỗi khi fetch chart node data: $e', error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }
}
