import 'dart:developer';

import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/event/ticket_history_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/ticket_history_state.dart';
import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TicketHistoryBloc extends Bloc<TicketHistoryEvent, TicketHistoryState> {
  final TicketOtherActionRepository _ticketOtherActionRepository;
  int currentPage = 1;
  int pageSize = 10;

  TicketHistoryBloc(
      {required TicketOtherActionRepository ticketOtherActionRepository})
      : _ticketOtherActionRepository = ticketOtherActionRepository,
        super(const TicketHistoryState()) {
    on<GetTicketHistory>(_onLoadTicketHistory);
    on<NextPage>(_onNextPage);
    on<ResetPage>(_onResetPage);
    on<InitialTicketHistory>(_onInitialTicketHistory);
  }

  Future<void> _onLoadTicketHistory(
      GetTicketHistory event, Emitter<TicketHistoryState> emit) async {
    try {
      if(event.nextPage) {
        emit(state.copyWith(status: ServiceStatus.loading));
      }else{
        emit(state.copyWith(status: ServiceStatus.loading, response: null, errorMessage: ''));
      }
      final currentData = state.response?.content ?? [];

      final listAction = await _ticketOtherActionRepository.getFilterHistory(
          event.procInstId, event.ticketId, currentPage, pageSize);
      final ticketHistory = await _ticketOtherActionRepository.getTicketHistory(
          event.procInstId, event.ticketId, currentPage, pageSize, listAction, event.taskDefKey);
      if (event.nextPage) {
        final newData = ticketHistory.content;
        final combinedData = [...currentData, ...newData];
        emit(state.copyWith(
          status: ServiceStatus.success,
          response: TicketOtherActionResponse(
            content: combinedData,
            totalElements: ticketHistory.totalElements,
            number: ticketHistory.number,
            numberOfElements: ticketHistory.numberOfElements,
            totalPages: ticketHistory.totalPages,
          ),
        ));
      } else {
        emit(state.copyWith(
          status: ServiceStatus.success,
          response: ticketHistory,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
          status: ServiceStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onNextPage(
      NextPage event, Emitter<TicketHistoryState> emit) async {

    final totalPages = state.response?.totalPages;
    if (totalPages != null && currentPage < totalPages) {
      currentPage++;
      await _onLoadTicketHistory(
          GetTicketHistory(
              procInstId: event.procInstId, ticketId: event.ticketId, nextPage: true),
          emit);
    }
  }

  Future<void> _onResetPage(
      ResetPage event, Emitter<TicketHistoryState> emit) async {
    currentPage = 1;
    emit(state.copyWith(
      status: ServiceStatus.initial,
      response: null,
      errorMessage: '',
    ));
    await _onLoadTicketHistory(
        GetTicketHistory(
            procInstId: event.procInstId, ticketId: event.ticketId, nextPage: false),
        emit);
  }

  Future<void> _onInitialTicketHistory(
      InitialTicketHistory event, Emitter<TicketHistoryState> emit) async {
    currentPage = 1;

    emit(state.copyWith(
      status: ServiceStatus.initial,
      response: null,
      errorMessage: '',
    ));
  }
}
