import 'dart:developer' as developer;
import 'package:bloc/bloc.dart';
import 'package:eapprove/blocs/chart/chart_event.dart';
import 'package:eapprove/blocs/chart/chart_state.dart';
import 'package:eapprove/repositories/chart_repository.dart';

class ChartBloc extends Bloc<ChartEvent, ChartState> {
  final ChartRepository chartRepository;

  ChartBloc({required this.chartRepository}) : super(ChartInitial()) {
    on<FetchChartsByUser>(_onFetchChartsByUser);
  }

  Future<void> _onFetchChartsByUser(
      FetchChartsByUser event, Emitter<ChartState> emit) async {
    emit(ChartLoading());
    try {
      final response = await chartRepository.fetchChartsByUser();
      developer.log('_onFetchChartsByUser - response: ${response.data}');
      if(response.code == 200){
        emit(ChartLoaded(response.data));
      }else{
        emit(ChartError(response.message));
      }

     
    } catch (e) {
      developer.log('Error fetching charts: ${e.toString()}');
      emit(ChartError(e.toString()));
    }
  }
} 