import 'package:eapprove/screens/ticket_other_action/widget/mention_model.dart';
import 'package:intl/intl.dart';

class CommentUtils {
  /// Converts plain text to HTML, handling mentions
  static String convertTextToHtml(String text, {List<MentionRange>? mentionList}) {
    if (text.isEmpty) return '<p></p>';

    // Use provided mentions list or empty list
    final mentions = mentionList ?? [];

    // 1) Escape HTML
    final escaped = text.replaceAll('&', '&amp;').replaceAll('<', '&lt;').replaceAll('>', '&gt;');

    // If no mentions, return immediately
    if (mentions.isEmpty) {
      return '<p>$escaped</p>';
    }

    final positions = <Map<String, int>>[];
    int cursor = 0;
    for (var m in mentions) {
      final raw = m.data.replaceAll('&', '&amp;').replaceAll('<', '&lt;').replaceAll('>', '&gt;');
      final idx = escaped.indexOf(raw, cursor);
      if (idx >= 0) {
        positions.add({'start': idx, 'length': raw.length});
        cursor = idx + raw.length;
      }
    }

    positions.sort((a, b) => a['start']!.compareTo(b['start']!));
    final buf = StringBuffer('<p>');
    int last = 0;
    for (var pos in positions) {
      final s = pos['start']!;
      final len = pos['length']!;
      if (s > last) {
        buf.write(escaped.substring(last, s));
      }
      final mentionHtml = escaped.substring(s, s + len);
      buf.write('<a href="#" class="wysiwyg-mention" data-mention="$mentionHtml">'
          '$mentionHtml</a>');
      last = s + len;
    }
    if (last < escaped.length) {
      buf.write(escaped.substring(last));
    }
    buf.write('</p>');
    return buf.toString();
  }

  /// Converts HTML to plain text, extracting mentions
  static String convertHtmlToText(String html) {
    String result = html;

    result = result.replaceAll(RegExp(r'<p>', caseSensitive: false), '');
    result = result.replaceAll(RegExp(r'</p>', caseSensitive: false), '');
    result = result.replaceAll(RegExp(r'<br>', caseSensitive: false), '\n');

    RegExp linkRegex = RegExp(r'<a[^>]*>(.*?)</a>', caseSensitive: false);
    while (linkRegex.hasMatch(result)) {
      final match = linkRegex.firstMatch(result);
      if (match != null) {
        final fullMatch = match.group(0) ?? '';
        final linkText = match.group(1) ?? '';
        result = result.replaceFirst(fullMatch, linkText);
      } else {
        break;
      }
    }

    result = result.replaceAll('&lt;', '<');
    result = result.replaceAll('&gt;', '>');
    result = result.replaceAll('&amp;', '&');

    return result;
  }

  /// Extracts mentions from HTML content
  static List<MentionRange> extractMentionsFromHtml(String html) {
    List<MentionRange> mentions = [];

    RegExp mentionRegex = RegExp(r'<a href="#" class="wysiwyg-mention" data-mention="([^"]+)">[^<]+</a>');

    String plainText = convertHtmlToText(html);
    int plainTextOffset = 0;

    for (var match in mentionRegex.allMatches(html)) {
      final mentionText = match.group(1) ?? '';

      // Find where this mention appears in the plain text
      int mentionIndex = plainText.indexOf(mentionText, plainTextOffset);
      if (mentionIndex >= 0) {
        mentions.add(MentionRange(mentionIndex, mentionText.length, mentionText));
        plainTextOffset = mentionIndex + mentionText.length;
      }
    }

    return mentions;
  }

  /// Format timestamp to readable date
  static String formatDate(int? timestamp) {
    if (timestamp == null) return '';
    final dt = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat('dd/MM/yyyy HH:mm').format(dt);
  }
}
