import 'package:eapprove/common/form/input_text_form_item.dart';
import 'package:eapprove/common/form/text_area_widget.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';

import 'package:eapprove/enum/enum.dart';

abstract class BaseFormItem {
  final FormItemInfo data;
  final Function(String, dynamic) onChange; 

  const BaseFormItem({
    required this.data,
    required this.onChange,
  });

  // Core methods that all form items must implement
  Widget build(BuildContext context);
  dynamic getValue();
  bool validate();
  void reset();
  void setValue(dynamic value);
}

// Factory class to create form items
class FormItemFactory {
  static BaseFormItem create(FormItemInfo data, Function(String, dynamic) onChange) {
    switch (data.type) {
      case FormItemType.input:
        return InputTextFormItem(data: data, onChange: onChange);
      case FormItemType.textArea:
        return TextAreaFormItem(data: data, onChange: onChange);
      case FormItemType.dropdown:
        return DropdownFormItem(data: data, onChange: onChange);
      case FormItemType.datePicker:
        return DatePickerFormItem(data: data, onChange: onChange);
      case FormItemType.checkbox:
        return CheckboxFormItem(data: data, onChange: onChange);
      case FormItemType.radioButton:
        return RadioButtonFormItem(data: data, onChange: onChange);
      case FormItemType.fileUpload:
        return FileUploadFormItem(data: data, onChange: onChange);
      case FormItemType.label:
        return LabelFormItem(data: data, onChange: onChange);
      case FormItemType.url:
        return URLFormItem(data: data, onChange: onChange);
      default:
        throw UnsupportedError('Unsupported form item type: ${data.type}');
    }
  }
}

// Placeholder implementations for form items
class TextAreaFormItem extends BaseFormItem {
  const TextAreaFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class DropdownFormItem extends BaseFormItem {
  const DropdownFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class DatePickerFormItem extends BaseFormItem {
  const DatePickerFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class CheckboxFormItem extends BaseFormItem {
  const CheckboxFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class RadioButtonFormItem extends BaseFormItem {
  const RadioButtonFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class FileUploadFormItem extends BaseFormItem {
  const FileUploadFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class LabelFormItem extends BaseFormItem {
  const LabelFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
}

class URLFormItem extends BaseFormItem {
  const URLFormItem({
    required super.data,
    required super.onChange,
  });

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();

  @override
  dynamic getValue() => data.value;

  @override
  bool validate() => true;

  @override
  void reset() => onChange(data.name ?? '', null);

  @override
  void setValue(dynamic value) => onChange(data.name ?? '', value);
} 