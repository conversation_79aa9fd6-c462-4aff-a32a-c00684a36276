import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';

abstract class AssistantOpinionEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class UploadAssistantOpinionlFileEvent extends AssistantOpinionEvent {
  final List<PlatformFile> files;

  UploadAssistantOpinionlFileEvent(this.files);

  @override
  List<Object?> get props => [files];
}

class SubmitAssistantOpinionEvent extends AssistantOpinionEvent {
  final String ticketProcId;
  final String opinion;
  final int status;
  final String assistantEmail;
  final int ticketId;
  final List<Object> url;

  SubmitAssistantOpinionEvent({
    required this.ticketProcId,
    required this.opinion,
    required this.status,
    required this.assistantEmail,
    required this.ticketId,
    required this.url,
  });

  @override
  List<Object?> get props =>
      [ticketProcId, opinion,status, assistantEmail, ticketId, url];
}
