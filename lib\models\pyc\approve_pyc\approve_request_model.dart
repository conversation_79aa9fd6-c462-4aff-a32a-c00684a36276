import 'package:flutter/foundation.dart';

class ApproveRequestModel {
  final String? search;
  final int? page;
  final int? limit;
  final List<String>? type;
  final String? taskStatus;
  final String? sortBy;
  final String? sortType;
  final List<String>? listService;
  final List<String>? listUser;
  final List<String>? listStatus;
  final String? pageType;
  final String? fromDate;
  final String? toDate;
  final List<String>? listPriority;
  final bool? filterChangeAssignee;
  final List<dynamic>? listRating;
  final List<String>? listOrgAssign;
  final List<dynamic>? listAssigned;
  final String? createDate;
  final String? finishDate;
  final String? cancelDate;
  final String? ticketCreateDate;
  final List<String>? listCreatedUser;
  final List<String>? listCompanyName;
  final List<String>? listCompanyCode;
  final List<String>? listChartNodeName;
  final List<String>? listTicketTitle;

  ApproveRequestModel({
    this.search,
    this.page,
    this.limit,
    this.type,
    this.taskStatus,
    this.sortBy,
    this.sortType,
    this.listService,
    this.listUser,
    this.listStatus,
    this.pageType,
    this.fromDate,
    this.toDate,
    this.listPriority,
    this.filterChangeAssignee,
    this.listRating,
    this.listOrgAssign,
    this.listAssigned,
    this.createDate,
    this.finishDate,
    this.cancelDate,
    this.ticketCreateDate,
    this.listCreatedUser,
    this.listCompanyName,
    this.listCompanyCode,
    this.listChartNodeName,
    this.listTicketTitle,
  });

  factory ApproveRequestModel.fromCategory(ApproveTicketCategory category,
      {int page = 1, int limit = 10, Map<String, dynamic>? additionalParams}) {
    final data = approveTaskStatusMapping[category]!;
    final requestData = {
      "search": "",
      "page": page,
      "limit": limit,
      "type": ["APPROVAL"],
      "taskStatus": data["taskStatus"],
      "sortBy": "ticketCreatedTime",
      "sortType": "DESC",
      "listService": ["-1"],
      "listUser": ["-1"],
      "listStatus": List<String>.from(data["listStatus"]),
      "pageType": "task",
      "fromDate": "",
      "toDate": "",
      "listPriority": ["-1", "Trung bình", "Thấp", "Test nha", "Rất cao", "Cao", "B"],
      "filterChangeAssignee": false,
      "listRating": ["-1", 0, 1],
      "listOrgAssign": [],
      "listAssigned": [],
      "createDate": "1",
      "finishDate": "1",
      "cancelDate": "1",
      "ticketCreateDate": "1",
      "listCreatedUser": ["-1"],
      "listCompanyName": ["-1"],
      "listCompanyCode": ["-1"],
      "listChartNodeName": ["-1"],
      "listTicketTitle": ["-1"]
    };

    return ApproveRequestModel(
      search: requestData["search"],
      page: requestData["page"],
      limit: requestData["limit"],
      type: requestData["type"],
      taskStatus: requestData["taskStatus"],
      sortBy: requestData["sortBy"],
      sortType: requestData["sortType"],
      listService: requestData["listService"],
      listUser: requestData["listUser"],
      listStatus: requestData["listStatus"],
      pageType: requestData["pageType"],
      fromDate: requestData["fromDate"],
      toDate: requestData["toDate"],
      listPriority: requestData["listPriority"],
      filterChangeAssignee: requestData["filterChangeAssignee"],
      listRating: requestData["listRating"],
      listOrgAssign: (requestData["listOrgAssign"] as List).map((e) => e.toString()).toList(),
      listAssigned: (requestData["listAssigned"] as List).map((e) => e.toString()).toList(),
      createDate: requestData["createDate"],
      finishDate: requestData["finishDate"],
      cancelDate: requestData["cancelDate"],
      ticketCreateDate: requestData["ticketCreateDate"],
      listCreatedUser: requestData["listCreatedUser"],
      listCompanyName: requestData["listCompanyName"],
      listCompanyCode: requestData["listCompanyCode"],
      listChartNodeName: requestData["listChartNodeName"],
      listTicketTitle: requestData["listTicketTitle"],
    );
  }

  ApproveRequestModel copyWith({
    String? search,
    int? page,
    int? limit,
    List<String>? type,
    String? taskStatus,
    String? sortBy,
    String? sortType,
    List<String>? listService,
    List<String>? listUser,
    List<String>? listStatus,
    String? pageType,
    String? fromDate,
    String? toDate,
    List<String>? listPriority,
    bool? filterChangeAssignee,
    List<dynamic>? listRating,
    List<String>? listOrgAssign,
    List<dynamic>? listAssigned,
    String? createDate,
    String? finishDate,
    String? cancelDate,
    String? ticketCreateDate,
    List<String>? listCreatedUser,
    List<String>? listCompanyName,
    List<String>? listCompanyCode,
    List<String>? listChartNodeName,
    List<String>? listTicketTitle,
  }) {
    return ApproveRequestModel(
      search: search ?? this.search,
      page: page ?? this.page,
      limit: limit ?? this.limit,
      type: type ?? this.type,
      taskStatus: taskStatus ?? this.taskStatus,
      sortBy: sortBy ?? this.sortBy,
      sortType: sortType ?? this.sortType,
      listService: listService ?? this.listService,
      listUser: listUser ?? this.listUser,
      listStatus: listStatus ?? this.listStatus,
      pageType: pageType ?? this.pageType,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      listPriority: listPriority ?? this.listPriority,
      filterChangeAssignee: filterChangeAssignee ?? this.filterChangeAssignee,
      listRating: listRating ?? this.listRating,
      listOrgAssign: listOrgAssign ?? this.listOrgAssign,
      listAssigned: listAssigned ?? this.listAssigned,
      createDate: createDate ?? this.createDate,
      finishDate: finishDate ?? this.finishDate,
      cancelDate: cancelDate ?? this.cancelDate,
      ticketCreateDate: ticketCreateDate ?? this.ticketCreateDate,
      listCreatedUser: listCreatedUser ?? this.listCreatedUser,
      listCompanyName: listCompanyName ?? this.listCompanyName,
      listCompanyCode: listCompanyCode ?? this.listCompanyCode,
      listChartNodeName: listChartNodeName ?? this.listChartNodeName,
      listTicketTitle: listTicketTitle ?? this.listTicketTitle,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "search": search,
      "page": page,
      "limit": limit,
      "type": type,
      "taskStatus": taskStatus,
      "sortBy": sortBy,
      "sortType": sortType,
      "listService": listService,
      "listUser": listUser,
      "listStatus": listStatus,
      "pageType": pageType,
      "fromDate": fromDate,
      "toDate": toDate,
      "listPriority": listPriority,
      "filterChangeAssignee": filterChangeAssignee,
      "listRating": listRating,
      "listOrgAssign": listOrgAssign,
      "listAssigned": listAssigned,
      "createDate": createDate,
      "finishDate": finishDate,
      "cancelDate": cancelDate,
      "ticketCreateDate": ticketCreateDate,
      "listCreatedUser": listCreatedUser,
      "listCompanyName": listCompanyName,
      "listCompanyCode": listCompanyCode,
      "listChartNodeName": listChartNodeName,
      "listTicketTitle": listTicketTitle,
    };
  }
}

enum ApproveTicketCategory { approval, approved, returned, canceled }

const Map<ApproveTicketCategory, Map<String, dynamic>> approveTaskStatusMapping = {
  ApproveTicketCategory.approval: {
    "taskStatus": "ONGOING",
    "listStatus": ["-1", "PROCESSING", "RECALLING", "ACTIVE", "OPENED"],
  },
  ApproveTicketCategory.approved: {
    "taskStatus": "COMPLETED",
    "listStatus": ["-1", "RECALLING", "ACTIVE", "COMPLETED", "RECALLING"],
  },
  ApproveTicketCategory.returned: {
    "taskStatus": "RECALLED",
    "listStatus": ["DELETED_BY_RU", "AGREE_TO_RECALL", "-1"],
  },
  ApproveTicketCategory.canceled: {
    "taskStatus": "CANCEL",
    "listStatus": [
      "-1",
      "PROCESSING",
      "ACTIVE",
      "RECALLING",
    ],
  },
};
