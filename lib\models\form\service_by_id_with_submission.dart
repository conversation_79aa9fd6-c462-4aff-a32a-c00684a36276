class ServiceByIdWithPermissionResponse {
  final int code;
  final String message;
  final ServiceData? data;

  ServiceByIdWithPermissionResponse({
    required this.code,
    required this.message,
    this.data,
  });

  factory ServiceByIdWithPermissionResponse.fromJson(Map<String, dynamic> json) {
    return ServiceByIdWithPermissionResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      data: json['data'] != null ? ServiceData.fromJson(json['data'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class ServiceData {
  final int? id;
  final int? parentId;
  final String? serviceName;
  final String? color;
  final String? icon;
  final int? serviceType;
  final int? processId;
  final String? processName;
  final String? url;
  final String? description;
  final String? note;
  final bool? notShowingWebsite;
  final bool? notShowingMoblie;
  final String? hideName;
  final String? visibleName;
  final String? visibleGroup;
  final String? hideGroup;
  final int? positionPackage;
  final int? idOrgChart;
  final int? masterParentId;
  final dynamic countChild;
  final String? procDefId;
  final dynamic priority;
  final dynamic priorityId;
  final dynamic priorityLabel;
  final String? iconName;
  final String? createdDate;
  final String? createdUser;
  final String? modifiedDate;
  final String? modifiedUser;
  final bool? hideAllUser;
  final bool? visibleAllUser;
  final int? submissionType;
  final dynamic applyFor;
  final dynamic visibleChart;
  final dynamic hideChart;
  final bool? specialFlow;
  final bool? specialApplyFor;
  final dynamic specialParentId;
  final dynamic specialCompanyCode;
  final dynamic listParentCompanyCode;
  final String? companyCode;
  final String? companyName;
  final dynamic child;
  final String? status;

  ServiceData({
    this.id,
    this.parentId,
    this.serviceName,
    this.color,
    this.icon,
    this.serviceType,
    this.processId,
    this.processName,
    this.url,
    this.description,
    this.note,
    this.notShowingWebsite,
    this.notShowingMoblie,
    this.hideName,
    this.visibleName,
    this.visibleGroup,
    this.hideGroup,
    this.positionPackage,
    this.idOrgChart,
    this.masterParentId,
    this.countChild,
    this.procDefId,
    this.priority,
    this.priorityId,
    this.priorityLabel,
    this.iconName,
    this.createdDate,
    this.createdUser,
    this.modifiedDate,
    this.modifiedUser,
    this.hideAllUser,
    this.visibleAllUser,
    this.submissionType,
    this.applyFor,
    this.visibleChart,
    this.hideChart,
    this.specialFlow,
    this.specialApplyFor,
    this.specialParentId,
    this.specialCompanyCode,
    this.listParentCompanyCode,
    this.companyCode,
    this.companyName,
    this.child,
    this.status,
  });

  factory ServiceData.fromJson(Map<String, dynamic> json) {
    return ServiceData(
      id: json['id'] as int,
      parentId: json['parentId'] as int,
      serviceName: json['serviceName'] as String,
      color: json['color'] as String? ?? '',
      icon: json['icon'] as String? ?? '',
      serviceType: json['serviceType'] as int,
      processId: json['processId'] as int,
      processName: json['processName'] as String?,
      url: json['url'] as String? ?? '',
      description: json['description'] as String? ?? '',
      note: json['note'] as String?,
      notShowingWebsite: json['notShowingWebsite'] as bool,
      notShowingMoblie: json['notShowingMoblie'] as bool,
      hideName: json['hideName'] as String?,
      visibleName: json['visibleName'] as String?,
      visibleGroup: json['visibleGroup'] as String?,
      hideGroup: json['hideGroup'] as String?,
      positionPackage: json['positionPackage'] as int,
      idOrgChart: json['idOrgChart'] as int,
      masterParentId: json['masterParentId'] as int,
      countChild: json['countChild'],
      procDefId: json['procDefId'] as String?,
      priority: json['priority'],
      priorityId: json['priorityId'],
      priorityLabel: json['priorityLabel'],
      iconName: json['iconName'] as String?,
      createdDate: json['createdDate'] as String,
      createdUser: json['createdUser'] as String,
      modifiedDate: json['modifiedDate'] as String,
      modifiedUser: json['modifiedUser'] as String,
      hideAllUser: json['hideAllUser'] as bool,
      visibleAllUser: json['visibleAllUser'] as bool,
      submissionType: json['submissionType'] as int,
      applyFor: json['applyFor'],
      visibleChart: json['visibleChart'],
      hideChart: json['hideChart'],
      specialFlow: json['specialFlow'] as bool,
      specialApplyFor: json['specialApplyFor'] as bool,
      specialParentId: json['specialParentId'],
      specialCompanyCode: json['specialCompanyCode'],
      listParentCompanyCode: json['listParentCompanyCode'],
      companyCode: json['companyCode'] as String,
      companyName: json['companyName'] as String,
      child: json['child'],
      status: json['status'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'serviceName': serviceName,
      'color': color,
      'icon': icon,
      'serviceType': serviceType,
      'processId': processId,
      'processName': processName,
      'url': url,
      'description': description,
      'note': note,
      'notShowingWebsite': notShowingWebsite,
      'notShowingMoblie': notShowingMoblie,
      'hideName': hideName,
      'visibleName': visibleName,
      'visibleGroup': visibleGroup,
      'hideGroup': hideGroup,
      'positionPackage': positionPackage,
      'idOrgChart': idOrgChart,
      'masterParentId': masterParentId,
      'countChild': countChild,
      'procDefId': procDefId,
      'priority': priority,
      'priorityId': priorityId,
      'priorityLabel': priorityLabel,
      'iconName': iconName,
      'createdDate': createdDate,
      'createdUser': createdUser,
      'modifiedDate': modifiedDate,
      'modifiedUser': modifiedUser,
      'hideAllUser': hideAllUser,
      'visibleAllUser': visibleAllUser,
      'submissionType': submissionType,
      'applyFor': applyFor,
      'visibleChart': visibleChart,
      'hideChart': hideChart,
      'specialFlow': specialFlow,
      'specialApplyFor': specialApplyFor,
      'specialParentId': specialParentId,
      'specialCompanyCode': specialCompanyCode,
      'listParentCompanyCode': listParentCompanyCode,
      'companyCode': companyCode,
      'companyName': companyName,
      'child': child,
      'status': status,
    };
  }
}
