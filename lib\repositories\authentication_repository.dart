import 'dart:convert';
import 'package:eapprove/models/authentication/login_model.dart';
import 'package:eapprove/models/authentication/token_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:logger/logger.dart';

class AuthenticationRepository {
  final ApiService _apiService;
  final Logger _logger = LoggerConfig.logger;
  final String _clientId = "emp-eapp-mobile-app";
  final String _clientSecret = "ggvcRsekFihETch2N3vY9plxu8AXBh01";
  final String _redirectUri = "https://uat-eapp-mobile.datxanh.com.vn/";

  AuthenticationRepository({required ApiService apiService}) : _apiService = apiService;

  Future<LoginResponse> login(String username, String password) async {
    try {
      final loginRequest = LoginRequest(
        clientId: _clientId,
        redirectUri: _redirectUri,
        username: username,
        password: password,
      );

      final response = await _apiService.post('v1/auth', loginRequest.toJson(),
          customBaseUrl: 'https://uat-api-sso.datxanh.com.vn/');

      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('login response: $responseBody');

      final jsonData = jsonDecode(responseBody);
      _logger.d('Parsed login JSON data: $jsonData');

      if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
        return LoginResponse.fromJson(jsonData);
      }

      _logger.w('Invalid response format');
      return LoginResponse.error(500, 'Invalid response format');
    } catch (e, stackTrace) {
      _logger.e(
        'Login repository error',
        error: e,
        stackTrace: stackTrace,
      );
      return LoginResponse.error(500, 'Connection error: ${e.toString()}');
    }
  }

  // Future<resetPassResponse> resetPassword(String username) async {
  //   try {
  //     final resetPassRequest = ResetPassRequest(
  //       clientId: _clientId,
  //       redirectUri: _redirectUri,
  //       username: username,
  //     );

  //     final response = await _apiService.post('/v1/auth/sendReqResetPassword', resetPassRequest.toJson());

  //     String responseBody = utf8.decode(response.bodyBytes);
  //     _logger.d('reset pass response: $responseBody');

  //     final jsonData = jsonDecode(responseBody);
  //     _logger.d('Parsed reset pass JSON data: $jsonData');

  //     if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
  //       return ResetPassResponse.fromJson(jsonData);
  //     }

  //     _logger.w('Invalid response format');
  //     return ResetPassResponse.error(500, 'Invalid response format');
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'Login repository error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     return ResetPassResponse.error(500, 'Connection error: ${e.toString()}');
  //   }
  // }

  // Future<FirstTimeLoginResponse> changePassFirstLogin(
  //     String username, String password, String rePassword, String hash) async {
  //   try {
  //     final firstTimeLoginRequest = FirstTimeLoginRequest(
  //       clientId: _clientId,
  //       redirectUri: _redirectUri,
  //       username: username,
  //       password: password,
  //       rePassword: rePassword,
  //       hash: hash,
  //     );

  //     final response = await _apiService.post('/v1/auth/changePasswordWithFirstLogin', firstTimeLoginRequest.toJson());

  //     String responseBody = utf8.decode(response.bodyBytes);
  //     _logger.d('reset pass response: $responseBody');

  //     final jsonData = jsonDecode(responseBody);
  //     _logger.d('Parsed reset pass JSON data: $jsonData');

  //     if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
  //       return FirstTimeLoginResponse.fromJson(jsonData);
  //     }

  //     _logger.w('Invalid response format');
  //     return FirstTimeLoginResponse.error(500, 'Invalid response format');
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'Login repository error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     return FirstTimeLoginResponse.error(500, 'Connection error: ${e.toString()}');
  //   }
  // }

  // Future<OtpVerifyResponse> verifyOtp(String otp, String sessionState, {String? hash}) async {
  //   try {
  //     final String actualSessionState;

  //     if (sessionState.isEmpty && hash != null && hash.isNotEmpty) {
  //       actualSessionState = hash;
  //       _logger.d('Using hash as session_state: $hash');
  //     } else {
  //       actualSessionState = sessionState;
  //       _logger.d('Using original sessionState: $sessionState');
  //     }

  //     final Map<String, dynamic> requestData = {'otp': otp, 'session_state': actualSessionState};

  //     _logger.d('Sending OTP verification request: $requestData');

  //     final response = await _apiService.post('/v1/auth/otp', requestData);

  //     String responseBody = utf8.decode(response.bodyBytes);
  //     _logger.d('OTP verification response: $responseBody');

  //     final jsonData = jsonDecode(responseBody);
  //     _logger.d('Parsed OTP verification JSON data: $jsonData');

  //     if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
  //       return OtpVerifyResponse.fromJson(jsonData);
  //     }

  //     _logger.w('Invalid response format');
  //     return OtpVerifyResponse.error(500, 'Invalid response format');
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'OTP verification error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     return OtpVerifyResponse.error(500, 'Connection error: ${e.toString()}');
  //   }
  // }

  // Future<OtpVerifyResponse> resendOtp(String sessionState, {String? hash}) async {
  //   try {
  //     final String stateToUse = sessionState.isEmpty && hash != null ? hash : sessionState;

  //     final Map<String, dynamic> requestData = {'session_state': stateToUse};

  //     _logger.d('Resending OTP with data: $requestData');

  //     final response = await _apiService.post('/v1/auth/reSendOtp', requestData);

  //     String responseBody = utf8.decode(response.bodyBytes);
  //     _logger.d('Resend OTP response: $responseBody');

  //     final jsonData = jsonDecode(responseBody);
  //     _logger.d('Parsed resend OTP JSON data: $jsonData');

  //     if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
  //       return OtpVerifyResponse.fromJson(jsonData);
  //     }

  //     _logger.w('Invalid response format');
  //     return OtpVerifyResponse.error(500, 'Invalid response format');
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'Resend OTP error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     return OtpVerifyResponse.error(500, 'Connection error: ${e.toString()}');
  //   }
  // }

  Future<TokenResponse> getToken(String code, {bool is2FA = false}) async {
    try {
      final tokenRequest = TokenRequest(
        clientId: _clientId,
        clientSecret: _clientSecret,
        grantType: "authorization_code",
        code: code,
        redirectUri: _redirectUri,
      );

      final response = await _apiService.post(
          'v1/auth/token', tokenRequest.toJson(),
          customBaseUrl: 'https://uat-api-sso.datxanh.com.vn/');

      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Get token response: $responseBody');

      final jsonData = jsonDecode(responseBody);
      _logger.d('Parsed token JSON data: $jsonData');

      if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
        return TokenResponse.fromJson(jsonData);
      }

      _logger.w('Invalid token response format');
      return TokenResponse.error(500, 'Invalid response format');
    } catch (e, stackTrace) {
      _logger.e(
        'Get token error',
        error: e,
        stackTrace: stackTrace,
      );
      return TokenResponse.error(500, 'Connection error: ${e.toString()}');
    }
  }

  Future<TokenResponse> refreshToken(String refreshToken) async {
    try {
      final refreshTokenRequest = RefreshTokenRequest(
        clientId: _clientId,
        clientSecret: _clientSecret,
        grantType: "refresh_token",
        refreshToken: refreshToken,
        redirectUri: _redirectUri,
      );

      final response = await _apiService.post(
        'v1/auth/refreshToken',
        refreshTokenRequest.toJson(),
      );

      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Refresh token response: $responseBody');

      final jsonData = jsonDecode(responseBody);
      _logger.d('Parsed refresh token JSON data: $jsonData');

      if (jsonData is Map<String, dynamic> && jsonData['meta'] is Map<String, dynamic>) {
        return TokenResponse.fromJson(jsonData);
      }

      _logger.w('Invalid refresh token response format');
      return TokenResponse.error(500, 'Invalid response format');
    } catch (e, stackTrace) {
      _logger.e(
        'Refresh token error',
        error: e,
        stackTrace: stackTrace,
      );
      return TokenResponse.error(500, 'Connection error: ${e.toString()}');
    }
  }
}
