import 'package:equatable/equatable.dart';

abstract class TicketDialogActionState extends Equatable {
  const TicketDialogActionState();
  @override
  List<Object?> get props => [];
}

class TicketDialogActionInitial extends TicketDialogActionState {}
class TicketDialogActionLoading extends TicketDialogActionState {} 

class UploadFileState extends TicketDialogActionState {
  final List<String> filePaths;

  const UploadFileState(this.filePaths);

  @override
  List<Object?> get props => [filePaths];
}

class UploadFileLoaded extends TicketDialogActionState {
  final List<dynamic> filePaths;

  const UploadFileLoaded(this.filePaths);

  @override
  List<Object?> get props => [filePaths];
}

class UploadFileError extends TicketDialogActionState {
  final String message;

  const UploadFileError(this.message);

  @override
  List<Object?> get props => [message];
}

class CancelTicketLoaded extends TicketDialogActionState {
  final String message;

  const CancelTicketLoaded({required this.message});

  @override
  List<Object?> get props => [message];
}

class CancelTicketError extends TicketDialogActionState {
  final String message;

  const CancelTicketError(this.message);

  @override
  List<Object?> get props => [message];
}

class DraftCancelTicketLoaded extends TicketDialogActionState {
  final String message;

  const DraftCancelTicketLoaded({required this.message});

  @override
  List<Object?> get props => [message];
}

class DraftCancelTicketError extends TicketDialogActionState {
  final String message;

  const DraftCancelTicketError(this.message);

  @override
  List<Object?> get props => [message];
}

class DraftReturnTicketError extends TicketDialogActionState {
  final String message;

  const DraftReturnTicketError(this.message);

  @override
  List<Object?> get props => [message];
}

class DraftReturnTicketLoaded extends TicketDialogActionState {
  final String message;

  const DraftReturnTicketLoaded({required this.message});

  @override
  List<Object?> get props => [message];
}

class ReturnTicketLoaded extends TicketDialogActionState {
  final String message;

  const ReturnTicketLoaded({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthorizedLoaded extends TicketDialogActionState {
  final String message;

  const AuthorizedLoaded({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthorizedError extends TicketDialogActionState {
  final String message;

  const AuthorizedError({required this.message});

  @override
  List<Object?> get props => [message];
}

class ReturnTicketError extends TicketDialogActionState {
  final String message;

  const ReturnTicketError(this.message);

  @override
  List<Object?> get props => [message];
}

class AuthorizationListLoaded extends TicketDialogActionState {
  final List<Map<String, dynamic>> data;

  const AuthorizationListLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

class AuthorizationListLoading extends TicketDialogActionState {}

class AuthorizationListError extends TicketDialogActionState {
  final String message;

  const AuthorizationListError(this.message);

  @override
  List<Object?> get props => [message];
}
