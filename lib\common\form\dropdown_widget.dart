// import 'package:eapprove/blocs/form/form_bloc.dart';
// import 'package:eapprove/blocs/form/form_event.dart';
// import 'package:eapprove/blocs/form/form_state.dart';
// import 'package:eapprove/common/form_v2/form_state_manager.dart';
// import 'package:eapprove/models/form/form_item_info.dart';
// import 'package:eapprove/models/form/individual_info_request_model.dart';
// import 'package:eapprove/models/form/md_service_request_model.dart';
// import 'package:eapprove/models/form/md_service_response.dart';
// import 'package:eapprove/common/form/custom_dropdown_multi_form.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'package:flutter_sdk/widgets/custom_dropdown.dart';


// // Constants
// const _kDefaultDropdownHeight = 40.0;
// const _kDefaultBorderRadius = 10.0;
// const _kDefaultPadding = 12.0;
// const _kDefaultSpacing = 8.0;

// // Mixins
// mixin DropdownDataMixin on State<DropdownWidget> {
//   bool _isLoading = false;
//   List<SelectItem> _cachedItems = [];
//   String? _lastRequestKey;
//   String? _lastDependentValue;

//   String _getRequestKey() {
//     if (widget.data.typeOption == 'masterData' ||
//         widget.data.optionType == 'masterData') {
//       final masterDataId =
//           widget.data.optionConfig?['masterDataId']?.toString() ?? '';
//       final conditionFilters = _buildConditionFilters();
//       return 'masterData_${masterDataId}_${conditionFilters.map((f) => f.toJson()).toList()}';
//     } else if (widget.data.typeOption == 'orgchart' ||
//         widget.data.optionType == 'orgchart') {
//       final orgchartType =
//           widget.data.optionConfig?['orgchartType']?.toString() ?? '';
//       final orgchartExpressions =
//           widget.data.optionConfig?['orgchartExpression'] as List<dynamic>? ??
//               [];
//       return 'orgchart_${orgchartType}_${orgchartExpressions.map((e) => e.toString()).toList()}';
//     }
//     return 'static_${widget.data.options?.length ?? 0}';
//   }

//   String? _getDependentValue() {
//     final dependentField =
//         widget.data.optionConfig?['dependentField']?.toString();
//     if (dependentField != null) {
//       final formState = context.read<FormBloc>().state;
//       if (formState.formResponse != null) {
//         final dependentItem =
//             formState.formResponse!.data.template.form.firstWhere(
//           (item) => item.name == dependentField,
//           orElse: () => FormItemInfo(),
//         );
//         return dependentItem.value?.toString();
//       }
//     }
//     return null;
//   }

//   List<ConditionFilter> _buildConditionFilters() {
//     final mdExpressions =
//         widget.data.optionConfig?['mdExpression'] as List<dynamic>?;

//     return mdExpressions?.map((expression) {
//           final begin = expression['type']?.toString() ?? '';
//           final operator = expression['expression']?.toString() ?? '=';
//           final condition = expression['condition']?.toString() ?? 'and';

//           return ConditionFilter(
//             condition: condition,
//             begin: begin,
//             operator: operator,
//             end: "1000",
//           );
//         }).toList() ??
//         [];
//   }
// }

// mixin DropdownDisplayMixin on State<DropdownWidget> {
//   String _formatDisplayValue(MdService item) {
//     if (item.id == null) return '';
//     final displayFields =
//         widget.data.optionConfig?['masterDataDisplay'] as List<dynamic>? ?? [];
//     if (displayFields.isEmpty) {
//       return item.id ?? '';
//     }

//     final displayValues = displayFields
//         .map((field) => item.toJson()[field]?.toString() ?? '')
//         .where((value) => value.isNotEmpty)
//         .toList();

//     return displayValues.isNotEmpty ? displayValues.join('_') : item.id ?? '';
//   }

//   List<String> _getDefaultDisplayFields(String orgchartType) {
//     switch (orgchartType) {
//       case 'department':
//         return ['departmentName'];
//       case 'position':
//         return ['positionName'];
//       case 'user':
//       default:
//         return ['username'];
//     }
//   }

//   String _getDefaultValueField(String orgchartType) {
//     switch (orgchartType) {
//       case 'department':
//         return 'username';
//       case 'position':
//         return 'positionId';
//       case 'user':
//       default:
//         return 'username';
//     }
//   }

//   String _getDefaultLabel(dynamic info, String orgchartType) {
//     switch (orgchartType) {
//       case 'department':
//         return '${info.username ?? ''}_${info.name ?? ''}_${info.position ?? ''}';
//       case 'position':
//         return info.position ?? '';
//       case 'user':
//       default:
//         return info.username ?? '';
//     }
//   }
// }

// class DropdownWidget extends StatefulWidget {
//   final FormItemInfo data;
//   final Function(String, dynamic)? onChange;
//   final FormStateManager stateManager;
  
//   const DropdownWidget({
//     Key? key,
//     required this.data,
//      this.onChange,
//     required this.stateManager,
//   }) : super(key: key);

//   @override
//   State<DropdownWidget> createState() => _DropdownWidgetState();
// }

// class _DropdownWidgetState extends State<DropdownWidget>
//     with DropdownDataMixin, DropdownDisplayMixin {
//   @override
//   void initState() {
//     super.initState();
//     // _fetchDropdownData();
//     // _fetchMasterData();
//     // _fetchOrgchartData();
//     // _buildSelectItems();
//     // _findDefaultValue();
//   }

//   @override
//   void didUpdateWidget(DropdownWidget oldWidget) {
//     super.didUpdateWidget(oldWidget);

//     final dependentValue = _getDependentValue();
//     if (dependentValue != _lastDependentValue) {
//       _lastDependentValue = dependentValue;
//       // _fetchDropdownData();
//     }

//     if (oldWidget.data != widget.data) {
//       final newRequestKey = _getRequestKey();
//       if (newRequestKey != _lastRequestKey) {
//         _lastRequestKey = newRequestKey;
//         // _fetchDropdownData();
//       }
//     }
//   }

//   void _fetchDropdownData() {
//     // Xác định loại API cần gọi dựa trên type của dropdown
//     if (widget.data.typeOption == 'masterData' ||
//         widget.data.optionType == 'masterData') {
//       _fetchMasterData();
//     } else if (widget.data.typeOption == 'orgchart' ||
//         widget.data.optionType == 'orgchart') {
//       _fetchOrgchartData();
//     }
//   }

//   void _fetchMasterData() {
//     // Chuẩn bị tham số request cho API master data
//     final masterDataId =
//         widget.data.optionConfig?['masterDataId']?.toString() ?? '';
//     if (masterDataId.isEmpty) return;

//     final conditionFilters = _buildConditionFilters();
//     final requestBody = MdServiceRequestBody(
//       masterDataId: masterDataId,
//       conditionFilter: conditionFilters,
//     );

//     // Gọi API master data
//     context.read<FormBloc>().add(GetMdServiceRequested(
//           requestBody: requestBody,
//         ));
//   }

//   void _fetchOrgchartData() {
//     // Lấy type và expressions của orgchart từ config
//     final orgchartType =
//         widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';
//     final orgchartExpressions =
//         widget.data.optionConfig?['orgchartExpression'] as List<dynamic>? ?? [];
    
//     if (orgchartType.isEmpty) return;

//     // Lấy giá trị phụ thuộc để lọc dữ liệu
//     String? dependentValue = _getDependentValue();
//     if (orgchartType == 'department') {
//       // Xử lý đặc biệt cho department type - lấy giá trị user trước
//       final formState = context.read<FormBloc>().state;
//       if (formState.formResponse != null) {
//         final userField = formState.formResponse!.data.template.form.firstWhere(
//           (item) => item.optionConfig?['orgchartType']?.toString() == 'user',
//           orElse: () => FormItemInfo(),
//         );
//         dependentValue = userField.value?.toString();
//       }
//     }

//     // Xây dựng điều kiện cho request API
//     final conditions = orgchartExpressions.map((expression) {
//       final filterType = orgchartType == 'department'
//           ? 'user'
//           : (expression['type']?.toString() ?? 'user');
//       final filterField = orgchartType == 'department'
//           ? 'username'
//           : (expression['fieldType']?.toString() ?? '');

//       return Condition(
//         expression: expression['condition']?.toString() ?? 'and',
//         filterType: filterType,
//         filterField: filterField,
//         operator: expression['expression']?.toString() ?? '=',
//         value: dependentValue ??
//             (context.read<FormBloc>().state.chartData?.data.isNotEmpty == true
//                 ? context.read<FormBloc>().state.chartData!.data[0].id.toString()
//                 : ''),
//       );
//     }).toList();

//     // Chuẩn bị model request
//     final requestModel = IndividualInfoRequestModel(
//       orgchart: [],
//       infoType: orgchartType == 'department' ? 'user' : orgchartType,
//       condition: conditions,
//     );

//     // Gọi API orgchart
//     context.read<FormBloc>().add(GetIndividualInfoRequested(
//           requestModel: requestModel,
//           dependentValue: dependentValue,
//         ));
//   }

//   @override
//   Widget build(BuildContext context) {
//     final options = widget.data.options ?? [];
//     final isMultiSelect = widget.data.validations?['isMulti'] == true;
//     final isUserType =
//         widget.data.optionConfig?['orgchartType']?.toString() == 'user';
//     final isDepartmentType =
//         widget.data.optionConfig?['orgchartType']?.toString() == 'department';

//     return BlocListener<FormBloc, FormInfoState>(
//       listenWhen: (previous, current) {
//         if (isUserType) {
//           return previous.userInfoResponse != current.userInfoResponse;
//         } else if (isDepartmentType) {
//           return previous.departmentInfoResponse != current.departmentInfoResponse;
//         } else if (widget.data.typeOption == 'masterData' ||
//             widget.data.optionType == 'masterData') {
//           return previous.mdServiceResponse != current.mdServiceResponse;
//         }
//         return false;
//       },
//       listener: (context, state) {
//         if (isUserType && state.userInfoResponse?.isSuccess == true) {
//           _handleUserDataResponse(state);
//         } else if (isDepartmentType &&
//             state.departmentInfoResponse?.isSuccess == true) {
//           _handleDepartmentDataResponse(state);
//         } else if ((widget.data.typeOption == 'masterData' ||
//                 widget.data.optionType == 'masterData') &&
//             state.mdServiceResponse?.isSuccess == true) {
//           _handleMasterDataResponse(state);
//         }
//       },
//       child: BlocBuilder<FormBloc, FormInfoState>(
//         buildWhen: (previous, current) {
//           if (isUserType) {
//             return previous.userInfoResponse != current.userInfoResponse;
//           } else if (isDepartmentType) {
//             return previous.departmentInfoResponse != current.departmentInfoResponse;
//           } else if (widget.data.typeOption == 'masterData' ||
//               widget.data.optionType == 'masterData') {
//             return previous.mdServiceResponse != current.mdServiceResponse;
//           }
//           return false;
//         },
//         builder: (context, state) {
//           if (_isLoading) {
//             return const Center(child: CircularProgressIndicator());
//           }

//           if (state.errorMessage != null) {
//             return Text(
//               state.errorMessage!,
//               style: const TextStyle(color: Colors.red),
//             );
//           }

//           return _buildDropdownContainer(options, isMultiSelect, state);
//         },
//       ),
//     );
//   }

//   void _handleUserDataResponse(FormInfoState state) {
//     if (state.userInfoResponse?.data.isNotEmpty == true) {
//       setState(() {
//         _isLoading = false;
//         _cachedItems = _buildSelectItems(widget.data.options ?? [], state);
//       });
//     }
//   }

//   void _handleDepartmentDataResponse(FormInfoState state) {
//     if (state.departmentInfoResponse?.data.isNotEmpty == true) {
//       setState(() {
//         _isLoading = false;
//         _cachedItems = _buildSelectItems(widget.data.options ?? [], state);
//       });
//     }
//   }

//   void _handleMasterDataResponse(FormInfoState state) {
//     if (state.mdServiceResponse?.data.isNotEmpty == true) {
//       setState(() {
//         _isLoading = false;
//         _cachedItems = _buildSelectItems(widget.data.options ?? [], state);
//       });
//     }
//   }

//   Widget _buildDropdownContainer(
//       List<Map<String, dynamic>> options, bool isMultiSelect, FormInfoState state) {
//     final labelWidget = _buildLabelWidget();

//     if ((widget.data.typeOption == 'masterData' ||
//             widget.data.optionType == 'masterData') &&
//         state.isLoading) {
//       return _buildLoadingContainer(labelWidget);
//     }

//     return Container(
//       margin: EdgeInsets.only(top: _kDefaultSpacing.h),
//       child: _buildVerticalLayout(options, isMultiSelect, state, labelWidget),
//     );
//   }

//   Widget _buildLoadingContainer(Widget labelWidget) {
//     return Padding(
//       padding: EdgeInsets.only(bottom: 16.h),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (widget.data.displayName?.isNotEmpty == true) labelWidget,
//           const SizedBox(height: _kDefaultSpacing),
//           const Center(child: CircularProgressIndicator(strokeWidth: 2)),
//         ],
//       ),
//     );
//   }

//   Widget _buildLabelWidget() {
//     if (widget.data.label?.isEmpty != false) {
//       return const SizedBox.shrink();
//     }
//     return Text(
//       widget.data.displayName ?? '',
//       style: TextStyle(
//         fontWeight: widget.data.fontWeight == 'bold'
//             ? FontWeight.bold
//             : FontWeight.normal,
//         fontStyle: widget.data.fontWeight == 'italic'
//             ? FontStyle.italic
//             : FontStyle.normal,
//         decoration: widget.data.fontWeight == 'underline'
//             ? TextDecoration.underline
//             : TextDecoration.none,
//       ),
//     );
//   }

//   Widget _buildVerticalLayout(List<Map<String, dynamic>> options,
//       bool isMultiSelect, FormInfoState state, Widget labelWidget) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         _buildDropdown(options, isMultiSelect, state),
//         if (widget.data.error?.isNotEmpty == true)
//           Padding(
//             padding: EdgeInsets.only(top: 4.h, left: 2.w),
//             child: Text(
//               widget.data.error ?? '',
//               style: TextStyle(
//                 color: Colors.red,
//                 fontSize: 12.sp,
//               ),
//             ),
//           ),
//       ],
//     );
//   }

//   Widget _buildDropdown(
//       List<Map<String, dynamic>> options, bool isMultiSelect, FormInfoState state) {
//     return isMultiSelect
//         ? _buildMultiSelectDropdown(options, state)
//         : _buildSingleSelectDropdown(options, state);
//   }

//   Widget _buildSingleSelectDropdown(
//       List<Map<String, dynamic>> options, FormInfoState state) {
//     final isMasterData = widget.data.typeOption == 'masterData' ||
//         widget.data.optionType == 'masterData';
//     final isOrgchart = widget.data.typeOption == 'orgchart' ||
//         widget.data.optionType == 'orgchart';

//     // if ((isMasterData && (state.isLoading || state.mdServiceResponse == null)) ||
//     //     (isOrgchart &&
//     //         (state.isLoading ||
//     //             (widget.data.optionConfig?['orgchartType']?.toString() == 'user'
//     //                     ? state.userInfoResponse
//     //                     : state.departmentInfoResponse) ==
//     //                 null))) {
//     //   return _buildLoadingPlaceholder();
//     // }

//     final selectItems = _buildSelectItems(options, state);
//     // final defaultValue = _findDefaultValue(options, state);

//     return CustomDropdownMenu(
//       label: widget.data.displayName ?? '',
//       placeholder: widget.data.placeholder ?? 'Select an option',
//       options: selectItems,
//       onSelected: _handleSingleSelection,
//       isFilled: true,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(_kDefaultBorderRadius.r),
//         border: Border.all(color: getColorSkin().lightGray, width: 1),
//         color: widget.data.readonly == true
//             ? getColorSkin().whiteSmoke
//             : getColorSkin().white,
//       ),
//       // defaultValue: null,
//       showDeleteIcon: true,
//       isDisabled: widget.data.readonly == true,
//       dropdownHeight: _kDefaultDropdownHeight.h,
//       isRequired: widget.data.validations?['required'] == true,
//     );
//   }

//   Widget _buildLoadingPlaceholder() {
//     return Container(
//       height: _kDefaultDropdownHeight.h,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(_kDefaultBorderRadius.r),
//         border: Border.all(color: getColorSkin().lightGray, width: 1),
//         color: getColorSkin().whiteSmoke,
//       ),
//       child: const Center(child: CircularProgressIndicator()),
//     );
//   }

//   void _handleSingleSelection(SelectItem? selectedItem) {
//     if (selectedItem != null) {
//       setState(() {
//         widget.data.value = selectedItem.value;
//       });
//       widget.onChange?.call(widget.data.name ?? '', selectedItem.value);
      
//       if (widget.data.optionConfig?['orgchartType']?.toString() == 'user') {
//         _triggerDepartmentApiCall(selectedItem.value);
//       }
//     } else {
//       setState(() {
//         widget.data.value = null;
//       });
//       widget.onChange?.call(widget.data.name ?? '', null);
//     }
//   }

//   void _triggerDepartmentApiCall(String selectedValue) {
//     final formState = context.read<FormBloc>().state;
//     if (formState.formResponse != null) {
//       final departmentField =
//           formState.formResponse!.data.template.form.firstWhere(
//         (item) =>
//             item.optionConfig?['orgchartType']?.toString() == 'department',
//         orElse: () => FormItemInfo(),
//       );

//       if (departmentField.name != null) {
//         final orgchartExpressions = departmentField
//                 .optionConfig?['orgchartExpression'] as List<dynamic>? ??
//             [];

//         final conditions = orgchartExpressions.map((expression) {
//           return Condition(
//             expression: expression['condition']?.toString() ?? 'and',
//             filterType: 'user',
//             filterField: 'username',
//             operator: expression['expression']?.toString() ?? '=',
//             value: selectedValue,
//           );
//         }).toList();

//         final requestModel = IndividualInfoRequestModel(
//           orgchart: [],
//           infoType: 'user',
//           condition: conditions,
//         );

//         context.read<FormBloc>().add(GetIndividualInfoRequested(
//               requestModel: requestModel,
//               dependentValue: selectedValue,
//             ));
//       }
//     }
//   }

//   /// Map data từ response API thành các SelectItem cho dropdown
//   List<SelectItem> _buildSelectItems(
//       List<Map<String, dynamic>> options, FormInfoState state) {
//     // Xử lý cho master data type
//     if (widget.data.typeOption == 'masterData' ||
//         widget.data.optionType == 'masterData') {
//       if (state.isLoading || state.mdServiceResponse == null) {
//         return [];
//       }

//       // Map response master data thành SelectItem
//       final items = state.mdServiceResponse!.data
//           .where((mdService) => mdService.id != null)
//           .map((mdService) {
//         final displayValue = _formatDisplayValue(mdService);
//         return SelectItem(
//           label: displayValue,
//           value: mdService.id!,
//         );
//       }).toList();

//       return items;
//     } 
//     // Xử lý cho orgchart type
//     else if (widget.data.typeOption == 'orgchart' ||
//         widget.data.optionType == 'orgchart') {
//       final isUserType =
//           widget.data.optionConfig?['orgchartType']?.toString() == 'user';
//       final isDepartmentType =
//           widget.data.optionConfig?['orgchartType']?.toString() == 'department';

//       if (isUserType && (state.isLoading || state.userInfoResponse == null)) {
//         return [];
//       } else if (isDepartmentType &&
//           (state.isLoading || state.departmentInfoResponse == null)) {
//         return [];
//       }

//       // Lấy cấu hình display và value field
//       final orgchartType =
//           widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';
//       final displayFields =
//           widget.data.optionConfig?['displayField'] as List<dynamic>? ??
//               _getDefaultDisplayFields(orgchartType);
//       final valueField = widget.data.optionConfig?['valueField']?.toString() ??
//           _getDefaultValueField(orgchartType);

//       final response =
//           isUserType ? state.userInfoResponse : state.departmentInfoResponse;
//       if (response == null) return [];

//       // Map response orgchart thành SelectItem
//       final items = response.data.map((info) {
//         final displayValues = displayFields
//             .map((field) => info.toJson()[field]?.toString() ?? '')
//             .where((value) => value.isNotEmpty)
//             .toList();

//         String label;
//         String value;

//         if (orgchartType == 'department') {
//           // Xử lý đặc biệt cho department type
//           value = info.username ?? '';
//           label = displayValues.isNotEmpty
//               ? displayValues.join('_')
//               : '${info.username ?? ''}_${info.name ?? ''}_${info.position ?? ''}';
//         } else {
//           label = displayValues.isNotEmpty
//               ? displayValues.join('_')
//               : _getDefaultLabel(info, orgchartType);
//           value = info.toJson()[valueField]?.toString() ?? '';
//         }

//         return SelectItem(label: label, value: value);
//       }).toList();

//       return items;
//     } 
//     // Xử lý cho static options
//     else {
//       return options.map((option) {
//         return SelectItem(
//           label: option['label']?.toString() ?? '',
//           value: option['value']?.toString() ?? '',
//         );
//       }).toList();
//     }
//   }

//   /// Tìm và map giá trị mặc định cho dropdown
//   SelectItem? _findDefaultValue(
//       List<Map<String, dynamic>> options, FormInfoState state) {
//     if (widget.data.value == null) {
//       return null;
//     }

//     // Xử lý giá trị mặc định cho orgchart type
//     if (widget.data.typeOption == 'orgchart' ||
//         widget.data.optionType == 'orgchart') {
//       final isUserType =
//           widget.data.optionConfig?['orgchartType']?.toString() == 'user';
//       final isDepartmentType =
//           widget.data.optionConfig?['orgchartType']?.toString() == 'department';

//       final response =
//           isUserType ? state.userInfoResponse : state.departmentInfoResponse;
//       if (response == null || response.data.isEmpty) {
//         return null;
//       }

//       // Lấy cấu hình field
//       final orgchartType =
//           widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';
//       final valueField = widget.data.optionConfig?['valueField']?.toString() ??
//           _getDefaultValueField(orgchartType);
//       final displayFields =
//           widget.data.optionConfig?['displayField'] as List<dynamic>? ??
//               _getDefaultDisplayFields(orgchartType);

//       // Tìm item phù hợp trong response data
//       final matchingItems = response.data
//           .where((info) =>
//               info.toJson()[valueField]?.toString() ==
//               widget.data.value?.toString())
//           .toList();

//       if (matchingItems.isEmpty) {
//         return null;
//       }

//       final selectedInfo = matchingItems.first;
//       String label;
//       String value;

//       if (orgchartType == 'department') {
//         // Xử lý đặc biệt cho department type
//         value = selectedInfo.username ?? '';
//         final displayValues = displayFields
//             .map((field) => selectedInfo.toJson()[field]?.toString() ?? '')
//             .where((value) => value.isNotEmpty)
//             .toList();
//         label = displayValues.isNotEmpty
//             ? displayValues.join('_')
//             : '${selectedInfo.code ?? ''}_${selectedInfo.title ?? ''}_${selectedInfo.position ?? ''}';
//       } else {
//         final displayValues = displayFields
//             .map((field) => selectedInfo.toJson()[field]?.toString() ?? '')
//             .where((value) => value.isNotEmpty)
//             .toList();
//         label = displayValues.isNotEmpty
//             ? displayValues.join('_')
//             : _getDefaultLabel(selectedInfo, orgchartType);
//         value = selectedInfo.toJson()[valueField]?.toString() ?? '';
//       }

//       if (label.isEmpty || value.isEmpty) {
//         return null;
//       }

//       return SelectItem(label: label, value: value);
//     }
//     // Xử lý giá trị mặc định cho master data type
//     else if (widget.data.typeOption == 'masterData' ||
//         widget.data.optionType == 'masterData') {
//       if (state.mdServiceResponse == null || state.mdServiceResponse!.data.isEmpty) {
//         return null;
//       }

//       final matchingItems = state.mdServiceResponse!.data
//           .where((mdService) =>
//               mdService.id?.toString() == widget.data.value?.toString())
//           .toList();

//       if (matchingItems.isEmpty) {
//         return null;
//       }

//       final selectedMdService = matchingItems.first;
//       final displayValue = _formatDisplayValue(selectedMdService);
//       if (displayValue.isEmpty || selectedMdService.id == null) {
//         return null;
//       }

//       return SelectItem(
//         label: displayValue,
//         value: selectedMdService.id!,
//       );
//     }
//     // Xử lý giá trị mặc định cho static options
//     else {
//       final matchingOptions = options
//           .where((option) =>
//               option['value']?.toString() == widget.data.value?.toString())
//           .toList();

//       if (matchingOptions.isEmpty) {
//         return null;
//       }

//       final selectedOption = matchingOptions.first;
//       final label = selectedOption['label']?.toString() ?? '';
//       final value = selectedOption['value']?.toString() ?? '';

//       if (label.isEmpty || value.isEmpty) {
//         return null;
//       }

//       return SelectItem(label: label, value: value);
//     }
//   }

//   /// Builds a multi-select dropdown implemented as chips and dialog
//   Widget _buildMultiSelectDropdown(
//       List<Map<String, dynamic>> options, FormInfoState state) {
//     final selectItems = _buildSelectItems(options, state);
//     final selectedItems = selectItems.where((item) {
//       return widget.data.value != null && 
//              (widget.data.value as List).contains(item.value);
//     }).toList();

//     debugPrint('_buildMultiSelectDropdown - Selected items: ${selectedItems.map((item) => item.value).toList()}');

//     return CustomDropdownMultiForm(
//       label: widget.data.displayName ?? '',
//       placeholder: widget.data.placeholder ?? 'Select options',
//       options: selectItems,
//       selectedItems: selectedItems,
//       onSelected: (items) {
//         setState(() {
//           widget.data.value = items.map((item) => item.value).toList();
//         });
//         widget.onChange?.call(widget.data.name ?? '', widget.data.value);
//       },
//       isFilled: true,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(_kDefaultBorderRadius.r),
//         border: Border.all(color: getColorSkin().lightGray, width: 1),
//         color: widget.data.readonly == true
//             ? getColorSkin().whiteSmoke
//             : getColorSkin().white,
//       ),
//       isDisabled: widget.data.readonly == true,
//       dropdownHeight: _kDefaultDropdownHeight.h,
//       isRequired: widget.data.validations?['required'] == true,
//       errorText: widget.data.error,
//       showError: widget.data.error?.isNotEmpty == true,
//       // margin: EdgeInsets.only(top: _kDefaultSpacing.h),
//     );
//   }
// }
