import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class SearchHistory extends StatelessWidget {
  final List<String> searchHistory;
  final Function(String) onSearchSelected;
  final VoidCallback? onClearAll;

  const SearchHistory({
    super.key,
    required this.searchHistory,
    required this.onSearchSelected,
    this.onClearAll,
  });

  @override
  Widget build(BuildContext context) {
    if (searchHistory.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Tìm kiếm gần đây',
                style: getTypoSkin().bodyRegular14Bold.copyWith(
                      color: getColorSkin().ink1,
                    ),
              ),
              if (onClearAll != null)
                TextButton(
                  onPressed: onClearAll,
                  child: Text(
                    'Xóa tất cả',
                    style: getTypoSkin().medium14.copyWith(
                          color: getColorSkin().ink1,
                        ),
                  ),
                ),
            ],
          ),
          // SizedBox(height: 12.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            alignment: WrapAlignment.start,
            children: searchHistory.map((term) {
              return GestureDetector(
                onTap: () => onSearchSelected(term),
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: getColorSkin().subtitle,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    term,
                    style: getTypoSkin().labelRegular20.copyWith(
                          color: getColorSkin().secondaryText,
                        ),
                    textAlign: TextAlign.left,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
