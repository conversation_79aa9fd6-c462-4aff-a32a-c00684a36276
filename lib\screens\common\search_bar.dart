import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/svg.dart';

class CustomSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final String defaultText;
  final ValueChanged<String>? onSubmitted;
  final ValueChanged<String>? onChanged;
  final TextInputAction? textInputAction;

  const CustomSearchBar({
    Key? key,
    required this.controller,
    required this.defaultText,
    this.onSubmitted,
    this.onChanged,
    this.textInputAction,
  }) : super(key: key);

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    searchQuery = widget.controller.text;
    widget.controller.addListener(_handleControllerChange);
  }

  void _handleControllerChange() {
    if (widget.controller.text != searchQuery) {
      setState(() {
        searchQuery = widget.controller.text;
      });
      if (widget.onChanged != null) {
        widget.onChanged!(searchQuery);
      }
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleControllerChange);
    super.dispose();
  }

  void _handleClear() {
    widget.controller.clear();
    setState(() {
      searchQuery = '';
    });
    if (widget.onSubmitted != null) {
      widget.onSubmitted!('');
    }
    if (widget.onChanged != null) {
      widget.onChanged!('');
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      onTapOutside: (_) {
        FocusScope.of(context).unfocus();
      },
      cursorColor: getColorSkin().black,
      controller: widget.controller,
      onSubmitted: widget.onSubmitted,
      textInputAction: widget.textInputAction,
      style: TextStyle(
        fontSize: 14.sp,
        color: getColorSkin().black,
      ),
      decoration: InputDecoration(
        hintText: widget.defaultText,
        hintStyle: TextStyle(
          fontSize: 14.sp,
          color: getColorSkin().secondaryText,
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 12.h,
        ),
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 8.w),
          child: SvgPicture.asset(
            StringImage.ic_search,
            color: getColorSkin().secondaryText,
            width: 14.r,
            height: 14.r,
          ),
        ),
        suffixIcon: searchQuery.isNotEmpty
            ? IconButton(
                icon: Icon(
                  Icons.close,
                  color: getColorSkin().grey4Background,
                  size: 20.r,
                ),
                onPressed: _handleClear,
              )
            : null,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(20.r),
          ),
          borderSide: BorderSide(
            color: getColorSkin().grey2,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(20.r),
          ),
          borderSide: BorderSide(
            color: getColorSkin().grey2,
            width: 1,
          ),
        ),
      ),
    );
  }
}
