import 'package:equatable/equatable.dart';

abstract class NotiEvent extends Equatable {
  const NotiEvent();

  @override
  List<Object> get props => [];
}

class FetchUnreadAmount extends NotiEvent {
  @override
  String toString() => 'FetchUnreadAmount';
}

class ReadNoti extends NotiEvent {
  @override
  String toString() => 'ReadNoti';
}

class ReadAllNotification extends NotiEvent {
  @override
  String toString() => 'ReadAllNotification';
}

class ResetUnreadAmount extends NotiEvent {
  @override
  String toString() => 'ResetUnreadAmount';
}

class GetNotification extends NotiEvent {
  final int page;
  final int size;
  final List<String> systems;
  final String search;

  const GetNotification({
    this.page = 0,
    this.size = 20,
    this.systems = const ['EAPP'],
    this.search = '',
  });
}

class ReadNotification extends NotiEvent {
  final String id;

  const ReadNotification({required this.id});
}

class ShowLoadingBeforeFetch extends NotiEvent {
  @override
  String toString() => 'ShowLoadingBeforeFetch';
}
