import 'dart:convert';
import 'package:eapprove/models/user/user_info_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import 'package:eapprove/models/form/user_info_model.dart';

class UserRepository {
  final Logger _logger = LoggerConfig.logger;
  final ApiService _apiService;

  UserRepository({required ApiService apiService}) : _apiService = apiService;

  Future<UserInfoResponse> fetchUserInfo() async {
    try {
      final response = await _apiService.post(
        'api/admin-api/user-info/user-info-basic',
        {},
        customBaseUrl: 'admin',
      );

      _logger.d('Response status Get User Info: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body: $responseBody');

      if (response.statusCode != 200) {
        _logger.e('API lỗi - Mã trạng thái: ${response.statusCode}');
        if (response.statusCode == 401) {
          throw Exception(
              'Lỗi 401: Phiên đăng nhập hết hạn, vui lòng đăng nhập lại.');
        } else if (response.statusCode == 500) {
          throw Exception('Lỗi 500: Lỗi server, vui lòng thử lại sau.');
        } else {
          throw Exception('API trả về mã lỗi: ${response.statusCode}');
        }
      }

      final Map<String, dynamic> data = jsonDecode(responseBody);

      if (data.containsKey('data')) {
        return UserInfoResponse.fromJson(data);
      } else {
        throw Exception('Dữ liệu trả về không hợp lệ: $data');
      }
    } catch (e, stackTrace) {
      _logger.e('⛔ Lỗi khi fetch user info: $e',
          error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }

  /// api call to get form content/value
  /// lấy thông tin ng đệ trình
  Future<UserInfoByUserNameResponse> getUserInfoByUserName(
      {required String username}) async {
    try {
      final response = await _apiService
          .get('customer/userInfo/getByUsername?username=$username');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final UserInfoByUserNameResponse userInfoResponse =
            UserInfoByUserNameResponse.fromJson(jsonData);
        debugPrint('[FormRepository]: User Info Response: $userInfoResponse');
        return userInfoResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data - getUserInfoByUserName: $e');
    }
  }
}
