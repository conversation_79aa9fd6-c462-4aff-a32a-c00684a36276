import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';

/// Helper class to parse complex table JSON structures
class TableJsonParser {
  /// Parses a table JSON object and enhances the FormItemInfo with proper column setup
  static FormItemInfo parseTableJson(Map<String, dynamic> json) {
    // Basic parsing with FormItemInfo.fromJson
    FormItemInfo tableItem = FormItemInfo.fromJson(json);

    // Process the columns with special handling
    _enhanceTableColumns(tableItem, json);

    return tableItem;
  }

  /// Enhances the table columns with proper type information and structure
  static void _enhanceTableColumns(FormItemInfo tableItem, Map<String, dynamic> json) {
    if (json['columns'] is! List) return;

    List<dynamic> columnsJson = json['columns'];
    List<FormItemInfo> parsedColumns = [];

    for (var columnJson in columnsJson) {
      if (columnJson is! Map<String, dynamic>) continue;

      // Determine the form item type for each column
      if (!columnJson.containsKey('type') || columnJson['type'] == null || columnJson['type'] == '') {
        String inferredType = _inferColumnType(columnJson);
        columnJson['type'] = inferredType;
        debugPrint("Inferred type for column ${columnJson['name']}: $inferredType");
      }

      // Parse the column as FormItemInfo
      FormItemInfo columnItem = FormItemInfo.fromJson(columnJson);
      parsedColumns.add(columnItem);
    }

    // Sort the columns by colSortOrder
    parsedColumns.sort((a, b) => (a.colSortOrder ?? 0).compareTo(b.colSortOrder ?? 0));

    // Update the table's columns
    tableItem.columns = parsedColumns;

    debugPrint("Enhanced table with ${parsedColumns.length} columns");
  }

  /// Infers the column type based on properties and naming conventions
  static String _inferColumnType(Map<String, dynamic> columnJson) {
    // Check for select/dropdown types
    if (columnJson['name']?.toString().startsWith('slt_') == true) {
      return 'select';
    }

    if (columnJson.containsKey('option') && columnJson['option'] is List &&
        (columnJson['option'] as List).isNotEmpty) {
      return 'select';
    }

    if (columnJson.containsKey('options') && columnJson['options'] is List &&
        (columnJson['options'] as List).isNotEmpty) {
      return 'select';
    }

    // Check for currency type
    if (columnJson.containsKey('currencyType') && columnJson['currencyType'] != null) {
      return 'currency';
    }

    // Check name prefixes for different types
    String name = columnJson['name']?.toString() ?? '';

    if (name.startsWith('txt_')) {
      // Check if it's a currency field
      if (name.contains('tien') || name.contains('amount') ||
          name.contains('price') || name.contains('cost')) {
        return 'currency';
      }
      return 'text';
    }

    if (name.startsWith('txa_')) {
      return 'textarea';
    }

    if (name.startsWith('chk_')) {
      return 'checkbox';
    }

    if (name.startsWith('rad_')) {
      return 'radio';
    }

    if (name.startsWith('dat_')) {
      return 'date';
    }

    // Default to text
    return 'text';
  }

  /// Fixes the table data structure for correct rendering
  static List<Map<String, dynamic>> fixTableData(List<dynamic> tableData, List<FormItemInfo> columns) {
    List<Map<String, dynamic>> fixedData = [];

    // Process each row
    for (var rowData in tableData) {
      if (rowData is! Map) continue;

      // Create a properly structured row
      Map<String, dynamic> fixedRow = {};

      // Process each column
      for (var column in columns) {
        String key = column.name ?? '';
        if (key.isEmpty) continue;

        // Get value from the original row if available
        if (rowData.containsKey(key)) {
          fixedRow[key] = rowData[key];
        } else {
          // Set default empty value
          fixedRow[key] = '';
        }
      }

      fixedData.add(fixedRow);
    }

    return fixedData;
  }
}