class StatusTicketRequest {
  final String? type;
  final int? page;
  final int? limit;
  final String? search;
  final String? sortBy;
  final String? sortType;
  final String? chartId;
  final String? code;
  final List<String>? status;

  StatusTicketRequest({
    this.type,
    this.page,
    this.limit,
    this.search,
    this.sortBy,
    this.sortType,
    this.chartId,
    this.code,
    this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      "type": type,
      "page": page,
      "limit": limit,
      "search": search,
      "sortBy": sortBy,
      "sortType": sortType,
      "chartId": chartId,
      "code": code,
      "status": status,
    };
  }

  factory StatusTicketRequest.fromJson(Map<String, dynamic> json) {
    return StatusTicketRequest(
      type: json["type"] as String?,
      page: json["page"] as int?,
      limit: json["limit"] as int?,
      search: json["search"] as String?,
      sortBy: json["sortBy"] as String?,
      sortType: json["sortType"] as String?,
      chartId: json["chartId"] as String?,
      code: json["code"] as String?,
      status: (json["status"] as List<dynamic>?)?.cast<String>(),
    );
  }
}

class StatusContentModel {
  int? id;
  String? chartId;
  String? configInformation;
  String? serverType;
  String? configCode;
  String? configName;
  String? configValue;
  String? description;
  List<String>? applyFor;
  String? createdDate;
  String? createdUser;
  String? modifiedDate;
  String? modifiedUser;
  String? status;
  List<dynamic>? shareWith;
  String? note;
  String? companyName;
  String? companyCode;

  StatusContentModel({
    this.id,
    this.chartId,
    this.configInformation,
    this.serverType,
    this.configCode,
    this.configName,
    this.configValue,
    this.description,
    this.applyFor,
    this.createdDate,
    this.createdUser,
    this.modifiedDate,
    this.modifiedUser,
    this.status,
    this.shareWith,
    this.note,
    this.companyName,
    this.companyCode,
  });

  factory StatusContentModel.fromJson(Map<String, dynamic> json) {
    return StatusContentModel(
      id: json["id"],
      chartId: json["chartId"],
      configInformation: json["configInformation"],
      serverType: json["serverType"],
      configCode: json["configCode"],
      configName: json["configName"],
      configValue: json["configValue"],
      description: json["description"],
      applyFor: (json["applyFor"] as List?)?.cast<String>(),
      createdDate: json["createdDate"],
      createdUser: json["createdUser"],
      modifiedDate: json["modifiedDate"],
      modifiedUser: json["modifiedUser"],
      status: json["status"],
      shareWith: json["shareWith"],
      note: json["note"],
      companyName: json["companyName"],
      companyCode: json["companyCode"],
    );
  }
}