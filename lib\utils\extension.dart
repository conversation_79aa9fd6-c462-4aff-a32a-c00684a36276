import 'dart:developer' as dev;
import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

String? detectLocale(String formattedDate) {
  List<String> locales = ['en_US', 'vi_VN', 'en', 'vi'];

  // Danh sách các format thời gian phổ biến
  List<String> formats = [
    'dd/MMM/yy hh:mm a', // 04/Apr/25 04:07 PM
    'dd-MMM-yy hh:mm a', // 04-Apr-25 04:07 PM
    'dd.MMM.yy hh:mm a', // 04.Apr.25 04:07 PM
    'MMM dd, yy hh:mm a', // Apr 04, 25 04:07 PM
    'dd/MM/yyyy HH:mm', // 04/04/2025 04:07
    'yyyy-MM-dd HH:mm', // 2025-04-04 04:07
    'dd-MM-yyyy HH:mm', // 04-04-2025 04:07
  ];

  // Thử parse với mỗi format
  for (String format in formats) {
    try {
      // Thử parse với format hiện tại
      DateTime parsedDate = DateFormat(format).parse(formattedDate);

      // Nếu parse thành công, kiểm tra locale
      for (String locale in locales) {
        try {
          // Format lại ngày đã parse với locale hiện tại
          String testFormat = DateFormat(format, locale).format(parsedDate);

          // So sánh với chuỗi đầu vào
          if (testFormat.toLowerCase() == formattedDate.toLowerCase()) {
            dev.log('Detected locale: $locale for format: $format');
            return locale;
          }
        } catch (e) {
          // Bỏ qua lỗi khi format không hỗ trợ locale
        }
      }
    } catch (e) {
      // Bỏ qua lỗi khi parse không thành công
    }
  }

  // Nếu không tìm thấy locale phù hợp, trả về mặc định là en_US
  dev.log('No locale detected for: $formattedDate, using default: en_US');
  return 'en_US';
}

extension DateTimeExtension on DateTime {
  String timeAgo(BuildContext context, String? outputFormat) {
    final now = DateTime.now();
    final difference = now.difference(this);
    if (difference.inSeconds < 60 && difference.inSeconds > 0) {
      if (difference.inSeconds < 1) {
        return 'justNow';
      }
      return '${difference.inSeconds} seconds ago';
    } else if (difference.inMinutes < 60 && difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return '1 minutes ago';
      }
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours <= 24 && difference.inHours > 0) {
      final minuteToHour = (difference.inMinutes / 60).round();
      if (minuteToHour >= 1 && minuteToHour < 2) {
        return '1 hours ago';
      }
      if (minuteToHour >= 2 && minuteToHour < 3) {
        return '2 hours ago';
      }
      return '$minuteToHour hours ago';
    }

    // else if (difference.inDays <= 30) {
    //   if (difference.inDays == 1) {
    //     return '1 ${appLoc.daysAgo}';
    //   }
    //   return '${difference.inDays} ${appLoc.daysAgo}';
    // }
    dev.log("timeAgo ${this}");
    final isVN = Localizations.localeOf(context).languageCode == 'vi';
    if (isVN) {
      return DateFormat('dd/MM/yyyy HH:mm:ss').format(this);
    } else {
      return DateFormat('yyyy/MM/dd HH:mm').format(this);
    }
  }
}

extension CustomDateFormatExtension on String {
  String formatCustomDate({
    String inputFormat = 'dd/MMM/yy hh:mm a',
    String outputFormat = 'dd/MM/yyyy HH:mm',
    String defaultString = '---',
    String? locale = 'en_US',
    BuildContext? context,
    bool checkLocale = false,
  }) {
    if (isEmpty) return defaultString;
    try {
      final currentLocale = detectLocale(this);
      if (checkLocale && currentLocale != null) {
        locale = currentLocale;
      }
      final data = DateFormat(outputFormat).format(
          DateFormat(inputFormat, checkLocale ? currentLocale : locale)
              .parse(this));
      final data2 =
          DateFormat(inputFormat, checkLocale ? currentLocale : locale)
              .parse(this);
      if (context != null) {
        // dev.log("timeAgo111111 ${data2.timeAgo(context, outputFormat)}");
        return data2.timeAgo(context, outputFormat);
      } else {
        return data;
      }
    } catch (e) {
      debugPrint('Error in formatCustomDate: $e');
      return defaultString;
    }
  }
}

extension DateTimeIntExtension on String {
  String toDateTimeString() {
    try {
      dev.log('toDateTimeString11111111111: $this');
      final dateTime = DateTime.fromMillisecondsSinceEpoch(int.parse(this));
      final formatter = DateFormat('dd/MM/yyyy HH:mm:ss');
      return formatter.format(dateTime);
    } catch (e) {
      return '';
    }
  }
}
