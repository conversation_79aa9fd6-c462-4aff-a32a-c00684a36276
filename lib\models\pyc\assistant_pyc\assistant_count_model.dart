class AssisPycCountRequestModel {
  final String? assistantEmail;
  final String? searchKey;

  AssisPycCountRequestModel({
    this.assistantEmail,
    this.searchKey,
  });

  factory AssisPycCountRequestModel.fromJson(Map<String, dynamic> json) {
    return AssisPycCountRequestModel(
      assistantEmail: json['assistantEmail'],
      searchKey: json['searchKey'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'assistantEmail': assistantEmail,
      'searchKey': searchKey,
    };
  }
}

class AssisPycCountModel {
  final String? tab;
  final dynamic? total;

  AssisPycCountModel({
    this.tab,
    this.total,
  });

  factory AssisPycCountModel.fromJson(Map<String, dynamic> json) {
    return AssisPycCountModel(
      tab: json['tab'],
      total: json['total'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tab': tab,
      'total': total,
    };
  }
}
