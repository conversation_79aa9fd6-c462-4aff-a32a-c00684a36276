import 'dart:convert';
import 'dart:developer';

import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

class TicketDialogActionRepository {
  final ApiService _apiService;

  TicketDialogActionRepository({required ApiService apiService}) : _apiService = apiService;

  Future<List<dynamic>> uploadlFile(List<PlatformFile> files) async {
    final fileData = files
        .map((file) => {
              'path': file.path,
              'name': file.name,
              'mimeType': 'application/octet-stream',
            })
        .toList();

    final response = await _apiService.uploadFiles(
      'business-process/file/upload-multi?type=ticket',
      fileData,
    );

    if (response.statusCode == 200) {
      final jsonBody = json.decode(response.body);
      final data = jsonBody['data'];
      if (data != null && data is List) {
        return data;
      }
      throw Exception('Upload thành công nhưng dữ liệu trả về không hợp lệ');
    } else {
      throw Exception('Upload file thất bại: ${response.body}');
    }
  }

  Future<dynamic> draftCancel({
    required String ticketProcId,
    required String reason,
    String? filePath,
  }) async {
    final response = await _apiService.post(
      'business-process/task/cancelDraft/$ticketProcId/draft',
      {
        "variables": {
          "__cancel_reason": {"value": reason, "type": "String"},
          if (filePath != null) "__cancel_file": {"value": filePath, "type": "String"},
        },
        "businessKey": null,
        "isCancelDraft": true,
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Lưu nháp thất bại: ${response.body}');
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return jsonData['message'];
    }
  }

  Future<dynamic> submitCancel({
    required String procInstId,
    required String taskDefKey,
    required int ticketId,
    required String reason,
    required List<String> filePaths,
  }) async {
    final response = await _apiService.post(
      'business-process/cancel/$procInstId?taskDefKey=$taskDefKey',
      {
        "reason": reason,
        "ticketId": ticketId,
        "attachFiles": filePaths,
        "attachFilesName": List.filled(filePaths.length, null),
        "attachFilesSize": List.filled(filePaths.length, 0),
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Gửi hủy phiếu thất bại: ${response.body}');
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return jsonData['message'];
    }
  }

  Future<dynamic> returnDraft({
    required Map<String, dynamic> otherVariables,
    required String ticketId,
    required String reason,
    String? filePath,
  }) async {
    final response = await _apiService.post(
      'business-process/task/$ticketId/$ticketId/draft',
      {
        "variables": {
          ...otherVariables,
          "__RU_reason": {"value": reason, "type": "String"},
          if (filePath != null) "__RU_file": {"value": filePath, "type": "String"},
        },
        "businessKey": null,
        "isReturnDraft": true,
      },
    );
    if (response.statusCode != 200) {
      throw Exception('Gửi hủy phiếu thất bại: ${response.body}');
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return jsonData['message'];
    }
  }

  Future<dynamic> returnSubmit({
    required String procInstId,
    required String procDefId,
    required String taskDefKey,
    required String reason,
    required int ticketId,
    required String taskId,
    List<String>? filePaths,
    List<String>? fileNames,
    List<int>? fileSizes,
  }) async {
    final response = await _apiService.post('business-process/bpmProcInst/requestUpdate', {
      "procInstId": procInstId,
      "procDefId": procDefId,
      "ruReason": reason,
      "currTaskKey": taskDefKey,
      "ruTaskKey": "start",
      "ticketId": ticketId,
      "isStartEvent": true,
      "attachFiles": filePaths ?? [],
      "attachFilesName": fileNames ?? [],
      "attachFilesSize": fileSizes ?? [],
      "taskId": taskId
    });

    if (response.statusCode != 200) {
      throw Exception('Gửi trả phiếu thất bại: ${response.body}');
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return jsonData['message'];
    }
  }

  Future<List<Map<String, dynamic>>> getListeAuthorized(String search) async {
    final response = await _apiService.get(
      'customer/chart/getChartInfoRole?isTicketFlow=true&name=$search',
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      final List<dynamic> rawData = jsonData['data'] ?? [];
      final List<Map<String, dynamic>> rawList = rawData.map((item) => item as Map<String, dynamic>).toList();
      return rawList;
    } else {
      throw Exception('Lấy dữ liệu thất bại: ${response.body}');
    }
  }

  Future<dynamic> authorized({
    required String taskDefKey,
    required String reason,
    required String ticketId,
    required String taskId,
    required String email,
    required int id,
    required String title,
  }) async {
    final response = await _apiService.post('business-process/task/changeImplementer', {
      "taskDefKey": taskDefKey,
      "reason": reason,
      "ticketId": ticketId,
      "taskId": taskId,
      "email": email,
      "id": id,
      "title": title,
    });

    if (response.statusCode != 200) {
      throw Exception('Ủy quyền thất bại: ${response.body}');
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return jsonData['message'];
    }
  }
}
