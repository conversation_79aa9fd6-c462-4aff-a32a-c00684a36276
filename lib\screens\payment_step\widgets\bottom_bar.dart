import 'dart:developer';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/ticket_history_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_info_dialog.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_share.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/ticket_other_action/approval_list_screen.dart';
import 'package:eapprove/screens/ticket_other_action/attachments_list_screen.dart';
import 'package:eapprove/screens/ticket_other_action/consultation_opinion_screen.dart';
import 'package:eapprove/screens/ticket_other_action/download_ticket_file.dart';
import 'package:eapprove/screens/ticket_other_action/payment_screen.dart';
import 'package:eapprove/screens/ticket_other_action/request_relation_ticket_history_screen.dart';
import 'package:eapprove/screens/ticket_other_action/step_history_screen.dart';
import 'package:eapprove/screens/ticket_other_action/ticket_history_screen.dart';
import 'package:eapprove/screens/ticket_other_action/work_flow_screen.dart';
import 'package:eapprove/screens/ticket_other_action/widget/ticket_other_action_dialog.dart';
import 'package:eapprove/screens/pyc/overview_ticket_board_screen.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/attachment_list_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/assistant_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/cancel_ticket_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/recover_request_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'dart:developer' as developer;
import 'package:flutter/scheduler.dart';

class BottomBarConstants {
  // Button texts
  static const String backButton = 'Quay về';
  static const String confirmButton = 'Xác nhận';
  static const String saveDraftButton = 'Lưu nháp';
  static const String submitButton = 'Gửi';
  static const String approveButton = 'Phê duyệt';
  static const String completeButton = 'Hoàn thành';
  static const String delegateButton = 'Ủy quyền';
  static const String resubmitButton = 'Đề trình lại';

  // Menu titles
  static const String otherTasksMenu = 'Tác vụ khác';
  static const String otherActionsMenu = 'Hành động khác';
  static const String approvalTasksMenu = 'Tác vụ duyệt';
  static const String additionalInfo = 'Bổ sung thông tin';

  // Action codes
  static const String historyTicket = 'HISTORY_TICKET';
  static const String historyStep = 'HISTORY_STEP';
  static const String attachmentList = 'ATTACHMENT_LIST';
  static const String historyRelatedRequest = 'HISTORY_RELATED_REQUEST';
  static const String workflow = 'WORKFLOW';
  static const String approverList = 'APPROVER_LIST';
  static const String share = 'SHARE';
  static const String consultationOpinion = 'CONSULTATION_OPINION';
  static const String assistantOpinion = 'ASSISTANT_OPINION';
  static const String viewPaymentSchedule = 'VIEW_PAYMENT_SCHEDULE';
  static const String additionalContent = 'ADDITIONAL_CONTENT';
  static const String recoverRequest = 'RECOVER_REQUEST';
  static const String cancelTicket = 'CANCEL_TICKET';
  static const String createRelatedProposal = 'CREATE_RELATED_PROPOSAL';
  static const String additionalRequest = 'ADDITIONAL_REQUEST';
  static const String downloadTicketFile = 'DOWNLOAD_TICKET_FILE';
  static const String generalInfo = 'GENERAL_INFO';
  static const String stepDetailInfo = 'STEP_DETAIL_INFO';
  static const String create = 'CREATE';
  static const String clone = 'CLONE';
  static const String inherit = 'INHERIT';
  static const String delegate = 'DELEGATE';
  static const String approve = 'APPROVE';
  static const String perform = 'PERFORM';
  static const String returnAction = 'RETURN';

  // Action titles
  static const String historyTicketTitle = 'Lịch sử phiếu';
  static const String historyStepTitle = 'Lịch sử bước';
  static const String attachmentListTitle = 'Danh sách tệp đính kèm';
  static const String historyRelatedRequestTitle = 'Lịch sử phiếu yêu cầu liên quan';
  static const String workflowTitle = 'Xem luồng nghiệp vụ';
  static const String approverListTitle = 'Danh sách người phê duyệt';
  static const String shareTitle = 'Chia sẻ';
  static const String consultationOpinionTitle = 'Ý kiến tham vấn';
  static const String assistantOpinionTitle = 'Ý kiến trợ lý';
  static const String viewPaymentScheduleTitle = 'Xem lịch thanh toán';
  static const String additionalContentTitle = 'Nội dung bổ sung';
  static const String recoverRequestTitle = 'Yêu cầu thu hồi';
  static const String cancelTicketTitle = 'Hủy phiếu';
  static const String createRelatedProposalTitle = 'Tạo tờ trình liên quan';
  static const String additionalRequestTitle = 'Yêu cầu bổ sung';
  static const String downloadTicketFileTitle = 'Tải xuống file tờ trình';
  static const String generalInfoTitle = 'Thông tin chung';
  static const String stepDetailInfoTitle = 'Thông tin chi tiết bước';
  static const String createTitle = 'Tạo';
  static const String cloneTitle = 'Nhân bản';
  static const String inheritTitle = 'Kế thừa';
  static const String delegateTitle = 'Ủy quyền';
  static const String approveTitle = 'Phê duyệt';
  static const String performTitle = 'Thực hiện';
  static const String returnTitle = 'Trả về';

  // Success messages
  static const String createProposalSuccess = 'Tạo tờ trình thành công';
  static const String resubmitSuccess = 'Đề trình lại thành công';

  // Dimensions
  static const double buttonHeight = 40.0;
  static const double bottomBarHeight = 64.0;
  static const double iconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double borderRadius = 8.0;
  static const double padding = 16.0;
  static const double smallPadding = 8.0;
  static const double spacing = 12.0;
  static const double itemHeight = 48.0;
}

class PopupMenuItemsProvider {
  static List<CustomPopupItem> getOtherTaskItems({
    required ThongTinChungModel data,
    required String? username,
    required bool showThreeButtons,
    required String? assignee,
    required bool isHandleTicket,
  }) {
    final bool isCreator = data.ticketStartUserId == username;
    final bool isApprover = assignee == username;

    List<CustomPopupItem> items = [
      CustomPopupItem(
        title: BottomBarConstants.historyRelatedRequestTitle,
        code: BottomBarConstants.historyRelatedRequest,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyTicketTitle,
        code: BottomBarConstants.historyTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyStepTitle,
        code: BottomBarConstants.historyStep,
      ),
      CustomPopupItem(
        title: BottomBarConstants.attachmentListTitle,
        code: BottomBarConstants.attachmentList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.approverListTitle,
        code: BottomBarConstants.approverList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.workflowTitle,
        code: BottomBarConstants.workflow,
      ),
      CustomPopupItem(
        title: BottomBarConstants.assistantOpinionTitle,
        code: BottomBarConstants.assistantOpinion,
      ),
      CustomPopupItem(
        title: BottomBarConstants.viewPaymentScheduleTitle,
        code: BottomBarConstants.viewPaymentSchedule,
      ),
    ];

    // Nếu là người duyệt (bao gồm cả trường hợp vừa là người tạo vừa là người duyệt)
    if (isApprover) {
      items.add(
        CustomPopupItem(
          title: BottomBarConstants.additionalRequestTitle,
          code: BottomBarConstants.additionalRequest,
        ),
      );
    }
    // Nếu chỉ là người tạo (không phải người duyệt)
    else if (isCreator) {
      items.addAll([
        CustomPopupItem(
          title: BottomBarConstants.additionalContentTitle,
          code: BottomBarConstants.additionalContent,
        ),
        CustomPopupItem(
          title: BottomBarConstants.recoverRequestTitle,
          code: BottomBarConstants.recoverRequest,
        ),
        CustomPopupItem(
          title: BottomBarConstants.cancelTicketTitle,
          code: BottomBarConstants.cancelTicket,
        ),
      ]);

      // Add items for active tickets
      if (!showThreeButtons &&
          data.ticketStatus != TicketStatus.completed.value &&
          data.ticketStatus != TicketStatus.closed.value) {
        items.add(
          CustomPopupItem(
            title: BottomBarConstants.createRelatedProposalTitle,
            code: BottomBarConstants.createRelatedProposal,
          ),
        );
      }
    }

    // Add download option for non-handle tickets
    if ((ProcessType.handleTicket.value == 1 || !showThreeButtons) && !isHandleTicket) {
      items.add(CustomPopupItem(
        title: BottomBarConstants.downloadTicketFileTitle,
        code: BottomBarConstants.downloadTicketFile,
      ));
    }

    return items;
  }

  static List<CustomPopupItem> getHandleTicketOtherTaskItems() {
    return [
      CustomPopupItem(title: BottomBarConstants.createTitle, code: BottomBarConstants.create),
      CustomPopupItem(title: BottomBarConstants.cloneTitle, code: BottomBarConstants.clone),
      CustomPopupItem(title: BottomBarConstants.shareTitle, code: BottomBarConstants.share),
      CustomPopupItem(
        title: BottomBarConstants.historyRelatedRequestTitle,
        code: BottomBarConstants.historyRelatedRequest,
      ),
      CustomPopupItem(
        title: BottomBarConstants.createRelatedProposalTitle,
        code: BottomBarConstants.createRelatedProposal,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyTicketTitle,
        code: BottomBarConstants.historyTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyStepTitle,
        code: BottomBarConstants.historyStep,
      ),
      CustomPopupItem(
        title: BottomBarConstants.attachmentListTitle,
        code: BottomBarConstants.attachmentList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.approverListTitle,
        code: BottomBarConstants.approverList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.workflowTitle,
        code: BottomBarConstants.workflow,
      ),
      CustomPopupItem(
        title: BottomBarConstants.viewPaymentScheduleTitle,
        code: BottomBarConstants.viewPaymentSchedule,
      ),
    ];
  }

  static List<CustomPopupItem> getOtherActionItems({required bool isHandleTicket}) {
    List<CustomPopupItem> items = [
      CustomPopupItem(title: BottomBarConstants.createTitle, code: BottomBarConstants.create),
      CustomPopupItem(title: BottomBarConstants.cloneTitle, code: BottomBarConstants.clone),
      CustomPopupItem(title: BottomBarConstants.shareTitle, code: BottomBarConstants.share),
    ];

    if (!isHandleTicket) {
      items.addAll([
        CustomPopupItem(
          title: BottomBarConstants.generalInfoTitle,
          code: BottomBarConstants.generalInfo,
        ),
        CustomPopupItem(title: 'Thông tin phiếu', code: 'TICKET_INFO'),
        CustomPopupItem(
          title: BottomBarConstants.stepDetailInfoTitle,
          code: BottomBarConstants.stepDetailInfo,
        ),
        CustomPopupItem(title: 'Thông tin đầu vào', code: 'INPUT_INFO'),
      ]);
    }

    return items;
  }

  static List<CustomPopupItem> getApproveTaskItems(dynamic ticket) {
    final isInPerformPhase = ticket is ProcContent;
    return [
      CustomPopupItem(title: BottomBarConstants.inheritTitle, code: BottomBarConstants.inherit),
      CustomPopupItem(title: BottomBarConstants.delegateTitle, code: BottomBarConstants.delegate),
      CustomPopupItem(
        title: BottomBarConstants.cancelTicketTitle,
        code: BottomBarConstants.cancelTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.additionalRequestTitle,
        code: BottomBarConstants.additionalRequest,
      ),
      CustomPopupItem(title: BottomBarConstants.returnTitle, code: BottomBarConstants.returnAction),
      CustomPopupItem(
        title: BottomBarConstants.assistantOpinionTitle,
        code: BottomBarConstants.assistantOpinion,
      ),
      if (isInPerformPhase)
        CustomPopupItem(title: BottomBarConstants.performTitle, code: BottomBarConstants.perform)
      else
        CustomPopupItem(title: BottomBarConstants.approveTitle, code: BottomBarConstants.approve),
    ];
  }

  static List<CustomPopupItem> getHandleTicketCreatorItems() {
    return [
      CustomPopupItem(title: BottomBarConstants.createTitle, code: BottomBarConstants.create),
      CustomPopupItem(title: BottomBarConstants.cloneTitle, code: BottomBarConstants.clone),
      CustomPopupItem(title: BottomBarConstants.shareTitle, code: BottomBarConstants.share),
    ];
  }

  static List<CustomPopupItem> getHandleTicketCreatorOtherTaskItems() {
    return [
      CustomPopupItem(
        title: BottomBarConstants.historyRelatedRequestTitle,
        code: BottomBarConstants.historyRelatedRequest,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyTicketTitle,
        code: BottomBarConstants.historyTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyStepTitle,
        code: BottomBarConstants.historyStep,
      ),
      CustomPopupItem(
        title: BottomBarConstants.attachmentListTitle,
        code: BottomBarConstants.attachmentList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.approverListTitle,
        code: BottomBarConstants.approverList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.workflowTitle,
        code: BottomBarConstants.workflow,
      ),
      CustomPopupItem(
        title: BottomBarConstants.additionalContentTitle,
        code: BottomBarConstants.additionalContent,
      ),
      CustomPopupItem(
        title: BottomBarConstants.viewPaymentScheduleTitle,
        code: BottomBarConstants.viewPaymentSchedule,
      ),
      CustomPopupItem(
        title: BottomBarConstants.cancelTicketTitle,
        code: BottomBarConstants.cancelTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.recoverRequestTitle,
        code: BottomBarConstants.recoverRequest,
      ),
    ];
  }

  static List<CustomPopupItem> getItemsForTablet({
    required ThongTinChungModel data,
    required String? assignee,
    required String? username,
  }) {
    final bool isCreator = data.ticketStartUserId == username;
    final bool isApprover = assignee == username;

    List<CustomPopupItem> commonItems = [
      CustomPopupItem(
        title: BottomBarConstants.historyTicketTitle,
        code: BottomBarConstants.historyTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.historyStepTitle,
        code: BottomBarConstants.historyStep,
      ),
      CustomPopupItem(
        title: BottomBarConstants.attachmentListTitle,
        code: BottomBarConstants.attachmentList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.approverListTitle,
        code: BottomBarConstants.approverList,
      ),
      CustomPopupItem(
        title: BottomBarConstants.workflowTitle,
        code: BottomBarConstants.workflow,
      ),
      CustomPopupItem(
        title: BottomBarConstants.consultationOpinionTitle,
        code: BottomBarConstants.consultationOpinion,
      ),
      CustomPopupItem(
        title: BottomBarConstants.assistantOpinionTitle,
        code: BottomBarConstants.assistantOpinion,
      ),
    ];

    List<CustomPopupItem> creatorItems = [
      CustomPopupItem(
        title: BottomBarConstants.createRelatedProposalTitle,
        code: BottomBarConstants.createRelatedProposal,
      ),
      CustomPopupItem(title: BottomBarConstants.shareTitle, code: BottomBarConstants.share),
      CustomPopupItem(
        title: BottomBarConstants.cancelTicketTitle,
        code: BottomBarConstants.cancelTicket,
      ),
      CustomPopupItem(
        title: BottomBarConstants.additionalContentTitle,
        code: BottomBarConstants.additionalContent,
      ),
      CustomPopupItem(
        title: BottomBarConstants.recoverRequestTitle,
        code: BottomBarConstants.recoverRequest,
      ),
    ];

    List<CustomPopupItem> approverItems = [
      CustomPopupItem(
        title: BottomBarConstants.additionalRequestTitle,
        code: BottomBarConstants.additionalRequest,
      ),
      CustomPopupItem(
        title: BottomBarConstants.viewPaymentScheduleTitle,
        code: BottomBarConstants.viewPaymentSchedule,
      ),
      CustomPopupItem(title: BottomBarConstants.inheritTitle, code: BottomBarConstants.inherit),
    ];

    List<CustomPopupItem> items = [...commonItems];

    if (isCreator) {
      items.addAll(creatorItems);
    }

    if (isApprover) {
      items.addAll(approverItems);
    }

    return items;
  }
}

class CustomPopupItem {
  final String title;
  final String? icon;
  final Function()? onTap;
  final bool isHidden;
  final String? code;

  CustomPopupItem({
    required this.title,
    this.icon,
    this.onTap,
    this.isHidden = false,
    this.code,
  });
}

enum TicketStatus {
  recalled('RECALLED'),
  deletedByRu('DELETED_BY_RU'),
  completed('COMPLETED'),
  closed('CLOSED');

  const TicketStatus(this.value);
  final String value;
}

enum ProcessType {
  createProposal(0),
  handleTicket(1);

  const ProcessType(this.value);
  final int value;
}

class BottomBarConfig {
  final bool showThreeButtons;
  final bool isHandleTicket;
  final bool isHandleTicketCreator;
  final bool isAssignee;
  final bool shouldShowCreatorActions;
  final bool isTablet;
  final bool isTabletModalActive;

  BottomBarConfig({
    required this.showThreeButtons,
    required this.isHandleTicket,
    required this.isHandleTicketCreator,
    required this.isAssignee,
    required this.shouldShowCreatorActions,
    required this.isTablet,
    required this.isTabletModalActive,
  });
}

/// Widget tạo bottom bar đơn giản cho payment step
Widget buildBottomBar(BuildContext context) {
  return BottomAppBar(
    color: getColorSkin().white,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: _buildBackButton(context),
          ),
        ),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(left: BottomBarConstants.smallPadding.w),
            child: _buildConfirmButton(context),
          ),
        ),
      ],
    ),
  );
}

/// Widget nút quay về
Widget _buildBackButton(BuildContext context) {
  return ElevatedButton(
    style: OutlinedButton.styleFrom(
      backgroundColor: getColorSkin().white,
      side: BorderSide(color: getColorSkin().primaryBlue),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
      ),
      padding: EdgeInsets.symmetric(vertical: BottomBarConstants.spacing.h),
    ),
    onPressed: () => Navigator.of(context).pop(),
    child: Text(
      BottomBarConstants.backButton,
      style: const TextStyle(
        fontSize: 16.0,
        color: Colors.blue,
      ),
    ),
  );
}

/// Widget nút xác nhận
Widget _buildConfirmButton(BuildContext context) {
  return ElevatedButton(
    onPressed: () {
      SnackbarCore.success(BottomBarConstants.createProposalSuccess);
      Navigator.pushNamed(context, 'listTicket', arguments: {
        'filterIndex': 0,
        'tabIndex': 0,
      });
    },
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.blue,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
      ),
      padding: EdgeInsets.symmetric(vertical: BottomBarConstants.spacing.h),
    ),
    child: Text(
      BottomBarConstants.confirmButton,
      style: const TextStyle(
        fontSize: 16.0,
        color: Colors.white,
      ),
    ),
  );
}

/// Widget chính cho handle ticket bottom bar
Widget buildHandleTicketBottomBar(BuildContext context, ThongTinChungModel data, int processType,
    {required String ticketStatus,
    required dynamic ticket,
    required bool isHandleTicket,
    String? assignee,
    String? userCreator,
    ThongTinDauVaoResponseModel? dataDauVao,
    bool formPdf = false,
    bool isTabletModalActive = false,
    int currentModalTab = 0,
    List<String> modalTabNames = const [],
    VoidCallback? modalSaveDraftAction,
    VoidCallback? modalPrimaryAction,
    bool showModalSaveDraft = false,
    bool isProcessing = false,
    String nodeType = '',
    Function(String nodeId, String nodeType)? onWorkflowNodeSelected}) {
  final config = _buildBottomBarConfig(
    data: data,
    ticketStatus: ticketStatus,
    isHandleTicket: isHandleTicket,
    assignee: assignee,
    isTabletModalActive: isTabletModalActive,
  );

  // Nếu là tablet và modal đang active, hiển thị tablet modal bottom bar
  if (config.isTablet) {
    if (config.isTabletModalActive) {
      return _buildTabletModalBottomBar(
        context: context,
        data: data,
        ticket: ticket,
        processType: processType,
        dataDauVao: dataDauVao,
        ticketStatus: ticketStatus,
        currentModalTab: currentModalTab,
        modalTabNames: modalTabNames,
        modalSaveDraftAction: modalSaveDraftAction,
        modalPrimaryAction: modalPrimaryAction,
        showModalSaveDraft: showModalSaveDraft,
        isProcessing: isProcessing,
      );
    }

    // Nếu là tablet và status là RECALLED hoặc DELETED_BY_RU, hiển thị tablet bottom bar
    if (ticketStatus == TicketStatus.recalled.value || ticketStatus == TicketStatus.deletedByRu.value) {
      return _buildTabletBottomBar(
          context, config, data, ticket, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType);
    }
  }

  if (config.showThreeButtons) {
    return _buildThreeButtonsBottomBar(context, config, data, ticket, processType, onWorkflowNodeSelected);
  } else if (config.isHandleTicket) {
    return _buildHandleTicketBottomBar(context, config, data, ticket, processType, onWorkflowNodeSelected);
  } else {
    return _buildDefaultBottomBar(
        context, config, data, ticket, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType);
  }
}

/// Tạo config cho bottom bar
BottomBarConfig _buildBottomBarConfig({
  required ThongTinChungModel data,
  required String ticketStatus,
  required bool isHandleTicket,
  required String? assignee,
  required bool isTabletModalActive,
}) {
  Box box = Hive.box('authentication');
  final String? username = box.get('username', defaultValue: 'default_user');

  final bool showThreeButtons =
      ticketStatus == TicketStatus.recalled.value || ticketStatus == TicketStatus.deletedByRu.value;
  final bool isHandleTicketCreator = data.ticketStartUserId == username && isHandleTicket;
  final bool isAssignee = assignee == username;
  final bool shouldShowCreatorActions = isHandleTicketCreator && !isAssignee;
  final bool isTablet = DeviceUtils.isTablet;

  return BottomBarConfig(
    showThreeButtons: showThreeButtons,
    isHandleTicket: isHandleTicket,
    isHandleTicketCreator: isHandleTicketCreator,
    isAssignee: isAssignee,
    shouldShowCreatorActions: shouldShowCreatorActions,
    isTablet: isTablet,
    isTabletModalActive: isTabletModalActive,
  );
}

/// Bottom bar cho trường hợp có 3 nút
Widget _buildThreeButtonsBottomBar(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
) {
  return Material(
    color: getColorSkin().white,
    child: SafeArea(
      bottom: true,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.smallPadding.h,
        ),
        child: Row(
          children: [
            Expanded(
              flex: 4,
              child: _buildOtherTasksButton(context, config, data, ticket, processType, onWorkflowNodeSelected),
            ),
            Expanded(
              flex: 4,
              child: _buildApprovalTasksButton(context, config, data, ticket, processType),
            ),
            Expanded(
              flex: 1,
              child: _buildResubmitButton(context),
            ),
          ],
        ),
      ),
    ),
  );
}

class TicketActionHandler {
  static String? _getTaskDefKey(dynamic ticket) {
    return TicketUtils.getTaskDefKey(ticket);
  }

  static void handleTicketAction(
    BuildContext context,
    String action,
    dynamic ticket,
    ThongTinChungModel data,
    int processType,
    ThongTinDauVaoResponseModel? dataDauVao,
    String ticketStatus,
    Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
    String nodeType,
  ) {
    final taskDefKey = _getTaskDefKey(ticket);
    final isTablet = DeviceUtils.isTablet;
    final isInPerformPhase = ticket is ProcContent;
    final procInsId = TicketUtils.getProcInstId(ticket);
    final ticketId = TicketUtils.getCorrectTicketId(ticket);
    switch (action) {
      case BottomBarConstants.historyTicket:
        _handleHistoryTicket(context, ticket, processType, isTablet);
        break;
      case BottomBarConstants.historyStep:
        _handleHistoryStep(context, ticket, processType, taskDefKey, isTablet);
        break;
      case BottomBarConstants.attachmentList:
        _handleAttachmentList(context, ticket, data, processType, isTablet);
        break;
      case BottomBarConstants.historyRelatedRequest:
        _handleHistoryRelatedRequest(context, ticket, isTablet);
        break;
      case BottomBarConstants.workflow:
        _handleWorkflow(context, ticket, data, isTablet, onWorkflowNodeSelected, nodeType);
        break;
      case BottomBarConstants.approverList:
        _handleApproverList(context, ticket, isTablet);
        break;
      case BottomBarConstants.share:
        _handleShare(context, data, processType, isTablet);
        break;  
      case BottomBarConstants.consultationOpinion:
        _handleConsultationOpinion(context, ticket, isTablet);
        break;
      case BottomBarConstants.assistantOpinion:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.assistantOpinionTitle,
          code: BottomBarConstants.assistantOpinion,
          ticketId: ticket.id.toString(),
          taskDefKey: taskDefKey?.toString() ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.returnAction:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.returnTitle,
          code: BottomBarConstants.returnAction,
          ticketId: ticket.id.toString(),
          taskDefKey: taskDefKey?.toString() ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.inherit:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.inheritTitle,
          code: BottomBarConstants.inherit,
          ticketId: ticket.id.toString(),
          taskDefKey: taskDefKey.toString(),
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.recoverRequest:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.recoverRequestTitle,
          code: BottomBarConstants.recoverRequest,
          ticketId: ticket?.ticketId?.toString() ?? '',
          taskDefKey: taskDefKey?.toString() ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.cancelTicket:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.cancelTicketTitle,
          code: BottomBarConstants.cancelTicket,
          ticketId: ticket?.ticketId?.toString() ?? '',
          taskDefKey: taskDefKey?.toString() ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.delegate:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.delegateTitle,
          code: BottomBarConstants.delegate,
          ticketId: ticket.id.toString(),
          taskDefKey: taskDefKey ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.approve:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.approveTitle,
          code: BottomBarConstants.approve,
          ticketId: ticket.id.toString(),
          taskDefKey: taskDefKey?.toString() ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.perform:
        _showTicketBottomSheet(
          context: context,
          title: BottomBarConstants.performTitle,
          code: BottomBarConstants.perform,
          ticketId: ticket.id.toString(),
          taskDefKey: taskDefKey?.toString() ?? '',
          ticketProcId: procInsId.toString(),
          ticket: ticket,
        );
        break;
      case BottomBarConstants.viewPaymentSchedule:
        _handleViewPaymentSchedule(context);
        break;
      case BottomBarConstants.additionalContent:
        _handleAdditionalContent(context, ticket, isTablet);
        break;
      case BottomBarConstants.additionalRequest:
        _handleAdditionalRequest(context, ticket, isTablet);
        break;
      case BottomBarConstants.downloadTicketFile:
        _handleDownloadTicketFile(context, ticket, data);
        break;
      case BottomBarConstants.generalInfo:
        _showGeneralInfoDialog(context, ticket.id.toString());
        break;
      case BottomBarConstants.stepDetailInfo:
        _showStepDetailInfoDialog(context, ticket.id.toString());
        break;
      case BottomBarConstants.create:
        _handleCreate(context, data, isTablet);
        break;
      default:
        break;
    }
  }

  static void _handleHistoryTicket(
    BuildContext context,
    dynamic ticket,
    int processType,
    bool isTablet,
  ) {
    if (processType != 0) {
      // Delay setting bottom nav visibility until after navigation
      Future.delayed(const Duration(milliseconds: 100), () {
        if (context.mounted) {
          debugPrint('Setting bottom nav visibility to true');
          context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
        }
      });
    }
    _showTabletPopup(
      context,
      BlocProvider.value(
        value: BlocProvider.of<TicketHistoryBloc>(context),
        child: TicketHistoryScreen(
          ticketId: int.parse(TicketUtils.getCorrectTicketId(ticket).toString()),
          ticketProcId: TicketUtils.getTicketId(ticket).toString(),
        ),
      ),
      isTablet,
    );
  }

  static void _handleHistoryStep(
    BuildContext context,
    dynamic ticket,
    int processType,
    String? taskDefKey,
    bool isTablet,
  ) {
    if (processType != 0) {
      context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
    }
    _showTabletPopup(
      context,
      BlocProvider.value(
        value: BlocProvider.of<TicketHistoryBloc>(context),
        child: StepHistoryScreen(
          ticketId: int.parse(TicketUtils.getCorrectTicketId(ticket).toString()),
          ticketProcId: TicketUtils.getTicketId(ticket).toString(),
          taskDefKey: taskDefKey,
        ),
      ),
      isTablet,
    );
  }

  static void _handleAttachmentList(
    BuildContext context,
    dynamic ticket,
    ThongTinChungModel data,
    int processType,
    bool isTablet,
  ) {
    if (processType != 0) {
      context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
    }
    _showTabletPopup(
      context,
      BlocProvider.value(
        value: BlocProvider.of<AttachmentListBloc>(context),
        child: AttachmentsListScreen(
          ticketId: int.parse(TicketUtils.getCorrectTicketId(ticket).toString()),
          ticketStartUserId: data.ticketStartUserId ?? '0',
        ),
      ),
      isTablet,
    );
  }

  static void _handleHistoryRelatedRequest(
    BuildContext context,
    dynamic ticket,
    bool isTablet,
  ) {
    context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
    _showTabletPopup(
      context,
      BlocProvider.value(
        value: BlocProvider.of<TicketHistoryBloc>(context),
        child: RequestRelationTicketHistoryScreen(
          ticketId: ticket.id?.toString() ?? '0',
          ticketProcId: ticket.ticketId?.toString() ?? '0',
        ),
      ),
      isTablet,
    );
  }

  static void _handleWorkflow(
    BuildContext context,
    dynamic ticket,
    ThongTinChungModel data,
    bool isTablet,
    Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
    String nodeType,
  ) async {
    context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
    final ticketId = TicketUtils.getCorrectTicketId(ticket);

    final selectedNodeData = await showTabletPopupWithResult<Map<String, String>?>(
      context,
      MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<WorkflowBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
        ],
        child: WorkflowScreen(
            procDefId: ticket.ticketProcDefId ?? '',
            procInstId:
                ticket is ApproveContent || ticket is ProcContent ? ticket.procInstId ?? '' : ticket.ticketId ?? '',
            data: data,
            ticketId: ticketId.toString()),
      ),
      isTablet,
      isSmall: true,
    );
    log('📥 [BottomBarConstants] Received selectedNodeData = $selectedNodeData');

    if (selectedNodeData != null && onWorkflowNodeSelected != null) {
      final selectedNodeId = selectedNodeData['nodeId'] ?? '';
      final selectedNodeType = selectedNodeData['nodeType'] ?? '';
      final shouldPopAll = selectedNodeData['shouldPopAll'] == 'true';

      // Gọi callback trước
      onWorkflowNodeSelected(selectedNodeId, selectedNodeType);

      // Pop về HandleTicketScreen
      if (shouldPopAll) {
        if (context.mounted) {
          Navigator.popUntil(context, (route) {
            return route.settings.name == '/handle-ticket' ||
                route.isFirst ||
                route.settings.arguments?.toString().contains('HandleTicketScreen') == true;
          });
        }
      }
    } else {
      log('⚠️ [BottomBar] selectedNodeData: $selectedNodeData');
      log('⚠️ [BottomBar] onWorkflowNodeSelected != null: ${onWorkflowNodeSelected != null}');
    }
  }

  static void _handleApproverList(
    BuildContext context,
    dynamic ticket,
    bool isTablet,
  ) {
    _showTabletPopup(
      context,
      MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
        ],
        child: ApproverListScreen(ticketId: TicketUtils.getCorrectTicketId(ticket)),
      ),
      isTablet,
    );
  }

  static void _handleShare(
    BuildContext context,
    ThongTinChungModel data,
    int processType,
    bool isTablet,
  ) {
    if (processType != 0) {
      context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
    }
    _showTabletPopup(
      context,
      BlocProvider.value(
        value: BlocProvider.of<TicketProcessDetailBloc>(context),
        child: TicketShareScreen(id: data.ticketId),
      ),
      isTablet,
    );
  }

  static void _handleConsultationOpinion(
    BuildContext context,
    dynamic ticket,
    bool isTablet,
  ) {
    _showTabletPopup(
      context,
      MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<ConsultationOpinionBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
        ],
        child: ConsultationOpinionScreen(
          ticketId: int.parse(ticket.id.toString()),
          ticketProcId: ticket.ticketId?.toString() ?? '0',
        ),
      ),
      isTablet,
    );
  }

  static void _handleViewPaymentSchedule(BuildContext context) {
    context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: BlocProvider.of<TicketProcessDetailBloc>(context),
        child: Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
          child: PaymentScreen(
            title: 'Thông tin File từ API',
            code: BottomBarConstants.viewPaymentSchedule,
          ),
        ),
      ),
    );
  }

  static void _handleAdditionalContent(
    BuildContext context,
    dynamic ticket,
    bool isTablet,
  ) {
    _showTabletPopup(
      context,
      MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<ConsultationOpinionBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
        ],
        child: ConsultationOpinionScreen(
          ticketId: int.parse(ticket.id.toString()),
          ticketProcId: ticket.ticketId?.toString() ?? '0',
          isAdditionalContent: true,
        ),
      ),
      isTablet,
    );
  }

  static void _handleAdditionalRequest(
    BuildContext context,
    dynamic ticket,
    bool isTablet,
  ) {
    _showTabletPopup(
      context,
      MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<ConsultationOpinionBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
        ],
        child: ConsultationOpinionScreen(
          ticketId: int.parse(ticket.id.toString()),
          ticketProcId: ticket.ticketId?.toString() ?? '0',
          isAdditionalContent: true,
        ),
      ),
      isTablet,
    );
  }

  static void _handleDownloadTicketFile(
    BuildContext context,
    dynamic ticket,
    ThongTinChungModel data,
  ) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: BlocProvider.of<AttachmentListBloc>(context),
        child: DownloadTicketFile(
          ticketId: int.parse(ticket.id.toString() ?? '0'),
          ticketStartUserId: data.ticketStartUserId ?? '0',
        ),
      ),
    );
  }

  static void _handleCreate(
    BuildContext context,
    ThongTinChungModel data,
    bool isTablet,
  ) {
    // Điều hướng đến trang tạo tờ trình mới
    if (isTablet) {
      // Trên tablet, điều hướng đến tab dịch vụ để chọn loại tờ trình
      context.read<BottomNavBloc>().add(const NavigateToTabletTab(0));
    } else {
      log('data.ticketProcDefId: ${data.ticketProcDefId}');
      log('data.procServiceName: ${data.procServiceName}');
      log('data.ticketId: ${data.ticketId}');
      // Trên mobile, điều hướng đến màn hình tạo tờ trình
      // Navigator.pushNamed(context, '/ticket-form/${data.ticketProcDefId ?? ''}');
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => TicketFormScreen(
      //         procDefId: data.ticketProcDefId ?? '',
      //         title: data.procServiceName ?? '',
      //         ticketId: data.ticketId,
      //     ),
      //   ),
      // );
    }
  }

  static void _showTabletPopup(
    BuildContext context,
    Widget content,
    bool isTablet, {
    bool isSmall = false,
  }) {
    if (isTablet) {
      showDialog(
        context: context,
        builder: (context) => Dialog(
          backgroundColor: const Color.fromARGB(0, 228, 150, 150),
          insetPadding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
            child: SizedBox(
              width: isSmall ? MediaQuery.of(context).size.width * 0.5 : MediaQuery.of(context).size.width * 0.8,
              child: content,
            ),
          ),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => content),
      );
    }
  }

  static Future<T?> showTabletPopupWithResult<T>(
    BuildContext context,
    Widget child,
    bool isTablet, {
    bool isSmall = false,
  }) async {
    return await Navigator.push<T>(
      context,
      MaterialPageRoute(builder: (_) => child),
    );
  }

  static Future<void> _showTicketBottomSheet({
    required BuildContext context,
    required String title,
    required String code,
    required String ticketId,
    required String taskDefKey,
    required String ticketProcId,
    required dynamic ticket,
  }) async {
    final ticketBloc = BlocProvider.of<TicketProcessDetailBloc>(context);
    final dialogBloc = BlocProvider.of<TicketDialogActionBloc>(context);
    final assistantOpinionBloc = BlocProvider.of<AssistantOpinionBloc>(context);
    final cancelTicketBloc = BlocProvider.of<CancelTicketBloc>(context);
    final recoverRequestBloc = BlocProvider.of<RecoverRequestBloc>(context);
    final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
    final formBloc = BlocProvider.of<FormBloc>(context);
    final userListBloc = BlocProvider.of<UserListBloc>(context);
    final isTablet = DeviceUtils.isTablet;

    // Lưu NavigatorState trước khi mở dialog
    final navigator = Navigator.of(context);

    dynamic result;
    if (isTablet) {
      result = await _showTabletDialog(
        context,
        ticketBloc,
        dialogBloc,
        assistantOpinionBloc,
        cancelTicketBloc,
        recoverRequestBloc,
        checkTypeBloc,
        formBloc,
        userListBloc,
        title,
        code,
        ticketId,
        taskDefKey,
        ticketProcId,
        ticket,
      );
    } else {
      result = await _showMobileBottomSheet(
        context,
        ticketBloc,
        dialogBloc,
        assistantOpinionBloc,
        cancelTicketBloc,
        recoverRequestBloc,
        checkTypeBloc,
        formBloc,
        userListBloc,
        title,
        code,
        ticketId,
        taskDefKey,
        ticketProcId,
        ticket,
      );
    }

    if (result is Map && result['success'] == true) {
      if (result['shouldNavigateToOverview'] == true) {
        // Mobile navigation to overview
        SchedulerBinding.instance.addPostFrameCallback((_) {
          try {
            navigator.pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const OverviewTicketScreen(),
                settings: const RouteSettings(name: '/overview'),
              ),
              (route) => false,
            );

            developer.log('Navigation completed successfully', name: 'BottomBar');
          } catch (e) {
            developer.log('Navigation error: $e', name: 'BottomBar');
          }
        });
      } else if (result['shouldRefreshData'] == true && DeviceUtils.isTablet) {
        // Tablet: chỉ refresh data, không navigate
        SchedulerBinding.instance.addPostFrameCallback((_) {
          try {
            // Trigger refresh data cho ticket list trong tablet
            final pycBloc = context.read<PycBloc>();
            final box = Hive.box('authentication');
            final username = box.get('username', defaultValue: '');

            // Refresh all ticket counts
            pycBloc.add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
            pycBloc.add(FetchProcAppCount(
                ProcAppCountRequestModel(type: "EXECUTION", filterChangeAssignee: false, search: "")));
            pycBloc.add(
                FetchProcAppCount(ProcAppCountRequestModel(type: "APPROVAL", filterChangeAssignee: false, search: "")));
            pycBloc.add(FetchAssisPycCount(AssisPycCountRequestModel(assistantEmail: username, searchKey: "")));

            developer.log('Tablet: Refreshing data after approval success', name: 'BottomBar');
          } catch (e) {
            developer.log('Tablet refresh error: $e', name: 'BottomBar');
          }
        });
      }
    } else {
      developer.log('No navigation needed. Result: $result', name: 'BottomBar');
    }
  }

  static Future<dynamic> _showTabletDialog(
    BuildContext context,
    TicketProcessDetailBloc ticketBloc,
    TicketDialogActionBloc dialogBloc,
    AssistantOpinionBloc assistantOpinionBloc,
    CancelTicketBloc cancelTicketBloc,
    RecoverRequestBloc recoverRequestBloc,
    CheckTypeBloc checkTypeBloc,
    FormBloc formBloc,
    UserListBloc userListBloc,
    String title,
    String code,
    String ticketId,
    String taskDefKey,
    String ticketProcId,
    dynamic ticket,
  ) async {
    return await showDialog<dynamic>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.2),
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
          ),
          child: _buildBlocProviders(
            ticketBloc,
            dialogBloc,
            assistantOpinionBloc,
            cancelTicketBloc,
            recoverRequestBloc,
            checkTypeBloc,
            formBloc,
            userListBloc,
            InfoTicketOtherActionDialog(
              title: title,
              code: code,
              ticketId: ticketId,
              taskDefKey: taskDefKey,
              ticketProcId: ticketProcId,
              ticket: ticket,
            ),
          ),
        );
      },
    );
  }

  static Future<dynamic> _showMobileBottomSheet(
    BuildContext context,
    TicketProcessDetailBloc ticketBloc,
    TicketDialogActionBloc dialogBloc,
    AssistantOpinionBloc assistantOpinionBloc,
    CancelTicketBloc cancelTicketBloc,
    RecoverRequestBloc recoverRequestBloc,
    CheckTypeBloc checkTypeBloc,
    FormBloc formBloc,
    UserListBloc userListBloc,
    String title,
    String code,
    String ticketId,
    String taskDefKey,
    String ticketProcId,
    dynamic ticket,
  ) async {
    return await showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext dialogContext) {
        return _buildBlocProviders(
          ticketBloc,
          dialogBloc,
          assistantOpinionBloc,
          cancelTicketBloc,
          recoverRequestBloc,
          checkTypeBloc,
          formBloc,
          userListBloc,
          Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(dialogContext).viewInsets.bottom,
            ),
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: SafeArea(
                top: false,
                child: SingleChildScrollView(
                  child: InfoTicketOtherActionDialog(
                    title: title,
                    code: code,
                    ticketId: ticketId,
                    taskDefKey: taskDefKey,
                    ticketProcId: ticketProcId,
                    ticket: ticket,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Widget _buildBlocProviders(
    TicketProcessDetailBloc ticketBloc,
    TicketDialogActionBloc dialogBloc,
    AssistantOpinionBloc assistantOpinionBloc,
    CancelTicketBloc cancelTicketBloc,
    RecoverRequestBloc recoverRequestBloc,
    CheckTypeBloc checkTypeBloc,
    FormBloc formBloc,
    UserListBloc userListBloc,
    Widget child,
  ) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: ticketBloc),
        BlocProvider.value(value: dialogBloc),
        BlocProvider.value(value: assistantOpinionBloc),
        BlocProvider.value(value: cancelTicketBloc),
        BlocProvider.value(value: recoverRequestBloc),
        BlocProvider.value(value: checkTypeBloc),
        BlocProvider.value(value: formBloc),
        BlocProvider.value(value: userListBloc),
      ],
      child: child,
    );
  }

  static void _showGeneralInfoDialog(BuildContext context, String ticketId) {
    showDialog(
      context: context,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
        ],
        child: InfoDialog(
          title: BottomBarConstants.generalInfoTitle,
          code: BottomBarConstants.generalInfo,
          ticketId: ticketId,
        ),
      ),
    );
  }

  static void _showStepDetailInfoDialog(BuildContext context, String ticketId) {
    showDialog(
      context: context,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
        ],
        child: InfoDialog(
          title: BottomBarConstants.stepDetailInfoTitle,
          code: BottomBarConstants.stepDetailInfo,
          ticketId: ticketId,
        ),
      ),
    );
  }
}

/// Bottom bar cho handle ticket
Widget _buildHandleTicketBottomBar(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
) {
  if (processType == ProcessType.createProposal.value) {
    return _buildCreateProposalBottomBar(context, config, data, ticket, processType, onWorkflowNodeSelected);
  } else {
    return _buildNormalHandleTicketBottomBar(context, config, data, ticket, processType, onWorkflowNodeSelected);
  }
}

/// Bottom bar cho tạo đề xuất
Widget _buildCreateProposalBottomBar(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
) {
  return SafeArea(
    bottom: true,
    child: Material(
      color: getColorSkin().white,
      child: Container(
        height: BottomBarConstants.bottomBarHeight.h,
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: _buildOtherTasksButton(context, config, data, ticket, processType, onWorkflowNodeSelected),
            ),
            SizedBox(width: BottomBarConstants.spacing.w),
            Expanded(
              child: _buildApprovalTasksButton(context, config, data, ticket, processType),
            ),
          ],
        ),
      ),
    ),
  );
}

/// Bottom bar thông thường cho handle ticket
Widget _buildNormalHandleTicketBottomBar(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
) {
  return SafeArea(
    bottom: true,
    child: Material(
      color: getColorSkin().white,
      child: Container(
        height: BottomBarConstants.bottomBarHeight.h,
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: _buildOtherTasksButton(context, config, data, ticket, processType, onWorkflowNodeSelected),
            ),
            SizedBox(width: BottomBarConstants.spacing.w),
            Expanded(
              child: _buildOtherActionsButton(context, config, data, ticket, processType),
            ),
          ],
        ),
      ),
    ),
  );
}

/// Bottom bar mặc định
Widget _buildDefaultBottomBar(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  ThongTinDauVaoResponseModel? dataDauVao,
  String ticketStatus,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
  String nodeType,
) {
  if (config.isTablet) {
    return _buildTabletBottomBar(
        context, config, data, ticket, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType);
  } else {
    return _buildMobileBottomBar(
        context, config, data, ticket, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected);
  }
}

/// Bottom bar cho tablet
Widget _buildTabletBottomBar(
    BuildContext context,
    BottomBarConfig config,
    ThongTinChungModel data,
    dynamic ticket,
    int processType,
    ThongTinDauVaoResponseModel? dataDauVao,
    String ticketStatus,
    Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
    String nodeType) {
  Box box = Hive.box('authentication');
  final String? username = box.get('username', defaultValue: 'default_user');
  final String? assignee = username;

  final items = PopupMenuItemsProvider.getItemsForTablet(
    data: data,
    assignee: assignee,
    username: username,
  );

  List<Widget> allButtons = [];

  // Thêm các action buttons
  for (var item in items) {
    allButtons.add(
      InkWell(
        onTap: () => _handleItemTap(
            context, item, ticket, data, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: BottomBarConstants.padding.w,
            vertical: 9.h,
          ),
          decoration: BoxDecoration(
            color: getColorSkin().grey3Background,
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: Text(item.title, style: getTypoSkin().title5Medium),
        ),
      ),
    );
  }

  // Thêm nút đề trình lại nếu status là RECALLED hoặc DELETED_BY_RU
  if (ticketStatus == TicketStatus.recalled.value || ticketStatus == TicketStatus.deletedByRu.value) {
    allButtons.add(
      InkWell(
        onTap: () => SnackbarCore.success(BottomBarConstants.resubmitSuccess),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: BottomBarConstants.padding.w,
            vertical: 9.h,
          ),
          decoration: BoxDecoration(
            color: getColorSkin().primaryBlue,
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: Text(
            BottomBarConstants.resubmitButton,
            style: getTypoSkin().title5Medium.copyWith(color: getColorSkin().white),
          ),
        ),
      ),
    );
  }

  return SafeArea(
    bottom: true,
    child: Material(
      color: getColorSkin().white,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        child: Wrap(
          alignment: WrapAlignment.end,
          spacing: BottomBarConstants.smallPadding.w,
          runSpacing: BottomBarConstants.smallPadding.h,
          children: allButtons,
        ),
      ),
    ),
  );
}

/// Bottom bar cho mobile
Widget _buildMobileBottomBar(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  ThongTinDauVaoResponseModel? dataDauVao,
  String ticketStatus,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
) {
  return SafeArea(
    bottom: true,
    child: Material(
      color: getColorSkin().white,
      child: Container(
        height: 56.h,
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.smallPadding.h,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: _buildOtherTasksButton(context, config, data, ticket, processType, onWorkflowNodeSelected),
            ),
            Expanded(
              child: _buildOtherActionsButton(context, config, data, ticket, processType),
            ),
          ],
        ),
      ),
    ),
  );
}

/// Nút tác vụ khác
Widget _buildOtherTasksButton(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
) {
  Box box = Hive.box('authentication');
  final String? username = box.get('username', defaultValue: 'default_user');

  List<CustomPopupItem> items;
  if (config.isHandleTicketCreator) {
    items = PopupMenuItemsProvider.getHandleTicketCreatorOtherTaskItems();
  } else {
    items = PopupMenuItemsProvider.getOtherTaskItems(
      data: data,
      username: username,
      showThreeButtons: config.showThreeButtons,
      assignee: username,
      isHandleTicket: config.isHandleTicket,
    );
  }

  return Container(
    margin: EdgeInsets.symmetric(horizontal: 4.w),
    child: InkWell(
      onTap: () => _showOtherTaskBottomSheet(
        context,
        items,
        data,
        ticket,
        processType,
        sheetTitle: BottomBarConstants.otherTasksMenu,
        onWorkflowNodeSelected: onWorkflowNodeSelected,
      ),
      child: _buildMenuButton(
        BottomBarConstants.otherTasksMenu,
        isOutlined: config.showThreeButtons || !config.isHandleTicket,
      ),
    ),
  );
}

/// Nút tác vụ duyệt/hành động khác
Widget _buildApprovalTasksButton(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
) {
  List<CustomPopupItem> items;
  String buttonText;

  if (config.shouldShowCreatorActions) {
    items = PopupMenuItemsProvider.getHandleTicketCreatorItems();
    buttonText = BottomBarConstants.otherActionsMenu;
  } else {
    items = PopupMenuItemsProvider.getApproveTaskItems(ticket);
    buttonText = BottomBarConstants.approvalTasksMenu;
  }

  return Container(
    margin: EdgeInsets.symmetric(horizontal: 4.w),
    child: InkWell(
      onTap: () => _showOtherTaskBottomSheet(
        context,
        items,
        data,
        ticket,
        processType,
        sheetTitle: buttonText,
      ),
      child: _buildMenuButton(buttonText),
    ),
  );
}

/// Nút hành động khác
Widget _buildOtherActionsButton(
  BuildContext context,
  BottomBarConfig config,
  ThongTinChungModel data,
  dynamic ticket,
  int processType,
) {
  final items = PopupMenuItemsProvider.getOtherActionItems(
    isHandleTicket: config.isHandleTicket,
  );

  return Container(
    margin: EdgeInsets.only(left: 0.w),
    child: InkWell(
      onTap: () => _showOtherTaskBottomSheet(
        context,
        items,
        data,
        ticket,
        processType,
        sheetTitle: BottomBarConstants.otherActionsMenu,
      ),
      child: _buildMenuButton(BottomBarConstants.otherActionsMenu),
    ),
  );
}

/// Nút đề trình lại
Widget _buildResubmitButton(BuildContext context) {
  return Container(
    margin: EdgeInsets.only(left: 4.w, right: 4.w),
    decoration: BoxDecoration(
      color: getColorSkin().primaryBlue,
      borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
    ),
    child: InkWell(
      onTap: () => SnackbarCore.success(BottomBarConstants.resubmitSuccess),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 9.h),
        height: BottomBarConstants.buttonHeight.h,
        child: Center(
          child: SvgPicture.asset(
            StringImage.ic_file_edit,
            width: BottomBarConstants.iconSize.w,
            height: BottomBarConstants.iconSize.h,
          ),
        ),
      ),
    ),
  );
}
//Nút Bổ sung thông tin

/// Widget tạo nút menu với style
Widget _buildMenuButton(String text, {bool isOutlined = false}) {
  return Container(
    padding: EdgeInsets.symmetric(vertical: 9.h),
    height: BottomBarConstants.buttonHeight.h,
    decoration: BoxDecoration(
      color: isOutlined ? getColorSkin().white : getColorSkin().primaryBlue,
      border: isOutlined ? Border.all(color: getColorSkin().primaryBlue) : null,
      borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          text,
          style: getTypoSkin().title5Medium.copyWith(
                color: isOutlined ? getColorSkin().primaryBlue : getColorSkin().white,
              ),
        ),
        SizedBox(width: BottomBarConstants.smallPadding.w),
        SizedBox(
          width: BottomBarConstants.smallIconSize.w,
          height: BottomBarConstants.smallIconSize.h,
          child: SvgPicture.asset(
            StringImage.ic_arrow_down,
            width: BottomBarConstants.smallIconSize.w,
            height: BottomBarConstants.smallIconSize.h,
            colorFilter: ColorFilter.mode(
              isOutlined ? getColorSkin().primaryBlue : getColorSkin().white,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    ),
  );
}

/// Bottom bar cho tablet modal
Widget _buildTabletModalBottomBar({
  required BuildContext context,
  required ThongTinChungModel data,
  required dynamic ticket,
  required int processType,
  required ThongTinDauVaoResponseModel? dataDauVao,
  required String ticketStatus,
  required int currentModalTab,
  required List<String> modalTabNames,
  required VoidCallback? modalSaveDraftAction,
  required VoidCallback? modalPrimaryAction,
  required bool showModalSaveDraft,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
  bool isProcessing = false,
  String nodeType = '',
}) {
  String primaryButtonText = _getPrimaryButtonText(currentModalTab, modalTabNames);

  Box box = Hive.box('authentication');
  final String? username = box.get('username', defaultValue: 'default_user');
  final String? assignee = username;

  final otherActions = PopupMenuItemsProvider.getItemsForTablet(
    data: data,
    assignee: assignee,
    username: username,
  );

  List<Widget> allButtons = [];

  // Thêm các action buttons
  for (var item in otherActions) {
    allButtons.add(
      _buildActionButton(
        text: item.title,
        onPressed: () => _handleItemTap(
            context, item, ticket, data, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType),
        isNormalAction: true,
      ),
    );
  }

  // Thêm nút lưu nháp nếu cần và không phải ở tab Hủy phiếu
  if (showModalSaveDraft && !(modalTabNames.length == 1 && modalTabNames[0] == 'Hủy phiếu')) {
    allButtons.add(
      _buildActionButton(
        text: BottomBarConstants.saveDraftButton,
        onPressed: isProcessing ? null : modalSaveDraftAction,
        isOutlined: DeviceUtils.isTablet ? false : true,
        isLoading: false, // Lưu nháp không cần loading
      ),
    );
  }

  // Thêm nút chính
  allButtons.add(
    _buildActionButton(
      text: primaryButtonText,
      onPressed: isProcessing ? null : modalPrimaryAction,
      isPrimary: true,
      isLoading: isProcessing,
    ),
  );

  return SafeArea(
    bottom: true,
    child: Material(
      color: getColorSkin().white,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        child: Wrap(
          alignment: WrapAlignment.end,
          spacing: BottomBarConstants.smallPadding.w,
          runSpacing: BottomBarConstants.smallPadding.h,
          children: allButtons,
        ),
      ),
    ),
  );
}

/// Tạo nút action
Widget _buildActionButton({
  required String text,
  required VoidCallback? onPressed,
  bool isPrimary = false,
  bool isOutlined = false,
  bool isNormalAction = false,
  bool isLoading = false,
}) {
  if (isPrimary) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: getColorSkin().primaryBlue,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        minimumSize: Size(0, 44.h),
      ),
      child: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(getColorSkin().white),
              ),
            )
          : Text(
              text,
              style: getTypoSkin().medium14.copyWith(color: getColorSkin().white),
            ),
    );
  } else if (isOutlined) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: getColorSkin().primaryBlue),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        minimumSize: Size(0, 44.h),
      ),
      child: Text(
        text,
        style: getTypoSkin().medium14.copyWith(color: getColorSkin().primaryBlue),
      ),
    );
  } else {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: BottomBarConstants.padding.w,
          vertical: BottomBarConstants.spacing.h,
        ),
        decoration: BoxDecoration(
          color: getColorSkin().grey3Background,
          borderRadius: BorderRadius.circular(BottomBarConstants.borderRadius.r),
          border: Border.all(
            color: getColorSkin().grey4Background,
            width: 0.5,
          ),
        ),
        child: Text(
          text,
          style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
        ),
      ),
    );
  }
}

/// Lấy text cho nút chính
String _getPrimaryButtonText(int currentModalTab, List<String> modalTabNames) {
  if (modalTabNames.isEmpty) return BottomBarConstants.confirmButton;

  // If there's only one tab and it's "Hủy phiếu", return "Hủy phiếu"
  if (modalTabNames.length == 1 && modalTabNames[0] == 'Hủy phiếu') {
    return BottomBarConstants.submitButton;
  }

  final currentTabName = modalTabNames[currentModalTab];

  switch (currentModalTab) {
    case 0:
      if (currentTabName == 'Thực hiện') {
        return BottomBarConstants.completeButton;
      } else if (currentTabName == 'Hủy phiếu') {
        return BottomBarConstants.submitButton;
      } else {
        return BottomBarConstants.approveButton;
      }
    case 1:
    case 2:
      return BottomBarConstants.submitButton;
    case 3:
      return BottomBarConstants.delegateButton;
    default:
      return BottomBarConstants.confirmButton;
  }
}

/// Xử lý khi tap vào item
void _handleItemTap(
    BuildContext context,
    CustomPopupItem item,
    dynamic ticket,
    ThongTinChungModel data,
    int processType,
    ThongTinDauVaoResponseModel? dataDauVao,
    String ticketStatus,
    Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
    String nodeType) {
  if (item.onTap != null) {
    item.onTap!.call();
  } else {
    TicketActionHandler.handleTicketAction(
      context,
      item.code ?? '',
      ticket,
      data,
      processType,
      dataDauVao,
      ticketStatus,
      onWorkflowNodeSelected,
      nodeType,
    );
  }
}

/// Hiển thị bottom sheet cho các tác vụ khác
void _showOtherTaskBottomSheet(
  BuildContext context,
  List<CustomPopupItem> items,
  ThongTinChungModel data,
  dynamic ticket,
  int processType, {
  ThongTinDauVaoResponseModel? dataDauVao,
  String ticketStatus = '',
  required String sheetTitle,
  Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
  String nodeType = '',
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    constraints: BoxConstraints(
      maxHeight: MediaQuery.of(context).size.height * 0.5,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(BottomBarConstants.padding.r),
      ),
    ),
    builder: (context) {
      return Container(
        margin: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(BottomBarConstants.padding.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBottomSheetHeader(context, sheetTitle),
            CustomDivider(
              color: getColorSkin().grey4Background,
              height: 1.h,
            ),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.symmetric(vertical: BottomBarConstants.smallPadding.h),
                itemCount: items.length,
                itemBuilder: (context, index) => _buildBottomSheetMenuItem(context, items[index], ticket, data,
                    processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType),
                separatorBuilder: (context, index) => CustomDivider(
                  color: getColorSkin().grey4Background,
                  height: 1.h,
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}

/// Header cho bottom sheet
Widget _buildBottomSheetHeader(BuildContext context, String title) {
  return Container(
    padding: EdgeInsets.symmetric(
      horizontal: BottomBarConstants.padding.w,
      vertical: BottomBarConstants.spacing.h,
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          icon: Icon(
            Icons.close,
            color: getColorSkin().primaryText,
            size: BottomBarConstants.iconSize.w,
          ),
          onPressed: () => Navigator.pop(context),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
        Expanded(
          child: Center(
            child: Text(
              title,
              style: getTypoSkin().title5Medium.copyWith(
                    color: getColorSkin().primaryText,
                  ),
            ),
          ),
        ),
        SizedBox(width: 40.w),
      ],
    ),
  );
}

/// Menu item cho bottom sheet
Widget _buildBottomSheetMenuItem(
    BuildContext context,
    CustomPopupItem item,
    dynamic ticket,
    ThongTinChungModel data,
    int processType,
    ThongTinDauVaoResponseModel? dataDauVao,
    String ticketStatus,
    Function(String nodeId, String nodeType)? onWorkflowNodeSelected,
    String nodeType) {
  return InkWell(
    onTap: () {
      Navigator.pop(context);
      _handleItemTap(
          context, item, ticket, data, processType, dataDauVao, ticketStatus, onWorkflowNodeSelected, nodeType);
    },
    child: Container(
      padding: EdgeInsets.symmetric(
        horizontal: BottomBarConstants.padding.w,
        vertical: BottomBarConstants.spacing.h,
      ),
      color: getColorSkin().white,
      child: Row(
        children: [
          if (item.icon != null)
            Padding(
              padding: EdgeInsets.only(right: BottomBarConstants.spacing.w),
              child: SvgPicture.asset(
                item.icon!,
                width: 20.w,
                height: 20.h,
                colorFilter: ColorFilter.mode(
                  getColorSkin().primaryText,
                  BlendMode.srcIn,
                ),
              ),
            ),
          Expanded(
            child: Text(
              item.title,
              style: getTypoSkin().body2Regular.copyWith(
                    color: getColorSkin().primaryText,
                  ),
            ),
          ),
        ],
      ),
    ),
  );
}
