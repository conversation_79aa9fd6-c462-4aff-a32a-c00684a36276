import 'package:eapprove/models/authentication/first_time_login_model.dart';
import 'package:eapprove/models/authentication/login_model.dart';
import 'package:eapprove/models/authentication/otp_verification_model.dart';
import 'package:eapprove/models/authentication/token_model.dart';
import 'package:equatable/equatable.dart';

enum AuthenticationStatus { unknown, authenticated, unauthenticated }

abstract class AuthenticationState extends Equatable {
  const AuthenticationState();

  @override
  List<Object?> get props => [];
}

class AuthenticationInitial extends AuthenticationState {}

class AuthenticationLoading extends AuthenticationState {}

class LoginSuccess extends AuthenticationState {
  final LoginResponse loginResponse;

  const LoginSuccess(this.loginResponse);

  @override
  List<Object?> get props => [loginResponse];
}

class AuthenticationFailure extends AuthenticationState {
  final String error;
  final Map<String, String?>? fieldErrors;

  const AuthenticationFailure(this.error, [this.fieldErrors]);

  @override
  List<Object?> get props => [error, fieldErrors];
}

class SavedLoginLoaded extends AuthenticationState {
  final String username;
  final String password;
  final bool rememberMe;

  const SavedLoginLoaded({
    required this.username,
    required this.password,
    required this.rememberMe,
  });

  @override
  List<Object?> get props => [username, password, rememberMe];
}

class FirstTimeLoginRequired extends AuthenticationState {
  final String hash;
  final String username;
  final bool is2FA;
  final String? sessionState;
  final String? email;

  const FirstTimeLoginRequired(
      {required this.hash,
      required this.username,
      required this.is2FA,
      this.sessionState,
      this.email});

  @override
  List<Object?> get props => [hash, username, is2FA, sessionState, email];
}

class FirstTimeLoginSuccess extends AuthenticationState {
  final FirstTimeLoginResponse response;

  const FirstTimeLoginSuccess(this.response);

  @override
  List<Object?> get props => [response];
}

class TokenSuccess extends AuthenticationState {
  final TokenResponse tokenResponse;

  const TokenSuccess(this.tokenResponse);

  @override
  List<Object?> get props => [tokenResponse];
}

class OtpVerificationRequired extends AuthenticationState {
  final String username;
  final String sessionState;
  final String? hash;
  final String? email;
  const OtpVerificationRequired({
    required this.username,
    required this.sessionState,
    this.hash,
    this.email,
  });

  @override
  List<Object?> get props => [username, sessionState, hash, email];
}

class OtpVerificationSuccess extends AuthenticationState {
  final OtpVerifyResponse response;

  const OtpVerificationSuccess(this.response);

  @override
  List<Object> get props => [response];
}

class OtpResendSuccess extends AuthenticationState {
  const OtpResendSuccess();
}
