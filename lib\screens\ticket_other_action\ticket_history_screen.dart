import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/ticket_history_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/ticket_history_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/ticket_history_state.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/models/ticket_other_action/ticket_history_model.dart';
import 'package:eapprove/screens/common/file_viewer.dart';
import 'package:eapprove/screens/ticket_other_action/widget/attachment_item.dart';
import 'package:eapprove/screens/ticket_other_action/widget/ticket_history_item.dart';
import 'package:eapprove/widgets/custom_bottom_navigationbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/svg.dart';

class TicketHistoryScreen extends StatefulWidget {
  const TicketHistoryScreen({super.key, required this.ticketId, required this.ticketProcId});

  final int ticketId;
  final String ticketProcId;

  @override
  State<TicketHistoryScreen> createState() => _TicketHistoryScreenState();
}

class _TicketHistoryScreenState extends State<TicketHistoryScreen> {
  List<TicketHistoryModel> ticketHistoryList = [];
  final ScrollController _scrollController = ScrollController();

  void mappingTicketHistoryList(List<dynamic> content) {
    this.ticketHistoryList = content.map((e) => TicketHistoryModel.fromJson(e)).toList();
  }

  @override
  void initState() {
    super.initState();
    context.read<TicketHistoryBloc>().add(InitialTicketHistory());
    context.read<TicketHistoryBloc>().add(GetTicketHistory(procInstId: widget.ticketProcId, ticketId: widget.ticketId));

    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
    // context.read<TicketHistoryBloc>().add(InitialTicketHistory());
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      context.read<TicketHistoryBloc>().add(NextPage(procInstId: widget.ticketProcId, ticketId: widget.ticketId));
    }
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return GradientBackground(
        showBottomImage: false,
        // showUpperImage: false,
        child: Scaffold(
          backgroundColor: isTablet ? getColorSkin().white : getColorSkin().transparent,
          appBar: CustomAppBar(
            automaticallyImplyLeading: isTablet ? false : true,
            centerTitle: true,
            title: 'Lịch sử phiếu',
            titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
            textColor: getColorSkin().black,
            actionsPadding: EdgeInsets.only(right: 18.w),
            backgroundColor: isTablet ? getColorSkin().lightBlue : getColorSkin().transparent,
            actions: [
              if (isTablet)
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: SvgPicture.asset(
                    StringImage.ic_close,
                    width: 24.w,
                    height: 24.h,
                    colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
                  ),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                )
            ],
          ),
          body: BlocBuilder<TicketHistoryBloc, TicketHistoryState>(buildWhen: (previous, current) {
            return previous.status != current.status ||
                previous.response != current.response ||
                previous.errorMessage != current.errorMessage;
          }, builder: (context, state) {
            if (state.status == ServiceStatus.success && state.response != null) {
              mappingTicketHistoryList(state.response?.content ?? []);
            } else if (state.status == ServiceStatus.failure && state.errorMessage != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                SnackbarCore.error(state.errorMessage ?? '');
              });
            }

            return RefreshIndicator(
              color: getColorSkin().primaryBlue,
              onRefresh: () async {
                context
                    .read<TicketHistoryBloc>()
                    .add(ResetPage(procInstId: widget.ticketProcId, ticketId: widget.ticketId));
              },
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.w, 4.h, 16.w, 0),
                  child: Column(
                    children: [
                      if (state.status == ServiceStatus.loading)
                        AppConstraint.buildShimmer(
                            child: SizedBox(
                          height: 500.h,
                          child: ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 5,
                            itemBuilder: (context, index) {
                              return Container(
                                width: double.infinity,
                                height: 100.h,
                                margin: EdgeInsets.symmetric(vertical: 8.h),
                                decoration: BoxDecoration(
                                  color: getColorSkin().white,
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                              );
                            },
                          ),
                        ))
                      else if (ticketHistoryList.isEmpty)
                        Center(
                          child: Padding(
                            padding: EdgeInsets.only(top: 100.h),
                            child: Text(
                              'Không có dữ liệu lịch sử',
                              style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                            ),
                          ),
                        )
                      else
                        DeviceUtils.isTablet
                            ? SizedBox(
                                height: MediaQuery.of(context).size.height - 200.h,
                                child: Column(
                                  children: [
                                    Container(
                                      height: 40.h,
                                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8.r),
                                        color: getColorSkin().ink5,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                              flex: 2,
                                              child: Text(
                                                'Người thực hiện',
                                                style: getTypoSkin().medium14.copyWith(
                                                      color: getColorSkin().ink1,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                              )),
                                          Expanded(
                                              flex: 2,
                                              child: Text(
                                                'Hành động',
                                                style: getTypoSkin().medium14.copyWith(
                                                      color: getColorSkin().ink1,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                              )),
                                          Expanded(
                                              flex: 2,
                                              child: Text(
                                                'Thời gian',
                                                style: getTypoSkin().medium14.copyWith(
                                                      color: getColorSkin().ink1,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                              )),
                                          Expanded(
                                              flex: 3,
                                              child: Text(
                                                'Nội dung',
                                                style: getTypoSkin().medium14.copyWith(
                                                      color: getColorSkin().ink1,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                              )),
                                          Expanded(
                                              flex: 2,
                                              child: Text(
                                                'Chứng từ đính kèm',
                                                style: getTypoSkin().medium14.copyWith(
                                                      color: getColorSkin().ink1,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                              )),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: ListView.builder(
                                        itemCount: ticketHistoryList.length,
                                        itemBuilder: (context, index) {
                                          final item = ticketHistoryList[index];
                                          final attachments = item.attachments?.map((e) => e.name).toList() ?? [];
                                          return Container(
                                            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                                            decoration: BoxDecoration(
                                              border: Border(
                                                bottom: BorderSide(
                                                  color: getColorSkin().grey4Background,
                                                  width: 1,
                                                ),
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                    flex: 2,
                                                    child: Text(
                                                      item.username,
                                                      style: getTypoSkin().medium14.copyWith(
                                                            color: getColorSkin().ink1,
                                                            fontWeight: FontWeight.w400,
                                                          ),
                                                    )),
                                                Expanded(
                                                    flex: 2,
                                                    child: Text(
                                                      item.action,
                                                      style: getTypoSkin().medium14.copyWith(
                                                            color: getColorSkin().ink1,
                                                            fontWeight: FontWeight.w400,
                                                          ),
                                                    )),
                                                Expanded(
                                                    flex: 2,
                                                    child: Text(
                                                      item.date,
                                                      style: getTypoSkin().medium14.copyWith(
                                                            color: getColorSkin().ink1,
                                                            fontWeight: FontWeight.w400,
                                                          ),
                                                    )),
                                                Expanded(
                                                    flex: 3,
                                                    child: Text(
                                                      item.description ?? '',
                                                      style: getTypoSkin().medium14.copyWith(
                                                            color: getColorSkin().ink1,
                                                            fontWeight: FontWeight.w400,
                                                          ),
                                                    )),
                                                Expanded(
                                                  flex: 2,
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: item.attachments?.map((file) {
                                                          return AttachmentItem(
                                                            document: file,
                                                            onView: (document) {
                                                              context
                                                                  .read<BottomNavBloc>()
                                                                  .add(const SetBottomNavVisibility(false));
                                                              Navigator.push(
                                                                context,
                                                                MaterialPageRoute(
                                                                  builder: (context) => FileViewer(
                                                                    item: document,
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                          );
                                                        }).toList() ??
                                                        [Text('')],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              )
                            : ListView.separated(
                                physics: NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: ticketHistoryList.length,
                                separatorBuilder: (context, index) => SizedBox(height: 16.h),
                                itemBuilder: (context, index) {
                                  return TicketHistoryItem(
                                    ticketHistory: ticketHistoryList[index],
                                  );
                                },
                              ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ));
  }
}
