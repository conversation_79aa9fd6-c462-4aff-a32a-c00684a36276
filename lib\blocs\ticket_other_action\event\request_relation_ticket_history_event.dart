import 'package:equatable/equatable.dart';

abstract class RequestRelationTicketHistoryEvent extends Equatable {
  const RequestRelationTicketHistoryEvent();

  @override
  List<Object?> get props => [];
}

class GetRequestRelationTicketHistory
    extends RequestRelationTicketHistoryEvent {
  final int ticketId;

  const GetRequestRelationTicketHistory({
    required this.ticketId,
  });

  @override
  List<Object?> get props => [ticketId];
}
