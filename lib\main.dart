import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_bloc.dart';
import 'package:eapprove/blocs/authorize_management/data_filter/filter_data_authorize_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/feature/feature_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/notification/notification_bloc.dart';
import 'package:eapprove/blocs/notification/notification_event.dart';
import 'package:eapprove/blocs/overlay/overlay_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_input/ticket_input_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/assistant_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/attachment_list_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/cancel_ticket_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/recover_request_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/ticket_history_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/repositories/account_repository.dart';
import 'package:eapprove/repositories/authentication_repository.dart';
import 'package:eapprove/repositories/authorize_management_repository.dart';
import 'package:eapprove/repositories/feature_repository.dart';
import 'package:eapprove/repositories/filter_data_authorize_repository.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:eapprove/repositories/list_user_info_repository.dart';
import 'package:eapprove/repositories/notification_repository.dart';
import 'package:eapprove/repositories/pyc_repository.dart';
import 'package:eapprove/repositories/service_repository.dart';
import 'package:eapprove/repositories/sign_print_repository.dart';
import 'package:eapprove/repositories/switch_account_repository.dart';
import 'package:eapprove/repositories/ticket_dialog_action_repository.dart';
import 'package:eapprove/repositories/ticket_input_repository.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:eapprove/repositories/user_repository.dart';
import 'package:eapprove/routers/mapping_router.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/services/token.dart';
import 'package:eapprove/widgets/overlay_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_color.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_sdk/widgets/custom_text_style.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'blocs/service/service_bloc.dart';
import 'blocs/sign_print/sign_print_bloc.dart';
import 'repositories/ticket_process_detail_repository.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/repositories/chart_repository.dart';
import 'package:provider/provider.dart' as provider_pkg;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/repositories/business_process_repository.dart';
import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
// import 'package:eapprove/blocs/theme/app_theme_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';

GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

NavigatorState get navigator => navigatorKey.currentState!;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Hive.initFlutter();
  await Hive.openBox("authentication");
  await Hive.openBox("user_box");
  await Hive.openBox("setting");

  // Box box = Hive.box('authentication'); 

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
  ));
  final tokenManager = TokenManager();
  // SystemChrome.setEnabledSystemUIMode(
  //   SystemUiMode.edgeToEdge,
  //   overlays: [SystemUiOverlay.top],
  // );
  final size = WidgetsBinding.instance.window.physicalSize;
  final pixelRatio = WidgetsBinding.instance.window.devicePixelRatio;
  final width = size.width / pixelRatio;
  final height = size.height / pixelRatio;
  final shortestSide = width < height ? width : height;

  if (shortestSide >= 550) {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  } else {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
  

  tokenManager.tokenStatus.listen((status) {
    if (status == TokenStatus.expired) {
      developer.log('Token expired');
    }
  });

  final interceptedClient = InterceptedClient.build(
    interceptors: [
      AuthorizationInterceptor(
        tokenManager: tokenManager,
        navigatorKey: navigatorKey,
      ),
    ],
  );

  final apiService = ApiService(
    httpClient: interceptedClient,
    tokenManager: tokenManager,
  );

  utf8.encoder;
  HttpOverrides.global = MyHttpOverrides();
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
  ));

  final authenticationRepository = AuthenticationRepository(apiService: apiService);
  final businessProcessRepository = BusinessProcessRepository(apiService: apiService);
  final accountRepository = AccountRepository(apiService: apiService);
  final serviceRepository = ServiceRepository(apiService: apiService);
  final formRepository = FormRepository(apiService: apiService);
  final switchAccountRepository = SwitchAccountRepository(apiService: apiService);
  final pycRepository = PycRepository(apiService: apiService);
  final filterDataAuthorizeRepository = FilterDataAuthorizeRepository(apiService: apiService);
  final userInfoRepository = UserInfoRepository(apiService);
  final ticketProcessDetailRepository = TicketProcessDetailRepository(apiService: apiService);
  final chartRepository = ChartRepository(apiService);
  final ticketOtherActionRepository = TicketOtherActionRepository(apiService: apiService);
  final ticketDialogActionRepository = TicketDialogActionRepository(apiService: apiService);
  final authorizeManagementRepository = AuthorizeManagementRepository(apiService: apiService);
  final userRepository = UserRepository(apiService: apiService);
  final notiRepository = NotiRepository(apiService: apiService);
  final featureRepository = FeatureRepository(apiService: apiService);
  final ticketInputRepository = TicketInputRepository(apiService: apiService);  final signPrintRepository = SignPrintRepository(apiService: apiService);

  final authenticationBloc = AuthenticationBloc(
    authenticationRepository: authenticationRepository,
    tokenManager: tokenManager,
  );

  authenticationBloc.add(CheckAuthenticationStatus());
  if (DeviceUtils.isTablet) {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.top],
    );
  }
  runApp(
    ProviderScope(
      child: MultiBlocProvider(
        providers: [
          BlocProvider<AuthenticationBloc>.value(
            value: authenticationBloc,
          ),
          BlocProvider<ServiceBloc>(
            create: (_) => ServiceBloc(serviceRepository, accountRepository),
          ),
          BlocProvider<FormBloc>(
            create: (_) => FormBloc(
              formRepository: formRepository,
              accountRepository: accountRepository,
            ),
          ),
          BlocProvider<UserListBloc>(
            create: (_) => UserListBloc(accountRepository: accountRepository),
          ),
          BlocProvider<PycBloc>(
            create: (_) => PycBloc(pycRepository),
          ),
          BlocProvider<FilterDataAuthorizeBloc>(
            create: (_) => FilterDataAuthorizeBloc(filterDataAuthorizeRepository),
          ),
          BlocProvider<UserInfoBloc>(
            create: (_) => UserInfoBloc(userInfoRepository),
          ),
          BlocProvider<AuthorizeManagementBloc>(
            create: (_) => AuthorizeManagementBloc(authorizeManagementRepository),
          ),
          BlocProvider<UserBloc>(
            create: (_) => UserBloc(userRepository: userRepository),
          ),
          BlocProvider<TicketProcessDetailBloc>(
            create: (_) => TicketProcessDetailBloc(ticketProcessDetailRepository, formRepository),
          ),
          BlocProvider<ChartBloc>(
            create: (_) => ChartBloc(chartRepository: chartRepository),
          ),
          BlocProvider<NotiBloc>(
            create: (_) => NotiBloc(notiRepository: notiRepository),
          ),
          BlocProvider<DropdownBloc>(
            create: (_) => DropdownBloc(formRepository: formRepository),
          ),
          BlocProvider<TicketHistoryBloc>(
            create: (_) => TicketHistoryBloc(ticketOtherActionRepository: ticketOtherActionRepository),
          ),
          BlocProvider<CheckTypeBloc>(
            create: (_) => CheckTypeBloc(repository: businessProcessRepository),
          ),
          BlocProvider<PrintFileTemplateBloc>(
            create: (_) => PrintFileTemplateBloc(repository: businessProcessRepository),
          ),
          BlocProvider<AttachmentListBloc>(
            create: (_) => AttachmentListBloc(ticketOtherActionRepository: ticketOtherActionRepository),
          ),
          BlocProvider<ConsultationOpinionBloc>(
            create: (_) => ConsultationOpinionBloc(repository: ticketOtherActionRepository),
          ),
          BlocProvider<CancelTicketBloc>(
            create: (_) => CancelTicketBloc(repository: ticketOtherActionRepository),
          ),
          BlocProvider<RecoverRequestBloc>(
            create: (_) => RecoverRequestBloc(repository: ticketOtherActionRepository),
          ),
          BlocProvider<AssistantOpinionBloc>(
            
            create: (_) => AssistantOpinionBloc(repository: ticketOtherActionRepository),
          ),
          BlocProvider<BpmProcInstBloc>(
            create: (_) => BpmProcInstBloc(repository: formRepository),
          ),
          BlocProvider<FeatureBloc>(
            create: (_) => FeatureBloc(repository: featureRepository),
          ),
          // BlocProvider<AppThemeBloc>(
          //   create: (_) => AppThemeBloc(),
          // ),
          BlocProvider<TicketInputBloc>(
            create: (_) => TicketInputBloc(
              repository: ticketInputRepository,
            ),
          ),
          BlocProvider<BottomNavBloc>(
            create: (_) => BottomNavBloc(),
          ),
          BlocProvider<SignPrintBloc>(
            create: (_) => SignPrintBloc(repository: signPrintRepository),
          ),
          BlocProvider<WorkflowBloc>(
            create: (_) => WorkflowBloc(repository: ticketOtherActionRepository),
          ),
          BlocProvider(
            create: (_) {
              debugPrint('Initializing OverlayBloc');
              return OverlayBloc();
            },
          ),
          BlocProvider<TicketDialogActionBloc>(
            create: (_) => TicketDialogActionBloc(repository: ticketDialogActionRepository),
          ),
          provider_pkg.Provider<SwitchAccountRepository>(
            create: (_) => switchAccountRepository,
          ),
        ],
        child: const MyApp(),
      ),
    ),
  );
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..connectionTimeout = const Duration(seconds: 20)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

class AuthorizationInterceptor implements InterceptorContract {
  final TokenManager tokenManager;
  final GlobalKey<NavigatorState> navigatorKey;

  AuthorizationInterceptor({
    required this.tokenManager,
    required this.navigatorKey,
  });

  @override
  Future<BaseRequest> interceptRequest({required BaseRequest request}) async {
    // Check for internet connectivity
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      if (navigatorKey.currentContext != null) {
        SnackbarCore.error('Vui Lòng Kiểm Tra Kết Nối Internet');
      }
      throw NoInternetException('No internet connection');
    }

    final token = await tokenManager.getFormattedToken();
    if (token != null && token.isNotEmpty) {
      request.headers['Authorization'] = token;
    }

    return request;
  }

  @override
  Future<BaseResponse> interceptResponse({required BaseResponse response}) async {
    if (response.statusCode == 401 && navigatorKey.currentContext != null) {
      try {
        final authBloc = BlocProvider.of<AuthenticationBloc>(navigatorKey.currentContext!);
        authBloc.add(const RefreshTokenRequested());
      } catch (e) {
        await tokenManager.clearTokenData();
        SnackbarCore.error('Phiên đăng nhập đã hết hạn, vui lòng đăng nhập lại');
      }
    }
    return response;
  }

  @override
  Future<bool> shouldInterceptRequest() async => true;

  @override
  Future<bool> shouldInterceptResponse() async => true;
}

// Custom exception for no internet connection
class NoInternetException implements Exception {
  final String message;

  NoInternetException(this.message);

  @override
  String toString() => message;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (_, constraints) {
        final designSize = constraints.maxWidth >= 550
            ? const Size(1194, 834) // tablet
            : const Size(375, 764); // điện thoại
        return ScreenUtilInit(
          designSize: designSize,
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return const MyAppView();
          },
        );
      },
    );
  }
}

class MyAppView extends StatelessWidget {
  const MyAppView({super.key});

  @override
  Widget build(BuildContext context) {
    final authBloc = context.read<AuthenticationBloc>();
    return provider_pkg.ChangeNotifierProvider(
      create: (_) => AppThemeProvider(),
      child: ThemeInitializer(
        child: MaterialApp.router(
          debugShowCheckedModeBanner: false,
          title: 'E-Approve',
          routerConfig: createAppRouter(authBloc),
          locale: const Locale('vi', 'VN'),
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('vi', 'VN'),
          ],
          builder: (context, child) {
            return Stack(
              children: [
                BotToastInit()(context, child),
                if (!DeviceUtils.isTablet) const OverlayWidget(),
              ],
            );
          },
          theme: ThemeData(
            extensions: [
              CustomTextStyle.defaultInstance(),
              CustomColor.defaultInstance(),
            ],
          ),
        ),
      ),
    );
  }
}
