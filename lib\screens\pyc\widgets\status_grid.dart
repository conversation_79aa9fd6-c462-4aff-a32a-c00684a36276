import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart'; // Add this import for PycState
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/screens/pyc/widgets/status_card.dart';
import 'package:eapprove/screens/pyc/widgets/status_card_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shimmer/shimmer.dart';

class StatusGrid extends StatelessWidget {
  final List<RequestStatusCardModel> cards;
  final int filterIndex;
  const StatusGrid({
    super.key,
    required this.cards,
    required this.filterIndex,
  });

  @override
  Widget build(BuildContext context) {
    Box box = Hive.box('authentication');
    final username = box.get('username', defaultValue: '');
    return BlocBuilder<PycBloc, PycState>(
      builder: (context, state) {
        bool isLoading = state.myPycCountStatus == PycStatus.loading ||
            state.procAppCountStatus == PycStatus.loading ||
            state.assisPycCountStatus == PycStatus.loading;

        return LayoutBuilder(
          builder: (context, constraints) {
            return RefreshIndicator(
              backgroundColor: getColorSkin().white,
              color: getColorSkin().primaryBlue,
              onRefresh: () async {
                final bloc = context.read<PycBloc>();
                bloc.add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
                bloc.add(FetchProcAppCount(
                    ProcAppCountRequestModel(type: "EXECUTION", filterChangeAssignee: false, search: "")));
                bloc.add(FetchProcAppCount(
                    ProcAppCountRequestModel(type: "APPROVAL", filterChangeAssignee: false, search: "")));
                bloc.add(FetchAssisPycCount(AssisPycCountRequestModel(assistantEmail: username, searchKey: "")));
              },
              child: isLoading
                  ? _buildShimmerGrid()
                  : GridView.builder(
                      shrinkWrap: true,
                      itemCount: cards.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 2.0,
                        crossAxisSpacing: 8.w,
                        mainAxisSpacing: 8.h,
                      ),
                      itemBuilder: (context, index) {
                        return StatusCard(
                          card: cards[index],
                          onTap: () async {
                            debugPrint('Tapped on card $index, filterIndex: $filterIndex');
                            final result = await Navigator.pushNamed(
                              context,
                              'listTicket',
                              arguments: {
                                'filterIndex': filterIndex,
                                'tabIndex': index,
                              },
                            );

                            if (result == true) {
                              final bloc = context.read<PycBloc>();
                              bloc.add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
                              bloc.add(FetchProcAppCount(ProcAppCountRequestModel(
                                  type: "EXECUTION", filterChangeAssignee: false, search: "")));
                              bloc.add(FetchProcAppCount(
                                  ProcAppCountRequestModel(type: "APPROVAL", filterChangeAssignee: false, search: "")));
                              bloc.add(FetchAssisPycCount(
                                  AssisPycCountRequestModel(assistantEmail: username, searchKey: "")));
                            }
                          },
                        );
                      },
                    ),
            );
          },
        );
      },
    );
  }

  Widget _buildShimmerGrid() {
    return GridView.builder(
      shrinkWrap: true,
      itemCount: 8,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.0,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 8.h,
      ),
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 100.w,
                  height: 20.h,
                  color: Colors.grey[300],
                ),
                SizedBox(height: 8.h),
                Container(
                  width: 50.w,
                  height: 20.h,
                  color: Colors.grey[300],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
