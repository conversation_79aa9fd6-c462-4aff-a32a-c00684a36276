import 'dart:convert';

class AuthorizeConditionResponse {
  final String tableName;
  final List<ColumnInfo> columns;
  final List<ConditionData> data;

  AuthorizeConditionResponse({
    required this.tableName,
    required this.columns,
    required this.data,
  });

  factory AuthorizeConditionResponse.fromJson(String jsonStr) {
    final Map<String, dynamic> json = jsonDecode(jsonStr);
    return AuthorizeConditionResponse(
      tableName: json['tableName'] ?? '',
      columns: (json['columns'] as List<dynamic>?)
              ?.map((e) => ColumnInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => ConditionData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

class ColumnInfo {
  final String name;
  final String label;

  ColumnInfo({
    required this.name,
    required this.label,
  });

  factory ColumnInfo.fromJson(Map<String, dynamic> json) {
    return ColumnInfo(
      name: json['name'] ?? '',
      label: json['label'] ?? '',
    );
  }
}

class ConditionData {
  final int stt;
  final List<OptionItem> sltDieuKienSoSanhOpt;
  final List<OptionItem> sltTenDieuKienUyQuyenOpt;
  final List<DieuKienUyQuyenOption> sltDieuKienUyQuyenOpt;
  final String sltDieuKienUyQuyenText;
  final String sltDieuKienUyQuyen;
  final String sltDieuKienSoSanhText;
  final String sltDieuKienSoSanh;
  final String txtGiaTriUyQuyen;

  ConditionData({
    required this.stt,
    required this.sltDieuKienSoSanhOpt,
    required this.sltTenDieuKienUyQuyenOpt,
    required this.sltDieuKienUyQuyenOpt,
    required this.sltDieuKienUyQuyenText,
    required this.sltDieuKienUyQuyen,
    required this.sltDieuKienSoSanhText,
    required this.sltDieuKienSoSanh,
    required this.txtGiaTriUyQuyen,
  });

  factory ConditionData.fromJson(Map<String, dynamic> json) {
    return ConditionData(
      stt: json['stt'] ?? 0,
      sltDieuKienSoSanhOpt: (json['slt_dieuKienSoSanh_opt'] as List<dynamic>?)
              ?.map((e) => OptionItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      sltTenDieuKienUyQuyenOpt: (json['slt_tenDieuKienUyQuyen_opt'] as List<dynamic>?)
              ?.map((e) => OptionItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      sltDieuKienUyQuyenOpt: (json['slt_dieuKienUyQuyen_opt'] as List<dynamic>?)
              ?.map((e) => DieuKienUyQuyenOption.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      sltDieuKienUyQuyenText: json['slt_dieuKienUyQuyen_text'] ?? '',
      sltDieuKienUyQuyen: json['slt_dieuKienUyQuyen'] ?? '',
      sltDieuKienSoSanhText: json['slt_dieuKienSoSanh_text'] ?? '',
      sltDieuKienSoSanh: json['slt_dieuKienSoSanh'] ?? '',
      txtGiaTriUyQuyen: json['txt_giaTriUyQuyen'] ?? '',
    );
  }
}

class OptionItem {
  final String label;
  final String value;

  OptionItem({
    required this.label,
    required this.value,
  });

  factory OptionItem.fromJson(Map<String, dynamic> json) {
    return OptionItem(
      label: json['label'] ?? '',
      value: json['value'] ?? '',
    );
  }
}

class DieuKienUyQuyenOption {
  final String value;
  final String label;
  final FieldItemOptions options;

  DieuKienUyQuyenOption({
    required this.value,
    required this.label,
    required this.options,
  });

  factory DieuKienUyQuyenOption.fromJson(Map<String, dynamic> json) {
    return DieuKienUyQuyenOption(
      value: json['value'] ?? '',
      label: json['label'] ?? '',
      options: FieldItemOptions.fromJson(json['options'] as Map<String, dynamic>? ?? {}),
    );
  }
}

class FieldItemOptions {
  final String id;
  final String name;
  final String label;
  final bool display;
  final bool readonly;
  final String type;
  final int fieldSortOrder;

  FieldItemOptions({
    required this.id,
    required this.name,
    required this.label,
    required this.display,
    required this.readonly,
    required this.type,
    required this.fieldSortOrder,
  });

  factory FieldItemOptions.fromJson(Map<String, dynamic> json) {
    return FieldItemOptions(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      label: json['label'] ?? '',
      display: json['display'] ?? true,
      readonly: json['readonly'] ?? false,
      type: json['type'] ?? '',
      fieldSortOrder: json['fieldSortOrder'] ?? 0,
    );
  }
} 