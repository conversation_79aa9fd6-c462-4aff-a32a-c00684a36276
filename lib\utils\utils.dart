import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Utils {
  /// Chuyển đổi String thành DateTime
  static DateTime dateTimeConverter(String date) {
    return DateTime.tryParse(date) ?? DateTime(1970, 1, 1);
  }

  /// Lưu token vào SharedPreferences
  static Future<void> saveStorage(String key, String token) async {
    final prefs = await SharedPreferences.getInstance();
    print("saveStorage - token::: ${token}");
    await prefs.setString(key, token);
  }

  /// Lấy token từ SharedPreferences
  static Future<String?> getValueStorage(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  /// Xóa một giá trị trong SharedPreferences
  static Future<void> removeStorage(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  /// <PERSON><PERSON>a toàn bộ dữ liệu trong SharedPreferences
  static Future<void> clearStorage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  ///chuyển ms thành datetime
  static String formatDate(int? timestamp) {
    if (timestamp == null) return "Không có ngày";
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat("dd-MM-yyyy").format(date);
  }

  /// Format số tiền thành định dạng tiền tệ
  static String formatCurrency(double? amount, {String locale = 'vi_VN'}) {
    if (amount == null) return "0";
    final formatter = NumberFormat("#,##0", locale);
    String formatted = formatter.format(amount);
    return formatted.replaceAll('.', ',');
  }

  /// Đọc số thành chữ tiếng Việt
  static String readNumber(double? number) {
    if (number == null || number == 0) return "Không đồng";

    final units = ['', 'nghìn', 'triệu', 'tỷ', 'nghìn tỷ', 'triệu tỷ', 'tỷ tỷ', 'nghìn tỷ tỷ', 'triệu tỷ tỷ'];
    final digits = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
    final tens = [
      '',
      'mười',
      'hai mươi',
      'ba mươi',
      'bốn mươi',
      'năm mươi',
      'sáu mươi',
      'bảy mươi',
      'tám mươi',
      'chín mươi'
    ];
    final hundreds = [
      '',
      'một trăm',
      'hai trăm',
      'ba trăm',
      'bốn trăm',
      'năm trăm',
      'sáu trăm',
      'bảy trăm',
      'tám trăm',
      'chín trăm'
    ];

    String result = '';
    int unitIndex = 0;
    int num = number.toInt();

    if (num == 0) return 'Không đồng';

    while (num > 0) {
      int group = num % 1000;
      num = num ~/ 1000;

      if (group > 0) {
        String groupText = '';

        // Hàng trăm
        int hundred = group ~/ 100;
        if (hundred > 0) {
          groupText += hundreds[hundred] + ' ';
        }

        // Hàng chục và đơn vị
        int remainder = group % 100;
        if (remainder > 0) {
          int ten = remainder ~/ 10;
          int digit = remainder % 10;

          if (ten > 0) {
            groupText += tens[ten] + ' ';
            if (digit > 0) {
              if (digit == 1 && ten == 1) {
                groupText += 'một ';
              } else if (digit == 5 && ten > 0) {
                groupText += 'lăm ';
              } else {
                groupText += digits[digit] + ' ';
              }
            }
          } else {
            groupText += digits[digit] + ' ';
          }
        }

        if (unitIndex < units.length) {
          groupText += units[unitIndex] + ' ';
        } else {
          // Nếu vượt quá giới hạn của mảng units, thêm "tỷ" vào cuối
          groupText += 'tỷ ';
        }
        result = groupText + result;
      }

      unitIndex++;
    }

    String finalResult = result.trim() + ' đồng';
    // Viết hoa ký tự đầu tiên
    if (finalResult.isNotEmpty) {
      finalResult = finalResult[0].toUpperCase() + finalResult.substring(1);
    }
    return finalResult;
  }

  static String formatDateTime(int? timestamp, {String pattern = 'dd/MM/yyyy HH:mm'}) {
    if (timestamp == null) return '';
    try {
      final dt = DateTime.fromMillisecondsSinceEpoch(timestamp, isUtc: true).toLocal();
      return DateFormat(pattern).format(dt);
    } catch (e) {
      return '';
    }
  }

  static String formatSlaHour(dynamic value) {
    if (value == null) return '';
    try {
      final val = value is double ? value : double.parse(value.toString());
      return val % 1 == 0 ? '${val.toInt()}h' : '${val}h';
    } catch (_) {
      return '';
    }
  }
  static String convertToUtcIso(String input) {
  // Parse input string to DateTime (local)
  final localDate = DateTime.parse(input);

  // Tạo DateTime mới với giờ 0, phút 0, giây 0, mili giây 0 (local)
  final startOfDay = DateTime(localDate.year, localDate.month, localDate.day);

  // Chuyển sang UTC
  final utc = startOfDay.toUtc();

  // Trả về chuỗi ISO8601 chuẩn
  return utc.toIso8601String();
}
}
