import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_event.dart';
import 'package:eapprove/blocs/dropdown/dropdown_state.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/all_submission_type_response.dart';
import 'package:eapprove/models/form/md_service_request_model.dart';
import 'package:eapprove/models/form/priority_management_request_model.dart';
import 'package:eapprove/services/api_form_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_check_box.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:developer' as developer;

class InformationSubmitterCard extends StatefulWidget {
  final String title;
  final int? processId;
  final dynamic chartData;
  final dynamic chartNodeData;
  final dynamic submitterInfoResponse;
  final FormStateManager stateManager;
  final ValueChanged<InformationSubmitterState?> onChanged;
  final AllSubmissionTypeResponse? allSubmissionType;
  const InformationSubmitterCard({
    super.key,
    required this.title,
    this.processId,
    this.chartData,
    this.chartNodeData,
    this.submitterInfoResponse,
    required this.stateManager,
    required this.onChanged,
    this.allSubmissionType,
  });

  @override
  State<InformationSubmitterCard> createState() =>
      _InformationSubmitterCardState();
}

class InformationSubmitterState {
  final bool receiveMail;
  final bool isAssistantCheck;
  final String? selectedDepartment;
  final String? selectedLocation;
  final String? selectedPriority;
  final String? requestSubject;
  final String? submissionType;
  final String? submissionTypeName;

  InformationSubmitterState({
    this.receiveMail = true,
    this.isAssistantCheck = false,
    this.selectedDepartment,
    this.selectedLocation = '',
    this.selectedPriority = '',
    this.requestSubject = '',
    this.submissionType,
    this.submissionTypeName,
  });

  InformationSubmitterState copyWith({
    bool? receiveMail,
    bool? isAssistantCheck,
    String? selectedDepartment,
    String? selectedLocation,
    String? selectedPriority,
    String? requestSubject,
    String? submissionType,
    String? submissionTypeName,
  }) {
    return InformationSubmitterState(
      receiveMail: receiveMail ?? this.receiveMail,
      isAssistantCheck: isAssistantCheck ?? this.isAssistantCheck,
      selectedDepartment: selectedDepartment ?? this.selectedDepartment,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      selectedPriority: selectedPriority ?? this.selectedPriority,
      requestSubject: requestSubject ?? this.requestSubject,
      submissionType: submissionType ?? this.submissionType,
      submissionTypeName: submissionTypeName ?? this.submissionTypeName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'receiveMail': receiveMail,
      'isAssistantCheck': isAssistantCheck,
      'selectedDepartment': selectedDepartment,
      'selectedLocation': selectedLocation,
      'selectedPriority': selectedPriority,
      'requestSubject': requestSubject,
      'submissionType': submissionType,
      'submissionTypeName': submissionTypeName,
    };
  }
}

class _InformationSubmitterCardState extends State<InformationSubmitterCard> {
  InformationSubmitterState _state = InformationSubmitterState();
  List<SelectItem> locationOptions = [
    SelectItem(label: '', value: '')
  ]; // Default option
  List<SelectItem> priorityOptions = [
    SelectItem(label: '', value: ''),
  ];

  @override
  void initState() {
    super.initState();

    _updateState(_state.copyWith(
      isAssistantCheck:
          widget.submitterInfoResponse.data.first.isAssistant ?? false,
      receiveMail: true,
      selectedDepartment: _getDefaultDepartment()?.value,
      selectedLocation: '',
      selectedPriority:
          widget.submitterInfoResponse.data.first.priorityId?.toString() ?? '',
      requestSubject: '',
      submissionType: _getDefaultSubmissionType()?.value,
      submissionTypeName: _getDefaultSubmissionType()?.label,
    ));

    _getLoadTemplateMDByCompaycode();
    _getPriorityManagement();
    _getDefaultSubmissionType();
  }

  void _updateState(InformationSubmitterState newState) {
    setState(() {
      _state = newState;
    });
    widget.onChanged(_state);
  }

  void _getPriorityManagement() async {
    final input = PriorityManagementRequestModel(
      search: '',
      sortBy: 'name',
      sortType: 'DESC',
      limit: 1000,
      page: 1,
      size: 1000,
      listActiveStatus: [1],
    );
    context
        .read<FormBloc>()
        .add(GetPriorityManagementRequested(requestBody: input));
  }

  void _getLoadTemplateMDByCompaycode() async {
    final Box boxUser = Hive.box('user_box');
    final String? logoCode = boxUser.get('logoCode');
    final requestModel =
        MdServiceRequestBody(masterDataId: "1062", conditionFilter: [
      ConditionFilter(
        condition: "and",
        begin: "code",
        operator: "=",
        end: logoCode ?? '1000',
      )
    ]);
    final response = await ApiFormService().getLoadTemplate(requestModel);
    debugPrint("response: $response");

    if (response != null &&
        response['data'] != null &&
        response['data'].isNotEmpty) {
      final name = response['data'][0]['name'];
      final inputLocation = {"masterDataId": "999"};
      final reponseLocation =
          await ApiFormService().getLoadTemplate(inputLocation);
      if (reponseLocation != null &&
          reponseLocation['data'] != null &&
          reponseLocation['data'].isNotEmpty) {
        // Update location options regardless of matching
        if (mounted) {
          setState(() {
            locationOptions = reponseLocation['data'].map<SelectItem>((item) {
              return SelectItem(
                label: item['name']?.toString() ?? '',
                value: item['code']?.toString() ?? '',
              );
            }).toList();
          });
        }

        // Try to find matching location, but don't throw if not found
        try {
          final matchingLocations = reponseLocation['data']
              .where((element) =>
                  element['name'].toString().toLowerCase().trim() ==
                  name.toLowerCase().trim())
              .toList();

          if (matchingLocations.isNotEmpty) {
            final location = matchingLocations.first;
            debugPrint("Found matching location: $location");
          }
        } catch (e) {
          debugPrint("Error finding matching location: $e");
        }
      }
    }
  }

  List<SelectItem> _buildDepartmentOptions() {
    final List<SelectItem> options = [];

    try {
      if (widget.chartNodeData != null && widget.chartNodeData.data != null) {
        for (var node in widget.chartNodeData.data) {
          try {
            final chartNodeName = node.chartNodeName?.toString() ?? '';

            options.add(SelectItem(
              label: chartNodeName,
              value: chartNodeName,
            ));
          } catch (e) {
            debugPrint('Error processing chart node: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Error building department options: $e');
    }

    return options;
  }

  SelectItem? _getDefaultDepartment() {
    try {
      if (widget.chartNodeData != null &&
          widget.chartNodeData.data != null &&
          widget.chartNodeData.data.isNotEmpty) {
        final firstNode = widget.chartNodeData.data.first;
        final chartNodeName = firstNode.chartNodeName?.toString() ?? '';
        final chartNodeId = firstNode.chartNodeId?.toString() ?? '';

        return SelectItem(
          label: chartNodeName,
          value: chartNodeId,
        );
      }
    } catch (e) {
      debugPrint('Error getting default department: $e');
    }

    return null;
  }

  SelectItem? _getDefaultSubmissionType() {
    try {
      if (widget.allSubmissionType != null && 
          widget.allSubmissionType!.data != null && 
          widget.submitterInfoResponse?.data.first?.serviceNamePopUp != null &&
          widget.submitterInfoResponse!.data.first.serviceNamePopUp.isNotEmpty) {
        
        final submsType = widget.submitterInfoResponse!.data.first.serviceNamePopUp[0].submissionType;
        
        if (submsType != null) {
          final matchingTypes = widget.allSubmissionType!.data!.where(
            (element) => element.id == submsType,
          ).toList();
          
          if (matchingTypes.isNotEmpty) {
            final submissionType = matchingTypes.first;
            developer.log('submissionType: ${submissionType.toJson()}', name: 'submissionType');
            return SelectItem(
              label: submissionType.typeName ?? '', 
              value: submissionType.id.toString() ?? ''
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error getting default submission type: $e');
    }

    return SelectItem(label: '', value: '');
  }


  @override
  Widget build(BuildContext context) {
    final departmentOptions = _buildDepartmentOptions();
    final defaultDepartment = _getDefaultDepartment();

    return MultiBlocListener(
      listeners: [
        BlocListener<FormBloc, FormInfoState>(
          listenWhen: (previous, current) =>
              previous.priorityManagementResponse !=
              current.priorityManagementResponse,
          listener: (context, state) {
            if (state.priorityManagementResponse?.data?.content != null) {
              setState(() {
                priorityOptions =
                    state.priorityManagementResponse!.data!.content.map((item) {
                  return SelectItem(
                    label: item.name ?? '',
                    value: item.id?.toString() ?? '',
                  );
                }).toList();
                // Set default priority if available
                if (priorityOptions.isNotEmpty) {
                  _state = _state.copyWith(
                      selectedPriority: priorityOptions.first.value);
                }
              });
            }
          },
        ),
      ],
      child: Container(
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomDropdownMenu(
                dropdownHeight: 40.h,
                label: 'Loại tờ trình',
                // placeholder: widget.title,
                options:[],
                onSelected: (value) {},
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: getColorSkin().lightGray, width: 1),
                  color: getColorSkin().whiteSmoke,
                ),
                defaultValue: _getDefaultSubmissionType(),
                showDeleteIcon: false,
                isFilled: true,
                isDisabled: true,
              ),
              SizedBox(height: 16.h),

              // Department dropdown - populated from chart node data
              CustomDropdownMenu(
                showDeleteIcon: false,
                dropdownHeight: 40.h,
                label: 'Phòng ban',
                placeholder: 'Phòng ban',
                options: departmentOptions,
                onSelected: (value) {
                  _updateState(
                      _state.copyWith(selectedDepartment: value?.value));
                },
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: getColorSkin().lightGray, width: 1),
                ),
                defaultValue: defaultDepartment,
              ),
              SizedBox(height: 10.h),

              CustomDropdownMenu(
                showDeleteIcon: false,
                dropdownHeight: 40.h,
                label: 'Nơi làm việc của người đệ trình',
                options: locationOptions,
                onSelected: (value) {
                  _updateState(_state.copyWith(selectedLocation: value?.value));
                },
                isFilled: true,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: getColorSkin().lightGray, width: 1),
                  color: getColorSkin().whiteSmoke,
                ),
                defaultValue: SelectItem(label: '', value: ''),
                isDisabled: true,
                disabledColor: getColorSkin().whiteSmoke,
              ),
              SizedBox(height: 16.h),

              CustomDropdownMenu(
                showDeleteIcon: false,
                dropdownHeight: 40.h,
                label: 'Độ ưu tiên',
                options: priorityOptions,
                onSelected: (value) {
                  _updateState(_state.copyWith(selectedPriority: value?.value));
                },
                isFilled: true,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    border:
                        Border.all(color: getColorSkin().lightGray, width: 1)),
                defaultValue: priorityOptions.isNotEmpty
                    ? priorityOptions.firstWhere(
                        (element) =>
                            element.value.toString() ==
                            widget.submitterInfoResponse.data.first.priorityId
                                ?.toString(),
                        orElse: () => SelectItem(label: '', value: ''),
                      )
                    : priorityOptions.firstOrNull,
              ),
              SizedBox(height: 16.h),

              LayoutBuilder(
                builder: (context, constraints) {
                  final bool hasEnoughSpace = constraints.maxWidth >= 320.w;

                  if (hasEnoughSpace) {
                    return Row(
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomCheckbox(
                              borderRadius: 5.r,
                              activeColor: getColorSkin().colorPrimary,
                              value: _state.isAssistantCheck,
                              onChanged: (value) {
                                _updateState(
                                    _state.copyWith(isAssistantCheck: value));
                              },
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              'Có trợ lý soát xét',
                              style: getTypoSkin().body2Regular,
                            ),
                          ],
                        ),
                        SizedBox(width: 16.w),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomCheckbox(
                              borderRadius: 5.r,
                              activeColor: getColorSkin().colorPrimary,
                              value: _state.receiveMail,
                              onChanged: (value) {
                                _updateState(
                                    _state.copyWith(receiveMail: value));
                              },
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              'Nhận thông báo Mail',
                              style: getTypoSkin().body2Regular,
                            ),
                          ],
                        ),
                      ],
                    );
                  } else {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomCheckbox(
                              borderRadius: 5.r,
                              activeColor: getColorSkin().colorPrimary,
                              value: _state.isAssistantCheck,
                              onChanged: (value) {
                                _updateState(
                                    _state.copyWith(isAssistantCheck: value));
                              },
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              'Có trợ lý soát xét',
                              style: getTypoSkin().body2Regular,
                            ),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomCheckbox(
                              borderRadius: 5.r,
                              activeColor: getColorSkin().colorPrimary,
                              value: _state.receiveMail,
                              onChanged: (value) {
                                _updateState(
                                    _state.copyWith(receiveMail: value));
                              },
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              'Nhận thông báo Mail',
                              style: getTypoSkin().body2Regular,
                            ),
                          ],
                        ),
                      ],
                    );
                  }
                },
              ),

              SizedBox(height: 14.h),
              CustomTextForm(
                showDeleteButton: false,
                labelText: 'Tên phiếu yêu cầu',
                onChanged: (value) {
                  _updateState(_state.copyWith(requestSubject: value));
                },
                onTapOutside: (_) {
                  if (mounted) {
                    FocusScope.of(context).unfocus();
                  }
                },
                filled: true,
                fillColor: getColorSkin().white,
                key: Key('requestSubject_${widget.title}'),
              ),
              SizedBox(height: 6.h),
            ],
          ),
        ),
      ),
    );
  }
}
