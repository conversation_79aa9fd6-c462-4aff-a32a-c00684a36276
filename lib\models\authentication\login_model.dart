import 'dart:developer';

import 'package:eapprove/models/meta.dart';

class LoginRequest {
  final String clientId;
  final String redirectUri;
  final String username;
  final String password;

  LoginRequest({
    required this.clientId,
    required this.redirectUri,
    required this.username,
    required this.password,
  });

  Map<String, dynamic> toJson() => {
        'client_id': clientId,
        'redirect_uri': redirectUri,
        'username': username,
        'password': password,
      };
}

class LoginResponse {
  final Meta meta;
  final LoginData? data;

  LoginResponse({
    required this.meta,
    this.data,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
      data: json['data'] != null && json['data'] is Map<String, dynamic>
          ? LoginData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  factory LoginResponse.error(int code, String message) {
    return LoginResponse(
      meta: Meta(code: code, message: message),
      data: null,
    );
  }
}

class LoginData {
  final String email;
  final String? code;
  final String? sessionState;
  final bool is2FA;
  final String? redirectUri;
  final String? clientId;
  final String? hash;
  final bool? temporary;

  LoginData({
    required this.email,
    this.code,
    this.sessionState,
    required this.is2FA,
    this.redirectUri,
    this.clientId,
    this.hash,
    this.temporary,
  });

  factory LoginData.fromJson(Map<String, dynamic> json) {
    try {
      return LoginData(
        email: json['email'] as String? ?? '',
        code: json['code'] as String?,
        sessionState: json['session_state'] as String?,
        is2FA: json['is2FA'] as bool? ?? false,
        redirectUri: json['redirect_uri'] as String?,
        clientId: json['client_id'] as String?,
        hash: json['hash'] as String?,
        temporary: json['temporary'] as bool?,
      );
    } catch (e) {
      log('Error parsing LoginData: $e');
      return LoginData(
        email: '',
        is2FA: false,
      );
    }
  }
}
