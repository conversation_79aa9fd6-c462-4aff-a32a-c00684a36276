import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';

abstract class CancelTicketEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class UploadCancelFileEvent extends CancelTicketEvent {
  final List<PlatformFile> files;

  UploadCancelFileEvent(this.files);

  @override
  List<Object?> get props => [files];
}

class SaveCancelDraftEvent extends CancelTicketEvent {
  final String ticketProcId;
  final String reason;
  final String? filePath;

  SaveCancelDraftEvent({
    required this.ticketProcId,
    required this.reason,
    this.filePath,
  });

  @override
  List<Object?> get props => [ticketProcId, reason, filePath];
}
class SubmitCancelTicketEvent extends CancelTicketEvent {
  final String ticketProcId;
  final String taskDefKey;
  final int ticketId;
  final String reason;
  final List<String> filePaths; // Đổi từ String? thành List<String>

  SubmitCancelTicketEvent({
    required this.ticketProcId,
    required this.taskDefKey,
    required this.ticketId,
    required this.reason,
    required this.filePaths,
  });

  @override
  List<Object?> get props => [ticketProcId, taskDefKey, ticketId, reason, filePaths];

}
