import 'package:equatable/equatable.dart';
import 'package:eapprove/models/form/individual_info_request_model.dart';
import 'package:eapprove/models/form/md_service_request_model.dart';
import 'package:eapprove/models/form/bpmProcInst_create_request_model.dart';

import 'package:eapprove/models/form/call_service_request_model.dart';
import 'package:eapprove/models/form/tao_to_trinh_request_body.dart';
import 'package:eapprove/models/form/priority_management_request_model.dart';

abstract class FormEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadFormRequested extends FormEvent {
  final dynamic procDefId;
  final bool isCreateTicket;
  final int? processId;
  final dynamic ticket;
  final bool? isApprove;
  final String? taskDefKey;

  LoadFormRequested({this.ticket, required this.procDefId, this.processId, this.isCreateTicket = true, this.isApprove = true, this.taskDefKey});
  @override
  List<Object> get props => [procDefId, processId ?? 1];
}

class GetIndividualInfoRequested extends FormEvent {
  final IndividualInfoRequestModel requestModel;
  final String? dependentValue;
  final String? requestKey;

  GetIndividualInfoRequested({
    required this.requestModel,
    this.dependentValue,
    this.requestKey,
  });

  @override
  List<Object> get props => [requestModel, dependentValue ?? '', requestKey ?? ''];
}

class GetChartDataRequested extends FormEvent {}

class GetNodeByUserAndChartIdRequested extends FormEvent {
  final String userId;

  GetNodeByUserAndChartIdRequested({
    required this.userId,
  });

  @override
  List<Object> get props => [userId];
}

class GetMdServiceRequested extends FormEvent {
  final MdServiceRequestBody requestBody;
  final String? requestKey;

  GetMdServiceRequested({required this.requestBody, this.requestKey});

  @override
  List<Object> get props => [requestBody, requestKey ?? ''];
}

class FormReset extends FormEvent {}

class ClearUploadState extends FormEvent {}

class GetBpmProcdefGetAllRequested extends FormEvent {
  final int processId;

  GetBpmProcdefGetAllRequested({required this.processId});

  @override
  List<Object> get props => [processId];
}

class LoadAllFormDataRequested extends FormEvent {
  final String procDefId;
  final dynamic processId;

  LoadAllFormDataRequested({
    required this.procDefId,
    this.processId,
  });
}

class TaoToTrinhRequested extends FormEvent {
  final TaoToTrinhRequestBody requestBody;

  TaoToTrinhRequested({required this.requestBody});

  @override
  List<Object> get props => [requestBody];
}

class GetApprovalVinaphacoRequested extends FormEvent {
  final String procDefId;

  GetApprovalVinaphacoRequested({required this.procDefId});

  @override
  List<Object> get props => [procDefId];
}

class UploadFileRequested extends FormEvent {
  final Map<String, dynamic> fileData;
  final Map<String, dynamic>? extraData;
  final String? name;
  final String? fileType;

  UploadFileRequested({
    required this.fileData,
    this.extraData,
    this.name,
    this.fileType,
  });

  @override
  List<Object> get props => [fileData, extraData ?? {}, name ?? '', fileType ?? ''];
}

class UploadMultiFilesRequested extends FormEvent {
  final List<Map<String, dynamic>> files;
  final Map<String, dynamic>? extraData;
  final String? name;
  final String? fileType;

  UploadMultiFilesRequested({
    required this.files,
    this.extraData,
    this.name,
    this.fileType,
  });

  @override
  List<Object> get props => [files, extraData ?? {}];
}

class CreateBpmProcInstRequested extends FormEvent {
  final String procDefId;
  final int ticketId;
  final BpmProcInstCreateRequestModel requestBody;
  final TaoToTrinhRequestBody? printSignZoneRequest;

  CreateBpmProcInstRequested({
    required this.procDefId,
    required this.ticketId,
    required this.requestBody,
    this.printSignZoneRequest,
  });

  @override
  List<Object> get props => [procDefId, ticketId, requestBody, printSignZoneRequest ?? {}];
}

class CallService extends FormEvent {
  final CallServiceRequestModel requestModel;
  final String? baseURL;

  CallService({required this.requestModel, this.baseURL});
}

class GetListBaseUrl extends FormEvent {
  GetListBaseUrl();

  @override
  List<Object> get props => [];
}

class GetServiceByIdWithPermission extends FormEvent {
  final int serviceId;

  GetServiceByIdWithPermission({required this.serviceId});

  @override
  List<Object> get props => [serviceId];
}

class GetUserInfoByUsername extends FormEvent {
  final String username;

  GetUserInfoByUsername({required this.username});

  @override
  List<Object> get props => [username];
}

class GetFinalTitleByListUser extends FormEvent {
  final List<String> usernames;

  GetFinalTitleByListUser({required this.usernames});

  @override
  List<Object> get props => [usernames];
}

class GetDefaultSignatureRequested extends FormEvent {
  final String username;

  GetDefaultSignatureRequested({required this.username});

  @override
  List<Object> get props => [username];
}

class FormSubmitting extends FormEvent {}

class GetAccountMultiChartRequested extends FormEvent {
  final String username;
  final String chartIds;

  GetAccountMultiChartRequested({
    required this.username,
    required this.chartIds,
  });

  @override
  List<Object> get props => [username, chartIds];
}

class GetPriorityManagementRequested extends FormEvent {
  final PriorityManagementRequestModel requestBody;

  GetPriorityManagementRequested({
    required this.requestBody,
  });
}
