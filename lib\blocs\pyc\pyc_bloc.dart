import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_data_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/repositories/pyc_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'pyc_event.dart';
import 'pyc_state.dart';

class PycBloc extends Bloc<PycEvent, PycState> {
  final PycRepository repository;


  PycBloc(this.repository) : super(const PycState(status: PycStatus.initial)) {
    on<FetchMyPycList>(_onFetchMyPycList);
    on<FetchProcPycList>(_onFetchProcPycList);
    on<FetchAppPycList>(_onFetchAppPycList);
    on<FetchAssisPycList>(_onFetchAssisPycList);
    on<FetchServiceList>(_onFetchServiceList);
    on<FetchAssigneeList>(_onFetchAssigneeList);
    on<FetchListChartId>(_onFetchChartIdList);
    on<FetchOrgChartList>(_onFetchOrgChartList);
    on<FetchFilter>(_onFetchFilterPycList);
    on<FetchTicketFilter>(_onFetchTicketFilter);
    on<FetchTaskDefKeyList>(_onFetchTaskDefKeyList);
    on<FetchListCreatedUser>(_onFetchListCreatedUser);
    on<FetchPriorityList>(_onFetchPriorityList);
    on<FetchSearchFilterPyc>(_onFetchSearchFilterPyc);
    on<FetchMyPycCount>(_onFetchMyPycCount);
    on<FetchProcAppCount>(_onFetchProcAppCount);
    on<FetchAssisPycCount>(_onFetchAssisPycCount);
    on<FetchStatusTicket>(_onFetchStatusTicket);
    on<FetchStatusTask>(_onFetchStatusTask);
    on<DeleteDraftTicket>(_onDeleteDraftTicket);
  }

  Future<void> _onFetchTaskDefKeyList(
    FetchTaskDefKeyList event,
    Emitter<PycState> emit,
  ) async {
    emit(state.copyWith(
      taskDefKeyStatus: PycStatus.loading,
      taskDefKeyRequestModel: event.requestModel,
      taskDefKeyErrorMessage: null,
    ));

    try {
      final response = await repository.getListTaskDefKey(
        requestBody: event.requestModel,
      );

      emit(state.copyWith(
        taskDefKeyStatus: PycStatus.loaded,
        taskDefKeyList: response.data,
      ));
    } catch (e) {
      emit(state.copyWith(
        taskDefKeyStatus: PycStatus.error,
        taskDefKeyErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchServiceList(
      FetchServiceList event, Emitter<PycState> emit) async {
    final isLoadMore = event.isLoadMore;
    final currentList = isLoadMore ? state.serviceList : <FilterServiceData>[];
    try {
      emit(state.copyWith(
        serviceListStatus: isLoadMore ? PycStatus.loaded : PycStatus.loading,
        isPaginating: isLoadMore,
        serviceListRequestModel: event.requestModel,
      ));

      final serviceResponse = await repository.getServiceList(
        requestBody: event.requestModel,
      );
      final newList = serviceResponse.data;
      final combinedList = [...currentList, ...newList];

      final hasMore = newList.length >= (event.requestModel.limit ?? 10);

      emit(state.copyWith(
        serviceListStatus: PycStatus.loaded,
        serviceList: combinedList,
        isPaginating: false,
        hasMore: hasMore,
      ));
    } catch (e) {
      emit(state.copyWith(
        serviceListStatus: PycStatus.error,
        serviceListErrorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchOrgChartList(
      FetchOrgChartList event, Emitter<PycState> emit) async {
    try {
      emit(state.copyWith(
        orgChartListStatus: PycStatus.loading,
        orgChartRequestModel: event.requestModel,
      ));
      debugPrint('rq body orgChart ${event.requestModel}');

      final response = await repository.getOrgChart(
        requestModel: event.requestModel,
      );
      debugPrint('response orgChart: $response');

      emit(state.copyWith(
        orgChartListStatus: PycStatus.loaded,
        orgChartList: response.data,
      ));
    } catch (e) {
      emit(state.copyWith(
        orgChartListStatus: PycStatus.error,
        orgChartErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchChartIdList(
      FetchListChartId event, Emitter<PycState> emit) async {
    emit(state.copyWith(chartIdListStatus: PycStatus.loading));
    try {
      final chartIdList = await repository.getListChartId();
      emit(state.copyWith(
        chartIdListStatus: PycStatus.loaded,
        chartIdList: chartIdList.data,
      ));
    } catch (e) {
      emit(state.copyWith(
        chartIdListStatus: PycStatus.error,
        chartIdListErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchAssigneeList(
      FetchAssigneeList event, Emitter<PycState> emit) async {
    try {
      emit(state.copyWith(
        assigneeListStatus: PycStatus.loading,
        assigneeListRequestModel: event.requestModel,
      ));

      final assigneeList = await repository.getAssigneeList(
        requestBody: event.requestModel,
      );
      debugPrint(
          '✅ [Bloc] Assignee list received, count: ${assigneeList.data.length}');
      emit(state.copyWith(
        assigneeListStatus: PycStatus.loaded,
        assigneeList: assigneeList.data,
      ));
    } catch (e) {
      emit(state.copyWith(
        assigneeListStatus: PycStatus.error,
        assigneeListErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchListCreatedUser(
    FetchListCreatedUser event,
    Emitter<PycState> emit,
  ) async {
    emit(state.copyWith(
      listCreatedUserStatus: PycStatus.loading,
      listCreatedUser: [],
      listCreatedUserRequestModel: event.requestModel,
      listCreatedUserErrorMessage: null,
    ));

    try {
      final result = await repository.getListCreatedUser(
        requestBody: event.requestModel,
        chartId: event.chartId ?? 0,
      );

      emit(state.copyWith(
        listCreatedUserStatus: PycStatus.loaded,
        listCreatedUser: result,
      ));
    } catch (e) {
      emit(state.copyWith(
        listCreatedUserStatus: PycStatus.error,
        listCreatedUserErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchMyPycList(
      FetchMyPycList event, Emitter<PycState> emit) async {
    final newRequestModel = event.requestModel ?? state.myPycRequestModel;
    final isLoadMore = event.isLoadMore ?? false;

    try {
      emit(state.copyWith(
        status: isLoadMore ? PycStatus.loaded : PycStatus.loading,
        isPaginating: isLoadMore,
        myPycRequestModel: newRequestModel,
        myPycResponseModel: isLoadMore ? state.myPycResponseModel : null,
      ));

      final response = await repository.getMyPyc(requestBody: newRequestModel!);

      final oldList = isLoadMore
          ? (state.myPycResponseModel?.data.content ?? [])
          : <MyPycContent>[];

      final newList = response.data.content ?? <MyPycContent>[];

      final List<MyPycContent> combined = [...oldList, ...newList];

      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );

      final hasMore = newList.length >= (newRequestModel.limit ?? 10);

      emit(state.copyWith(
        status: PycStatus.loaded,
        myPycResponseModel: updatedResponse,
        isPaginating: false,
        hasMore: hasMore,
        statusTicketResponseModel: state.statusTicketResponseModel,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: PycStatus.error,
        errorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchProcPycList(
      FetchProcPycList event, Emitter<PycState> emit) async {
    final newRequestModel = event.requestModel ?? state.procPycRequestModel;
    final isLoadMore = event.isLoadMore ?? false;

    try {
      emit(state.copyWith(
        status: isLoadMore ? PycStatus.loaded : PycStatus.loading,
        isPaginating: isLoadMore,
        procPycRequestModel: newRequestModel,
        procPycResponseModel: isLoadMore ? state.procPycResponseModel : null,
      ));

      final response =
          await repository.getProcPyc(requestBody: newRequestModel!);

      final oldList = isLoadMore
          ? (state.procPycResponseModel?.data.content ?? <ProcContent>[])
          : <ProcContent>[];

      final newList = response.data.content ?? <ProcContent>[];

      final List<ProcContent> combined = [...oldList, ...newList];

      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );

      final hasMore = newList.length >= (newRequestModel.limit ?? 10);

      emit(state.copyWith(
          status: PycStatus.loaded,
          procPycResponseModel: updatedResponse,
          isPaginating: false,
          hasMore: hasMore,
          statusTicketResponseModel: state.statusTicketResponseModel));
      
    } catch (e) {
      emit(state.copyWith(
        status: PycStatus.error,
        errorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchAppPycList(
      FetchAppPycList event, Emitter<PycState> emit) async {
    final newRequestModel = event.requestModel ?? state.appPycRequestModel;
    final isLoadMore = event.isLoadMore ?? false;

    try {
      emit(state.copyWith(
        status: isLoadMore ? PycStatus.loaded : PycStatus.loading,
        isPaginating: isLoadMore,
        appPycRequestModel: newRequestModel,
        appPycResponseModel: isLoadMore ? state.appPycResponseModel : null,
      ));

      final response =
          await repository.getApprovePyc(requestBody: newRequestModel!);

      final oldList = isLoadMore
          ? (state.appPycResponseModel?.data.content ?? <ApproveContent>[])
          : <ApproveContent>[];

      final newList = response.data.content ?? <ApproveContent>[];

      final List<ApproveContent> combined = [...oldList, ...newList];

      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );

      final hasMore = newList.length >= (newRequestModel.limit ?? 10);

      emit(state.copyWith(
          status: PycStatus.loaded,
          appPycResponseModel: updatedResponse,
          isPaginating: false,
          hasMore: hasMore,
          statusTicketResponseModel: state.statusTicketResponseModel));
       
    } catch (e) {
      emit(state.copyWith(
        status: PycStatus.error,
        errorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchAssisPycList(
      FetchAssisPycList event, Emitter<PycState> emit) async {
    final newRequestModel = event.requestModel ?? state.assisPycRequestModel;
    final isLoadMore = event.isLoadMore ?? false;

    try {
      emit(state.copyWith(
        status: isLoadMore ? PycStatus.loaded : PycStatus.loading,
        isPaginating: isLoadMore,
        assisPycRequestModel: newRequestModel,
        assisPycResponseModel: isLoadMore ? state.assisPycResponseModel : null,
      ));

      final response =
          await repository.getAssisPyc(requestBody: newRequestModel!);

      final oldList = isLoadMore
          ? (state.assisPycResponseModel?.data.content ?? [])
          : <AssisContent>[];

      final newList = response.data.content ?? <AssisContent>[];

      final List<AssisContent> combined = [...oldList, ...newList];

      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );

      final hasMore = newList.length >= (newRequestModel.limit ?? 10);

      emit(state.copyWith(
          status: PycStatus.loaded,
          assisPycResponseModel: updatedResponse,
          isPaginating: false,
          hasMore: hasMore,
          statusTicketResponseModel: state.statusTicketResponseModel));
          
    } catch (e) {
      emit(state.copyWith(
        status: PycStatus.error,
        errorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchFilterPycList(
      FetchFilter event, Emitter<PycState> emit) async {
    emit(state.copyWith(
      filterStatus: PycStatus.loading,
      filterRequestModel: event.requestModel,
    ));

    try {
      final response = await repository.getFilterPyc(
        requestBody: event.requestModel,
      );

      final existing = state.filterList
          .where((f) => f.type != event.requestModel.type)
          .toList();

      final mergedList = [...existing, ...response.data];

      emit(state.copyWith(
        filterStatus: PycStatus.loaded,
        filterList: mergedList,
      ));
    } catch (e) {
      emit(state.copyWith(
        filterStatus: PycStatus.error,
        filterErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchTicketFilter(
      FetchTicketFilter event, Emitter<PycState> emit) async {
    final isLoadMore = event.isLoadMore;

    emit(state.copyWith(
      ticketFilterStatus: isLoadMore ? PycStatus.loaded : PycStatus.loading,
      isPaginating: isLoadMore,
      ticketFilterRequestModel: event.requestModel,
    ));

    try {
      final response = await repository.getTicketFilterPyc(
        requestBody: event.requestModel,
      );

      final oldList = isLoadMore
          ? (state.ticketFilterResponseModel?.data.content ?? [])
          : <TicketContentModel>[];

      final newList = response.data.content ?? [];

      final List<TicketContentModel> combined = [...oldList, ...newList];

      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );

      final hasMore = newList.length >= (event.requestModel.limit ?? 10);

      emit(state.copyWith(
        ticketFilterStatus: PycStatus.loaded,
        ticketFilterResponseModel: updatedResponse,
        isPaginating: false,
        hasMore: hasMore,
      ));
    } catch (e) {
      emit(state.copyWith(
        ticketFilterStatus: PycStatus.error,
        ticketFilterErrorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchSearchFilterPyc(
      FetchSearchFilterPyc event, Emitter<PycState> emit) async {
    final isLoadMore = event.isLoadMore;
    emit(state.copyWith(
      searchFilterStatus: isLoadMore ? PycStatus.loaded : PycStatus.loading,
      isPaginating: isLoadMore,
      searchFilterRequestModel: event.requestModel,
    ));
    try {
      final response = await repository.getSearchFilterPyc(
        requestBody: event.requestModel,
      );

      final oldList = isLoadMore
          ? (state.searchFilterResponseModel?.data.content ?? [])
          : <SearchFilterContentModel>[];

      final newList = response.data.content ?? [];

      final List<SearchFilterContentModel> combined = [...oldList, ...newList];

      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );

      final hasMore = newList.length >= (event.requestModel.limit ?? 10);

      emit(state.copyWith(
        searchFilterStatus: PycStatus.loaded,
        searchFilterResponseModel: updatedResponse,
        isPaginating: false,
        hasMore: hasMore,
      ));
    } catch (e) {
      emit(state.copyWith(
        searchFilterStatus: PycStatus.error,
        searchFilterErrorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchPriorityList(
      FetchPriorityList event, Emitter<PycState> emit) async {
    emit(state.copyWith(
      priorityListStatus: PycStatus.loading,
      priorityListRequestModel: event.requestModel,
    ));
    try {
      final response = await repository.getListPriority(
        requestBody: event.requestModel,
      );

      emit(state.copyWith(
        priorityListStatus: PycStatus.loaded,
        priorityListResponseModel: response,
      ));
    } catch (e) {
      emit(state.copyWith(
        priorityListStatus: PycStatus.error,
        priorityListErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchMyPycCount(
    FetchMyPycCount event,
    Emitter<PycState> emit,
  ) async {
    emit(state.copyWith(
      myPycCountStatus: PycStatus.loading,
      myPycCountRequestModel: event.requestModel,
      myPycCountErrorMessage: null,
    ));
    try {
      final response =
          await repository.getMyPycCount(requestBody: event.requestModel);
      emit(state.copyWith(
        myPycCountStatus: PycStatus.loaded,
        myPycCountResponseModel: response,
      ));
      // Nếu có từ khóa tìm kiếm, lấy số lượng đã lọc
      if (event.requestModel.search.isNotEmpty) {
        final filteredRequest = MyPycCountRequestModel(
          event.requestModel.ownerProcess,
          event.requestModel.search,
        );
        final filteredResponse = await repository.getMyPycCount(requestBody: filteredRequest);
        emit(state.copyWith(
          myPycCountFiltered: filteredResponse,
        ));
      } else {
        // Nếu không có từ khóa tìm kiếm, xóa số lượng đã lọc
        emit(state.copyWith(myPycCountFiltered: null));
      }
      debugPrint('📊 MyPycCountDataModel = ${state.myPycCountResponseModel?.data.toJson()}');
    } catch (e) {
      emit(state.copyWith(
        myPycCountStatus: PycStatus.error,
        myPycCountErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchProcAppCount(
    FetchProcAppCount event,
    Emitter<PycState> emit,
  ) async {
    final type = event.requestModel.type;

    if (type == 'EXECUTION') {
      emit(state.copyWith(
        procAppCountStatus: PycStatus.loading,
        procCountRequestModel: event.requestModel,
        procAppCountErrorMessage: null,
      ));
    } else if (type == 'APPROVAL') {
      emit(state.copyWith(
        procAppCountStatus: PycStatus.loading,
        appCountRequestModel: event.requestModel,
        procAppCountErrorMessage: null,
      ));
    }

    try {
      final response = await repository.getProcAppCount(
        requestBody: event.requestModel,
      );

      if (type == 'EXECUTION') {
        emit(state.copyWith(
          procAppCountStatus: PycStatus.loaded,
          procCountResponseModel: response,
        ));
      } else if (type == 'APPROVAL') {
        emit(state.copyWith(
          procAppCountStatus: PycStatus.loaded,
          appCountResponseModel: response,
        ));
      }

      if (event.requestModel.search!.isNotEmpty) {
        final filteredRequest = ProcAppCountRequestModel(
          type: type,
          filterChangeAssignee: event.requestModel.filterChangeAssignee,
          search: event.requestModel.search,
        );
        final filteredResponse = await repository.getProcAppCount(requestBody: filteredRequest);

        if (type == 'EXECUTION') {
          emit(state.copyWith(procCountFiltered: filteredResponse));
        } else if (type == 'APPROVAL') {
          emit(state.copyWith(appCountFiltered: filteredResponse));
        }
      } else {
        // Nếu không có từ khóa tìm kiếm, xóa số lượng đã lọc
        if (type == 'EXECUTION') {
          emit(state.copyWith(procCountFiltered: null));
        } else if (type == 'APPROVAL') {
          emit(state.copyWith(appCountFiltered: null));
        }
      }
    } catch (e) {
      if (type == 'EXECUTION') {
        emit(state.copyWith(
          procAppCountStatus: PycStatus.error,
          procAppCountErrorMessage: e.toString(),
        ));
      } else if (type == 'APPROVAL') {
        emit(state.copyWith(
          procAppCountStatus: PycStatus.error,
          procAppCountErrorMessage: e.toString(),
        ));
      }
    }
  }

  Future<void> _onFetchAssisPycCount(
    FetchAssisPycCount event,
    Emitter<PycState> emit,
  ) async {
    emit(state.copyWith(
      assisPycCountStatus: PycStatus.loading,
      assisPycCountRequestModel: event.requestModel,
      assisPycCountErrorMessage: null,
    ));
    try {
      final response = await repository.getAssisPycCount(
        requestBody: event.requestModel,
      );
      emit(state.copyWith(
        assisPycCountStatus: PycStatus.loaded,
        assisPycCountResponseModel: response,
      ));
      if (event.requestModel.searchKey!.isNotEmpty) {
        final filteredRequest = AssisPycCountRequestModel(
          assistantEmail: event.requestModel.assistantEmail,
          searchKey: event.requestModel.searchKey,
        );
        final filteredResponse = await repository.getAssisPycCount(requestBody: filteredRequest);
        emit(state.copyWith(assisPycCountFiltered: filteredResponse));
      } else {
        // Nếu không có từ khóa tìm kiếm, xóa số lượng đã lọc
        emit(state.copyWith(assisPycCountFiltered: null));
      }
    } catch (e) {
      emit(state.copyWith(
        assisPycCountStatus: PycStatus.error,
        assisPycCountErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchStatusTicket(
    FetchStatusTicket event,
    Emitter<PycState> emit,
  ) async {
    emit(state.copyWith(
      statusTicketStatus: PycStatus.loading,
      statusTicketRequestModel: event.requestModel,
    ));
    try {
      final response =
          await repository.getStatusTicket(requestBody: event.requestModel);
      debugPrint('rq body statusticket ${event.requestModel}');

      emit(state.copyWith(
        statusTicketResponseModel: response,
        statusTicketRequestModel: event.requestModel,
        statusTicketStatus: PycStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(
        statusTicketStatus: PycStatus.error,
        statusTicketErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onFetchStatusTask(
    FetchStatusTask event,
    Emitter<PycState> emit,
  ) async {
    emit(state.copyWith(
      statusTaskStatus: PycStatus.loading,
      statusTaskRequestModel: event.requestModel,
    ));
    try {
      final response = await repository.getStatusTicket(requestBody: event.requestModel);
      debugPrint('rq body statustask ${event.requestModel}');

      emit(state.copyWith(
        statusTaskResponseModel: response,
        statusTaskRequestModel: event.requestModel,
        statusTaskStatus: PycStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(
        statusTaskStatus: PycStatus.error,
        statusTaskErrorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteDraftTicket(
      DeleteDraftTicket event, Emitter<PycState> emit) async {
    debugPrint("🗑️ Bắt đầu xoá draft ticket với id = ${event.id}");

    try {
      final result = await repository.deleteDraftTicket([event.id]);

      debugPrint(
          "📥 Kết quả xoá: code = ${result.code}, message = ${result.message}, data = ${result.data}");

      if (result.code == 1 && result.data == true) {
        final currentList = state.myPycResponseModel?.data.content ?? [];

        final updatedList = currentList.where((e) => e.id != event.id).toList();

        final updatedResponse = state.myPycResponseModel?.copyWith(
          data: state.myPycResponseModel!.data.copyWith(content: updatedList),
        );

        emit(state.copyWith(myPycResponseModel: updatedResponse));
        debugPrint("✅ Đã xoá thành công ticket id = ${event.id}");
      } else {
        debugPrint("❌ Xoá thất bại từ server: ${result.message}");
      }
    } catch (e) {
      debugPrint("❌ Exception khi xoá draft ticket: $e");
    }
  }
}
