import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';

class CardRelatedDocument extends StatefulWidget {
  final AuthorizeItemData request;

  const CardRelatedDocument({
    super.key,
    required this.request,
  });

  @override
  State<CardRelatedDocument> createState() => _CardRelatedDocumentState();
}

class _CardRelatedDocumentState extends State<CardRelatedDocument> {
  late List<String> relatedDocuments;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // Initialize with empty list for now since we don't have related documents data
    relatedDocuments = [];
  }

  @override
  Widget build(BuildContext context) {
    return CustomExpandedList<String>(
      expandedSvgIconPath: StringImage.ic_arrow_up,
      collapsedSvgIconPath: StringImage.ic_arrow_right,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      childPadding: EdgeInsets.symmetric(horizontal: 12.w),
      title: "4. Tài liệu liên quan",
      isBuildLeading: false,
      isExpanded: true,
      titleStyle:
          getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
      child: _buildFormContent(),
    );
  }

  Widget _buildFormContent() {
    if (relatedDocuments.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Text(
            'Không có tài liệu liên quan',
            style: getTypoSkin().label4Regular.copyWith(
                  color: getColorSkin().secondaryText,
                ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...relatedDocuments.map((fileName) {
          return Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 8.h),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: getColorSkin().ink5),
            ),
            child: Text(
              fileName,
              style: getTypoSkin()
                  .buttonText2Regular
                  .copyWith(color: getColorSkin().primaryBlue),
            ),
          );
        }),
      ],
    );
  }
}
