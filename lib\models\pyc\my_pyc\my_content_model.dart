class TicketTaskDto {
  final int? id;
  final String? taskId;
  final String? taskDefKey;
  final String? taskName;
  final String? taskPriority;
  final String? taskAssignee;
  final int? taskCreatedTime;
  final int? taskStartedTime;
  final int? taskSla;
  final int? taskFinishedTime;
  final int? taskDoneTime;
  final String? taskStatus;
  final String? procInstId;
  final String? endKey;
  final String? procDefId;
  final String? taskType;
  final String? affected;
  final int? responseTime;
  final int? finishTime;
  final String? newTaskId;
  final String? startPermission;
  final String? newStatus;
  final String? printId;
  final String? ticketId;
  final String? editPermission;
  final String? ticketProcDefId;
  final String? fullName;
  final List<String>? taskUsers;
  final String? priorityId;
  final String? color;
  final List<dynamic>? listStatus;
  final List<dynamic>? listVariables;
  final List<dynamic>? listSignForm;
  final List<dynamic>? signedFiles;
  final String? newId;
  final String? draftVariables;
  final String? taskActionUser;
  final String? taskOrgAssignee;
  final String? taskProcDefId;

  TicketTaskDto({
    this.id,
    this.taskId,
    this.taskDefKey,
    this.taskName,
    this.taskPriority,
    this.taskAssignee,
    this.taskCreatedTime,
    this.taskStartedTime,
    this.taskSla,
    this.taskFinishedTime,
    this.taskDoneTime,
    this.taskStatus,
    this.procInstId,
    this.endKey,
    this.procDefId,
    this.taskType,
    this.affected,
    this.listStatus,
    this.listVariables,
    this.responseTime,
    this.finishTime,
    this.newTaskId,
    this.startPermission,
    this.newStatus,
    this.printId,
    this.ticketId,
    this.listSignForm,
    this.editPermission,
    this.ticketProcDefId,
    this.fullName,
    this.taskUsers,
    this.priorityId,
    this.color,
    this.signedFiles,
    this.newId,
    this.draftVariables,
    this.taskActionUser,
    this.taskOrgAssignee,
    this.taskProcDefId,
  });

  factory TicketTaskDto.fromJson(Map<String, dynamic> json) {
    return TicketTaskDto(
      id: (json['id'] as num?)?.toInt(),
      taskId: json['taskId'],
      taskDefKey: json['taskDefKey'],
      taskName: json['taskName'],
      taskPriority: json['taskPriority'],
      taskAssignee: json['taskAssignee'],
      taskCreatedTime: (json['taskCreatedTime'] as num?)?.toInt(),
      taskStartedTime: (json['taskStartedTime'] as num?)?.toInt(),
      taskSla: (json['taskSla'] as num?)?.toInt(),
      taskFinishedTime: (json['taskFinishedTime'] as num?)?.toInt(),
      taskDoneTime: (json['taskDoneTime'] as num?)?.toInt(),
      taskStatus: json['taskStatus'],
      procInstId: json['procInstId'],
      endKey: json['endKey'],
      procDefId: json['procDefId'],
      taskType: json['taskType'],
      affected: json['affected'],
      responseTime: (json['responseTime'] as num?)?.toInt(),
      finishTime: (json['finishTime'] as num?)?.toInt(),
      newTaskId: json['newTaskId'],
      startPermission: json['startPermission'],
      newStatus: json['newStatus'],
      printId: json['printId'],
      ticketId: json['ticketId'],
      editPermission: json['editPermission'],
      ticketProcDefId: json['ticketProcDefId'],
      fullName: json['fullName'],
      taskUsers: json['taskUsers'] != null
          ? List<String>.from(json['taskUsers'])
          : null,
      priorityId: json['priorityId'],
      color: json['color'],
      newId: json['newId'],
      draftVariables: json['draftVariables'],
      taskActionUser: json['taskActionUser'],
      taskOrgAssignee: json['taskOrgAssignee'],
      taskProcDefId: json['taskProcDefId'],
      listStatus: json['listStatus'] is List
          ? List<dynamic>.from(json['listStatus'])
          : null,
      listVariables: json['listVariables'] is List
          ? List<dynamic>.from(json['listVariables'])
          : null,
      listSignForm: json['listSignForm'] is List
          ? List<dynamic>.from(json['listSignForm'])
          : null,
      signedFiles: json['signedFiles'] is List
          ? List<dynamic>.from(json['signedFiles'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'taskDefKey': taskDefKey,
      'taskName': taskName,
      'taskPriority': taskPriority,
      'taskAssignee': taskAssignee,
      'taskCreatedTime': taskCreatedTime,
      'taskStartedTime': taskStartedTime,
      'taskSla': taskSla,
      'taskFinishedTime': taskFinishedTime,
      'taskDoneTime': taskDoneTime,
      'taskStatus': taskStatus,
      'procInstId': procInstId,
      'endKey': endKey,
      'procDefId': procDefId,
      'taskType': taskType,
      'affected': affected,
      'listStatus': listStatus,
      'listVariables': listVariables,
      'responseTime': responseTime,
      'finishTime': finishTime,
      'newTaskId': newTaskId,
      'startPermission': startPermission,
      'newStatus': newStatus,
      'printId': printId,
      'ticketId': ticketId,
      'listSignForm': listSignForm,
      'editPermission': editPermission,
      'ticketProcDefId': ticketProcDefId,
      'fullName': fullName,
      'taskUsers': taskUsers,
      'priorityId': priorityId,
      'color': color,
      'signedFiles': signedFiles,
      'newId': newId,
      'draftVariables': draftVariables,
      'taskActionUser': taskActionUser,
      'taskOrgAssignee': taskOrgAssignee,
      'taskProcDefId': taskProcDefId,
    };
  }
}

class MyPycContent {
  final String? ticketTitle;
  final String? procServiceName;
  final String? endKey;
  final String? ticketStartUserId;
  final int? chartId;
  final int? processId;
  final String? chartNodeName;
  final String? requestCode;
  final int? id;
  final int? serviceId;
  final String? companyCode;
  final String? startKey;
  final int? ticketCreatedTime;
  final String? submissionTypeName;
  final String? chartName;
  final String? ticketProcDefId;
  final String? ticketStatus;
  final double? ticketRating;
  final int? ticketEditTime;
  final int? slaFinish;
  final String? ticketId;
  final int? slaResponse;
  final String? sharedUser;
  final String? actionUser;
  final int? ticketStartedTime;
  final int? ticketEndTime;
  final int? ticketCanceledTime;
  final String? cancelReason;
  final int? ticketFinishTime;
  final String? ticketPriority;
  final int? ticketClosedTime;
  final String? comment;
  final List<TicketTaskDto>? ticketTaskDtoList;

  MyPycContent({
    this.ticketTitle,
    this.procServiceName,
    this.endKey,
    this.ticketStartUserId,
    this.chartId,
    this.processId,
    this.chartNodeName,
    this.requestCode,
    this.id,
    this.serviceId,
    this.companyCode,
    this.startKey,
    this.ticketCreatedTime,
    this.submissionTypeName,
    this.chartName,
    this.ticketProcDefId,
    this.ticketStatus,
    this.ticketRating,
    this.ticketEditTime,
    this.slaFinish,
    this.ticketId,
    this.slaResponse,
    this.sharedUser,
    this.actionUser,
    this.ticketStartedTime,
    this.ticketEndTime,
    this.ticketCanceledTime,
    this.cancelReason,
    this.ticketFinishTime,
    this.ticketPriority,
    this.ticketClosedTime,
    this.comment,
    this.ticketTaskDtoList,
  });

  factory MyPycContent.fromJson(Map<String, dynamic> json) {
    return MyPycContent(
      ticketTitle: json['ticketTitle'],
      procServiceName: json['procServiceName'],
      endKey: json['endKey'],
      ticketStartUserId: json['ticketStartUserId'],
      chartId: (json['chartId'] as num?)?.toInt(),
      processId: (json['processId'] as num?)?.toInt(),
      chartNodeName: json['chartNodeName'],
      requestCode: json['requestCode'],
      id: (json['id'] as num?)?.toInt(),
      serviceId: (json['serviceId'] as num?)?.toInt(),
      companyCode: json['companyCode'],
      startKey: json['startKey'],
      ticketCreatedTime: (json['ticketCreatedTime'] as num?)?.toInt(),
      submissionTypeName: json['submissionTypeName'],
      chartName: json['chartName'],
      ticketProcDefId: json['ticketProcDefId'],
      ticketStatus: json['ticketStatus'],
      ticketRating: (json['ticketRating'] as num?)?.toDouble(),
      ticketEditTime: (json['ticketEditTime'] as num?)?.toInt(),
      slaFinish: (json['slaFinish'] as num?)?.toInt(),
      ticketId: json['ticketId'],
      slaResponse: (json['slaResponse'] as num?)?.toInt(),
      sharedUser: json['sharedUser'],
      actionUser: json['actionUser'],
      ticketStartedTime: (json['ticketStartedTime'] as num?)?.toInt(),
      ticketEndTime: (json['ticketEndTime'] as num?)?.toInt(),
      ticketCanceledTime: (json['ticketCanceledTime'] as num?)?.toInt(),
      cancelReason: json['cancelReason'],
      ticketFinishTime: (json['ticketFinishTime'] as num?)?.toInt(),
      ticketPriority: json['ticketPriority'],
      ticketClosedTime: (json['ticketClosedTime'] as num?)?.toInt(),
      comment: json['comment'],
      ticketTaskDtoList: (json['ticketTaskDtoList'] as List<dynamic>?)
          ?.map((e) => TicketTaskDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ticketTitle': ticketTitle,
      'procServiceName': procServiceName,
      'endKey': endKey,
      'ticketStartUserId': ticketStartUserId,
      'chartId': chartId,
      'processId': processId,
      'chartNodeName': chartNodeName,
      'requestCode': requestCode,
      'id': id,
      'serviceId': serviceId,
      'companyCode': companyCode,
      'startKey': startKey,
      'ticketCreatedTime': ticketCreatedTime,
      'submissionTypeName': submissionTypeName,
      'chartName': chartName,
      'ticketProcDefId': ticketProcDefId,
      'ticketStatus': ticketStatus,
      'ticketRating': ticketRating,
      'ticketEditTime': ticketEditTime,
      'slaFinish': slaFinish,
      'ticketId': ticketId,
      'slaResponse': slaResponse,
      'sharedUser': sharedUser,
      'actionUser': actionUser,
      'ticketStartedTime': ticketStartedTime,
      'ticketEndTime': ticketEndTime,
      'ticketCanceledTime': ticketCanceledTime,
      'cancelReason': cancelReason,
      'ticketFinishTime': ticketFinishTime,
      'ticketPriority': ticketPriority,
      'ticketClosedTime': ticketClosedTime,
      'comment': comment,
      'ticketTaskDtoList': ticketTaskDtoList?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'MyPycContent(ticketId: $ticketId, ticketProcDefId: $ticketProcDefId, id: ${id})';
  }
}
