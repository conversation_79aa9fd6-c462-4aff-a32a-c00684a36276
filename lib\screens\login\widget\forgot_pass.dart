import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/screens/login/widget/login_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_sdk/widgets/form.dart';

class ForgotPasswordFormWidget extends StatefulWidget {
  final VoidCallback onBackToLogin;
  final VoidCallback onConfirm;
  final String? initialUsername;

  const ForgotPasswordFormWidget({
    super.key,
    required this.onBackToLogin,
    required this.onConfirm,
    this.initialUsername,
  });

  @override
  State<ForgotPasswordFormWidget> createState() =>
      _ForgotPasswordFormWidgetState();
}

class _ForgotPasswordFormWidgetState extends State<ForgotPasswordFormWidget> {
  final TextEditingController _usernameController = TextEditingController();
  final _formKey = GlobalKey<FFormState>();
  @override
  void initState() {
    super.initState();
    if (widget.initialUsername != null) {
      _usernameController.text = widget.initialUsername!;
    }
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() == true) {
      context.read<AuthenticationBloc>().add(
            ResetPassSubmitted(
              username: _usernameController.text.trim(),
            ),
          );
      _usernameController.clear();
      widget.onConfirm();
    }
  }

  @override
  Widget build(BuildContext context) {
    return LoginContainer(
      child: FForm(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 20.h),
            Row(
              children: [
                IconButton(
                    onPressed: widget.onBackToLogin,
                    icon: FIcon(icon: FIconData.icBack)),
                SizedBox(width: 12.w),
                Text(
                  'Quên mật khẩu',
                  style: getTypoSkin().title1Medium.copyWith(
                        color: getColorSkin().title,
                      ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Text(
              'Nhập tên tài khoản và hệ thống sẽ thông tin hướng dẫn tạo mật khẩu mới.',
              style: getTypoSkin().buttonText2Regular.copyWith(
                    color: getColorSkin().secondaryText,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              softWrap: true,
            ),
            SizedBox(height: 20.h),
            CustomTextField(
              backgroundColor: getColorSkin().white,
              controller: _usernameController,
              showClearIcon: true,
              labelText: 'Tên tài khoản/tên đăng nhập',
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return FTextFieldStatus(
                      status: TFStatus.error,
                      message: 'Tên tài khoản không được để trống');
                }
                return FTextFieldStatus(status: TFStatus.success);
              },
            ),
            SizedBox(height: 16.h),
            BlocBuilder<AuthenticationBloc, AuthenticationState>(
              builder: (context, state) {
                return FFilledButton(
                    onPressed:
                        state is AuthenticationLoading ? null : _handleSubmit,
                    isMaxWith: true,
                    child: const Text('Xác nhận mật khẩu mới'));
              },
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }
}

class ConfirmForgotPasswordFormWidget extends StatelessWidget {
  final VoidCallback onBackToLogin;
  final VoidCallback onBackToForgotPassword;

  const ConfirmForgotPasswordFormWidget({
    super.key,
    required this.onBackToLogin,
    required this.onBackToForgotPassword,
  });

  @override
  Widget build(BuildContext context) {
    return LoginContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 20.h),
          Row(
            children: [
              IconButton(
                  onPressed: onBackToForgotPassword,
                  icon: FIcon(icon: FIconData.icBack)),
              SizedBox(width: 12.w),
              Text(
                'Quên mật khẩu',
                style: getTypoSkin().title1Medium.copyWith(
                      color: getColorSkin().title,
                    ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          Text(
            'Bạn vui lòng kiểm tra mail công ty để thực hiện thay đổi mật khẩu.',
            style: getTypoSkin().buttonText2Regular.copyWith(
                  color: getColorSkin().secondaryText,
                ),
            textAlign: TextAlign.center,
            maxLines: 2,
            softWrap: true,
          ),
          SizedBox(height: 16.h),
          FFilledButton(
              onPressed: onBackToLogin,
              isMaxWith: true,
              child: const Text('Quay về đăng nhập')),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }
}
