class WorkFlowRequestmodel {
  final String? procDefId;
  final String? procInstId;
  final String? fromNodeId;

  WorkFlowRequestmodel({this.procDefId, this.procInstId, this.fromNodeId});
  factory WorkFlowRequestmodel.fromJson(Map<String, dynamic> json) {
    return WorkFlowRequestmodel(
      procDefId:json['procDefId'] as String?,
      procInstId:json['procInstId'] as String?,
      fromNodeId:  json['fromNodeId'] as String?,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'procDefId': procDefId,
      'procInstId': procInstId,
      'fromNodeId': fromNodeId,
    };
  }
}