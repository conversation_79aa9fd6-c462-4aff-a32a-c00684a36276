import 'dart:convert';
import 'dart:developer';

import 'package:eapprove/models/generic_respone_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:flutter/foundation.dart';

class FilterPycRepository {
  final ApiService _apiService;

  FilterPycRepository({required ApiService apiService})
      : _apiService = apiService;
  Future<GenericResponseModel> getFilterPyc(
      {required FilterRequestModel requestBody}) async {
    try {
      final response = await _apiService.post(
        'business-process/bpm-ticket-filter/search-filter',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('FilterPyc jsonData: $jsonData');
        final GenericResponseModel<FilterData> filterResponseModel =
            GenericResponseModel<FilterData>.fromJson(
          jsonData,
          (data) => FilterData.fromJson(data),
        );

        return filterResponseModel;
      } else {
        throw Exception('Failed to load services: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      log('Error in getServices: $e, stackTrace: $stackTrace');
      throw Exception('Failed to load services: $e');
    }
  }
}
