import 'dart:developer' as developer;

import 'dart:developer';

import 'package:eapprove/models/handle_ticket/assistant_opinion_request_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:eapprove/repositories/ticket_process_detail_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'ticket_process_detail_event.dart';
import 'ticket_process_detail_state.dart';

class TicketProcessDetailBloc extends Bloc<TicketProcessDetailEvent, TicketProcessDetailState> {
  final TicketProcessDetailRepository _repository;
  final FormRepository _formRepository;

  TicketProcessDetailBloc(this._repository, this._formRepository) : super(TicketProcessDetailInitial()) {
    on<LoadTicketProcessDetail>(_onLoadTicketProcessDetail);
    on<LoadTicketProcessDetailById>(_onLoadTicketProcessDetailById);
    on<LoadThongTinPhieu>(_onLoadThongTinPhieu);
    on<LoadThongTinDauVao>(_onLoadThongTinDauVao);
    on<LoadAssistantOpinion>(_onLoadAssistantOpinion);
    on<CheckToTrinhHandleTicket>(_onCheckToTrinhRequested);
    on<GetSignedFileUrl>(_onGetSignedFileUrl);
    on<LoadUserTaskInfo>(_onLoadUserTaskInfo);
    on<LoadShareTickets>(_onLoadShareTickets);
    on<DeleteShareTicket>(_onDeleteShareTicket);
    on<AddShareTicket>(_onAddShareTicket);
    on<LoadUserDropdown>(_onLoadUserDropdown);
    on<ApproveTicket>(_onApproveTicket);
    on<SignPrintZone>(_onSignPrintZone);
    on<LoadInheritableTasksList>(_onLoadInheritableTasksList);
    on<SaveDraft>(_onSaveDraft);
  }

  Future<void> _onLoadTicketProcessDetail(
    LoadTicketProcessDetail event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    emit(TicketProcessDetailLoading());

    final (statusCode, ticketProcessDetail) = await _repository.getTicketProcessDetail(
      procInstId: event.procInstId,
      taskDefKey: event.taskDefKey,
      user: event.user,
      status: event.status,
    );

    if (statusCode == 200 && ticketProcessDetail != null) {
      emit(TicketProcessDetailLoaded(ticketProcessDetail));
    } else {
      emit(TicketProcessDetailError(
        'Không thể tải chi tiết phiếu',
        errorCode: statusCode,
      ));
    }
  }

  //thong tin chung section
  Future<void> _onLoadTicketProcessDetailById(
    LoadTicketProcessDetailById event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    emit(TicketProcessDetailLoading());
    try {
      developer.log('TicketProcessDetailBloc start: ${event.ticketId}');
      final ticketProcessDetail = await _repository.getTicketProcessDetailById(
        ticketId: event.ticketId,
      );

      debugPrint('TicketProcessDetailBloc by id after: ${ticketProcessDetail.toJson()}');
      emit(TicketProcessDetailByIdLoaded(ticketProcessDetail));
    } catch (e) {
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onLoadThongTinPhieu(
    LoadThongTinPhieu event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    emit(TicketProcessDetailLoading());
    try {
      final thongTinPhieu = await _repository.getThongTinPhieu(
        ticketId: event.procInstId,
      );
      emit(ThongTinPhieuLoaded(thongTinPhieu));
    } catch (e) {
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onLoadThongTinDauVao(
    LoadThongTinDauVao event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    emit(TicketProcessDetailLoading());
    try {
      final thongTinDauVao = await _repository.getThongTinDauVao(
        taskId: event.taskId,
        type: event.type,
      );
      debugPrint('ThongTinDauVaoBloc after : ${thongTinDauVao.toJson()}');
      emit(ThongTinDauVaoLoaded(thongTinDauVao));
    } catch (e) {
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onLoadAssistantOpinion(
    LoadAssistantOpinion event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    emit(TicketProcessDetailLoading());
    try {
      debugPrint('Loading assistant opinion for ticket: ${event.ticketId}');

      var ticketIdValue = event.ticketId;
      if (ticketIdValue is String) {
        try {
          ticketIdValue = int.parse(ticketIdValue);
        } catch (e) {
          debugPrint('Error converting ticketId to int: $e');
        }
      }

      final assistantOpinion = await _repository.getAssistantOpinion(
        requestBody: AssistantOpinionRequestModel(
            ticketId: ticketIdValue, page: 1, limit: 9999, sortBy: "id", sortType: "DESC", size: 9999),
      );
      debugPrint('AssistantOpinionBloc after : ${assistantOpinion.toJson()}');
      emit(AssistantOpinionLoaded(assistantOpinion));
    } catch (e) {
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onCheckToTrinhRequested(CheckToTrinhHandleTicket event, Emitter<TicketProcessDetailState> emit) async {
    try {
      log('[TicketProcessDetailBloc]: Making checkToTrinh API call');
      log('  procDefId: ${event.procDefId}');
      log('  serviceId: ${event.serviceId}');
      log('  taskDefKey: ${event.taskDefKey}');

      // Make only ONE API call
      final response = await _formRepository.checkToTrinh(
        procDefId: event.procDefId,
        serviceId: event.serviceId,
        taskDefKey: event.taskDefKey ?? '',
      );

      log('[TicketProcessDetailBloc]: Received response: ${response.code}');
      log('[TicketProcessDetailBloc]: ProcessType: ${response.data.processType}');

      if (response.code == 200 || response.code == 1) {
        emit(TicketProcessDetailCheckToTrinhLoaded(response));
      } else {
        emit(TicketProcessDetailError(response.message));
      }
    } catch (e) {
      log('[TicketProcessDetailBloc]: Error in checkToTrinh: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onGetSignedFileUrl(
    GetSignedFileUrl event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      ThongTinChungModel? thongTinChungModel;
      if (state is TicketProcessDetailByIdLoaded) {
        thongTinChungModel = (state as TicketProcessDetailByIdLoaded).thongTinChungModel;
      }

      emit(TicketProcessDetailLoading());
      final fileUrl = await _repository.getSignedFileUrl(event.fileName);
      emit(SignedFileUrlLoaded(fileUrl, event.originalFileName, thongTinChungModel!));
    } catch (e) {
      log('❌ Lỗi khi lấy URL tệp đã ký: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onLoadUserTaskInfo(
    LoadUserTaskInfo event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    emit(TicketProcessDetailLoading());
    try {
      debugPrint('Loading user task info for ticket: ${event.ticketId}');
      final userTaskInfo = await _repository.getAllUserTaskInfo(ticketId: event.ticketId);
      debugPrint('Successfully loaded user task info');
      emit(UserTaskInfoLoaded(userTaskInfo));
    } catch (e) {
      debugPrint('Error loading user task info: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

//chia sẻ
  Future<void> _onLoadShareTickets(
    LoadShareTickets event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      emit(TicketProcessDetailLoading());

      final shareResponse = await _repository.getShareTickets(
        procInstId: event.procInstId,
        search: event.search,
        page: event.page,
        limit: event.limit,
        type: event.type,
      );

      emit(ShareTicketsLoaded(
        shareResponse: shareResponse,
        currentPage: event.page,
        currentType: event.type,
      ));
    } catch (e) {
      debugPrint('Error loading share tickets: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onDeleteShareTicket(
    DeleteShareTicket event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      emit(TicketProcessDetailLoading());

      final success = await _repository.deleteShareTicket(event.shareId);

      if (success) {
        emit(ShareTicketActionSuccess(message: 'Xóa chia sẻ thành công'));

        // Tự động tải lại danh sách sau khi xóa
        add(LoadShareTickets(procInstId: event.procInstId));
      } else {
        emit(TicketProcessDetailError('Không thể xóa chia sẻ'));
      }
    } catch (e) {
      debugPrint('Error deleting share ticket: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onAddShareTicket(
    AddShareTicket event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      debugPrint('Adding share ticket for procInstId: ${event.procInstId}');

      emit(TicketProcessDetailLoading());

      final success = await _repository.addShareTicket(
        procInstId: event.procInstId,
        sharedUsers: event.sharedUsers,
      );

      if (success) {
        emit(ShareTicketActionSuccess(message: 'Chia sẻ thành công'));

        // Tự động tải lại danh sách sau khi thêm
        add(LoadShareTickets(procInstId: event.procInstId));
      } else {
        emit(TicketProcessDetailError('Không thể chia sẻ phiếu'));
      }
    } catch (e) {
      debugPrint('Error adding share ticket: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onLoadUserDropdown(
    LoadUserDropdown event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      debugPrint('Loading user dropdown');

      emit(TicketProcessDetailLoading());

      final userResponse = await _repository.getUsersForDropdown();

      emit(UserDropdownLoaded(
        userResponse: userResponse,
      ));
    } catch (e) {
      debugPrint('Error loading user dropdown: $e');
      emit(TicketProcessDetailError(e.toString()));
    }
  }

  Future<void> _onApproveTicket(
    ApproveTicket event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      developer.log('Approving ticket with taskId: ${event.taskId}, procInstId: ${event.procInstId}');

      final approveResponse = await _repository.approveTicket(
        taskId: event.taskId,
        procInstId: event.procInstId,
        signComplete: event.signComplete,
        body: event.body,
      );

      developer.log('Approve response: ${approveResponse.code}, isComplete: ${approveResponse.data.isComplete}');

      if (approveResponse.code == 1) {
        if (approveResponse.data.isComplete) {
          // Success and complete - emit success
          emit(ApproveTicketSuccess(
            response: approveResponse,
            message: approveResponse.message,
          ));
        } else {
          emit(ApproveTicketError(
            message: 'Phê duyệt chưa hoàn thành. Vui lòng thử lại.',
          ));
        }
      } else {
        emit(ApproveTicketError(
          message: approveResponse.message,
        ));
      }
    } catch (e, stackTrace) {
      developer.log('Error approving ticket: $e, StackTrace: $stackTrace');
      emit(ApproveTicketError(message: 'Lỗi khi phê duyệt: $e'));
    }
  }

  Future<void> _onSignPrintZone(
    SignPrintZone event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      developer.log('Signing print zone with requestBody: ${event.requestBody}');

      final signResponse = await _repository.signPrintZone(
        requestBody: event.requestBody,
      );

      developer.log('Sign response: ${signResponse}');

      if (signResponse['code'] == 1) {
        emit(SignPrintZoneSuccess(
          response: signResponse,
          message: signResponse['message'] ?? 'Ký thành công',
        ));
      } else {
        emit(SignPrintZoneError(
          message: signResponse['message'] ?? 'Ký thất bại',
        ));
      }
    } catch (e, stackTrace) {
      developer.log('Error signing print zone: $e, StackTrace: $stackTrace');
      emit(SignPrintZoneError(message: 'Lỗi khi ký: $e'));
    }
  }

  Future<void> _onLoadInheritableTasksList(
    LoadInheritableTasksList event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      emit(TicketProcessDetailLoading());

      developer.log('Loading inheritable tasks list for procDefId: ${event.procDefId}, ticketId: ${event.ticketId}');

      final inheritableTasksResponse = await _repository.getInheritableTasksList(
        procDefId: event.procDefId,
        ticketId: event.ticketId,
      );

      developer.log('Successfully loaded inheritable tasks list: ${inheritableTasksResponse.data.length} tasks');

      emit(InheritableTasksListLoaded(inheritableTasksResponse));
    } catch (e, stackTrace) {
      developer.log('Error loading inheritable tasks list: $e, StackTrace: $stackTrace');
      emit(TicketProcessDetailError('Lỗi khi tải danh sách tasks: $e'));
    }
  }

  Future<void> _onSaveDraft(
    SaveDraft event,
    Emitter<TicketProcessDetailState> emit,
  ) async {
    try {
      developer.log('Saving draft for taskId: ${event.taskId}, procInstId: ${event.procInstId}');

      final draftResponse = await _repository.saveDraft(
        taskId: event.taskId,
        procInstId: event.procInstId,
        variables: event.variables,
      );

      developer.log('Save draft response: ${draftResponse}');

      if (draftResponse['code'] == 1) {
        emit(SaveDraftSuccess(
          message: draftResponse['message'] ?? 'Lưu nháp thành công',
        ));
      } else {
        emit(SaveDraftError(
          message: draftResponse['message'] ?? 'Lưu nháp thất bại',
        ));
      }
    } catch (e, stackTrace) {
      developer.log('Error saving draft: $e, StackTrace: $stackTrace');
      emit(SaveDraftError(message: 'Lỗi khi lưu nháp: $e'));
    }
  }
}
