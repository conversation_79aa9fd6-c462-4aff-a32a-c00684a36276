import 'package:equatable/equatable.dart';

enum CancelTicketStatus { initial, loading, success, failure }

class CancelTicketState extends Equatable {
  final CancelTicketStatus status;
  final List<String> filePaths;
  final String? error;
  final String? actionType; 

  const CancelTicketState({
    required this.status,
    required this.filePaths,
    this.error,
    this.actionType,
  });

  factory CancelTicketState.initial() => CancelTicketState(
        status: CancelTicketStatus.initial,
        filePaths: [],
        error: null,
        actionType: null
      );

  CancelTicketState copyWith({
    CancelTicketStatus? status,
    List<String>? filePaths,
    String? error,
  String? actionType
    
  }) {
    return CancelTicketState(
      status: status ?? this.status,
      filePaths: filePaths ?? this.filePaths,
      error: error,
      actionType: actionType ?? this.actionType
    );
  }

  @override
  List<Object?> get props => [status, filePaths, error];
}
