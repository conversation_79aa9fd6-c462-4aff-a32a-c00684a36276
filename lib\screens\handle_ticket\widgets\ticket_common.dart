import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/attachment_list_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/ticket_history_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart' as user_events;
import 'package:eapprove/blocs/user/user_state.dart';
import 'package:eapprove/blocs/user/user_state.dart' as user_events;
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/screens/ticket_other_action/attachments_list_screen.dart';
import 'package:eapprove/screens/ticket_other_action/consultation_opinion_screen.dart';
import 'package:eapprove/screens/ticket_other_action/ticket_history_screen.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:eapprove/widgets/custom_expanded_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_info_row.dart';

class TicketCommonInformationBlock extends StatefulWidget {
  final String title;
  final String userId;
  final dynamic ticket;
  final String? assignee;

  const TicketCommonInformationBlock({
    super.key,
    required this.title,
    required this.userId,
    required this.ticket,
    this.assignee,
  });

  @override
  State<TicketCommonInformationBlock> createState() => _TicketCommonInformationBlockState();
}

class _TicketCommonInformationBlockState extends State<TicketCommonInformationBlock> {
  late Map<String, String> ticketInformationData;
  List<StatusContentModel> ticketStatusList = [];
  @override
  void initState() {
    super.initState();

    context.read<PycBloc>().add(FetchStatusTicket(
          StatusTicketRequest(
            code: "STATUS_TICKET",
            type: "system",
            page: 1,
            limit: 9999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));
  }

  String getStatusDisplayName(String? statusCode) {
    if (statusCode == null) return '';

    // Ánh xạ thủ công nếu không tồn tại configName
    final mappedCode = {
          'COMPLETED': 'COMPLETE',
          'CLOSED': 'COMPLETE',
        }[statusCode] ??
        statusCode;

    final status = ticketStatusList.firstWhere(
      (s) => s.configValue == mappedCode,
      orElse: () => StatusContentModel(),
    );

    return status.configName ?? statusCode;
  }

  Widget _buildStatusBadge(String status) {
    if (status.isEmpty) return Container();

    final blocState = context.read<PycBloc>().state;

    final match = ticketStatusList.firstWhere(
      (item) => item.configName == status || item.configValue == status,
      orElse: () {
        debugPrint('[TicketCommonInformationBlock] ⚠️ Không tìm thấy status: $status');
        return StatusContentModel();
      },
    );

    final statusCode = match.configValue;
    if (statusCode == null) return Container();

    final textColor = blocState.getStatusColor(statusCode);
    final bgColor = blocState.getStatusBackgroundColor(statusCode);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 12.sp,
          color: textColor,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<PycBloc, PycState>(
          listenWhen: (prev, curr) =>
              prev.statusTicketStatus != curr.statusTicketStatus ||
              prev.statusTicketResponseModel != curr.statusTicketResponseModel,
          listener: (context, state) {
            if (state.statusTicketStatus == PycStatus.loaded && state.statusTicketResponseModel != null) {
              setState(() {
                ticketStatusList = state.statusTicketResponseModel!.data.content ?? [];
              });

              final ticketState = context.read<TicketProcessDetailBloc>().state;
              if (ticketState is TicketProcessDetailByIdLoaded) {
                _updateTicketData(ticketState.thongTinChungModel);
              }
            }
          },
        ),
      ],
      child: BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
        buildWhen: (previous, current) {
          if (current is TicketProcessDetailByIdLoaded) {
            return current.thongTinChungModel.ticketStartUserId == widget.userId;
          }
          return false;
        },
        builder: (context, state) {
          if (state is TicketProcessDetailByIdLoaded && state.thongTinChungModel.ticketStartUserId == widget.userId) {
            return _buildExpandedWidget(state.thongTinChungModel);
          }

          return SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildExpandedWidget(ThongTinChungModel thongTinChungModel) {
    _updateTicketData(thongTinChungModel);

    return BlocBuilder<FormBloc, FormInfoState>(
      builder: (context, formState) {
        return BlocBuilder<UserBloc, UserState>(
          builder: (context, userState) {
            if (userState is user_events.UserInfoByUsername) {
              final user = userState.userInfoByUserNameResponse.data;
              final titleName = user.userTitleDtos.first.titleName ?? '';
              final firstname = user.firstname ?? '';
              final lastname = user.lastname ?? '';
              final username = user.username ?? '';
              ticketInformationData['Người tạo'] = '$username - $firstname $lastname - $titleName'.trim();
            }

            return CustomExpandedList<String>(
              expandedSvgIconPath: StringImage.ic_arrow_up,
              collapsedSvgIconPath: StringImage.ic_arrow_right,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              title: widget.title,
              isCommonInfo: true,
              assignee: widget.assignee,
              onAdditionalContentPressed: () {
                final myPycBloc = BlocProvider.of<PycBloc>(context);
                final ticketProcessDetailBloc = BlocProvider.of<TicketProcessDetailBloc>(context);
                final consultationOpinionBloc = BlocProvider.of<ConsultationOpinionBloc>(context);
                showDialog(
                  context: context,
                  barrierDismissible: true,
                  builder: (dialogContext) => MultiBlocProvider(
                    providers: [
                      BlocProvider.value(
                        value: myPycBloc,
                      ),
                      BlocProvider.value(
                        value: ticketProcessDetailBloc,
                      ),
                      BlocProvider.value(
                        value: consultationOpinionBloc,
                      ),
                    ],
                    child: Dialog(
                      backgroundColor: Colors.transparent,
                      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                      child: Expanded(
                        child: ConsultationOpinionScreen(
                          ticketId: TicketUtils.getCorrectTicketId(widget.ticket),
                          ticketProcId: TicketUtils.getProcInstId(widget.ticket),
                          isAdditionalContent: true,
                        ),
                      ),
                    ),
                  ),
                );
              },
              onAttachmentListPressed: () {
                final dynamic ticketId = TicketUtils.getCorrectTicketId(widget.ticket);
                showDialog(
                  context: context,
                  builder: (dialogContext) => BlocProvider.value(
                    value: BlocProvider.of<AttachmentListBloc>(context),
                    child: Dialog(
                      backgroundColor: Colors.transparent,
                      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                      child: Expanded(
                        child: AttachmentsListScreen(
                          ticketId: ticketId,
                          ticketStartUserId: thongTinChungModel.ticketStartUserId,
                        ),
                      ),
                    ),
                  ),
                );
              },
              onPaymentSchedulePressed: () {},
              onHistoryPressed: () {
                final dynamic ticketId = TicketUtils.getCorrectTicketId(widget.ticket);
                final dynamic ticketProcId = TicketUtils.getTicketId(widget.ticket);
                showDialog(
                  context: context,
                  builder: (dialogContext) => BlocProvider.value(
                    value: BlocProvider.of<TicketHistoryBloc>(context),
                    child: Dialog(
                      backgroundColor: Colors.transparent,
                      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                      child: Expanded(
                        child: TicketHistoryScreen(
                          ticketId: ticketId,
                          ticketProcId: ticketProcId,
                        ),
                      ),
                    ),
                  ),
                );
              },
              cardColor: getColorSkin().white,
              isBuildLeading: true,
              iconPath: StringImage.ticket_info,
              iconWidth: 24.w,
              iconHeight: 24.h,
              isExpanded: true,
              titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
              child: _buildFormContent(),
            );
          },
        );
      },
    );
  }

  void _updateTicketData(ThongTinChungModel thongTinChungModel) {
    context.read<FormBloc>().add(GetChartDataRequested());
    context.read<FormBloc>().add(GetNodeByUserAndChartIdRequested(userId: thongTinChungModel.ticketStartUserId));
    context.read<UserBloc>().add(user_events.GetUserInfoByUsername(widget.userId));

    ticketInformationData = {
      'Loại yêu cầu': thongTinChungModel.procServiceName ?? '',
      'Trạng thái': getStatusDisplayName(thongTinChungModel.ticketStatus),
      'Mã công ty': thongTinChungModel.companyCode ?? '',
      'Thời gian đệ trình': Utils.formatDateTime(thongTinChungModel.ticketCreatedTime),
      'Độ ưu tiên': thongTinChungModel.priority ?? 'Trung bình',
      'Loại tờ trình': thongTinChungModel.submissionTypeName ?? '',
      'Người tạo': '', // gán ngay sau
      'Cam kết hoàn thành ': Utils.formatSlaHour(thongTinChungModel.slaFinishProcess),
      'Dự kiến hoàn thành ': Utils.formatDateTime(thongTinChungModel.slaFinishTimeProcess),
      'Hoàn thành thực tế ': Utils.formatDateTime(thongTinChungModel.slaFinishTime),
    };
  }

  Widget _buildFormContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: ticketInformationData.entries.map((entry) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: InfoRow(
            textAlign: TextAlign.left,
            label: entry.key,
            value: entry.value,
            textStyle: getTypoSkin().label4Regular.copyWith(color: getColorSkin().ink1),
            widgetValue: entry.key == 'Trạng thái'
                ? Expanded(
                    flex: 5,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: _buildStatusBadge(entry.value),
                    ),
                  )
                : null,
          ),
        );
      }).toList(),
    );
  }
}
