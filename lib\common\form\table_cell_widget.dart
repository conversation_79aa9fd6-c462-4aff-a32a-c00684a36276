import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

/// A specialized widget for select/dropdown fields in table cells
class TableCellSelectWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic) onChange;

  const TableCellSelectWidget({
    Key? key,
    required this.data,
    required this.onChange,
  }) : super(key: key);

  @override
  State<TableCellSelectWidget> createState() => _TableCellSelectWidgetState();
}

class _TableCellSelectWidgetState extends State<TableCellSelectWidget> {
  @override
  void initState() {
    super.initState();
    debugPrint('TableCellSelectWidget: ${widget.data.name} - value: ${widget.data.value}');
    debugPrint('TableCellSelectWidget: ${widget.data.name} - options count: ${_getOptions().length}');
  }

  // Get all available options combining options and option properties
  List<Map<String, dynamic>> _getOptions() {
    List<Map<String, dynamic>> result = [];

    // Add options from 'options' property
    if (widget.data.options != null && widget.data.options!.isNotEmpty) {
      result.addAll(widget.data.options!);
    }

    // Add options from 'option' property
    if (widget.data.option != null && widget.data.option!.isNotEmpty) {
      result.addAll(widget.data.option!);
    }

    return result;
  }

  @override
  Widget build(BuildContext context) {
    final options = _getOptions();

    return Container(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      width: double.infinity, // Ensure it takes full width of cell
      child: _buildDropdown(options),
    );
  }

  Widget _buildDropdown(List<Map<String, dynamic>> options) {
    return DropdownButtonFormField<String>(
      value: widget.data.value?.toString(),
      items: options.map<DropdownMenuItem<String>>((option) {
        return DropdownMenuItem<String>(
          value: option['value']?.toString() ?? '',
          child: Text(
            option['label']?.toString() ?? '',
            style: TextStyle(
              fontSize: 14.sp,
              color: getColorSkin().ink1,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
      onChanged: widget.data.readonly == true
          ? null
          : (value) {
        setState(() {
          widget.data.value = value;
        });
        widget.onChange(widget.data.name ?? '', value);
      },
      decoration: InputDecoration(
        hintText: widget.data.placeholder ?? 'Select',
        contentPadding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
        isDense: true, // Important for compact display in table cell
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: getColorSkin().lightGray),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: getColorSkin().lightGray),
        ),
        filled: true,
        fillColor: widget.data.readonly == true
            ? getColorSkin().whiteSmoke
            : getColorSkin().white,
      ),
      isExpanded: true, // Make dropdown take full width
      icon: Icon(Icons.arrow_drop_down, color: getColorSkin().ink1, size: 18.sp),
      style: TextStyle(
        fontSize: 14.sp,
        color: getColorSkin().ink1,
      ),
      dropdownColor: getColorSkin().white,
    );
  }
}