import 'dart:math';

import 'package:collection/collection.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_state.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/common/form/checkbox_widget.dart';
import 'package:eapprove/common/form/common_form_widget.dart';
import 'package:eapprove/common/form/date_picker_widget.dart';
import 'package:eapprove/common/form/radio_button_widget.dart';
import 'package:eapprove/common/form/splitter_widget.dart';
import 'package:eapprove/common/form/tab_widget.dart';
import 'package:eapprove/common/form/text_area_widget.dart';
import 'package:eapprove/common/form/text_input_widget.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/common/form_v2/widget/form_dropdown_mixin.dart';
import 'package:eapprove/common/form_v2/widget/form_text_input.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'widget/form_text_input.dart';
import 'dart:developer' as developer;

import 'package:flutter_sdk/widgets/custom_expand_item.dart';

class FormBuilderV2 extends StatefulWidget {
  final List<FormItemInfo> widgets;
  final FormStateManager stateManager;
  final Function(String, String, dynamic)? onFieldChanged;
  final bool? isReadOnly;
  final bool isTablet;
  final List<dynamic>? row;
  final List<dynamic>? step;
  final List<dynamic>? col;
  final List<dynamic>? tab;

  const FormBuilderV2({
    Key? key,
    required this.widgets,
    required this.stateManager,
    this.onFieldChanged,
    this.isReadOnly = false,
    this.isTablet = false,
    this.row,
    this.step,
    this.col,
    this.tab,
  }) : super(key: key);

  @override
  State<FormBuilderV2> createState() => _FormBuilderV2State();
}

class _FormBuilderV2State extends State<FormBuilderV2> {
  @override
  void initState() {
    super.initState();
    widget.stateManager
        .initBloc(context.read<DropdownBloc>(), context.read<FormBloc>());

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.stateManager.prefetchDropdownData();
      // _prefetchAllWidgetsData();
    });
    widget.stateManager.addListener(_handleStateChange);
  }

  /// Prefetch data for all widgets that need API calls, regardless of display state
  void _prefetchAllWidgetsData() {
    for (var formWidget in widget.widgets) {
      // Check if widget needs to fetch data
      if (formWidget.optionType == 'masterData' || 
          formWidget.optionType == 'orgchart' || 
          formWidget.optionType == 'api_link') {
        // Fetch data regardless of display state
        widget.stateManager.fetchDropdownOptions(formWidget);
      }

      // Handle matrix widgets specially
      if (formWidget.type == FormItemType.matrix && formWidget.columns != null) {
        for (var column in formWidget.columns!) {
          if (column.optionType == 'masterData' || 
              column.optionType == 'orgchart' || 
              column.optionType == 'api_link') {
            widget.stateManager.fetchDropdownOptions(column);
          }
        }
      }
    }
  }

  @override
  void dispose() {
    widget.stateManager.removeListener(_handleStateChange);
    super.dispose();
  }

  void _handleStateChange() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DropdownBloc, DropdownState>(
      listener: (context, state) {
        // Handle dropdown state changes for all widgets
        for (var formWidget in widget.widgets) {
          final key = formWidget.parentName?.isNotEmpty == true
              ? '${formWidget.parentName}.${formWidget.name}'
              : formWidget.name ?? "";
              
          final isLoading = state.isLoading?[key] ?? false;
          if (!isLoading) {
            final response = state.dropdownResponses?[key];
            if (response != null && response.isSuccess) {
              developer.log('Data loaded for widget: ${formWidget.name}', name: 'form_builder');
            }
          }

          // Handle matrix widgets specially
          if (formWidget.type == FormItemType.matrix && formWidget.columns != null) {
            for (var column in formWidget.columns!) {
              final columnKey = column.parentName?.isNotEmpty == true
                  ? '${column.parentName}.${column.name}'
                  : column.name ?? "";
                  
              final columnLoading = state.isLoading?[columnKey] ?? false;
              if (!columnLoading) {
                final columnResponse = state.dropdownResponses?[columnKey];
                if (columnResponse != null && columnResponse.isSuccess) {
                  developer.log('Data loaded for matrix column: ${column.name}', name: 'form_builder');
                }
              }
            }
          }
        }
      },
      child: _buildDefaultLayout(),
    );
  }

  Widget _buildDefaultLayout() {
    final List<FormItemInfo> splitters = [];
    final List<FormItemInfo> tabs = [];
    final List<FormItemInfo> groupedWidgets = [];

    // First pass: Identify all splitters and tabs
    for (var widget in widget.widgets) {
      if (widget.type == FormItemType.splitter && widget.splitter!.isEmpty) {
        splitters.add(widget);
      }
      if (widget.type == FormItemType.tab) {
        tabs.add(widget);
      }
      groupedWidgets.add(widget);
    }

    // If tablet mode and row/column config exists, use that layout
    if (DeviceUtils.isTablet) {
      return _buildTabletLayout();
    }

    if (splitters.isEmpty && tabs.isEmpty) {
      return Container(
        margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children:
              groupedWidgets.map((widget) => _buildWidget(widget)).toList(),
        ),
      );
    }

    splitters.sort((a, b) {
      final aRowOrder = a.rowSortOrder ?? 0;
      final bRowOrder = b.rowSortOrder ?? 0;
      if (aRowOrder != bRowOrder) return aRowOrder.compareTo(bRowOrder);

      return 0; // fallback to stable order if equal
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ...splitters.map((splitter) {
          return Container(
            child: _buildSplitter(splitter, groupedWidgets),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildTabletLayout() {
    // Sort rows by sortOrder
    final sortedRows = List<Map<String, dynamic>>.from(widget.row ?? []);
    sortedRows
        .sort((a, b) => (a['sortOrder'] ?? 0).compareTo(b['sortOrder'] ?? 0));

    // Group rows by splitter, tab, step
    final Map<String, List<Map<String, dynamic>>> splitterGroups = {};
    final Map<String, List<Map<String, dynamic>>> tabGroups = {};
    final Map<String, List<Map<String, dynamic>>> stepGroups = {};
    final List<Map<String, dynamic>> rootRows = [];

    for (var row in sortedRows) {
      // Nếu row có splitter => group vào splitter cha
      if (row['splitter'] != null && row['splitter'].toString().isNotEmpty) {
        final splitterId = row['splitter'].toString();
        splitterGroups.putIfAbsent(splitterId, () => []).add(row);
      } else if (row['tab'] != null && row['tab'].toString().isNotEmpty) {
        // Nếu không có splitter, group vào tab
        final tabId = row['tab'].toString();
        tabGroups.putIfAbsent(tabId, () => []).add(row);
      } else if (row['step'] != null && row['step'].toString().isNotEmpty) {
        // Nếu không có splitter, group vào step
        final stepId = row['step'].toString();
        stepGroups.putIfAbsent(stepId, () => []).add(row);
      } else {
        // Nếu không có splitter, tab, step => là row gốc
        rootRows.add(row);
      }
    }
    debugPrint("splitterGroups ${splitterGroups.length}");
    // Hàm đệ quy render splitter lồng nhau
    Widget buildSplitter(String splitterId) {
      // Tìm splitter widget
      debugPrint("splitterIdsplitterId:$splitterId");
      final splitterWidget = widget.widgets.firstWhere(
        (w) => w.id == splitterId && w.type == FormItemType.splitter,
        orElse: () => FormItemInfo(
          id: splitterId,
          type: FormItemType.splitter,
          label: 'Section',
        ),
      );

      // Lấy các row con của splitter này
      final childrenRows = splitterGroups[splitterId] ?? [];
      // Chia thành row thường và splitter con
      final normalRows = <Map<String, dynamic>>[];
      final childSplitterIds = <String>[];
      for (var row in childrenRows) {
        // Tìm field có row == row['id'] và type == splitter
        final fieldInRow = widget.widgets.firstWhereOrNull(
          (w) => w.row == row['id'] && w.type == FormItemType.splitter,
        );

        if (fieldInRow != null) {
          // Row này chứa splitter con → render tiếp
          childSplitterIds.add(fieldInRow.id.toString());
        } else {
          normalRows.add(row);
        }
      }
      // debugPrint("childSplitterIds: ${childSplitterIds}");
      return Container(
        margin: EdgeInsets.only(top:  12.h ),
        child: CustomExpandedList<String>(
          childrenPadding: EdgeInsets.zero,
          expandedSvgIconPath: StringImage.ic_arrow_up,
          collapsedSvgIconPath: StringImage.ic_arrow_right,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          cardElevation: 1,
          cardMargin: EdgeInsets.only(bottom: 8.h),
          childPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 18.w),
          title: splitterWidget.label ?? '',
          isExpanded: splitterWidget.splitterOpen == 2,
          titleStyle: TextStyle(
            fontWeight: splitterWidget.fontWeight == 'bold' ||
                    splitterWidget.fontWeight == 'font-bold'
                ? FontWeight.bold
                : FontWeight.normal,
            fontStyle: splitterWidget.fontWeight == 'italic'
                ? FontStyle.italic
                : FontStyle.normal,
            decoration: splitterWidget.fontWeight == 'underline'
                ? TextDecoration.underline
                : TextDecoration.none,
            color: getColorSkin().ink1,
            fontSize: 16.sp,
          ),
          isBuildLeading: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (splitterWidget.splitterDesc != null &&
                  splitterWidget.splitterDesc!.isNotEmpty)
                Text(
                  splitterWidget.splitterDesc!,
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    fontSize: 14.sp,
                    color: getColorSkin().ink2,
                  ),
                ),
              ...normalRows.map((row) => _buildTabletRow(row)).toList(),
              ...childSplitterIds
                  .map((childId) => buildSplitter(childId))
                  .toList(),
            ],
          ),
        ),
      );
    }

    // Render splitter gốc (splitter không có splitter cha)
    final rootSplitterIds = widget.widgets
        .where((w) =>
            w.type == FormItemType.splitter &&
            (w.splitter == null || w.splitter == ""))
        .map((w) => w.id)
        .whereType<String>() // Only keep non-null String ids
        .toList();

    final splitterWidgets =
        rootSplitterIds.map((id) => buildSplitter(id)).toList();

    // Render các row gốc (không thuộc splitter, tab, step)
    final rootRowWidgets = rootRows.map((row) => _buildTabletRow(row)).toList();

    // Render tab/step nếu có
    final tabContents = tabGroups.entries.map((tabEntry) {
      final tabId = tabEntry.key;
      final tabRows = tabEntry.value;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: tabRows.map((row) => _buildTabletRow(row)).toList(),
      );
    }).toList();
    final stepContents = stepGroups.entries.map((stepEntry) {
      final stepId = stepEntry.key;
      final stepRows = stepEntry.value;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: stepRows.map((row) => _buildTabletRow(row)).toList(),
      );
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ...splitterWidgets,
        ...rootRowWidgets,
        // ...tabContents,
        // ...stepContents,
      ],
    );
  }

  Widget _buildTabletRow(Map<String, dynamic> row) {
    // Get columns for this row
    final rowColumns =
        (widget.col ?? []).where((col) => col['row'] == row['id']).toList();

    // Sort columns by sortOrder
    rowColumns
        .sort((a, b) => (a['sortOrder'] ?? 0).compareTo(b['sortOrder'] ?? 0));

    // Get form items for this row
    final rowItems = widget.widgets
        .where((item) =>
            item.row == row['id'] &&
            rowColumns.any((col) => col['id'] == item.col) &&
            item.type != FormItemType.splitter)
        .toList();

    // Sort items by fieldSortOrder
    rowItems.sort(
        (a, b) => (a.fieldSortOrder ?? 0).compareTo(b.fieldSortOrder ?? 0));

    // Group items by column
    final Map<String, List<FormItemInfo>> columnGroups = {};
    for (var item in rowItems) {
      columnGroups.putIfAbsent(item.col ?? '', () => []).add(item);
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: rowColumns.map((column) {
          final columnItems = columnGroups[column['id']] ?? [];

          return Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children:
                    columnItems.map((item) => _buildWidget(item)).toList(),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSplitter(
      FormItemInfo splitter, List<FormItemInfo> groupedWidgets) {
    try {
      bool isInitiallyExpanded =
          splitter.splitterOpen != null ? splitter.splitterOpen == 2 : true;

      // Get all children of this splitter
      final children = groupedWidgets.where((e) => e.splitter == splitter.id).toList();

      // Sort children by their order
      children.sort((a, b) {
        // First sort by step if available
        final aStep = double.tryParse(a.step ?? '') ?? 0;
        final bStep = double.tryParse(b.step ?? '') ?? 0;
        if (aStep != bStep) {
          return aStep.compareTo(bStep);
        }

        // Then sort by row order if available
        final aRowOrder = a.rowSortOrder ?? 0;
        final bRowOrder = b.rowSortOrder ?? 0;
        if (aRowOrder != bRowOrder) {
          return aRowOrder.compareTo(bRowOrder);
        }

        // Then sort by column order if available
        final aColOrder = a.colSortOrder ?? 0;
        final bColOrder = b.colSortOrder ?? 0;
        if (aColOrder != bColOrder) {
          return aColOrder.compareTo(bColOrder);
        }

        // Then sort by field order
        final aFieldOrder = a.fieldSortOrder ?? 0;
        final bFieldOrder = b.fieldSortOrder ?? 0;
        if (aFieldOrder != bFieldOrder) {
          return aFieldOrder.compareTo(bFieldOrder);
        }

        return 0;
      });

      Widget childContent = Column(
        children: [
          if (splitter.splitterDesc != null &&
              splitter.splitterDesc!.isNotEmpty)
            Text(
              splitter.splitterDesc!,
              style: TextStyle(
                fontStyle: FontStyle.italic,
                fontSize: 14.sp,
                color: getColorSkin().ink2,
              ),
            ),
          // Render child form items
          ...(() {
            return children.map((child) {
              // Get event expression results before checking type
              final eventResults = child.eventExpression?.isNotEmpty == true
                  ? widget.stateManager
                      .getEventExpressionResults(child.name.toString())
                  : null;

              // Process event expression results for display
              bool shouldDisplay = child.display ?? true;
              if (eventResults != null) {
                final hienThi = eventResults['hien_thi'];
                if (hienThi != null) {
                  shouldDisplay = hienThi is bool
                      ? hienThi
                      : hienThi.toString().toLowerCase() == 'true';
                }
              }

              if (shouldDisplay == false) {
                return const SizedBox.shrink();
              }

              if (child.type == FormItemType.splitter) {
                return Container(
                  child: _buildSplitter(child, groupedWidgets),
                );
              } else if (child.type == FormItemType.tab) {
                // Handle tab inside splitter
                // Inject tab config from widget.tab (top-level) into optionConfig['tab'] if not present
                final tabConfig = (widget.tab ?? []).firstWhere(
                  (t) => t['id'] == child.id,
                  orElse: () => null,
                );
                FormItemInfo tabData = child;
                if (tabConfig != null) {
                  final newOptionConfig = Map<String, dynamic>.from(child.optionConfig ?? {});
                  newOptionConfig['tab'] = [tabConfig];
                  tabData = child.copyWith(optionConfig: newOptionConfig);
                }
                final tabChildren = groupedWidgets.where((widget) => 
                  (widget.tab == child.id ) 
                ).toList();

                developer.log('tabData:::: ${tabData.name}',name:"tabWidget");
                developer.log('tabChildren:::: ${tabChildren.map((e) => e.name)}',name:"tabChildrentabWidget");
                return TabWidget(
                  data: tabData,
                  childFormItems: tabChildren,
                  stateManager: widget.stateManager,
                );
              } else {
                return _buildWidget(child);
              }
            }).toList();
          })(),
        ],
      );

      // Use CustomExpandedList for the splitter
      return CustomExpandedList<String>(
        childrenPadding: EdgeInsets.zero,
        expandedSvgIconPath: StringImage.ic_arrow_up,
        collapsedSvgIconPath: StringImage.ic_arrow_right,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        cardElevation: 1,
        cardMargin: EdgeInsets.only(top: 12.h),
        childPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 18.w),
        title: splitter.label ?? '',
        isExpanded: isInitiallyExpanded,
        titleStyle: TextStyle(
          fontWeight: splitter.fontWeight == 'bold' ||
                  splitter.fontWeight == 'font-bold'
              ? FontWeight.bold
              : FontWeight.normal,
          fontStyle: splitter.fontWeight == 'italic'
              ? FontStyle.italic
              : FontStyle.normal,
          decoration: splitter.fontWeight == 'underline'
              ? TextDecoration.underline
              : TextDecoration.none,
          color: getColorSkin().ink1,
          fontSize: 16.sp,
        ),
        isBuildLeading: false,
        child: childContent,
      );
    } catch (e) {
      // Return error widget for splitter
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[50],
          border: Border.all(color: Colors.red),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red),
                const SizedBox(width: 8),
                Text('Error in splitter: ${splitter.label ?? 'Unnamed'}',
                    style: const TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 8),
            Text('Error: $e'),
          ],
        ),
      );
    }
  }

  Widget _buildWidget(FormItemInfo formWidget) {
    // Get event expression results before building the widget
    final eventResults = formWidget.eventExpression?.isNotEmpty == true
        ? widget.stateManager
            .getEventExpressionResults(formWidget.name.toString())
        : null;

    // --- BẮT ĐẦU xử lý switchField ---
    FormItemInfo currentWidget = formWidget;

    // Process event expression results
    bool shouldDisplay = currentWidget.display ?? true;
    String currentLabel = currentWidget.label ?? '';
    bool currentReadonly = currentWidget.readonly ?? false;
    String currentSuggestText = currentWidget.suggestText ?? '';
    bool currentRequired = currentWidget.validations?['required'] ?? false;

    if (eventResults != null) {
      // Process ten_truong
      final tenTruong = eventResults['ten_truong']?.toString();
      if (tenTruong != null) {
        currentLabel = tenTruong;
      }

      // Process bat_buoc
      final batBuoc = eventResults['bat_buoc']?.toString();
      if (batBuoc != null) {
        currentRequired = batBuoc.toLowerCase() == 'true';
      }

      // Process chi_duoc_doc
      final chiDuocDoc = eventResults['chi_duoc_doc']?.toString();

      if (chiDuocDoc != null) {
        currentReadonly = chiDuocDoc.toLowerCase() == 'true';
      }

      // Process huong_dan
      final huongDan = eventResults['huong_dan']?.toString();
      if (huongDan != null && huongDan.isNotEmpty) {
        currentSuggestText = huongDan;
      } else if (formWidget.suggestText?.isNotEmpty == true) {
        currentSuggestText = formWidget.suggestText!;
      } else if (formWidget.tooltip != null) {
        currentSuggestText = formWidget.tooltip!;
      }

      // Process hien_thi
      final hienThi = eventResults['hien_thi'];
      if (hienThi != null) {
        shouldDisplay = hienThi is bool
            ? hienThi
            : hienThi.toString().toLowerCase() == 'true';
      }
    }


    // Sử dụng currentWidget đã merge config nếu có switchField
    final mergedWidget = currentWidget.copyWith(
      id: currentWidget.id,
      name: currentWidget.name,
      type: currentWidget.type,
      label: currentLabel,
      readonly: widget.isReadOnly == true ? true : currentReadonly,
      suggestText: currentSuggestText,
      validations: {...?currentWidget.validations, 'required': currentRequired},
      display: shouldDisplay,
      splitter: currentWidget.splitter,
      fieldSortOrder: currentWidget.fieldSortOrder,
      eventExpression: currentWidget.eventExpression,
      placeholder: currentWidget.placeholder,
      value: currentWidget.value,
      useTimeDefault: currentWidget.useTimeDefault,
      fontWeight: currentWidget.fontWeight,
      optionConfig: currentWidget.optionConfig,
      displayName: currentWidget.displayName,
    );

    String? currentError =
        widget.stateManager.getError(currentWidget.name.toString());

    Widget formContent = Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormItem(mergedWidget),
          if (currentError != null)
            Padding(
              padding: EdgeInsets.only(top: 4.h),
              child: Text(
                currentError,
                style: TextStyle(color: Colors.red, fontSize: 12.sp),
              ),
            ),
        ],
      ),
    );

    return formContent;
  }

  Widget _buildFormItem(FormItemInfo formWidget) {
    try {
      return FormItemWidget(
        data: formWidget,
        onChange: widget.onFieldChanged,
        stateManager: widget.stateManager,
        isReadOnly: widget.isReadOnly,
      );
    } catch (e) {
      // Return an error widget instead of crashing
      return Text('Error rendering field: ${formWidget.name ?? 'Unknown'}');
    }
  }
}
