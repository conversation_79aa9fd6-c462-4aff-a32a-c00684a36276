import 'dart:convert';

class ChartNodeResponseModel {
  final int code;
  final String message;
  final List<ChartNodeData> data;

  const ChartNodeResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ChartNodeResponseModel.fromJson(Map<String, dynamic> json) {
    return ChartNodeResponseModel(
      code: json['code'] as int,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((item) => ChartNodeData.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  factory ChartNodeResponseModel.fromJsonString(String jsonString) {
    return ChartNodeResponseModel.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }

  bool get isSuccess => code == 1;
}

class ChartNodeData {
  final String chartNodeCode;
  final String chartNodeName;
  final int chartNodeId;

  const ChartNodeData({
    required this.chartNodeCode,
    required this.chartNodeName,
    required this.chartNodeId,
  });

  factory ChartNodeData.fromJson(Map<String, dynamic> json) {
    return ChartNodeData(
      chartNodeCode: json['chartNodeCode'] as String,
      chartNodeName: json['chartNodeName'] as String,
      chartNodeId: json['chartNodeId'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'chartNodeCode': chartNodeCode,
      'chartNodeName': chartNodeName,
      'chartNodeId': chartNodeId,
    };
  }
}