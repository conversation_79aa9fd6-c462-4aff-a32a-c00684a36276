import 'package:eapprove/models/pyc/ticket_task_model.dart';

class ProcContent {
  final int? id;
  final int? ticketId;
  final String? taskId;
  final String? procInstId;
  final String? ticketEndActId;
  final String? ticketStartActId;
  final String? createdUser;
  final String? taskDefKey;
  final String? taskStatus;
  final String? procTitle;
  String? get ticketTitle => procTitle;
  final String? taskName;
  final String? serviceName;
  final int? taskPriority;
  final String? startUser;
  final int? taskCreatedTime;
  final double? slaFinish;
  final double? slaResponse;
  final String? ticketProcDefId;
  final int? slaFinishTime;
  final int? remainingTime;
  final String? priorityName;
  final String? requestCode;
  final String? companyCode;
  final double? ticketRating;
  final String? ticketStatus;
  final int? ticketCreatedTime;
  final String? chartNodeName;
  final int? serviceId;

  final List<TicketTask>? ticketTaskDtoList;

  ProcContent(
      {this.id,
      this.ticketId,
      this.taskId,
      this.procInstId,
      this.ticketEndActId,
      this.ticketStartActId,
      this.createdUser,
      this.taskDefKey,
      this.taskStatus,
      this.procTitle,
      this.taskName,
      this.serviceName,
      this.taskPriority,
      this.startUser,
      this.taskCreatedTime,
      this.slaFinish,
      this.slaResponse,
      this.ticketProcDefId,
      this.slaFinishTime,
      this.remainingTime,
      this.priorityName,
      this.requestCode,
      this.companyCode,
      this.ticketStatus,
      this.serviceId,
      this.ticketRating,
      this.ticketCreatedTime,
      this.chartNodeName,
      this.ticketTaskDtoList});

  factory ProcContent.fromJson(Map<String, dynamic> json) {
    return ProcContent(
      id: json['id'],
      ticketId: json['ticketId'],
      taskId: json['taskId'],
      procInstId: json['procInstId'],
      ticketEndActId: json['ticketEndActId'],
      ticketStartActId: json['ticketStartActId'],
      createdUser: json['createdUser'],
      taskDefKey: json['taskDefKey'],
      taskStatus: json['taskStatus'],
      procTitle: json['procTitle'],
      taskName: json['taskName'],
      serviceName: json['serviceName'],
      taskPriority: json['taskPriority'],
      startUser: json['startUser'],
      taskCreatedTime: json['taskCreatedTime'],
      slaFinish: json['slaFinish'],
      slaResponse: json['slaResponse'],
      ticketProcDefId: json['ticketProcDefId'],
      slaFinishTime: json['slaFinishTime'],
      remainingTime: json['remainingTime'],
      priorityName: json['priorityName'],
      requestCode: json['requestCode'],
      companyCode: json['companyCode'],
      serviceId: json['serviceId'],
      ticketRating: json['ticketRating'],
      ticketStatus: json['ticketStatus'],
      ticketCreatedTime: json['ticketCreatedTime'],
      chartNodeName: json['chartNodeName'],
      ticketTaskDtoList: json['ticketTaskDtoList'] != null
          ? (json['ticketTaskDtoList'] as List).map((i) => TicketTask.fromJson(i)).toList()
          : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'taskId': taskId,
      'procInstId': procInstId,
      'ticketEndActId': ticketEndActId,
      'ticketStartActId': ticketStartActId,
      'createdUser': createdUser,
      'taskDefKey': taskDefKey,
      'taskStatus': taskStatus,
      'procTitle': procTitle,
      'taskName': taskName,
      'serviceName': serviceName,
      'taskPriority': taskPriority,
      'startUser': startUser,
      'taskCreatedTime': taskCreatedTime,
      'slaFinish': slaFinish,
      'slaResponse': slaResponse,
      'ticketProcDefId': ticketProcDefId,
      'slaFinishTime': slaFinishTime,
      'remainingTime': remainingTime,
      'priorityName': priorityName,
      'requestCode': requestCode,
      'companyCode': companyCode,
      'ticketRating': ticketRating,
      'ticketStatus': ticketStatus,
      'serviceId': serviceId,
      'ticketCreatedTime': ticketCreatedTime,
      'chartNodeName': chartNodeName,
      'ticketTaskDtoList': ticketTaskDtoList?.map((e) => e.toJson()).toList(),
    };
  }
}
