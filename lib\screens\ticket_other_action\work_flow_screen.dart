import 'dart:developer';

import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:eapprove/screens/ticket_other_action/widget/work_flow_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/work_flow_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/work_flow_state.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';

class WorkflowScreen extends StatefulWidget {
final String procDefId;
final String procInstId;
final ThongTinChungModel? data;
final TaskInfo? taskInfo;
final dynamic ticketId;

const WorkflowScreen(
    {super.key, required this.procDefId, required this.procInstId, this.data, required this.ticketId, this.taskInfo});

@override
State<WorkflowScreen> createState() => _WorkflowScreenState();
}

class _WorkflowScreenState extends State<WorkflowScreen> {
int selectedNodeIndex = 0;
Map<String, String> nodeLabels = {}; // map id->name
List<Map<String, dynamic>> workflowNodes = [];

void selectNode(int index) {
  setState(() {
    selectedNodeIndex = index;
  });
}

@override
void initState() {
  super.initState();
  final ticketTaskDtoList = widget.data?.ticketTaskDtoList;
  final firstTaskDefKey = ticketTaskDtoList?.firstOrNull?.taskDefKey;
  context.read<WorkflowBloc>().add(
        LoadWorkflow(
          procDefId: widget.procDefId,
          procInstId: widget.procInstId,
          fromNodeId: '',
        ),
      );
  log('🚀 initState: dispatch LoadWorkflow', name: 'WorkflowScreen');

  final bloc = context.read<WorkflowBloc>();
  bloc.stream.listen((state) {
    if (state is WorkflowLoaded && firstTaskDefKey != null) {
      final nodes = <Map<String, dynamic>>[];
      for (final group in state.workflow.data ?? []) {
        for (final node in group.nodes ?? []) {
          nodes.add({
            'id': node.id,
            'position': node.position ?? 0,
          });
        }
      }
      nodes.sort((a, b) => (a['position'] as int).compareTo(b['position'] as int));
      final index = nodes.indexWhere((n) => n['id'] == firstTaskDefKey);
      if (index != -1) {
        setState(() {
          selectedNodeIndex = index;
        });
      }
    }
  });

  context.read<TicketProcessDetailBloc>().add(
        LoadUserTaskInfo(ticketId: widget.ticketId),
      );

  final pycBloc = context.read<PycBloc>();

  // Lấy status ticket trước
  pycBloc.add(FetchStatusTicket(
    StatusTicketRequest(
      code: "STATUS_TICKET",
      type: "system",
      page: 1,
      limit: 9999,
      search: "",
      sortBy: "id",
      sortType: "DESC",
      chartId: "",
      status: ["active"],
    ),
  ));

  pycBloc.add(FetchStatusTask(
    StatusTicketRequest(
      code: "STATUS_TASK",
      type: "system",
      page: 1,
      limit: 9999,
      search: "",
      sortBy: "id",
      sortType: "DESC",
      chartId: "",
      status: ["active"],
    ),
  ));
}

@override
Widget build(BuildContext context) {
  final isTablet = DeviceUtils.isTablet;
  return Scaffold(
    appBar: CustomAppBar(
      automaticallyImplyLeading: isTablet ? false : true,
      title: 'Luồng nghiệp vụ',
      backgroundColor: isTablet ? getColorSkin().lightBlue : getColorSkin().white,
      titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
      centerTitle: true,
      actions: [
        if (isTablet)
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: SvgPicture.asset(
              StringImage.ic_close,
              width: 24.w,
              height: 24.h,
              colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
            ),
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(),
          )
      ],
    ),
    backgroundColor: getColorSkin().whiteSmoke,
    body: WorkflowWidget(
      procDefId: widget.procDefId,
      procInstId: widget.procInstId,
      data: widget.data,
      ticketId: widget.ticketId,
      isEmbedded: false,
      showBottomInfo: true,
      onNodeSelected: (index, nodeType) {
      },
    ),
  );
}
}
