import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/models/authorize_management/list_user_info_response.dart';
import 'package:eapprove/utils/user_info_display_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_sdk/widgets/custom_info_row.dart';
import 'package:flutter_sdk/widgets/custom_radio_group.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_state.dart';

class CardInformationDetail extends StatefulWidget {
  final bool isDisabled;
  final AuthorizeItemData request;

  const CardInformationDetail({
    super.key,
    this.isDisabled = false,
    required this.request,
  });

  @override
  State<CardInformationDetail> createState() => _CardInformationDetailState();
}

class _CardInformationDetailState extends State<CardInformationDetail> {
  late Map<String, String> authorizationData;
  String authorizationEffectValue = 'during';
  Map<String, UserInfo> _userInfoMap = {};

  final List<RadioOption> authorizationEffectOptions = [
    RadioOption(
      title: 'Trong thời gian ủy quyền',
      value: 'during',
    ),
    RadioOption(
      title: 'Đến hoàn thành công việc',
      value: 'complete',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  String _getUserDisplayName(String userId) {
    final userInfo = _userInfoMap[userId];
    if (userInfo == null) return userId;
    return UserInfoDisplayUtils.getUserFullDisplay(userInfo);
  }

  void _initializeData() {
    authorizationData = {
      'Loại ủy quyền': 'Thêm mới',
      'Người ủy quyền': '${widget.request.assignUser}',
      'Người được ủy quyền': '${widget.request.assignedUser}',
      'Thời gian ủy quyền':
          '${widget.request.startDate} ▸ ${widget.request.endDate}',
      'Mã tờ trình': '${widget.request.requestCode}',
      'Phạm vi ủy quyền': widget.request.serviceRange[0].serviceName,
      'Ghi chú': '${widget.request.description}',
    };
    authorizationEffectValue =
        widget.request.effect == 0 ? 'during' : 'complete';
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserInfoBloc, UserInfoState>(
      builder: (context, userState) {
        if (userState is UserInfoLoaded) {
          _userInfoMap = {
            for (var user in userState.response.data.content)
              user.username: user
          };
        }

        return CustomExpandedList<String>(
          expandedSvgIconPath: StringImage.ic_arrow_up,
          collapsedSvgIconPath: StringImage.ic_arrow_right,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          title: "1. Thông tin",
          isExpanded: true,
          isBuildLeading: false,
          titleStyle:
              getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: _buildFormContent(),
          ),
        );
      },
    );
  }

  Widget _buildFormContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...authorizationData.entries.map((entry) {
          TextStyle? textStyle;

          if (entry.key == 'Mã tờ trình') {
            textStyle =
                getTypoSkin().label4Regular.copyWith(color: Colors.blue);
          }

          String value = entry.value;
          if (entry.key == 'Người ủy quyền') {
            value = _getUserDisplayName(entry.value);
          }

          if (entry.key == 'Người được ủy quyền') {
            value = _getUserDisplayName(entry.value);
          }

          return InfoRow(
            maxLines: 2,
            label: entry.key,
            value: value,
            textStyle: textStyle,
            textAlign: TextAlign.left,
          );
        }),
        SizedBox(height: 8.h),
        CustomRadioGroup(
          label: 'Hiệu lực ủy quyền',
          groupValue: authorizationEffectValue,
          options: authorizationEffectOptions,
          isDisabled: widget.isDisabled,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                authorizationEffectValue = value;
              });
            }
          },
        ),
      ],
    );
  }
}
