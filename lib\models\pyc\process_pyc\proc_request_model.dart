class ProcRequestModel {
  final String? search;
  final int? page;
  final int? limit;
  final List<String>? type;
  final String? taskStatus;
  final String? sortBy;
  final String? sortType;
  final List<String>? listStatus;
  final List<String>? listCreatedUser;
  final List<String>? listCompanyName;
  final List<String>? listCompanyCode;
  final List<String>? listChartNodeName;
  final List<String>? listProcTitle;
  final String? pageType;
  final String? fromDate;
  final String? toDate;
  final bool? filterChangeAssignee;
  final List<dynamic>? listRating;
  final String? createDate;
  final String? finishDate;
  final String? cancelDate;
  final String? ticketCreateDate;
  final List<String>? listUser;
  final int? serviceId;

  ProcRequestModel({
    this.search,
    this.page,
    this.limit,
    this.type,
    this.taskStatus,
    this.sortBy,
    this.sortType,
    this.listStatus,
    this.listCreatedUser,
    this.listCompanyName,
    this.listCompanyCode,
    this.listChartNodeName,
    this.listProcTitle,
    this.pageType,
    this.fromDate,
    this.toDate,
    this.filterChangeAssignee,
    this.listRating,
    this.createDate,
    this.finishDate,
    this.cancelDate,
    this.ticketCreateDate,
    this.listUser,
    this.serviceId,
  });

  factory ProcRequestModel.fromCategory(ProcPycTicketCategory category,
      {int page = 1, int limit = 5, Map<String, dynamic>? additionalParams}) {
    final data = procPycTaskStatusMapping[category]!;
    final requestData = {
      "taskStatus": data["taskStatus"],
      "listStatus": List<String>.from(data["listStatus"]),
      "search": "",
      "page": page,
      "limit": limit,
      "type": ["EXECUTION"],
      "sortBy": "taskCreatedTime",
      "sortType": "DESC",
      "listCreatedUser": ["-1"],
      "listCompanyName": ["-1"],
      "listCompanyCode": ["-1"],
      "listChartNodeName": ["-1"],
      "listProcTitle": ["-1"],
      "pageType": "task",
      "fromDate": "",
      "toDate": "",
      "filterChangeAssignee": false,
      "listRating": ["-1", 0, 1],
      "createDate": "1",
      "finishDate": "1",
      "cancelDate": "1",
      "ticketCreateDate": "1",
      "listUser": ["-1"]
    };
    return ProcRequestModel(
      search: requestData["search"],
      page: requestData["page"],
      limit: requestData["limit"],
      type: requestData["type"],
      taskStatus: requestData["taskStatus"],
      sortBy: requestData["sortBy"],
      sortType: requestData["sortType"],
      listStatus: requestData["listStatus"],
      listCreatedUser: requestData["listCreatedUser"],
      listCompanyName: requestData["listCompanyName"],
      listCompanyCode: requestData["listCompanyCode"],
      listChartNodeName: requestData["listChartNodeName"],
      listProcTitle: requestData["listProcTitle"],
      pageType: requestData["pageType"],
      fromDate: requestData["fromDate"],
      toDate: requestData["toDate"],
      filterChangeAssignee: requestData["filterChangeAssignee"],
      listRating: requestData["listRating"],
      createDate: requestData["createDate"],
      finishDate: requestData["finishDate"],
      cancelDate: requestData["cancelDate"],
      ticketCreateDate: requestData["ticketCreateDate"],
      listUser: requestData["listUser"],
      serviceId: additionalParams?["serviceId"],
    );
  }
  factory ProcRequestModel.fromJson(Map<String, dynamic> json) {
    return ProcRequestModel(
      search: json['search'],
      page: json['page'],
      limit: json['limit'],
      type: List<String>.from(json['type']),
      taskStatus: json['taskStatus'],
      sortBy: json['sortBy'],
      sortType: json['sortType'],
      listStatus: List<String>.from(json['listStatus']),
      listCreatedUser: List<String>.from(json['listCreatedUser']),
      listCompanyName: List<String>.from(json['listCompanyName']),
      listCompanyCode: List<String>.from(json['listCompanyCode']),
      listChartNodeName: List<String>.from(json['listChartNodeName']),
      listProcTitle: List<String>.from(json['listProcTitle']),
      pageType: json['pageType'],
      fromDate: json['fromDate'],
      toDate: json['toDate'],
      filterChangeAssignee: json['filterChangeAssignee'],
      listRating: List<dynamic>.from(json['listRating']),
      createDate: json['createDate'],
      finishDate: json['finishDate'],
      cancelDate: json['cancelDate'],
      ticketCreateDate: json['ticketCreateDate'],
      listUser: List<String>.from(json['listUser']),
      serviceId: json['serviceId'],
    );
  }
  ProcRequestModel copyWith({
    String? search,
    int? page,
    int? limit,
    List<String>? type,
    String? taskStatus,
    String? sortBy,
    String? sortType,
    List<String>? listStatus,
    List<String>? listCreatedUser,
    List<String>? listCompanyName,
    List<String>? listCompanyCode,
    List<String>? listChartNodeName,
    List<String>? listProcTitle,
    String? pageType,
    String? fromDate,
    String? toDate,
    bool? filterChangeAssignee,
    List<dynamic>? listRating,
    String? createDate,
    String? finishDate,
    String? cancelDate,
    String? ticketCreateDate,
    List<String>? listUser,
    int? serviceId,
  }) {
    return ProcRequestModel(
      search: search ?? this.search,
      page: page ?? this.page,
      limit: limit ?? this.limit,
      type: type ?? this.type,
      taskStatus: taskStatus ?? this.taskStatus,
      sortBy: sortBy ?? this.sortBy,
      sortType: sortType ?? this.sortType,
      listStatus: listStatus ?? this.listStatus,
      listCreatedUser: listCreatedUser ?? this.listCreatedUser,
      listCompanyName: listCompanyName ?? this.listCompanyName,
      listCompanyCode: listCompanyCode ?? this.listCompanyCode,
      listChartNodeName: listChartNodeName ?? this.listChartNodeName,
      listProcTitle: listProcTitle ?? this.listProcTitle,
      pageType: pageType ?? this.pageType,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      filterChangeAssignee: filterChangeAssignee ?? this.filterChangeAssignee,
      listRating: listRating ?? this.listRating,
      createDate: createDate ?? this.createDate,
      finishDate: finishDate ?? this.finishDate,
      cancelDate: cancelDate ?? this.cancelDate,
      ticketCreateDate: ticketCreateDate ?? this.ticketCreateDate,
      listUser: listUser ?? this.listUser,
      serviceId: serviceId ?? this.serviceId,
    );
  }

  Map<String, dynamic> toJson() => {
        'search': search,
        'page': page,
        'limit': limit,
        'type': type,
        'taskStatus': taskStatus,
        'sortBy': sortBy,
        'sortType': sortType,
        'listStatus': listStatus,
        'listCreatedUser': listCreatedUser,
        'listCompanyName': listCompanyName,
        'listCompanyCode': listCompanyCode,
        'listChartNodeName': listChartNodeName,
        'listProcTitle': listProcTitle,
        'pageType': pageType,
        'fromDate': fromDate,
        'toDate': toDate,
        'filterChangeAssignee': filterChangeAssignee,
        'listRating': listRating,
        'createDate': createDate,
        'finishDate': finishDate,
        'cancelDate': cancelDate,
        'ticketCreateDate': ticketCreateDate,
        'listUser': listUser,
        'serviceId': serviceId,
      };
}

enum ProcPycTicketCategory {
  execution,
  completed,
  returned,
  canceled,
}

const Map<ProcPycTicketCategory, Map<String, dynamic>> procPycTaskStatusMapping = {
  ProcPycTicketCategory.execution: {
    "taskStatus": "ONGOING",
    "listStatus": ["-1", "PROCESSING", "RECALLING", "ACTIVE", "OPENED"],
  },
  ProcPycTicketCategory.completed: {
    "taskStatus": "COMPLETED",
    "listStatus": ["-1", "PROCESSING", "RECALLING", "ACTIVE", "COMPLETED"],
  },
  ProcPycTicketCategory.returned: {
    "taskStatus": "RECALLED",
    "listStatus": ["DELETED_BY_RU", "AGREE_TO_RECALL", "-1"],
  },
  ProcPycTicketCategory.canceled: {
    "taskStatus": "CANCEL",
    "listStatus": ["-1", "PROCESSING", "RECALLING", "ACTIVE", "CANCEL"],
  },
};
