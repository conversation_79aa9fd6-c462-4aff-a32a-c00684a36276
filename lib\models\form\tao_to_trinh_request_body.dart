class TaoToTrinhRequestBody {
  String? name;
  String? procDefId;
  String? pdfContent;
  String? bpmPrintPhaseId;
  String? procInstId;
  String? uploadWordsChange;
  List<Zones>? zones;
  List<TaskKey>? taskKey;

  TaoToTrinhRequestBody(
      {this.name,
      this.procDefId,
      this.pdfContent,
      this.bpmPrintPhaseId,
      this.procInstId,
      this.uploadWordsChange,
      this.zones,
      this.taskKey});

  TaoToTrinhRequestBody.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    procDefId = json['procDefId'];
    pdfContent = json['pdfContent'];
    bpmPrintPhaseId = json['bpmPrintPhaseId'];
    procInstId = json['procInstId'];
    uploadWordsChange = json['uploadWordsChange'];
    if (json['zones'] != null) {
      zones = <Zones>[];
      json['zones'].forEach((v) {
        zones!.add(new Zones.fromJson(v));
      });
    }
    if (json['taskKey'] != null) {
      taskKey = <TaskKey>[];
      json['taskKey'].forEach((v) {
        taskKey!.add(new TaskKey.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['procDefId'] = this.procDefId;
    data['pdfContent'] = this.pdfContent;
    data['bpmPrintPhaseId'] = this.bpmPrintPhaseId;
    data['procInstId'] = this.procInstId;
    data['uploadWordsChange'] = this.uploadWordsChange;
    if (this.zones != null) {
      data['zones'] = this.zones!.map((v) => v.toJson()).toList();
    }
    if (this.taskKey != null) {
      data['taskKey'] = this.taskKey!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  static TaoToTrinhRequestBody? from(json) {}
}

class Zones {
  String? taskDefKey;
  int? orderSign;
  String? email;
  int? page;
  int? wResize;
  int? hResize;
  String? sign;
  int? x;
  int? y;
  int? w;
  int? h;
  int? scale;
  String? firstName;
  String? lastName;
  String? position;
  String? name;
  String? signType;
  String? chartNodeLevel;

  Zones(
      {this.taskDefKey,
      this.orderSign,
      this.email,
      this.page,
      this.wResize,
      this.hResize,
      this.sign,
      this.x,
      this.y,
      this.w,
      this.h,
      this.scale,
      this.firstName,
      this.lastName,
      this.position,
      this.name,
      this.signType,
      this.chartNodeLevel});

  Zones.fromJson(Map<String, dynamic> json) {
    taskDefKey = json['taskDefKey'];
    orderSign = json['orderSign'];
    email = json['email'];
    page = json['page'];
    wResize = json['wResize'];
    hResize = json['hResize'];
    sign = json['sign'];
    x = json['x'];
    y = json['y'];
    w = json['w'];
    h = json['h'];
    scale = json['scale'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    position = json['position'];
    name = json['name'];
    signType = json['signType'];
    chartNodeLevel = json['chartNodeLevel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['taskDefKey'] = this.taskDefKey;
    data['orderSign'] = this.orderSign;
    data['email'] = this.email;
    data['page'] = this.page;
    data['wResize'] = this.wResize;
    data['hResize'] = this.hResize;
    data['sign'] = this.sign;
    data['x'] = this.x;
    data['y'] = this.y;
    data['w'] = this.w;
    data['h'] = this.h;
    data['scale'] = this.scale;
    data['firstName'] = this.firstName;
    data['lastName'] = this.lastName;
    data['position'] = this.position;
    data['name'] = this.name;
    data['signType'] = this.signType;
    data['chartNodeLevel'] = this.chartNodeLevel;
    return data;
  }
}

class TaskKey {
  int? taskType;

  TaskKey({this.taskType});

  TaskKey.fromJson(Map<String, dynamic> json) {
    taskType = json['taskType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['taskType'] = this.taskType;
    return data;
  }
}
