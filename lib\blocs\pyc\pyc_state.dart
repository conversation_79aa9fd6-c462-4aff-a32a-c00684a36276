import 'dart:ui';

import 'package:eapprove/models/generic_respone_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_request_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/models/pyc/generic_data_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_request_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_request_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

enum PycStatus { initial, loading, loaded, error }

class PycState extends Equatable {
  final PycStatus status;
  final PycStatus serviceListStatus;
  final List<FilterServiceData> serviceList;

  final GenericResponseModel<GenericDataPyc<MyPycContent>>? myPycResponseModel;
  final GenericResponseModel<GenericDataPyc<ProcContent>>? procPycResponseModel;
  final GenericResponseModel<GenericDataPyc<ApproveContent>>? appPycResponseModel;
  final GenericResponseModel<GenericDataPyc<AssisContent>>? assisPycResponseModel;

  final GenericResponseModel<MyPycCountDataModel>? myPycCountFiltered;
  final GenericResponseModel<ProcAppCountDataModel>? procCountFiltered;
  final GenericResponseModel<ProcAppCountDataModel>? appCountFiltered;
  final GenericResponseModel<List<AssisPycCountModel>>? assisPycCountFiltered;

  final MyPycRequestModel? myPycRequestModel;
  final ProcRequestModel? procPycRequestModel;
  final ApproveRequestModel? appPycRequestModel;
  final AssisRequestModel? assisPycRequestModel;
  final RequestListService? serviceListRequestModel;

  final List<ListUserInfoDataModel> assigneeList;
  final PycStatus assigneeListStatus;
  final String? assigneeListErrorMessage;
  final ListAssigneeRequestModel? assigneeListRequestModel;

  final List<ListChartIdDataModel> chartIdList;
  final PycStatus chartIdListStatus;
  final String? chartIdListErrorMessage;

  final PycStatus orgChartListStatus;
  final List<OrgData> orgChartList;
  final String? orgChartErrorMessage;
  final ChartFilterRequestModel? orgChartRequestModel;

  final PycStatus filterStatus;
  final List<FilterData> filterList;
  final String? filterErrorMessage;
  final FilterRequestModel? filterRequestModel;

  final GenericResponseModel<GenericDataPyc<TicketContentModel>>? ticketFilterResponseModel;
  final FilterTicketRequestModel? ticketFilterRequestModel;
  final PycStatus ticketFilterStatus;
  final String? ticketFilterErrorMessage;

  final List<ListTaskDefKeyDataModel> taskDefKeyList;
  final PycStatus taskDefKeyStatus;
  final String? taskDefKeyErrorMessage;
  final ListTaskDefKeyRequestModel? taskDefKeyRequestModel;

  final List<ListUserInfoDataModel> listCreatedUser;
  final PycStatus listCreatedUserStatus;
  final String? listCreatedUserErrorMessage;
  final ListCreatedUserRequestModel? listCreatedUserRequestModel;

  final GenericResponseModel<GenericDataPyc<ListPriorityDataModel>>? priorityListResponseModel;
  final ListPriorityRequestModel? priorityListRequestModel;
  final PycStatus priorityListStatus;
  final String? priorityListErrorMessage;

  final GenericResponseModel<GenericDataPyc<SearchFilterContentModel>>? searchFilterResponseModel;
  final SearchFilterRequestModel? searchFilterRequestModel;
  final PycStatus searchFilterStatus;
  final String? searchFilterErrorMessage;

  final GenericResponseModel<GenericDataPyc<StatusContentModel>>? statusTicketResponseModel;
  final GenericResponseModel<GenericDataPyc<StatusContentModel>>? statusTaskResponseModel;
  final StatusTicketRequest? statusTicketRequestModel;
  final StatusTicketRequest? statusTaskRequestModel;
  final PycStatus statusTicketStatus;
  final PycStatus statusTaskStatus;
  final String? statusTicketErrorMessage;
  final String? statusTaskErrorMessage;

  final GenericResponseModel<MyPycCountDataModel>? myPycCountResponseModel;
  final GenericResponseModel<ProcAppCountDataModel>? procCountResponseModel;
  final GenericResponseModel<ProcAppCountDataModel>? appCountResponseModel;
  final GenericResponseModel<List<AssisPycCountModel>>? assisPycCountResponseModel;

  final MyPycCountRequestModel? myPycCountRequestModel;
  final ProcAppCountRequestModel? procCountRequestModel;
  final ProcAppCountRequestModel? appCountRequestModel;
  final AssisPycCountRequestModel? assisPycCountRequestModel;

  final PycStatus myPycCountStatus;
  final String? myPycCountErrorMessage;

  final PycStatus procAppCountStatus;
  final String? procAppCountErrorMessage;

  final PycStatus assisPycCountStatus;
  final String? assisPycCountErrorMessage;

  final String? errorMessage;
  final String? serviceListErrorMessage;

  final bool isPaginating;
  final bool hasMore;

  const PycState({
    this.myPycCountFiltered,
    this.procCountFiltered,
    this.appCountFiltered,
    this.assisPycCountFiltered,
    this.status = PycStatus.initial,
    this.serviceListStatus = PycStatus.initial,
    this.serviceList = const [],
    this.myPycResponseModel,
    this.procPycResponseModel,
    this.appPycResponseModel,
    this.assisPycResponseModel,
    this.myPycRequestModel,
    this.procPycRequestModel,
    this.appPycRequestModel,
    this.assisPycRequestModel,
    this.serviceListRequestModel,
    this.assigneeList = const [],
    this.assigneeListStatus = PycStatus.initial,
    this.assigneeListErrorMessage,
    this.assigneeListRequestModel,
    this.chartIdList = const [],
    this.chartIdListStatus = PycStatus.initial,
    this.chartIdListErrorMessage,
    this.orgChartList = const [],
    this.orgChartListStatus = PycStatus.initial,
    this.orgChartErrorMessage,
    this.orgChartRequestModel,
    this.filterStatus = PycStatus.initial,
    this.filterList = const [],
    this.filterErrorMessage,
    this.filterRequestModel,
    this.ticketFilterResponseModel,
    this.ticketFilterRequestModel,
    this.ticketFilterStatus = PycStatus.initial,
    this.ticketFilterErrorMessage,
    this.taskDefKeyList = const [],
    this.taskDefKeyStatus = PycStatus.initial,
    this.taskDefKeyErrorMessage,
    this.taskDefKeyRequestModel,
    this.listCreatedUser = const [],
    this.listCreatedUserStatus = PycStatus.initial,
    this.listCreatedUserErrorMessage,
    this.listCreatedUserRequestModel,
    this.priorityListResponseModel,
    this.priorityListRequestModel,
    this.priorityListStatus = PycStatus.initial,
    this.priorityListErrorMessage,
    this.searchFilterResponseModel,
    this.searchFilterRequestModel,
    this.searchFilterStatus = PycStatus.initial,
    this.searchFilterErrorMessage,
    this.myPycCountResponseModel,
    this.procCountResponseModel,
    this.appCountResponseModel,
    this.assisPycCountResponseModel,
    this.myPycCountRequestModel,
    this.procCountRequestModel,
    this.appCountRequestModel,
    this.assisPycCountRequestModel,
    this.myPycCountStatus = PycStatus.initial,
    this.myPycCountErrorMessage,
    this.procAppCountStatus = PycStatus.initial,
    this.procAppCountErrorMessage,
    this.assisPycCountStatus = PycStatus.initial,
    this.assisPycCountErrorMessage,
    this.statusTicketResponseModel,
    this.statusTicketRequestModel,
    this.statusTicketStatus = PycStatus.initial,
    this.statusTicketErrorMessage,
    this.statusTaskResponseModel,
    this.statusTaskRequestModel,
    this.statusTaskStatus = PycStatus.initial,
    this.statusTaskErrorMessage,
    this.errorMessage,
    this.serviceListErrorMessage,
    this.isPaginating = false,
    this.hasMore = true,
  });

  PycState copyWith({
    GenericResponseModel<MyPycCountDataModel>? myPycCountFiltered,
    GenericResponseModel<ProcAppCountDataModel>? procCountFiltered,
    GenericResponseModel<ProcAppCountDataModel>? appCountFiltered,
    GenericResponseModel<List<AssisPycCountModel>>? assisPycCountFiltered,
    PycStatus? status,
    PycStatus? serviceListStatus,
    List<FilterServiceData>? serviceList,
    GenericResponseModel<GenericDataPyc<MyPycContent>>? myPycResponseModel,
    GenericResponseModel<GenericDataPyc<ProcContent>>? procPycResponseModel,
    GenericResponseModel<GenericDataPyc<ApproveContent>>? appPycResponseModel,
    GenericResponseModel<GenericDataPyc<AssisContent>>? assisPycResponseModel,
    MyPycRequestModel? myPycRequestModel,
    ProcRequestModel? procPycRequestModel,
    ApproveRequestModel? appPycRequestModel,
    AssisRequestModel? assisPycRequestModel,
    RequestListService? serviceListRequestModel,
    List<ListUserInfoDataModel>? assigneeList,
    PycStatus? assigneeListStatus,
    String? assigneeListErrorMessage,
    ListAssigneeRequestModel? assigneeListRequestModel,
    List<ListChartIdDataModel>? chartIdList,
    PycStatus? chartIdListStatus,
    String? chartIdListErrorMessage,
    PycStatus? orgChartListStatus,
    List<OrgData>? orgChartList,
    String? orgChartErrorMessage,
    ChartFilterRequestModel? orgChartRequestModel,
    PycStatus? filterStatus,
    List<FilterData>? filterList,
    String? filterErrorMessage,
    FilterRequestModel? filterRequestModel,
    GenericResponseModel<GenericDataPyc<TicketContentModel>>? ticketFilterResponseModel,
    FilterTicketRequestModel? ticketFilterRequestModel,
    PycStatus? ticketFilterStatus,
    String? ticketFilterErrorMessage,
    List<ListTaskDefKeyDataModel>? taskDefKeyList,
    PycStatus? taskDefKeyStatus,
    String? taskDefKeyErrorMessage,
    ListTaskDefKeyRequestModel? taskDefKeyRequestModel,
    List<ListUserInfoDataModel>? listCreatedUser,
    PycStatus? listCreatedUserStatus,
    String? listCreatedUserErrorMessage,
    ListCreatedUserRequestModel? listCreatedUserRequestModel,
    GenericResponseModel<GenericDataPyc<ListPriorityDataModel>>? priorityListResponseModel,
    ListPriorityRequestModel? priorityListRequestModel,
    PycStatus? priorityListStatus,
    String? priorityListErrorMessage,
    GenericResponseModel<GenericDataPyc<SearchFilterContentModel>>? searchFilterResponseModel,
    SearchFilterRequestModel? searchFilterRequestModel,
    PycStatus? searchFilterStatus,
    String? searchFilterErrorMessage,
    GenericResponseModel<MyPycCountDataModel>? myPycCountResponseModel,
    GenericResponseModel<ProcAppCountDataModel>? procCountResponseModel,
    GenericResponseModel<ProcAppCountDataModel>? appCountResponseModel,
    GenericResponseModel<List<AssisPycCountModel>>? assisPycCountResponseModel,
    MyPycCountRequestModel? myPycCountRequestModel,
    ProcAppCountRequestModel? procCountRequestModel,
    ProcAppCountRequestModel? appCountRequestModel,
    AssisPycCountRequestModel? assisPycCountRequestModel,
    PycStatus? myPycCountStatus,
    String? myPycCountErrorMessage,
    PycStatus? procAppCountStatus,
    String? procAppCountErrorMessage,
    PycStatus? assisPycCountStatus,
    String? assisPycCountErrorMessage,
    GenericResponseModel<GenericDataPyc<StatusContentModel>>? statusTicketResponseModel,
    StatusTicketRequest? statusTicketRequestModel,
    PycStatus? statusTicketStatus,
    String? statusTicketErrorMessage,
    GenericResponseModel<GenericDataPyc<StatusContentModel>>? statusTaskResponseModel,
    StatusTicketRequest? statusTaskRequestModel,
    PycStatus? statusTaskStatus,
    String? statusTaskErrorMessage,
    String? errorMessage,
    String? serviceListErrorMessage,
    bool? isPaginating,
    bool? hasMore,
  }) {
    return PycState(
      myPycCountFiltered: myPycCountFiltered ?? this.myPycCountFiltered,
      procCountFiltered: procCountFiltered ?? this.procCountFiltered,
      appCountFiltered: appCountFiltered ?? this.appCountFiltered,
      assisPycCountFiltered: assisPycCountFiltered ?? this.assisPycCountFiltered,
      status: status ?? this.status,
      serviceListStatus: serviceListStatus ?? this.serviceListStatus,
      serviceList: serviceList ?? this.serviceList,
      myPycResponseModel: myPycResponseModel ?? this.myPycResponseModel,
      procPycResponseModel: procPycResponseModel ?? this.procPycResponseModel,
      appPycResponseModel: appPycResponseModel ?? this.appPycResponseModel,
      assisPycResponseModel: assisPycResponseModel ?? this.assisPycResponseModel,
      myPycRequestModel: myPycRequestModel ?? this.myPycRequestModel,
      procPycRequestModel: procPycRequestModel ?? this.procPycRequestModel,
      appPycRequestModel: appPycRequestModel ?? this.appPycRequestModel,
      assisPycRequestModel: assisPycRequestModel ?? this.assisPycRequestModel,
      serviceListRequestModel: serviceListRequestModel ?? this.serviceListRequestModel,
      assigneeList: assigneeList ?? this.assigneeList,
      assigneeListStatus: assigneeListStatus ?? this.assigneeListStatus,
      assigneeListErrorMessage: assigneeListErrorMessage ?? this.assigneeListErrorMessage,
      assigneeListRequestModel: assigneeListRequestModel ?? this.assigneeListRequestModel,
      errorMessage: errorMessage ?? this.errorMessage,
      chartIdList: chartIdList ?? this.chartIdList,
      chartIdListStatus: chartIdListStatus ?? this.chartIdListStatus,
      chartIdListErrorMessage: chartIdListErrorMessage ?? this.chartIdListErrorMessage,
      orgChartList: orgChartList ?? this.orgChartList,
      orgChartListStatus: orgChartListStatus ?? this.orgChartListStatus,
      orgChartErrorMessage: orgChartErrorMessage ?? this.orgChartErrorMessage,
      orgChartRequestModel: orgChartRequestModel ?? this.orgChartRequestModel,
      filterStatus: filterStatus ?? this.filterStatus,
      filterList: filterList ?? this.filterList,
      filterErrorMessage: filterErrorMessage ?? this.filterErrorMessage,
      filterRequestModel: filterRequestModel ?? this.filterRequestModel,
      ticketFilterResponseModel: ticketFilterResponseModel ?? this.ticketFilterResponseModel,
      ticketFilterRequestModel: ticketFilterRequestModel ?? this.ticketFilterRequestModel,
      ticketFilterStatus: ticketFilterStatus ?? this.ticketFilterStatus,
      ticketFilterErrorMessage: ticketFilterErrorMessage ?? this.ticketFilterErrorMessage,
      taskDefKeyList: taskDefKeyList ?? this.taskDefKeyList,
      taskDefKeyStatus: taskDefKeyStatus ?? this.taskDefKeyStatus,
      taskDefKeyErrorMessage: taskDefKeyErrorMessage ?? this.taskDefKeyErrorMessage,
      taskDefKeyRequestModel: taskDefKeyRequestModel ?? this.taskDefKeyRequestModel,
      listCreatedUser: listCreatedUser ?? this.listCreatedUser,
      listCreatedUserStatus: listCreatedUserStatus ?? this.listCreatedUserStatus,
      listCreatedUserErrorMessage: listCreatedUserErrorMessage ?? this.listCreatedUserErrorMessage,
      listCreatedUserRequestModel: listCreatedUserRequestModel ?? this.listCreatedUserRequestModel,
      serviceListErrorMessage: serviceListErrorMessage ?? this.serviceListErrorMessage,
      priorityListResponseModel: priorityListResponseModel ?? this.priorityListResponseModel,
      priorityListRequestModel: priorityListRequestModel ?? this.priorityListRequestModel,
      priorityListStatus: priorityListStatus ?? this.priorityListStatus,
      priorityListErrorMessage: priorityListErrorMessage ?? this.priorityListErrorMessage,
      searchFilterResponseModel: searchFilterResponseModel ?? this.searchFilterResponseModel,
      searchFilterRequestModel: searchFilterRequestModel ?? this.searchFilterRequestModel,
      searchFilterStatus: searchFilterStatus ?? this.searchFilterStatus,
      searchFilterErrorMessage: searchFilterErrorMessage ?? this.searchFilterErrorMessage,
      myPycCountResponseModel: myPycCountResponseModel ?? this.myPycCountResponseModel,
      procCountResponseModel: procCountResponseModel ?? this.procCountResponseModel,
      appCountResponseModel: appCountResponseModel ?? this.appCountResponseModel,
      assisPycCountResponseModel: assisPycCountResponseModel ?? this.assisPycCountResponseModel,
      myPycCountRequestModel: myPycCountRequestModel ?? this.myPycCountRequestModel,
      procCountRequestModel: procCountRequestModel ?? this.procCountRequestModel,
      appCountRequestModel: appCountRequestModel ?? this.appCountRequestModel,
      assisPycCountRequestModel: assisPycCountRequestModel ?? this.assisPycCountRequestModel,
      myPycCountStatus: myPycCountStatus ?? this.myPycCountStatus,
      myPycCountErrorMessage: myPycCountErrorMessage ?? this.myPycCountErrorMessage,
      procAppCountStatus: procAppCountStatus ?? this.procAppCountStatus,
      procAppCountErrorMessage: procAppCountErrorMessage ?? this.procAppCountErrorMessage,
      assisPycCountStatus: assisPycCountStatus ?? this.assisPycCountStatus,
      assisPycCountErrorMessage: assisPycCountErrorMessage ?? this.assisPycCountErrorMessage,
      statusTicketResponseModel: statusTicketResponseModel ?? this.statusTicketResponseModel,
      statusTicketRequestModel: statusTicketRequestModel ?? this.statusTicketRequestModel,
      statusTicketStatus: statusTicketStatus ?? this.statusTicketStatus,
      statusTicketErrorMessage: statusTicketErrorMessage ?? this.statusTicketErrorMessage,
      statusTaskResponseModel: statusTaskResponseModel ?? this.statusTaskResponseModel,
      statusTaskRequestModel: statusTaskRequestModel ?? this.statusTaskRequestModel,
      statusTaskStatus: statusTaskStatus ?? this.statusTaskStatus,
      statusTaskErrorMessage: statusTaskErrorMessage ?? this.statusTaskErrorMessage,
      isPaginating: isPaginating ?? this.isPaginating,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  @override
  List<Object?> get props => [
        myPycCountFiltered,
        procCountFiltered,
        appCountFiltered,
        assisPycCountFiltered,
        status,
        serviceListStatus,
        serviceList,
        myPycResponseModel,
        procPycResponseModel,
        appPycResponseModel,
        assisPycResponseModel,
        myPycRequestModel,
        procPycRequestModel,
        appPycRequestModel,
        assisPycRequestModel,
        serviceListRequestModel,
        assigneeList,
        assigneeListStatus,
        assigneeListErrorMessage,
        assigneeListRequestModel,
        chartIdList,
        chartIdListStatus,
        chartIdListErrorMessage,
        errorMessage,
        orgChartList,
        orgChartListStatus,
        orgChartErrorMessage,
        orgChartRequestModel,
        filterStatus,
        filterList,
        filterErrorMessage,
        filterRequestModel,
        ticketFilterResponseModel,
        ticketFilterRequestModel,
        ticketFilterStatus,
        ticketFilterErrorMessage,
        taskDefKeyList,
        taskDefKeyStatus,
        taskDefKeyErrorMessage,
        taskDefKeyRequestModel,
        listCreatedUser,
        listCreatedUserStatus,
        listCreatedUserErrorMessage,
        listCreatedUserRequestModel,
        priorityListResponseModel,
        priorityListRequestModel,
        priorityListStatus,
        priorityListErrorMessage,
        serviceListErrorMessage,
        isPaginating,
        hasMore,
        searchFilterResponseModel,
        searchFilterRequestModel,
        searchFilterStatus,
        searchFilterErrorMessage,
        myPycCountResponseModel,
        procCountResponseModel,
        appCountResponseModel,
        assisPycCountResponseModel,
        myPycCountRequestModel,
        procCountRequestModel,
        appCountRequestModel,
        assisPycCountRequestModel,
        myPycCountStatus,
        myPycCountErrorMessage,
        procAppCountStatus,
        procAppCountErrorMessage,
        assisPycCountStatus,
        assisPycCountErrorMessage,
        statusTicketResponseModel,
        statusTicketRequestModel,
        statusTicketStatus,
        statusTicketErrorMessage,
        statusTaskResponseModel,
        statusTaskRequestModel,
        statusTaskStatus,
        statusTaskErrorMessage,
      ];
}

extension PycStateExt on PycState {
  static const Map<String, String> _statusFixMap = {
    "COMPLETED": "COMPLETE",
    "CLOSED": "CLOSE",
  };
  static String fixStatusCode(String? status) {
    final upper = status?.toUpperCase();
    return _statusFixMap[upper] ?? upper ?? '';
  }

  /// TICKET STATUS CONFIG MAP
  Map<String, String> get statusConfigMap {
    final list = statusTicketResponseModel?.data.content ?? [];
    return {
      for (var e in list)
        if (e.configValue != null && e.configName != null) e.configValue!: e.configName!
    };
  }

  /// TASK STATUS CONFIG MAP
  Map<String, String> get statusTaskConfigMap {
    final list = statusTaskResponseModel?.data.content ?? [];
    return {
      for (var e in list)
        if (e.configValue != null && e.configName != null) e.configValue!: e.configName!
    };
  }

  /// DÙNG CHUNG CHO TICKET (mặc định)
  String? getStatusLabel(String? status) => getStatusTicketLabel(status);

  /// LABEL TICKET
  String? getStatusTicketLabel(String? status) {
    final upperStatus = status?.toUpperCase();
    final fixedStatus = _statusFixMap[upperStatus] ?? upperStatus;
    return statusConfigMap[fixedStatus];
  }

  /// LABEL TASK
  String? getStatusTaskLabel(String? status) {
    final upperStatus = status?.toUpperCase();
    final fixedStatus = _statusFixMap[upperStatus] ?? upperStatus;
    return statusTaskConfigMap[fixedStatus];
  }

  /// COLOR (TICKET)
  Color getStatusColor(String? status) {
    final fixedStatus = _statusFixMap[status] ?? status;
    switch (fixedStatus) {
      case "PROCESSING":
      case "OPENED":
      case "ACTIVE":
        return getColorSkin().primaryBlue;
      case "RECALLING":
      case "ADDITIONAL_REQUEST":
      case "AGREE_TO_RECALL":
      case "DELETED_BY_RU":
      case "RECALLED":
      case "WAIT":
        return getColorSkin().orange;
      case "COMPLETE":
      case "CLOSE":
        return getColorSkin().successPrimary;
      case "CANCEL":
        return getColorSkin().red;
      case "DRAFT":
      case "FOLLOWED":
        return getColorSkin().primaryText;
      default:
        return getColorSkin().primaryText;
    }
  }

  /// BACKGROUND COLOR (TICKET)
  Color getStatusBackgroundColor(String? status) {
    final fixedStatus = _statusFixMap[status] ?? status;
    switch (fixedStatus) {
      case "PROCESSING":
      case "OPENED":
      case "ACTIVE":
        return getColorSkin().blue2;
      case "RECALLING":
      case "ADDITIONAL_REQUEST":
      case "AGREE_TO_RECALL":
      case "DELETED_BY_RU":
      case "RECALLED":
      case "WAIT":
        return getColorSkin().orange2;
      case "COMPLETE":
      case "CLOSE":
        return getColorSkin().green2;
      case "CANCEL":
        return getColorSkin().red2;
      case "DRAFT":
      case "FOLLOWED":
        return getColorSkin().grey2;
      default:
        return getColorSkin().grey2;
    }
  }

  /// COLOR (TASK)
  Color getStatusTaskColor(String? status) {
    final fixedStatus = _statusFixMap[status] ?? status;
    switch (fixedStatus) {
      case "WAIT":
      case "RECALLING":
        return getColorSkin().orange;
      case "PROCESSING":
      case "ACTIVE":
        return getColorSkin().primaryBlue;
      case "COMPLETE":
      case "CLOSE":
        return getColorSkin().successPrimary;
      case "CANCEL":
        return getColorSkin().red;
      default:
        return getColorSkin().primaryText;
    }
  }

  /// BACKGROUND COLOR (TASK)
  Color getStatusTaskBackgroundColor(String? status) {
    final fixedStatus = _statusFixMap[status] ?? status;
    switch (fixedStatus) {
      case "WAIT":
      case "RECALLING":
        return getColorSkin().orange2;
      case "PROCESSING":
      case "ACTIVE":
        return getColorSkin().blue2;
      case "COMPLETE":
      case "CLOSE":
        return getColorSkin().green2;
      case "CANCEL":
        return getColorSkin().red2;
      default:
        return getColorSkin().grey2;
    }
  }
}
