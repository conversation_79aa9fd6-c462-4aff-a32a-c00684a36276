import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';

class NoteTextField extends StatelessWidget {
  final String initialValue;
  final Function(String) onChanged;

  const NoteTextField({
    super.key,
    required this.initialValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextForm(
      maxLines: 4,
      labelText: "Mô",
      hintText: "Mô tả nội dung công việc ticket này",
      onChanged: (value) {
        if (value != null) {
          onChanged(value);
        }
      },
      initialValue: initialValue,
      filled: true,
      fillColor: Colors.white,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 12.h,
      ),
    );
  }
}