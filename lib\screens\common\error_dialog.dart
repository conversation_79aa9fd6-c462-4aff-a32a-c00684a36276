import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:eapprove/assets/StringIcon.dart';

class ErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onRetry;
  final bool showDialog;
  final Widget? child;

  const ErrorDialog({
    super.key,
    this.title = 'Đã có lỗi xảy ra',
    this.message = 'Bạn vui lòng thử lại sau',
    required this.onRetry,
    this.showDialog = true,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    if (!showDialog) {
      return child ?? const SizedBox.shrink();
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      elevation: 0,
      backgroundColor: Colors.white,
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 70.w,
              height: 70.w,
              decoration: const BoxDecoration(
                // color: Color(0xFFE53935),
                shape: BoxShape.circle,
              ),
              child: SvgPicture.asset(
                StringImage.ic_exclamation,
                width: 40.w,
                height: 40.w,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(height: 20.h),
            Text(
              title,
              style: getTypoSkin().bodyRegular14Bold.copyWith(
                color: getColorSkin().black,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 6.h),
            Text(
              message,
              style: getTypoSkin().buttonRegular14.copyWith(
                color: getColorSkin().black,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            // "Thử lại" button
            SizedBox(
              width: 120.w,
              child: FFilledButton(
                backgroundColor: getColorSkin().primaryBlue,
                onPressed: () {
                  Navigator.of(context).pop(); // Close the dialog
                  onRetry();
                },
                size: FButtonSize.size40,
                child: Text(
                  'Thử lại',
                  style: getTypoSkin().medium16.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void showErrorDialog({
  required BuildContext context,
  required VoidCallback onRetry,
  String? title,
  String? message,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => ErrorDialog(
      title: title ?? 'Đã có lỗi xảy ra',
      message: message ?? 'Bạn vui lòng thử lại sau',
      onRetry: onRetry,
    ),
  );
}

class ErrorWrapper extends StatelessWidget {
  final bool hasError;
  final Widget child;
  final VoidCallback onRetry;
  final String? title;
  final String? message;

  const ErrorWrapper({
    super.key,
    required this.hasError,
    required this.child,
    required this.onRetry,
    this.title,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    if (hasError) {
      // Show dialog on the next frame to avoid build issues
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (dialogContext) => ErrorDialog(
            title: title ?? 'Đã có lỗi xảy ra',
            message: message ?? 'Bạn vui lòng thử lại sau',
            onRetry: onRetry,
            showDialog: true,
          ),
        );
      });
      
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 48.w,
              color: getColorSkin().ink1,
            ),
            SizedBox(height: 16.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Text(
                message ?? 'Bạn vui lòng thử lại sau',
                style: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().ink1,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 16.h),
            FFilledButton(
              backgroundColor: getColorSkin().primaryBlue,
              onPressed: onRetry,
              size: FButtonSize.size40,
              child: Text(
                'Thử lại',
                style: getTypoSkin().medium16.copyWith(
                  color: getColorSkin().white,
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    return child;
  }
} 