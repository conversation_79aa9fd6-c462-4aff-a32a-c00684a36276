class BpmProcInstCreateResponseModel {
  int? code;
  String? message;
  Data? data;

  BpmProcInstCreateResponseModel({this.code, this.message, this.data});

  BpmProcInstCreateResponseModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = this.code;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? startKey;
  String? requestCode;
  String? procInstId;
  int? ticketId;
  List<dynamic>? actionApiResults;

  Data({
    this.startKey,
    this.requestCode,
    this.procInstId,
    this.ticketId,
    this.actionApiResults,
  });

  Data.fromJson(Map<String, dynamic> json) {
    startKey = json['startKey'];
    requestCode = json['requestCode'];
    procInstId = json['procInstId'];
    ticketId = json['ticketId'];
    actionApiResults = json['actionApiResults'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['startKey'] = this.startKey;
    data['requestCode'] = this.requestCode;
    data['procInstId'] = this.procInstId;
    data['ticketId'] = this.ticketId;
    if (this.actionApiResults != null) {
      data['actionApiResults'] = this.actionApiResults;
    }
    return data;
  }
}