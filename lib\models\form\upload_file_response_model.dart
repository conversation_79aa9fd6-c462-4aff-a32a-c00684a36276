class UploadSingleFileResponseModel {
  int? code;
  String? message;
  Data? data;
  String? name;     

  UploadSingleFileResponseModel({this.code, this.message, this.data, this.name});

  UploadSingleFileResponseModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['name'] = this.name;
    return data;
  }
}

class Data {
  String? fileName;
  String? type;
  String? downloadUrl;
  String? contentType;
  int? size;

  Data(
      {this.fileName,
        this.type,
        this.downloadUrl,
        this.contentType,
        this.size});

  Data.fromJson(Map<String, dynamic> json) {
    fileName = json['fileName'];
    type = json['type'];
    downloadUrl = json['downloadUrl'];
    contentType = json['contentType'];
    size = json['size'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['fileName'] = this.fileName;
    data['type'] = this.type;
    data['downloadUrl'] = this.downloadUrl;
    data['contentType'] = this.contentType;
    data['size'] = this.size;
    return data;
  }
}

class UploadMultiFileResponseModel {
  int? code;
  String? message;
  List<Data>? data;

  UploadMultiFileResponseModel({this.code, this.message, this.data});

  UploadMultiFileResponseModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
