import 'package:equatable/equatable.dart';
import 'package:eapprove/models/ticket_other_action/work_flow_respone_model.dart';

abstract class WorkflowState extends Equatable {
  const WorkflowState();

  @override
  List<Object?> get props => [];
}

class WorkflowInitial extends WorkflowState {
  const WorkflowInitial();
}

class WorkflowLoading extends WorkflowState {
  const WorkflowLoading();
}

class WorkflowLoaded extends WorkflowState {
  final WorkflowResponse workflow;

  const WorkflowLoaded(this.workflow);

  @override
  List<Object?> get props => [workflow];
}

class WorkflowError extends WorkflowState {
  final String message;

  const WorkflowError(this.message);

  @override
  List<Object?> get props => [message];
}

class WorkflowEmpty extends WorkflowState {
  const WorkflowEmpty();
}
