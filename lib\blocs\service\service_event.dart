import 'package:equatable/equatable.dart';

abstract class ServiceEvent extends Equatable {
  const ServiceEvent();

  @override
  List<Object?> get props => [];
}

class ServiceFetchRoot extends ServiceEvent {
  const ServiceFetchRoot();
}

class ServiceFetchChild extends ServiceEvent {
  final int parentId;

  const ServiceFetchChild(this.parentId);

  @override
  List<Object?> get props => [parentId];
}

class SearchService extends ServiceEvent {
  final String searchKey;
  const SearchService(this.searchKey);

  @override
  List<Object?> get props => [searchKey];
}

class ShowSearchUI extends ServiceEvent {
  const ShowSearchUI();
}

class UpdateSearchTerm extends ServiceEvent {
  final String searchTerm;
  const UpdateSearchTerm(this.searchTerm);

  @override
  List<Object?> get props => [searchTerm];
}

class ClearSearchUI extends ServiceEvent {
  const ClearSearchUI();
}

class ToggleSearchVisibility extends ServiceEvent {
  final bool isVisible;
  const ToggleSearchVisibility(this.isVisible);

  @override
  List<Object?> get props => [isVisible];
}

class DownloadPdf extends ServiceEvent {
  final String fileName;
  const DownloadPdf(this.fileName);

  @override
  List<Object?> get props => [fileName];
}

class OpenWebUrl extends ServiceEvent {
  final String url;
  const OpenWebUrl(this.url);

  @override
  List<Object?> get props => [url];
}

class ClearPdfStatus extends ServiceEvent {
  const ClearPdfStatus();
}

class CancelDownload extends ServiceEvent {
  const CancelDownload();
}

// In service_event.dart
class ClearServiceState extends ServiceEvent {
  const ClearServiceState();
}

//for form

