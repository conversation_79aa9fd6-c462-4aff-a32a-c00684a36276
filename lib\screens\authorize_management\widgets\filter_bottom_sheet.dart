import 'package:eapprove/widgets/custom_dropdown_multi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_bottomsheet.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/authorize_management/data_filter/filter_data_authorize_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_state.dart';
import 'package:eapprove/models/authorize_management/filter_data_authorize_response.dart';
import 'package:eapprove/models/authorize_management/list_user_info_response.dart';
import 'package:eapprove/utils/user_info_display_utils.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'package:eapprove/widgets/custom_datepicker.dart';

class FilterBottomSheet extends StatefulWidget {
  final Function(Map<String, dynamic>)? onApplyFilter;
  final Map<String, dynamic>? initialFilters;

  const FilterBottomSheet({
    super.key,
    this.onApplyFilter,
    this.initialFilters,
  });

  static Future<void> show({
    required BuildContext context,
    Function(Map<String, dynamic>)? onApplyFilter,
    Map<String, dynamic>? initialFilters,
  }) {
    return CustomBottomSheet.show(
      maxHeight: MediaQuery.of(context).size.height * 0.9,
      titlePadding: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.zero,
      titleAlign: TextAlign.left,
      context: context,
      closeIconAfterTitle: true,
      title: 'Bộ lọc',
      closeIcon: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => Navigator.of(context).pop(),
      ),
      isScrollControlled: true,
      enableDrag: false,
      isDismissible: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      children: MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: context.read<FilterDataAuthorizeBloc>(),
          ),
          BlocProvider.value(
            value: context.read<UserInfoBloc>(),
          ),
        ],
        child: FilterBottomSheet(
          onApplyFilter: onApplyFilter,
          initialFilters: initialFilters,
        ),
      ),
    );
  }

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  Box box = Hive.box('authentication');

  final Map<String, dynamic> _filterValues = {
    'authorizer': ['-1'],
    'authorized': ['-1'],
    'serviceScope': ['-1'],
    'startDateUyQuyen': null,
    'endDateUyQuyen': null,
    'status': [-3, -2, -1, 0, 1, -4],
    'creator': ['-1'],
    'createStartDate': null,
    'createEndDate': null,
    'listDateFilter': [],
  };

  final Map<String, String> _keyLabels = {
    'authorizer': 'người ủy quyền',
    'authorized': 'người được ủy quyền',
    'serviceScope': 'phạm vi dịch vụ',
    'status': 'trạng thái',
    'creator': 'người tạo',
  };

  final Map<String, String> _statusLabels = {
    '1': 'Hiệu lực',
    '0': 'Hiệu lực',
    '-1': 'Hết hiệu lực',
    '-2': 'Hết hiệu lực',
    '-3': 'Hết hiệu lực',
    '-4': 'Hết hiệu lực',
  };

  Map<String, UserInfo> _userInfoMap = {};

  String _getPlaceholder(String key) {
    return 'Chọn ${_keyLabels[key] ?? key}';
  }

  @override
  void initState() {
    super.initState();
    _loadFilterData();
    _initializeFilterValues();
  }

  void _initializeFilterValues() {
    if (widget.initialFilters != null) {
      // Create a new map to avoid reference issues
      final Map<String, dynamic> initialValues =
          Map<String, dynamic>.from(widget.initialFilters!);

      // Handle date values
      if (initialValues.containsKey('listDateFilter')) {
        final List<dynamic> dateFilters =
            initialValues['listDateFilter'] as List<dynamic>;
        for (var filter in dateFilters) {
          if (filter['type'] == 'startDate') {
            if (filter['fromDate'] != null &&
                filter['fromDate'].toString().isNotEmpty) {
              _filterValues['startDateUyQuyen'] =
                  _parseDateFromApi(filter['fromDate']);
            }
            if (filter['toDate'] != null &&
                filter['toDate'].toString().isNotEmpty) {
              _filterValues['endDateUyQuyen'] =
                  _parseDateFromApi(filter['toDate']);
            }
          } else if (filter['type'] == 'createdDate') {
            if (filter['fromDate'] != null &&
                filter['fromDate'].toString().isNotEmpty) {
              _filterValues['createStartDate'] =
                  _parseDateFromApi(filter['fromDate']);
            }
            if (filter['toDate'] != null &&
                filter['toDate'].toString().isNotEmpty) {
              _filterValues['createEndDate'] =
                  _parseDateFromApi(filter['toDate']);
            }
          }
        }
      }

      // Handle other filter values
      _filterValues['authorizer'] = initialValues['assignUser'] ?? ['-1'];
      _filterValues['authorized'] = initialValues['assignedUser'] ?? ['-1'];
      _filterValues['serviceScope'] = initialValues['serviceScope'] ?? ['-1'];
      _filterValues['status'] =
          initialValues['status'] ?? [-3, -2, -1, 0, 1, -4];
      _filterValues['creator'] = initialValues['listCreatedUser'] ?? ['-1'];
      _filterValues['listDateFilter'] = initialValues['listDateFilter'] ?? [];
    }
  }

  DateTime _parseDateFromApi(String dateStr) {
    final parts = dateStr.split('-');
    return DateTime(
      int.parse(parts[0]), // year
      int.parse(parts[1]), // month
      int.parse(parts[2]), // day
    );
  }

  void _loadFilterData() {
    final username = box.get('username', defaultValue: '');

    context.read<FilterDataAuthorizeBloc>().add(
          LoadFilterDataAuthorize(
            search: '',
            sortBy: 'createdDate',
            sortType: 'DESC',
            limit: '10',
            page: '1',
            totalPages: 0,
            totalElements: 0,
            listAssignUser: const ['-1'],
            listAssignedUser: const ['-1'],
            status: const [-3, -2, -1, 0, 1, -4],
            listCreatedUser: const ['-1'],
            listUpdatedUser: const ['-1'],
            listDateFilter: const [
              {'fromDate': '', 'toDate': '', 'type': ''}
            ],
            userLogin: username,
          ),
        );
  }

  String _getUserDisplayName(String userId) {
    final userInfo = _userInfoMap[userId];
    if (userInfo == null) return userId;

    return UserInfoDisplayUtils.getUserDisplayWithUsername(userInfo);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserInfoBloc, UserInfoState>(
      builder: (context, userState) {
        return BlocBuilder<FilterDataAuthorizeBloc, FilterDataAuthorizeState>(
          builder: (context, filterState) {
            if (filterState is FilterDataAuthorizeLoading ||
                userState is UserInfoLoading) {
              return Center(child: AppConstraint.buildLoading(context));
            }

            if (filterState is FilterDataAuthorizeError) {
              return Center(child: Text('Error: ${filterState.message}'));
            }

            if (userState is UserInfoError) {
              return Center(child: Text('Error: ${userState.message}'));
            }

            final filterData = filterState is FilterDataAuthorizeLoaded
                ? filterState.filterData.data
                : null;
            print('FilterData: $filterData');

            if (userState is UserInfoLoaded) {
              _userInfoMap = {
                for (var user in userState.response.data.content)
                  user.username: user
              };
              print('Updated UserInfoMap: $_userInfoMap');
            }

            return SizedBox(
              height: MediaQuery.of(context).size.height * 0.8,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 6.h),
                        child: Column(
                          children: [
                            _buildFilterItem(
                              label: 'Người ủy quyền',
                              child: _buildDropdown(
                                'authorizer',
                                filterData?.assignUser ?? [],
                              ),
                            ),
                            _buildFilterItem(
                              label: 'Người được ủy quyền',
                              child: _buildDropdown(
                                'authorized',
                                filterData?.assignedUser ?? [],
                              ),
                            ),
                            _buildFilterItem(
                              label: 'Phạm vi dịch vụ',
                              child: _buildServiceRangeDropdown(
                                'serviceScope',
                                filterData?.serviceRange ?? [],
                              ),
                            ),
                            _buildFilterItem(
                              label: 'Thời gian ủy quyền',
                              child: _buildDateRangePicker(
                                  'startDateUyQuyen', 'endDateUyQuyen'),
                            ),
                            _buildFilterItem(
                              label: 'Trạng thái',
                              child: _buildDropdown(
                                'status',
                                filterData?.status
                                        .map((e) => e.toString())
                                        .toList() ??
                                    [],
                              ),
                            ),
                            _buildFilterItem(
                              label: 'Người tạo',
                              child: _buildDropdown(
                                'creator',
                                filterData?.createdUser ?? [],
                              ),
                            ),
                            _buildFilterItem(
                              label: 'Thời gian tạo',
                              child: _buildDateRangePicker(
                                  'createStartDate', 'createEndDate'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  _buildActionButtons(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFilterItem({required String label, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.normal,
          ),
        ),
        SizedBox(height: 8.h),
        child,
      ],
    );
  }

  Widget _buildDropdown(String key, List<String> options) {
    if (options.isEmpty) {
      return CustomDropdownMulti(
        label: '',
        showLabel: false,
        placeholder: _getPlaceholder(key),
        options: const [],
        defaultValues: [],
        dropdownHeight: 48.h,
        onSelected: (values) {
          setState(() {
            if (values.isEmpty) {
              _filterValues[key] =
                  key == 'status' ? ['-3', '-2', '-1', '0', '1', '-4'] : ['-1'];
              return;
            }
            _filterValues[key] = values.map((v) => v.value).toList();
          });
        },
      );
    }

    List<SelectItem> selectItems;
    if (key == 'status') {
      // Group status values by their labels
      final Map<String, List<String>> groupedStatus = {};
      for (var status in options) {
        final label = _statusLabels[status] ?? status;
        groupedStatus.putIfAbsent(label, () => []).add(status);
      }

      // Create unique items for each label
      selectItems = [
        SelectItem(
          label: 'Tất cả',
          value: '[-3,-2,-1,0,1,-4]',
        ),
        ...groupedStatus.entries.map((entry) {
          return SelectItem(
            label: entry.key,
            value: '[${entry.value.join(',')}]',
          );
        }).toList()
      ];
    } else if (key == 'authorizer' || key == 'authorized' || key == 'creator') {
      // User-related dropdowns with user info mapping
      selectItems = [
        SelectItem(label: 'Tất cả', value: '-1'),
        ...options.map((userId) {
          return SelectItem(
            label: _getUserDisplayName(userId),
            value: userId,
          );
        }).toList()
      ];
    } else {
      selectItems = [
        SelectItem(label: 'Tất cả', value: '-1'),
        ...options.map((option) {
          return SelectItem(label: option, value: option);
        }).toList()
      ];
    }

    List<SelectItem> selectedItems = [];
    if (_filterValues[key] != null) {
      try {
        if (key == 'status') {
          final String compareValue = '[${_filterValues[key].join(',')}]';
          selectedItems =
              selectItems.where((item) => item.value == compareValue).toList();
        } else {
          if (_filterValues[key] is List) {
            selectedItems = selectItems
                .where(
                    (item) => (_filterValues[key] as List).contains(item.value))
                .toList();
          } else {
            final item = selectItems.firstWhere(
              (item) => item.value == _filterValues[key].toString(),
              orElse: () => selectItems.first,
            );
            selectedItems = [item];
          }
        }
      } catch (e) {
        selectedItems = [];
      }
    }

    return CustomDropdownMulti(
      label: '',
      showLabel: false,
      placeholder: _getPlaceholder(key),
      options: selectItems,
      defaultValues: selectedItems,
      dropdownHeight: 48.h,
      onSelected: (values) {
        setState(() {
          if (values.isEmpty) {
            _filterValues[key] =
                key == 'status' ? ['-3', '-2', '-1', '0', '1', '-4'] : ['-1'];
            return;
          }

          if (values.any((item) =>
              item.value == '-1' || item.value == '[-3,-2,-1,0,1,-4]')) {
            _filterValues[key] =
                key == 'status' ? ['-3', '-2', '-1', '0', '1', '-4'] : ['-1'];
            return;
          }

          if (key == 'status') {
            final allSelectedStatus = values.expand((item) {
              final statusStr =
                  item.value.replaceAll('[', '').replaceAll(']', '');
              return statusStr.split(',');
            }).toList();
            _filterValues[key] = allSelectedStatus;
          } else {
            _filterValues[key] = values.map((item) => item.value).toList();
          }
        });
      },
    );
  }

  Widget _buildServiceRangeDropdown(
      String key, List<ServiceRange> serviceRanges) {
    if (serviceRanges.isEmpty) {
      return CustomDropdownMulti(
        label: '',
        showLabel: false,
        placeholder: _getPlaceholder(key),
        options: const [],
        defaultValues: [],
        dropdownHeight: 48.h,
        onSelected: (values) {
          setState(() {
            _filterValues[key] =
                values.isEmpty ? ['-1'] : values.map((v) => v.value).toList();
          });
        },
      );
    }

    final List<SelectItem> selectItems = [
      SelectItem(label: 'Tất cả', value: '-1'),
      ...serviceRanges.map((service) {
        return SelectItem(
            label: service.serviceName, value: service.id.toString());
      }).toList()
    ];

    List<SelectItem> selectedItems = [];
    if (_filterValues[key] != null) {
      try {
        if (_filterValues[key] is List) {
          selectedItems = selectItems
              .where(
                  (item) => (_filterValues[key] as List).contains(item.value))
              .toList();
        } else {
          final item = selectItems.firstWhere(
            (item) => item.value == _filterValues[key].toString(),
            orElse: () => selectItems.first,
          );
          selectedItems = [item];
        }
      } catch (e) {
        selectedItems = [];
      }
    }

    return CustomDropdownMulti(
      label: '',
      showLabel: false,
      placeholder: _getPlaceholder(key),
      options: selectItems,
      defaultValues: selectedItems,
      dropdownHeight: 48.h,
      onSelected: (values) {
        setState(() {
          if (values.isEmpty || values.any((item) => item.value == '-1')) {
            _filterValues[key] = ['-1'];
            return;
          }
          _filterValues[key] = values.map((item) => item.value).toList();
        });
      },
    );
  }

  Widget _buildDateRangePicker(String startKey, String endKey) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () => _selectDate(context, startKey),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _filterValues[startKey] != null
                        ? _formatDate(_filterValues[startKey])
                        : 'Chọn ngày',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _filterValues[startKey] != null
                          ? Colors.black
                          : Colors.grey,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Icon(Icons.calendar_today, size: 18.sp),
                ],
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          child: Text('-', style: TextStyle(fontSize: 16.sp)),
        ),
        Expanded(
          child: InkWell(
            onTap: () => _selectDate(context, endKey),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _filterValues[endKey] != null
                        ? _formatDate(_filterValues[endKey])
                        : 'Chọn ngày',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _filterValues[endKey] != null
                          ? Colors.black
                          : Colors.grey,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Icon(Icons.calendar_today, size: 18.sp),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, String key) async {
    final DateTime? picked = await CustomDatePicker.show(
      context: context,
      initialDate: _filterValues[key] ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      setState(() {
        _filterValues[key] = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildActionButtons() {
    return SafeArea(
      child: Column(
        children: [
          CustomDivider(color: getColorSkin().ink4),
          Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Close button
                    SizedBox(
                      width: 80.w,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                        ),
                        child: const FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text('Đóng'),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),

                    // Reset to Default button
                    Flexible(
                      child: SizedBox(
                        child: ElevatedButton(
                          onPressed: () {
                            setState(() {
                              // Reset all filter values to default
                              _filterValues['authorizer'] = ['-1'];
                              _filterValues['authorized'] = ['-1'];
                              _filterValues['serviceScope'] = ['-1'];
                              _filterValues['startDateUyQuyen'] = null;
                              _filterValues['endDateUyQuyen'] = null;
                              _filterValues['status'] = [-3, -2, -1, 0, 1, -4];
                              _filterValues['creator'] = ['-1'];
                              _filterValues['createStartDate'] = null;
                              _filterValues['createEndDate'] = null;
                              _filterValues['listDateFilter'] = [];
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.blue,
                            side: const BorderSide(color: Colors.blue),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 8.w),
                          ),
                          child: const FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text('Đặt về mặc định'),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),

                    // Filter button
                    SizedBox(
                      width: 80.w,
                      child: ElevatedButton(
                        onPressed: () {
                          // Update the listDateFilter before applying the filter
                          _updateListDateFilterForFilter();

                          // Prepare the filter data for the API call
                          final Map<String, dynamic> apiFilterData = {
                            'assignUser': _filterValues['authorizer'],
                            'assignedUser': _filterValues['authorized'],
                            'listCreatedUser': _filterValues['creator'],
                            'status': _filterValues['status'],
                            'listDateFilter': _filterValues['listDateFilter'],
                            'serviceScope': _filterValues['serviceScope'],
                          };

                          // Print for debugging
                          print('Current filter values: $_filterValues');
                          print('API Filter data: $apiFilterData');

                          if (widget.onApplyFilter != null) {
                            widget.onApplyFilter!(apiFilterData);
                          }
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                        ),
                        child: const FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text('Lọc'),
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  void _updateListDateFilterForFilter() {
    // Clear the existing listDateFilter
    _filterValues['listDateFilter'] = [];

    // Add authorization date filter if either startDateUyQuyen or endDateUyQuyen is set
    if (_filterValues['startDateUyQuyen'] != null ||
        _filterValues['endDateUyQuyen'] != null) {
      final DateTime? startDate = _filterValues['startDateUyQuyen'];
      final DateTime? endDate = _filterValues['endDateUyQuyen'];

      _filterValues['listDateFilter'].add({
        'fromDate': startDate != null ? _formatDateForApi(startDate) : '',
        'toDate': endDate != null ? _formatDateForApi(endDate) : '',
        'type': 'startDate'
      });
    }

    // Add creation date filter if either createStartDate or createEndDate is set
    if (_filterValues['createStartDate'] != null ||
        _filterValues['createEndDate'] != null) {
      final DateTime? startDate = _filterValues['createStartDate'];
      final DateTime? endDate = _filterValues['createEndDate'];

      _filterValues['listDateFilter'].add({
        'fromDate': startDate != null ? _formatDateForApi(startDate) : '',
        'toDate': endDate != null ? _formatDateForApi(endDate) : '',
        'type': 'createdDate'
      });
    }
  }

  String _formatDateForApi(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }
}
