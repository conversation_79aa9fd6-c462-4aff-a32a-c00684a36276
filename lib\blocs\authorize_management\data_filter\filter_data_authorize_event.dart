abstract class FilterDataAuthorizeEvent {}

class LoadFilterDataAuthorize extends FilterDataAuthorizeEvent {
  final String search;
  final String sortBy;
  final String sortType;
  final String limit;
  final String page;
  final int totalPages;
  final int totalElements;
  final List<String> listAssignUser;
  final List<String> listAssignedUser;
  final List<int> status;
  final List<String> listCreatedUser;
  final List<String> listUpdatedUser;
  final List<Map<String, String>> listDateFilter;
  final String userLogin;

  LoadFilterDataAuthorize({
    this.search = '',
    this.sortBy = 'createdDate',
    this.sortType = 'DESC',
    this.limit = '10',
    this.page = '1',
    this.totalPages = 0,
    this.totalElements = 0,
    this.listAssignUser = const ['-1'],
    this.listAssignedUser = const ['-1'],
    this.status = const [-3, -2, -1, 0, 1, -4],
    this.listCreatedUser = const ['-1'],
    this.listUpdatedUser = const ['-1'],
    this.listDateFilter = const [{'fromDate': '', 'toDate': '', 'type': ''}],
    this.userLogin = 'employee',
  });
} 