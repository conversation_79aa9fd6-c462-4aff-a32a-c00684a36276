import 'package:eapprove/module_main.dart';
import 'package:eapprove/screens/payment_step/widgets/pdf_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class InformationTable extends StatefulWidget {
  const InformationTable({Key? key}) : super(key: key);

  @override
  _InformationTableState createState() => _InformationTableState();
}

class _InformationTableState extends State<InformationTable> {
  final List<List<String>> tableData = [
    ['Mã NCC', 'Công ty cổ phần thương mại và phát triển hệ thống PC', '<PERSON><PERSON> thống PC'],
    ['Nơi giao hàng', '<PERSON><PERSON> Nội', 'Chi nhánh 1'],
    ['Thời gian NCC giao hàng', '05', '<PERSON><PERSON>y'],
    ['Thời gian dự kiến', '05', '<PERSON><PERSON>n tới'],
    ['<PERSON><PERSON><PERSON><PERSON> thức thanh toán', 'TM/CK', '<PERSON><PERSON> thanh toán'],
    ['<PERSON><PERSON> cách sản phẩm', '<PERSON><PERSON><PERSON><PERSON> dụng mới', '<PERSON>àng nhập khẩu'],
    ['Bảo hành sản phẩm', '5 năm', 'Toàn quốc'],
    ['Tình trạng tồn kho', 'Còn hàng', '20 sản phẩm'],
    ['Thông tin khác', 'Đang vận chuyển', 'Giao trong ngày'],
    ['Báo giá', '1,5,6_Cat6, RAM, Ổ cứng, Hệ thốngPC.pdf', 'Ổ cứng'],
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Table(
        border: TableBorder.all(color: getColorSkin().grey2),
        columnWidths: {
          0: FixedColumnWidth(110.w),
          1: FixedColumnWidth(170.w),
          2: FixedColumnWidth(120.w),
        },
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: tableData.map((row) {
          return TableRow(
            children: row.asMap().entries.map((entry) {
              final int index = entry.key;
              final String cell = entry.value;
              if (row[0] == 'Báo giá' && index == 1) {
                return TableCell(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: GestureDetector(
                      onTap: () {
                          Navigator.push(context, 
                              MaterialPageRoute(builder: (context) => 
                              const PdfViewerPage(pdfName: 'test.pdf')));
                      },
                      child: Text(
                        cell,
                        style: getTypoSkin().title5Medium.copyWith(color: getColorSkin().primaryBlue),
                      ),
                    ),
                  ),
                );
              } else {
                return TableCell(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      cell,
                      style: TextStyle(
                        fontSize: 14,
                        color: getColorSkin().ink1,
                      ),
                    ),
                  ),
                );
              }
            }).toList(),
          );
        }).toList(),
      ),
    );
  }
}