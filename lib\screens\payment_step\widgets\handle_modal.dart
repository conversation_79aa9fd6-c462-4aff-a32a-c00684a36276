import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:async';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_event.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_state.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:eapprove/services/api_form_service.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';

import 'package:eapprove/screens/payment_step/widgets/tabcontent/approve_tab.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/authorize_tab.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/cancel_tab.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/return_tab.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_sdk/widgets/form.dart';
import 'package:eapprove/screens/tablet/pyc/ticket_screen_with_counts.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';

typedef TabletModalCallback = void Function({
  required int selectedTab,
  required List<String> tabNames,
  required VoidCallback? onSaveDraft,
  required VoidCallback? onPrimaryAction,
  required bool showSaveDraft,
  bool isProcessing,
});

class HandleModal extends StatefulWidget {
  final ThongTinDauVaoResponseModel? dataDauVao;
  final ThongTinChungModel data;
  final dynamic ticket;
  final String? taskIdValue;
  final String ticketStatus;
  final int initialTab;
  final bool hideHeader;
  final TabletModalCallback? onTabletCallback;
  final VoidCallback? onSuccess;

  const HandleModal({
    Key? key,
    required this.data,
    this.dataDauVao,
    required this.ticket,
    this.taskIdValue,
    required this.ticketStatus,
    this.initialTab = 0,
    this.hideHeader = false,
    this.onTabletCallback,
    this.onSuccess,
  }) : super(key: key);

  @override
  State<HandleModal> createState() => _HandleModalState();
}

class _HandleModalState extends State<HandleModal> with SingleTickerProviderStateMixin {
  int _selectedTab = 0;
  late final FormBloc _formBloc;
  late List<String> _tabs;
  SelectItem? _selectedDelegate;
  final bool _isSigned = false; // Đã ký duyệt
  bool _isDone = false; // Đã kiểm duyệt (Block hết)
  final _cancelForm = GlobalKey<FFormState>();
  final _returnForm = GlobalKey<FFormState>();
  final _authorizedForm = GlobalKey<FFormState>();
  final Map<String, String?> _cancelFieldErrors = {};
  final Map<String, String?> _returnFieldErrors = {};
  final Map<String, String?> _authorizedFieldErrors = {};
  final List<PlatformFile> _pickedReturnFile = [];
  final List<PlatformFile> _pickedCancelFile = [];

  Future<void> _pickedCancelFileUpload() async {
    final result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result != null) {
      final newFiles = result.files.where((newFile) => !_pickedCancelFile.any((f) => f.name == newFile.name)).toList();

      setState(() {
        _pickedCancelFile.addAll(newFiles);
      });
    }
  }

  Future<void> _pickedReturnFileUpload() async {
    final result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result != null) {
      final newFiles = result.files.where((newFile) => !_pickedReturnFile.any((f) => f.name == newFile.name)).toList();

      setState(() {
        _pickedReturnFile.addAll(newFiles);
      });
    }
  }

  final TextEditingController _approveContent = TextEditingController();
  final TextEditingController _fund = TextEditingController();
  final TextEditingController _reasonCancel = TextEditingController();
  final TextEditingController _reasonReturn = TextEditingController();
  final TextEditingController _explain = TextEditingController();
  final TextEditingController _delegateReasonController = TextEditingController();
  late AnimationController _arrowController;
  bool _isLoading = true;
  late Box box;
  late String username;

  @override
  void initState() {
    box = Hive.box('authentication');
    username = box.get('username', defaultValue: 'employee');
    _formBloc = context.read<FormBloc>();
    _selectedTab = widget.initialTab;
    _isDone = widget.ticketStatus == 'COMPLETED';
    _arrowController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _arrowController.value = 0.0;
    _arrowController.forward();

    // Set default values
    _fund.text = 'DEPARTME_DU AN DEPARTMENT';
    _explain.text = 'Giải trình thanh toán';
    _tabs = ['Ký duyệt', 'Trả về', 'Hủy phiếu', 'Ủy quyền'];
    // Load tabs
    _initTabs();

    // Initialize blocs
    context.read<TicketDialogActionBloc>().add(TicketDialogActionInitialEvent());
    context.read<TicketDialogActionBloc>().add(AuthorizeListGetListEvent());

    if (widget.ticket != null) {
      final processId = int.tryParse(widget.ticket.ticketId?.toString() ?? '') ?? 0;
      final procDefId = widget.ticket.ticketProcDefId ?? '';

      final checkTypeBloc = context.read<CheckTypeBloc>();
      checkTypeBloc.add(CheckTypeRequested(
        serviceId: processId,
        procDefId: procDefId,
      ));

      // Load approval data
      _formBloc.add(GetApprovalVinaphacoRequested(
        procDefId: procDefId,
      ));

      _formBloc.add(GetListBaseUrl());

      context.read<UserListBloc>().add(FetchUserInfoByToken());

      _getSignature();
    } else {
      developer.log('HandleModal - Widget.ticket is null', name: 'HandleModal');
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyTabletCallback();
    });
    super.initState();
  }

  VoidCallback? _approveTabSaveDraft;
  VoidCallback? _approveTabHandleApprove;
  bool _isApproveProcessing = false;

  void _setApproveTabCallbacks({
    VoidCallback? onSaveDraft,
    VoidCallback? onApprove,
    bool? isProcessing,
  }) {
    _approveTabSaveDraft = onSaveDraft;
    _approveTabHandleApprove = onApprove;
    _isApproveProcessing = isProcessing ?? false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyTabletCallback();
    });
  }

  void _notifyTabletCallback() {
    if (widget.onTabletCallback != null && DeviceUtils.isTablet) {
      bool isInPerformPhase = widget.ticket is ProcContent;

      VoidCallback? saveDraftAction;
      VoidCallback? primaryAction;
      bool showSaveDraft = false;

      switch (_selectedTab) {
        case 0: // Ký duyệt/Thực hiện
          showSaveDraft = true;
          saveDraftAction = _approveTabSaveDraft;

          if (isInPerformPhase && !_isDone) {
            primaryAction = _approveTabHandleApprove;
          } else if (!_isSigned && !isInPerformPhase && !_isDone) {
            primaryAction = _approveTabHandleApprove;
          }
          break;

        case 1: // Trả về
          showSaveDraft = false;
          primaryAction = () => _handleReturn(isDraft: false);
          break;

        case 2: // Hủy phiếu
          showSaveDraft = false;
          primaryAction = () => _handleCancel(isDraft: false);
          break;

        case 3: // Ủy quyền
          showSaveDraft = false;
          primaryAction = () {
            // Logic ủy quyền
          };
          break;
      }

      widget.onTabletCallback!(
        selectedTab: _selectedTab,
        tabNames: _tabs,
        onSaveDraft: saveDraftAction,
        onPrimaryAction: primaryAction,
        showSaveDraft: showSaveDraft,
        isProcessing: _selectedTab == 0 ? _isApproveProcessing : false,
      );
    }
  }

  @override
  void dispose() {
    _arrowController.dispose();
    _approveContent.dispose();
    _fund.dispose();
    _reasonCancel.dispose();
    _reasonReturn.dispose();
    _explain.dispose();
    _delegateReasonController.dispose();
    // context.read<FormBloc>().add(FormReset());
    super.dispose();
  }

  void _showLoadingDialog() {
    if (_isLoading) return;
    setState(() => _isLoading = true);
    showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );
  }

  void _hideLoadingDialog() {
    if (_isLoading) {
      Navigator.of(context).pop();
      setState(() => _isLoading = false);
    }
  }

  String? _getTaskDefKey(dynamic ticket) {
    if (ticket.ticketTaskDtoList != null && ticket.ticketTaskDtoList!.isNotEmpty) {
      return ticket.ticketTaskDtoList!.first.taskDefKey;
    }
    return '';
  }

  void _handleReturn({required bool isDraft}) {
    setState(() => _returnFieldErrors.clear());
    if (_returnForm.currentState?.validate() != true) return;
    _showLoadingDialog();
    if (_pickedReturnFile.isNotEmpty) {
      BlocProvider.of<TicketDialogActionBloc>(context).add(
        UploadFileEvent(
          _pickedReturnFile,
          onSuccess: (file) {
            _triggerReturnEvent(
              isDraft,
              file
                  .map((e) => {
                        'fileName': e['fileName'] as String,
                        'size': e['size'] as int,
                      })
                  .toList(),
            );
          },
          onError: (message) {
            _hideLoadingDialog();
            SnackbarCore.error(message);
          },
        ),
      );
    } else {
      _triggerReturnEvent(isDraft, []);
    }
  }

  void _triggerReturnEvent(bool isDraft, List<Map<String, dynamic>> filePaths) {
    try {
      if (isDraft) {
        final dataInput = widget.dataDauVao?.data.listVariables.map((e) => e).toList();
        final dataToGet = [
          "system_chartId_service",
          "system_chartNodeId_ticket",
          "system_completeTicketId",
          "system_chartNodeCustomId",
          "system_chartCustomId",
          "system_companyCode",
          "__cancel_reason"
        ];
        final dataInputFiltered = dataInput?.where((e) => dataToGet.contains(e.name)).toList();
        final Map<String, dynamic> result = {};
        if (dataInputFiltered != null && dataInputFiltered.isNotEmpty) {
          for (var item in dataInputFiltered) {
            result[item.name] = {'value': item.value, 'type': item.type};
          }
        }
        BlocProvider.of<TicketDialogActionBloc>(context).add(
          ReturnDraftEvent(
            otherVariables: result,
            ticketProcId: widget.data.ticketId ?? '',
            reason: _reasonReturn.text,
            filePath:
                filePaths.isNotEmpty ? filePaths.map((file) => file['fileName'] as String).toList().join(',') : null,
          ),
        );
      } else {
        BlocProvider.of<TicketDialogActionBloc>(context).add(
          ReturnSubmitEvent(
              procDefId: widget.data.ticketProcDefId ?? '',
              taskDefKey: _getTaskDefKey(widget.ticket) ?? '',
              procInstId: widget.data.ticketId ?? '',
              reason: _reasonReturn.text,
              taskId: widget.taskIdValue ?? '',
              ticketId: widget.data.id ?? 0,
              fileNames: _pickedReturnFile.isNotEmpty ? _pickedReturnFile.map((file) => file.name).toList() : [],
              filePaths: filePaths.isNotEmpty ? filePaths.map((file) => file['fileName'] as String).toList() : [],
              fileSizes: filePaths.isNotEmpty ? filePaths.map((file) => file['size'] as int).toList() : []),
        );
      }
    } catch (e) {
      _hideLoadingDialog();
      SnackbarCore.error('Đã xảy ra lỗi: ${e.toString()}');
    }
  }

  void _handleCancel({required bool isDraft}) {
    setState(() => _cancelFieldErrors.clear());
    if (_cancelForm.currentState?.validate() != true) return;
    _showLoadingDialog();
    if (_pickedCancelFile.isNotEmpty) {
      BlocProvider.of<TicketDialogActionBloc>(context).add(
        UploadFileEvent(
          _pickedCancelFile,
          onSuccess: (filePaths) {
            _triggerCancelEvent(isDraft, filePaths.map((e) => e['fileName'] as String).toList());
          },
          onError: (message) {
            _hideLoadingDialog();
            SnackbarCore.error(message);
          },
        ),
      );
    } else {
      _triggerCancelEvent(isDraft, []);
    }
  }

  void _triggerCancelEvent(bool isDraft, List<String> filePaths) {
    try {
      if (isDraft) {
        BlocProvider.of<TicketDialogActionBloc>(context).add(
          CancelDraftEvent(
            ticketProcId: widget.data.ticketId ?? '',
            reason: _reasonCancel.text,
            filePath: filePaths.isNotEmpty ? filePaths.join(',') : null,
          ),
        );
      } else {
        BlocProvider.of<TicketDialogActionBloc>(context).add(
          CancelSubmitEvent(
            ticketProcId: widget.data.ticketId ?? '',
            ticketId: widget.data.id ?? 0,
            taskDefKey: _getTaskDefKey(widget.ticket) ?? '',
            reason: _reasonCancel.text,
            filePaths: filePaths,
          ),
        );
      }
    } catch (e) {
      _hideLoadingDialog();
      SnackbarCore.error('Đã xảy ra lỗi: ${e.toString()}');
    }
  }

  void _initTabs() {
    bool isInPerformPhase = widget.ticket is ProcContent;
    bool isApprovalPhase = widget.ticket is ApproveContent;

    if (_isSigned || isInPerformPhase) {
      _tabs = ['Thực hiện', 'Trả về', 'Hủy phiếu', 'Ủy quyền'];
    } else {
      _tabs = ['Ký duyệt', 'Trả về', 'Hủy phiếu', 'Ủy quyền'];
    }

    if (DeviceUtils.isTablet) {
      _tabs.add('Hành động khác');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    final double topInset = MediaQuery.of(context).padding.top;

    return MultiBlocListener(
      listeners: [
        BlocListener<TicketDialogActionBloc, TicketDialogActionState>(
          listener: (context, state) {
            print('stateeeeeeeeeeeeeeeeee: $state}');
            if (state is DraftCancelTicketLoaded ||
                state is CancelTicketLoaded ||
                state is DraftReturnTicketLoaded ||
                state is ReturnTicketLoaded ||
                state is AuthorizedLoaded) {
              String message = '';
              if (state is DraftCancelTicketLoaded) {
                message = state.message;
              } else if (state is CancelTicketLoaded) {
                message = state.message;
              } else if (state is DraftReturnTicketLoaded) {
                message = state.message;
              } else if (state is ReturnTicketLoaded) {
                message = state.message;
              } else if (state is AuthorizedLoaded) {
                message = state.message;
              }
              _hideLoadingDialog();
              SnackbarCore.success(message);
              widget.onSuccess?.call();
              Navigator.of(context).pop();
            }

            if (state is UploadFileError ||
                state is DraftCancelTicketError ||
                state is DraftReturnTicketError ||
                state is ReturnTicketError ||
                state is CancelTicketError ||
                state is AuthorizedError) {
              final errorMessage = (state as dynamic).message;
              _hideLoadingDialog();

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Lỗi: $errorMessage')),
              );
            }
          },
        ),
        BlocListener<FormBloc, FormInfoState>(
          listener: (context, state) {
            developer.log('Form State Changed:', name: 'HandleModal');
            developer.log('IsLoading: ${state.isLoading}', name: 'HandleModal');
            developer.log('HasData: ${state.hasData}', name: 'HandleModal');
            developer.log('FormResponse: ${state.formResponse?.toString()}', name: 'HandleModal');
          },
        ),
        BlocListener<CheckTypeBloc, CheckTypeState>(
          listener: (context, state) {
            developer.log('CheckType State Changed:', name: 'HandleModal');
            developer.log('State: $state', name: 'HandleModal');
          },
        ),
      ],
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(
            top: topInset + (isTablet ? 5.h : 5.h),
            bottom: isTablet ? 0 : 50.h,
          ),
          child: Align(
            alignment: Alignment.topCenter,
            child: Material(
              color: getColorSkin().white,
              child: Container(
                width: 400.w,
                constraints: BoxConstraints(
                  maxWidth: 400.w,
                  minWidth: 320.w,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      color: getColorSkin().white,
                      height: isTablet ? null : 40.h,
                      child: Stack(
                        clipBehavior: Clip.none, // cho phép overflow
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                              left: isTablet ? 0.w : 40.w,
                              right: isTablet ? 0.w : 0,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      children: List.generate(_tabs.length, (tabIndex) {
                                        final tabName = _tabs[tabIndex];
                                        final isSelected = _selectedTab == tabIndex;
                                        return Container(
                                          margin: EdgeInsets.only(right: isTablet ? 4.w : 8.w),
                                          child: GestureDetector(
                                            onTap: () {
                                              setState(() => _selectedTab = tabIndex);
                                              _notifyTabletCallback();
                                            },
                                            child: AnimatedContainer(
                                              duration: const Duration(milliseconds: 200),
                                              padding: EdgeInsets.symmetric(
                                                horizontal: isTablet ? 3.w : 8.w,
                                                vertical: 2.h,
                                              ),
                                              height: 28.h,
                                              decoration: isSelected
                                                  ? BoxDecoration(
                                                      color: getColorSkin().lightBlue.withAlpha(100),
                                                      borderRadius: BorderRadius.circular(8.r),
                                                    )
                                                  : null,
                                              alignment: Alignment.center,
                                              child: Text(
                                                _tabs[tabIndex],
                                                style: getTypoSkin().medium14.copyWith(
                                                      color:
                                                          isSelected ? getColorSkin().primaryBlue : getColorSkin().ink3,
                                                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        );
                                      }),
                                    ),
                                  ),
                                ),
                                if (isTablet) SizedBox(width: 8.w),
                                if (isTablet)
                                  Container(
                                    height: 24.h,
                                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                                    decoration: BoxDecoration(
                                      color: getColorSkin().white,
                                      border: Border.all(color: getColorSkin().primaryBlue),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: CustomPopupMenu(
                                      items: getOtherActionItems(),
                                      child: Container(
                                        alignment: Alignment.center,
                                        padding: EdgeInsets.symmetric(horizontal: 5.w),
                                        height: 24.h,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              'Hành động khác',
                                              style: getTypoSkin().regular12.copyWith(
                                                    color: getColorSkin().primaryBlue,
                                                    height: 1.0,
                                                  ),
                                            ),
                                            SizedBox(width: 4.w),
                                            SizedBox(
                                              width: 12.w,
                                              height: 12.h,
                                              child: SvgPicture.asset(
                                                StringImage.ic_arrow_down,
                                                width: 12.w,
                                                height: 12.h,
                                                colorFilter: ColorFilter.mode(
                                                  getColorSkin().primaryBlue,
                                                  BlendMode.srcIn,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      onTap: (code) {
                                        // code là một trong các giá trị do getOtherActionItems() trả về,
                                        // ví dụ: 'CREATE', 'CLONE', 'GENERAL_INFO', …
                                        switch (code) {
                                          case 'CREATE':
                                            // xử lý "Tạo"
                                            break;
                                          case 'CLONE':
                                            // xử lý "Nhân bản"
                                            break;
                                          case 'SHARE':
                                            // xử lý "Chia sẻ"
                                            break;
                                          case 'GENERAL_INFO':
                                            // xử lý "Thông tin chung"
                                            break;
                                          case 'TICKET_INFO':
                                            // xử lý "Thông tin phiếu"
                                            break;
                                          case 'STEP_DETAIL_INFO':
                                            // xử lý "Thông tin chi tiết bước"
                                            break;
                                          case 'INPUT_INFO':
                                            // xử lý "Thông tin đầu vào"
                                            break;
                                        }
                                      },
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Positioned(
                            left: isTablet ? -65.w : 0,
                            top: isTablet ? -5.w : 0,
                            bottom: isTablet ? -5.w : 0,
                            child: InkWell(
                              onTap: () {
                                _formBloc.add(FormReset());
                                _arrowController.reverse();
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                width: 40.w,
                                height: 40.w,
                                color: getColorSkin().primaryBlue,
                                alignment: Alignment.center,
                                child: RotationTransition(
                                  turns: Tween(begin: 0.0, end: 0.5).animate(_arrowController),
                                  child: Icon(
                                    Icons.chevron_left_rounded,
                                    color: Colors.white,
                                    size: 28.sp,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildTabContent(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    bool isInPerformPhase = widget.ticket is ProcContent;
    final isTablet = DeviceUtils.isTablet;
    final Box authBox = Hive.box('authentication');
    final String? username = authBox.get('username');
    final String? ticketStartUserId = TicketUtils.safeGetProperty(widget.ticket, 'ticketStartUserId')?.toString();
    final String? assignee = username;
    final bool isCreatorAndAssignee = username == ticketStartUserId && assignee == username;

    // Nếu là người tạo và không phải người được giao việc, chỉ hiển thị form hủy phiếu
    if (DeviceUtils.isTablet && username == ticketStartUserId && !isCreatorAndAssignee) {
      return CancelTab(
        formKey: _cancelForm,
        reasonCancel: _reasonCancel,
        pickedCancelFile: _pickedCancelFile,
        onPick: _pickedCancelFileUpload,
        onRemove: (file) => setState(() => _pickedCancelFile.remove(file)),
        onSaveDraft: () => _handleCancel(isDraft: true),
        onCancel: () => _handleCancel(isDraft: false),
        hideButtonsOnTablet: isTablet,
      );
    }

    // Nếu là người được giao việc hoặc vừa là người tạo vừa là người được giao việc
    if (assignee == username || isCreatorAndAssignee) {
      switch (_selectedTab) {
        case 0:
          return ApproveTabWrapper(
            isInPerformPhase: isInPerformPhase,
            hideButtonsOnTablet: isTablet,
            ticket: widget.ticket,
            isSigned: _isSigned,
            isDone: _isDone,
            onCallbacksReady: _setApproveTabCallbacks,
          );

        case 1:
          // Tab 1: Trả về
          return ReturnTab(
            formKey: _returnForm,
            reasonReturn: _reasonReturn,
            pickedReturnFile: _pickedReturnFile,
            onPick: _pickedReturnFileUpload,
            onRemove: (file) => setState(() => _pickedReturnFile.remove(file)),
            onSaveDraft: () => _handleReturn(isDraft: true),
            onReturn: () => _handleReturn(isDraft: false),
            hideButtonsOnTablet: isTablet,
          );

        case 2:
          // Tab 2: Hủy phiếu
          return CancelTab(
            formKey: _cancelForm,
            reasonCancel: _reasonCancel,
            pickedCancelFile: _pickedCancelFile,
            onPick: _pickedCancelFileUpload,
            onRemove: (file) => setState(() => _pickedCancelFile.remove(file)),
            onSaveDraft: () => _handleCancel(isDraft: true),
            onCancel: () => _handleCancel(isDraft: false),
            hideButtonsOnTablet: isTablet,
          );

        case 3:
          // Tab 3: Ủy quyền
          return AuthorizeTab(
            formKey: _authorizedForm,
            delegateReasonController: _delegateReasonController,
            selectedDelegate: _selectedDelegate,
            onSelected: (v) => setState(() => _selectedDelegate = v),
            onSearchChanged: (value) {
              context.read<TicketDialogActionBloc>().add(
                    AuthorizeListGetListEvent(search: value),
                  );
            },
            ticketId: widget.data.ticketId ?? '',
            taskId: widget.taskIdValue ?? '',
            taskDefKey: _getTaskDefKey(widget.ticket) ?? '',
            id: widget.data.id ?? 0,
            hideButtonsOnTablet: isTablet,
          );
        default:
          return const SizedBox.shrink();
      }
    }
    return SizedBox.shrink();
  }

  void _getSignature() async {
    final Box authBox = Hive.box('authentication');
    final String? currentUsername = authBox.get('username');

    final user = await ApiFormService().getByUsername(currentUsername ?? "");
    final title = await ApiFormService().getTitleByUsername(currentUsername ?? "");
    developer.log('getByUsernamegetByUsername: $user', name: 'HandleModal');
    developer.log('getTitleByUsernamegetTitleByUsername: $title', name: 'HandleModal');
  }
}

class FormUtils {
  static Widget buildLabel(String text, {bool required = false}) {
    return Padding(
      padding: EdgeInsets.only(top: 5.h),
      child: Row(
        children: [
          Text(
            text,
            style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.w600),
          ),
          if (required) Text(' *', style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().red)),
        ],
      ),
    );
  }

//— Helpers để tránh lặp style button
  static ButtonStyle outlinedBtnStyle() => OutlinedButton.styleFrom(
        side: BorderSide(color: getColorSkin().primaryBlue),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        minimumSize: Size(0, 44.h),
      );

  static ButtonStyle elevatedBtnStyle() => ElevatedButton.styleFrom(
        backgroundColor: getColorSkin().primaryBlue,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        minimumSize: Size(0, 44.h),
      );

//— Helper vẽ phần upload file + danh sách file
  static Widget buildAttachmentPicker({
    required List<PlatformFile> files,
    required VoidCallback onPick,
    required ValueChanged<PlatformFile> onRemove,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('Chứng từ đính kèm', style: getTypoSkin().regular12.copyWith(fontWeight: FontWeight.w600)),
            SizedBox(width: 8.w),
            InkWell(onTap: onPick, child: Icon(Icons.file_upload_outlined)),
          ],
        ),
        if (files.isNotEmpty)
          ...files.map((f) => Row(
                children: [
                  Icon(Icons.attach_file, size: 16.sp),
                  Expanded(child: Text(f.name, overflow: TextOverflow.ellipsis)),
                  IconButton(icon: Icon(Icons.close, size: 16.sp), onPressed: () => onRemove(f)),
                ],
              )),
      ],
    );
  }
}

class ApproveTabWrapper extends StatefulWidget {
  final bool isInPerformPhase;
  final bool hideButtonsOnTablet;
  final dynamic ticket;
  final bool isSigned;
  final bool isDone;
  final Function({VoidCallback? onSaveDraft, VoidCallback? onApprove, bool? isProcessing}) onCallbacksReady;

  const ApproveTabWrapper({
    Key? key,
    required this.isInPerformPhase,
    required this.hideButtonsOnTablet,
    required this.ticket,
    required this.isSigned,
    required this.isDone,
    required this.onCallbacksReady,
  }) : super(key: key);

  @override
  State<ApproveTabWrapper> createState() => _ApproveTabWrapperState();
}

class _ApproveTabWrapperState extends State<ApproveTabWrapper> {
  VoidCallback? _handleSaveDraft;
  VoidCallback? _handleApprove;
  bool _isProcessing = false;

  void _setCallbacks(VoidCallback? saveDraft, VoidCallback? approve) {
    _handleSaveDraft = saveDraft;
    _handleApprove = approve;

    widget.onCallbacksReady(
      onSaveDraft: _handleSaveDraft,
      onApprove: _handleApprove,
      isProcessing: _isProcessing,
    );
  }

  void _setProcessingState(bool isProcessing) {
    if (_isProcessing != isProcessing) {
      setState(() {
        _isProcessing = isProcessing;
      });

      widget.onCallbacksReady(
        onSaveDraft: _handleSaveDraft,
        onApprove: _handleApprove,
        isProcessing: _isProcessing,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApproveTab(
      isInPerformPhase: widget.isInPerformPhase,
      hideButtonsOnTablet: widget.hideButtonsOnTablet,
      ticket: widget.ticket,
      isSigned: widget.isSigned,
      isDone: widget.isDone,
      onCallbacksReady: _setCallbacks,
      onProcessingStateChanged: _setProcessingState,
    );
  }
}
