class AllSubmissionTypeResponse {
  int? code;
  String? message;
  List<Data>? data;

  AllSubmissionTypeResponse({this.code, this.message, this.data});

  AllSubmissionTypeResponse.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? id;
  String? typeName;
  String? departmentCreate;
  String? description;
  String? shareWith;
  String? companyCode;
  String? createdUser;
  List<int>? createdDate;
  String? modifiedUser;
  List<int>? modifiedDate;
  int? scopeApply;
  int? status;

  Data(
      {this.id,
        this.typeName,
        this.departmentCreate,
        this.description,
        this.shareWith,
        this.companyCode,
        this.createdUser,
        this.createdDate,
        this.modifiedUser,
        this.modifiedDate,
        this.scopeApply,
        this.status});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    typeName = json['typeName'];
    departmentCreate = json['departmentCreate'];
    description = json['description'];
    shareWith = json['shareWith'];
    companyCode = json['companyCode'];
    createdUser = json['createdUser'];
    createdDate = json['createdDate'] != null ? List<int>.from(json['createdDate']) : null;
    modifiedUser = json['modifiedUser'];
    modifiedDate = json['modifiedDate'] != null ? List<int>.from(json['modifiedDate']) : null;
    scopeApply = json['scopeApply'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['typeName'] = this.typeName;
    data['departmentCreate'] = this.departmentCreate;
    data['description'] = this.description;
    data['shareWith'] = this.shareWith;
    data['companyCode'] = this.companyCode;
    data['createdUser'] = this.createdUser;
    data['createdDate'] = this.createdDate;
    data['modifiedUser'] = this.modifiedUser;
    data['modifiedDate'] = this.modifiedDate;
    data['scopeApply'] = this.scopeApply;
    data['status'] = this.status;
    return data;
  }
}
