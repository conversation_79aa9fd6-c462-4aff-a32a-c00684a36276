import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/feature_repository.dart';
import 'package:eapprove/blocs/feature/feature_event.dart';
import 'package:eapprove/blocs/feature/feature_state.dart';

class FeatureBloc extends Bloc<FeatureEvent, FeatureState> {
  final FeatureRepository _repository;

  FeatureBloc({required FeatureRepository repository})
      : _repository = repository,
        super(FeatureInitial()) {
    on<LoadFeatures>(_onLoadFeatures);
    on<UpdateFeatureVisibility>(_onUpdateFeatureVisibility);
  }

  Future<void> _onLoadFeatures(
    LoadFeatures event,
    Emitter<FeatureState> emit,
  ) async {
    emit(FeatureLoading());

    try {
      final feature = await _repository.getFeatureByTenant();

      final Map<int, bool> tabVisibility = {
        1: true, // Phiếu yêu cầu của tôi (My Ticket)
        2: false, // Phiếu yêu cầu cần thực hiện (Execution)
        3: true, // Phiếu yêu cầu cần phê duyệt (Approval)
        4: false, // Phiếu yêu cầu trợ lý (Assistant)
        5: false, // Quản lý ủy quyền
      };

      final featureCheckData = feature.data?.featureCheckData;
      if (featureCheckData != null) {
        tabVisibility[2] = (featureCheckData.countDataByBpmExecution ?? 0) > 0;

        tabVisibility[4] = (featureCheckData.countDataByBpmAssistant ?? 0) > 0;
        tabVisibility[5] = (featureCheckData.countDataByAssign ?? 0) > 0;
      }

      log('Feature data loaded: ${feature.data?.features?.length} features');
      log('API count values - Execution: ${featureCheckData?.countDataByBpmExecution}, '
          'Assistant: ${featureCheckData?.countDataByBpmAssistant}, '
          'Assign: ${featureCheckData?.countDataByAssign}');
      log('Tab visibility: $tabVisibility');

      emit(FeatureLoaded(
        featureData: feature,
        tabVisibility: tabVisibility,
      ));
    } catch (e) {
      log('Error fetching feature data: $e');
      emit(FeatureError(e.toString()));
    }
  }

  Future<void> _onUpdateFeatureVisibility(
    UpdateFeatureVisibility event,
    Emitter<FeatureState> emit,
  ) async {
    if (state is! FeatureLoaded) {
      return;
    }

    final currentState = state as FeatureLoaded;
    final Map<int, bool> tabVisibility = Map.from(currentState.tabVisibility);

    log("Updating tab visibility - isAdmin: ${event.isAdmin}");

    if (event.isAdmin) {
      // Admin can see all tabs
      tabVisibility[1] = true; // My Ticket
      tabVisibility[2] = true; // Execution
      tabVisibility[3] = true; // Approval
      tabVisibility[4] = true; // Assistant
    } else {
      tabVisibility[1] = true; // My Ticket
      tabVisibility[3] = true; // Approval

      final featureCheckData = currentState.featureData.data?.featureCheckData;

      tabVisibility[2] = (featureCheckData?.countDataByBpmExecution ?? 0) > 0;
      tabVisibility[4] = (featureCheckData?.countDataByBpmAssistant ?? 0) > 0;

      log("Tab visibility updated based on API counts:");
      log("Tab 2 (Execution): ${tabVisibility[2]}, count: ${featureCheckData?.countDataByBpmExecution}");
      log("Tab 4 (Assistant): ${tabVisibility[4]}, count: ${featureCheckData?.countDataByBpmAssistant}");
    }

    emit(currentState.copyWith(tabVisibility: tabVisibility));
  }
}
