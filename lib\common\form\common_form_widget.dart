import 'package:eapprove/common/form/checkbox_widget.dart';
import 'package:eapprove/common/form/column_widget.dart';
import 'package:eapprove/common/form/date_picker_widget.dart';
// import 'package:eapprove/common/form/dropdown_widget.dart';
import 'package:eapprove/common/form/file_upload_widget.dart';
import 'package:eapprove/common/form/formula_widget.dart';
import 'package:eapprove/common/form/label_widget.dart';
import 'package:eapprove/common/form/matrix_widget.dart';
import 'package:eapprove/common/form/row_widget.dart';
import 'package:eapprove/common/form/radio_button_widget.dart';
import 'package:eapprove/common/form/splitter_widget.dart';
import 'package:eapprove/common/form/step_widget.dart';
import 'package:eapprove/common/form/tab_widget.dart';
import 'package:eapprove/common/form/table_widget_v2.dart';
import 'package:eapprove/common/form/text_input_widget.dart';
import 'package:eapprove/common/form/url_widget.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/common/form_v2/widget/form_dropdown_mixin.dart';
import 'package:eapprove/common/form_v2/widget/form_text_input.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/enum/enum.dart';

class FormItemWidget extends StatelessWidget {
  final FormItemInfo data;
  final Function(String, String, dynamic)? onChange;
  final List<FormItemInfo>? childFormItems;
  final FormStateManager stateManager;
  final FocusNode? focusNode;
  final int? rowIndex;
  final bool? isReadOnly;
  final bool? isShowLabel;

  const FormItemWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
    this.childFormItems,
    this.focusNode,
    this.rowIndex,
    this.isReadOnly,
    this.isShowLabel = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Display debug information
    // Render the appropriate widget based on type
    switch (data.type) {
      case FormItemType.row:
        return RowWidget(
          data: data,
          columnContents: _parseColumnContents(data.content),
          columns: data.columns ?? [],
        );

      case FormItemType.column:
        return ColumnWidget(
          data: data,
          columnContent: _parseColumnContent(data.content),
          onDelete: () => onChange?.call(data.id ?? '', 'delete', null),
          onDuplicate: () => onChange?.call(data.id ?? '', 'duplicate', null),
        );

      case FormItemType.step:
        return StepWidget(
          data: data,
          stepContents: {
            for (var item in childFormItems ?? [])
              if (item.id != null) item.id!: item,
          },
        );

      case FormItemType.tab:
        return TabWidget(
          data: data,
          childFormItems: childFormItems ?? [],
          stateManager: stateManager,
        );

      case FormItemType.splitter:
        return SplitterWidget(data: data, stateManager: stateManager);

      case FormItemType.input:
      case FormItemType.textArea:
      case FormItemType.number:
      case FormItemType.currency:
        return Container(
            child:  TextInputForm(
              formWidget: data,
              stateManager: stateManager,
              focusNode: focusNode,
              rowIndex: rowIndex,
              isShowLabel: isShowLabel,
            ),
            );

      case FormItemType.checkbox:
        return CheckboxWidget(
          data: data,
          stateManager: stateManager,
          rowIndex: rowIndex,
        );

      case FormItemType.radioButton:
        return RadioButtonWidget(
          data: data,
          stateManager: stateManager,
          rowIndex: rowIndex,
        );

      case FormItemType.dropdown:
        return Container(
          child: DropdownWidget(
          data: data,
          stateManager: stateManager,
          rowIndex: rowIndex,
          isShowLabel: isShowLabel,
        ),
        );

      case FormItemType.datePicker:
        return DatePickerWidget(
          data: data,
          stateManager: stateManager,
          rowIndex: rowIndex,
          isShowLabel: isShowLabel,
        );

      case FormItemType.fileUpload:
        return FileUploadWidget(
          data: data,
          stateManager: stateManager,
          rowIndex: rowIndex,
          isShowLabel: isShowLabel,
        );

      case FormItemType.table:
        return TableWidget(
          data: data,
          stateManager: stateManager,
          isReadOnly: isReadOnly,
        );

      case FormItemType.matrix:
        return MatrixWidget(
          data: data,
          stateManager: stateManager,
          isReadOnly: isReadOnly,
        );

      case FormItemType.label:
        return Row(
          children: [
            LabelWidget(
              data: data,
              stateManager: stateManager,
            )
          ],
        );

      case FormItemType.url:
        return URLWidget(
          data: data,
          stateManager: stateManager,
          isShowLabel: isShowLabel,
        );

      case FormItemType.divider:
        // Handle divider case - if you have a divider widget, you can use it here
        return Divider(height: 20, thickness: 1, color: Colors.grey.shade300);

      case FormItemType.formula:
        return FormulaWidget(
          data: data,
          stateManager: stateManager,
          rowIndex: rowIndex,
          isShowLabel: isShowLabel,
        );

      default:
        // Handle unknown types with informative error widget
        return Container(
          // padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.amber.shade700),
            borderRadius: BorderRadius.circular(4),
            color: Colors.amber.shade100,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.warning_amber_rounded,
                      color: Colors.amber.shade800),
                  const SizedBox(width: 8),
                  Text(
                    'Unsupported field type: ${data.type}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.amber.shade900,
                    ),
                  ),
                ],
              ),
              if (data.label != null)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text('Label: ${data.label}'),
                ),
            ],
          ),
        );
    }
  }

  // Helper method to parse column contents for RowWidget
  Map<String, List<Widget>> _parseColumnContents(
      Map<String, dynamic>? content) {
    if (content == null) return {};

    final Map<String, List<Widget>> result = {};
    content.forEach((key, value) {
      if (value is List) {
        result[key] = value.whereType<Widget>().toList();
      }
    });
    return result;
  }

  // Helper method to parse column content
  List<Widget> _parseColumnContent(Map<String, dynamic>? content) {
    if (content == null) return [];

    final List<Widget> result = [];
    content.forEach((key, value) {
      if (value is List) {
        result.addAll(value.whereType<Widget>());
      } else if (value is Widget) {
        result.add(value);
      }
    });
    return result;
  }
}
