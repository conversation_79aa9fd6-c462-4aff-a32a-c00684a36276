import 'package:equatable/equatable.dart';

abstract class FeatureEvent extends Equatable {
  const FeatureEvent();

  @override
  List<Object?> get props => [];
}

class LoadFeatures extends FeatureEvent {
  const LoadFeatures();
}

class UpdateFeatureVisibility extends FeatureEvent {
  final bool isAdmin;
  final Map<String, dynamic> pycCounts;

  const UpdateFeatureVisibility({
    required this.isAdmin,
    required this.pycCounts,
  });

  @override
  List<Object?> get props => [isAdmin, pycCounts];
}