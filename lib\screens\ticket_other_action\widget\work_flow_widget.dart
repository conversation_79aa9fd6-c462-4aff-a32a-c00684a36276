import 'dart:convert';
import 'dart:developer';

import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/state/work_flow_state.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';

class WorkflowWidget extends StatefulWidget {
  final String procDefId;
  final String procInstId;
  final ThongTinChungModel? data;
  final dynamic ticketId;
  final bool showBottomInfo;
  final bool isEmbedded;
  final void Function(String nodeId, String nodeType)? onNodeSelected;
  final WorkflowState? cachedWorkflowState;
  const WorkflowWidget({
    super.key,
    required this.procDefId,
    required this.procInstId,
    this.data,
    required this.ticketId,
    this.showBottomInfo = false,
    this.isEmbedded = false,
    this.onNodeSelected,
    this.cachedWorkflowState,
  });

  @override
  State<WorkflowWidget> createState() => _WorkflowWidgetState();
}

class _WorkflowWidgetState extends State<WorkflowWidget> {
  int selectedNodeIndex = 0;
  List<Map<String, dynamic>> workflowNodes = [];
  WorkflowState? _localWorkflowState;

  @override
  void initState() {
    super.initState();
    if (widget.cachedWorkflowState != null) {
      _localWorkflowState = widget.cachedWorkflowState;
      _initializeWithCachedData();
    } else {
      _initializeWorkflow();
    }
  }

  void _initializeWithCachedData() {
    if (_localWorkflowState is WorkflowLoaded) {
      final state = _localWorkflowState as WorkflowLoaded;
      _buildWorkflowNodes(state);
      _setInitialSelectedNode();
    }
  }

  void _initializeWorkflow() {
    final ticketTaskDtoList = widget.data?.ticketTaskDtoList;
    final firstTaskDefKey = ticketTaskDtoList?.firstOrNull?.taskDefKey;

    final bloc = context.read<WorkflowBloc>();
    bloc.stream.listen((state) {
      if (state is WorkflowLoaded && firstTaskDefKey != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _localWorkflowState = state;
            });
            _buildWorkflowNodes(state);
            _setInitialSelectedNode();
          }
        });
      }
    });
  }

  void _buildWorkflowNodes(WorkflowLoaded state) {
    workflowNodes.clear();
    for (final group in state.workflow.data ?? []) {
      for (final node in group.nodes ?? []) {
        if (node.type == null) {
          debugPrint('Node bị bỏ qua vì thiếu type: id=${node.id}');
          continue;
        }

        String nodeName = node.name?.trim() ?? '';
        if (nodeName.isEmpty) {
          if (node.type == 'startEvent') {
            nodeName = 'Bắt đầu';
          } else if (node.type == 'endEvent') {
            nodeName = 'Kết thúc';
          } else {
            nodeName = 'Không có tên';
          }
        }

        workflowNodes.add({
          'id': node.id,
          'name': nodeName,
          'type': node.type,
          'position': node.position ?? 0,
          'icon': node.type == 'startEvent'
              ? StringImage.ic_start
              : node.type == 'endEvent'
                  ? StringImage.ic_destination
                  : StringImage.ic_manager,
        });
      }
    }
    workflowNodes.sort((a, b) => (a['position'] as int).compareTo(b['position'] as int));
  }

  // THÊM MỚI: Set initial selected node
  void _setInitialSelectedNode() {
    final ticketTaskDtoList = widget.data?.ticketTaskDtoList;
    final firstTaskDefKey = ticketTaskDtoList?.firstOrNull?.taskDefKey;

    if (firstTaskDefKey != null) {
      final index = workflowNodes.indexWhere((n) => n['id'] == firstTaskDefKey);
      if (index != -1) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              selectedNodeIndex = index;
            });
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // THÊM MỚI: Nếu có cached data, sử dụng ngay lập tức mà không cần check PycState
    if (_localWorkflowState is WorkflowLoaded) {
      return _buildWorkflowContent(_localWorkflowState as WorkflowLoaded);
    }

    // Nếu không có cached data, listen từ bloc và cần check cả WorkflowBloc và PycBloc
    return BlocBuilder<WorkflowBloc, WorkflowState>(
      builder: (context, workflowState) {
        // Nếu workflow đã loaded, cache và hiển thị ngay
        if (workflowState is WorkflowLoaded) {
          if (_localWorkflowState == null) {
            _localWorkflowState = workflowState;
            _buildWorkflowNodes(workflowState);
            _setInitialSelectedNode();
          }
          return _buildWorkflowContent(workflowState);
        }

        // Chỉ khi workflow chưa loaded thì mới check PycState
        return BlocBuilder<PycBloc, PycState>(
          builder: (context, pycState) {
            final isStatusLoaded = pycState.statusTicketStatus == PycStatus.loaded;

            if (!isStatusLoaded) {
              return Container(
                color: widget.isEmbedded ? Colors.transparent : getColorSkin().whiteSmoke,
                child: Center(child: AppConstraint.buildLoading(context)),
              );
            }

            // Workflow chưa loaded nhưng PycState đã loaded
            return Container(
              color: widget.isEmbedded ? Colors.transparent : getColorSkin().whiteSmoke,
              child: Center(child: AppConstraint.buildLoading(context)),
            );
          },
        );
      },
    );
  }

  Widget _buildWorkflowContent(WorkflowLoaded state) {
    return Container(
      color: widget.isEmbedded ? Colors.transparent : getColorSkin().whiteSmoke,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    for (int i = 0; i < workflowNodes.length; i++) ...[
                      GestureDetector(
                        onTap: () => selectNode(i),
                        child: _buildNodeWidget(
                          icon: workflowNodes[i]['icon'],
                          label: workflowNodes[i]['name'] ?? '',
                          selected: selectedNodeIndex == i,
                          nodeType: workflowNodes[i]['type'],
                          nodeId: workflowNodes[i]['id'],
                        ),
                      ),
                      if (i < workflowNodes.length - 1) _buildConnector(selectedNodeIndex == i),
                    ],
                    SizedBox(height: 32.h),
                  ],
                ),
              ),
            ),
          ),
          if (widget.showBottomInfo && workflowNodes.isNotEmpty && selectedNodeIndex < workflowNodes.length)
            _buildBottomInfoSection(),
        ],
      ),
    );
  }

  void selectNode(int index) {
    setState(() {
      selectedNodeIndex = index;
    });
    final selectedId = workflowNodes[index]['id'];
    log('📌 [WorkflowWidget] Selected node index: $index - id: $selectedId', name: 'WorkflowWidget');
    final nodeId = workflowNodes[index]['id'];
    final nodeType = workflowNodes[index]['type'] as String;
    widget.onNodeSelected?.call(nodeId, nodeType);
  }

  // void selectNode(int index) {
  //   setState(() {
  //     selectedNodeIndex = index;
  //   });

  //   if (index < workflowNodes.length) {
  //     final selectedId = workflowNodes[index]['id'];
  //     log('📌 [WorkflowWidget] Selected node index: $index - id: $selectedId', name: 'WorkflowWidget');

  //     // Truyền nodeId thay vì index
  //     widget.onNodeSelected?.call(selectedId);
  //   }
  // }

  Widget _buildNodeWidget({
    required String icon,
    required String label,
    required bool selected,
    required String nodeType,
    required String nodeId,
  }) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            border: Border.all(
              color: selected ? getColorSkin().primaryBlue : Colors.transparent,
              width: 2.w,
            ),
          ),
          child: Column(
            children: [
              SvgPicture.asset(
                icon,
                height: widget.isEmbedded ? 36.r : 48.r,
                width: widget.isEmbedded ? 36.r : 48.r,
              ),
              SizedBox(height: 8.h),
              Text(
                label,
                style: getTypoSkin().title6Regular.copyWith(
                      color: getColorSkin().ink1,
                      fontSize: widget.isEmbedded ? 12.sp : 14.sp,
                    ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        if (selected)
          Positioned(
            bottom: -8.h,
            child: SvgPicture.asset(
              StringImage.ic_plus_circle,
              height: 16.r,
              width: 16.r,
            ),
          ),
      ],
    );
  }

  Widget _buildConnector(bool isActive) {
    return SizedBox(
      height: widget.isEmbedded ? 40.h : 50.h,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            child: CustomConnector(
              height: widget.isEmbedded ? 30.h : 40.h,
              lineColor: isActive ? getColorSkin().primaryBlue : getColorSkin().ink2,
            ),
          ),
          Positioned(
            bottom: 3.h,
            child: SvgPicture.asset(
              StringImage.ic_arrow_down,
              height: 16.r,
              width: 16.r,
              colorFilter: ColorFilter.mode(
                isActive ? getColorSkin().primaryBlue : getColorSkin().ink2,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomInfoSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
        color: getColorSkin().white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Thông tin bước',
                  style: getTypoSkin().medium20.copyWith(
                        color: getColorSkin().ink1,
                      ),
                ),
                FFilledButton(
                  size: FButtonSize.size32,
                  onPressed: () {
                    final selected = workflowNodes[selectedNodeIndex];
                    final nodeId = selected['id'] as String;
                    final nodeType = selected['type'] as String;

                    Navigator.pop(context, {
                      'nodeId': nodeId,
                      'nodeType': nodeType,
                      'shouldPopAll': 'true',
                    });
                  },
                  backgroundColor: getColorSkin().primaryBlue,
                  child: Text(
                    'Xem thông tin bước',
                    style: getTypoSkin().bodyRegular14.copyWith(
                          color: getColorSkin().white,
                        ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          if (workflowNodes[selectedNodeIndex]['type'] == 'startEvent') ...[
            _buildCreatorInfo(),
          ] else if (workflowNodes[selectedNodeIndex]['type'] == 'userTask') ...[
            _buildAssigneeInfo(),
          ] else if (workflowNodes[selectedNodeIndex]['type'] == 'endEvent') ...[
            _buildEndEventInfo(),
          ],
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildCreatorInfo() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 150.w,
                child: Text(
                  'Người tạo',
                  style: getTypoSkin().title6Regular.copyWith(
                        color: getColorSkin().ink3,
                      ),
                ),
              ),
              Expanded(
                child: Builder(builder: (context) {
                  String creator = widget.data?.ticketStartUserId ?? '';
                  if (context.watch<TicketProcessDetailBloc>().state is UserTaskInfoLoaded) {
                    final userState = context.read<TicketProcessDetailBloc>().state as UserTaskInfoLoaded;
                    final creatorInfo = userState.getCreator();
                    if (creatorInfo != null) {
                      creator =
                          '${creatorInfo.username ?? ''} - ${creatorInfo.fullName ?? ''} - ${creatorInfo.title ?? ''}';
                    }
                  }
                  return Text(
                    creator,
                    style: getTypoSkin().title6Regular.copyWith(
                          color: getColorSkin().ink1,
                        ),
                  );
                }),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 140.w,
                child: Text(
                  'Trạng thái',
                  style: getTypoSkin().title6Regular.copyWith(
                        color: getColorSkin().ink3,
                      ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: context.read<PycBloc>().state.getStatusColor(widget.data?.ticketStatus),
                    width: 1.w,
                  ),
                  borderRadius: BorderRadius.circular(16.r),
                  color: context.read<PycBloc>().state.getStatusBackgroundColor(widget.data?.ticketStatus),
                ),
                child: Text(
                  context.read<PycBloc>().state.getStatusLabel(widget.data?.ticketStatus) ?? '',
                  style: getTypoSkin().label5Regular.copyWith(
                        color: context.read<PycBloc>().state.getStatusColor(widget.data?.ticketStatus),
                      ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAssigneeInfo() {
    return Builder(builder: (context) {
      final String selectedNodeId = workflowNodes[selectedNodeIndex]['id'] ?? '';
      String assignee = '';
      String? taskStatus;
      log('Selected Node ID: $selectedNodeId', name: 'AssigneeInfo');
      log('Current state: ${context.watch<TicketProcessDetailBloc>().state}', name: 'TicketProcessDetailBloc');

      if (context.watch<TicketProcessDetailBloc>().state is UserTaskInfoLoaded) {
        final userState = context.read<TicketProcessDetailBloc>().state as UserTaskInfoLoaded;
        TaskInfo? matchedTask;
        final tasks = userState.userTaskInfo.data ?? [];

        for (final task in tasks) {
          if (task.taskDefKey == selectedNodeId) {
            matchedTask = task;
            break;
          }
        }
        final AssigneeInfo? assigneeInfo = matchedTask?.lstAssigneeInfo?.firstOrNull;

        if (assigneeInfo != null) {
          assignee = assigneeInfo.getFormattedInfo();
          final rawStatus = assigneeInfo.taskStatus?.trim();
          taskStatus = (rawStatus != null && rawStatus.isNotEmpty) ? rawStatus : 'WAIT';

          log('Assignee Info: $assignee', name: 'AssigneeInfo');
          log('Task Status: $taskStatus', name: 'AssigneeInfo');
        }
      }

      return Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 150.w,
                  child: Text(
                    'Người thực hiện',
                    style: getTypoSkin().title6Regular.copyWith(
                          color: getColorSkin().ink3,
                        ),
                  ),
                ),
                Expanded(
                  child: Text(
                    assignee,
                    style: getTypoSkin().title6Regular.copyWith(
                          color: getColorSkin().ink1,
                        ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: 140.w,
                  child: Text(
                    'Trạng thái',
                    style: getTypoSkin().title6Regular.copyWith(
                          color: getColorSkin().ink3,
                        ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: context.read<PycBloc>().state.getStatusTaskColor(taskStatus),
                      width: 1.w,
                    ),
                    borderRadius: BorderRadius.circular(16.r),
                    color: context.read<PycBloc>().state.getStatusTaskBackgroundColor(taskStatus),
                  ),
                  child: Text(
                    context.read<PycBloc>().state.getStatusTaskLabel(taskStatus) ?? '',
                    style: getTypoSkin().label5Regular.copyWith(
                          color: context.read<PycBloc>().state.getStatusTaskColor(taskStatus),
                        ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  Widget _buildEndEventInfo() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          if (widget.data?.ticketStatus?.toUpperCase() == 'COMPLETED') ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 150.w,
                  child: Text(
                    'Người thực hiện',
                    style: getTypoSkin().title6Regular.copyWith(
                          color: getColorSkin().ink3,
                        ),
                  ),
                ),
                Expanded(
                  child: Builder(builder: (context) {
                    String assignee = widget.data?.ticketTaskDtoList.firstOrNull?.taskAssignee ?? '';
                    if (context.watch<TicketProcessDetailBloc>().state is UserTaskInfoLoaded) {
                      final userState = context.read<TicketProcessDetailBloc>().state as UserTaskInfoLoaded;
                      final assigneeInfo = userState.getCurrentAssignee();
                      if (assigneeInfo != null) {
                        assignee =
                            '${assigneeInfo.username ?? ''} - ${assigneeInfo.fullName ?? ''} - ${assigneeInfo.title ?? ''}';
                      }
                    }
                    return Text(
                      assignee,
                      style: getTypoSkin().title6Regular.copyWith(
                            color: getColorSkin().ink1,
                          ),
                    );
                  }),
                ),
              ],
            ),
            SizedBox(height: 16.h),
          ],
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 140.w,
                child: Text(
                  'Trạng thái',
                  style: getTypoSkin().title6Regular.copyWith(
                        color: getColorSkin().ink3,
                      ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: context.read<PycBloc>().state.getStatusColor(
                        widget.data?.ticketStatus?.toUpperCase() == 'COMPLETED' ? widget.data?.ticketStatus : 'WAIT'),
                    width: 1.w,
                  ),
                  borderRadius: BorderRadius.circular(16.r),
                  color: context.read<PycBloc>().state.getStatusBackgroundColor(
                      widget.data?.ticketStatus?.toUpperCase() == 'COMPLETED' ? widget.data?.ticketStatus : 'WAIT'),
                ),
                child: Text(
                  context.read<PycBloc>().state.getStatusLabel(widget.data?.ticketStatus?.toUpperCase() == 'COMPLETED'
                          ? widget.data?.ticketStatus
                          : 'WAIT') ??
                      context.read<PycBloc>().state.getStatusLabel('WAIT') ??
                      'Chờ tới lượt',
                  style: getTypoSkin().label5Regular.copyWith(
                        color: context.read<PycBloc>().state.getStatusColor(
                            widget.data?.ticketStatus?.toUpperCase() == 'COMPLETED'
                                ? widget.data?.ticketStatus
                                : 'WAIT'),
                      ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// CustomConnector và ConnectorPainter classes từ code cũ
class CustomConnector extends StatelessWidget {
  final double height;
  final Color lineColor;
  final bool showPlus;
  final bool showArrow;

  const CustomConnector({
    super.key,
    required this.height,
    required this.lineColor,
    this.showPlus = false,
    this.showArrow = false,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 2.w,
        height: height,
        child: Center(
          child: CustomPaint(
            size: Size(2.w, height),
            painter: ConnectorPainter(
              lineColor: lineColor,
              lineWidth: 2.w,
            ),
          ),
        ),
      ),
    );
  }
}

class ConnectorPainter extends CustomPainter {
  final Color lineColor;
  final double lineWidth;

  ConnectorPainter({
    required this.lineColor,
    required this.lineWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final linePaint = Paint()
      ..color = lineColor
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(0, 0, lineWidth, size.height),
      linePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
