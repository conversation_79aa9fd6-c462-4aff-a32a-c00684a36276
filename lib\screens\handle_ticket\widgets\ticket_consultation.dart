import 'dart:developer';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/screens/ticket_other_action/consultation_opinion_screen.dart';
import 'package:eapprove/screens/ticket_other_action/widget/comment_utils.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/consultation_opinion_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/consultation_opinion_state.dart';
import 'package:eapprove/models/ticket_other_action/consultation_opinion_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:intl/intl.dart';

class TicketConsultationBlock extends StatefulWidget {
  final String title;
  final dynamic ticketId;
  final String procInstId;

  const TicketConsultationBlock({
    super.key,
    required this.title,
    required this.ticketId,
    required this.procInstId,
  });

  @override
  State<TicketConsultationBlock> createState() => _TicketConsultationBlockState();
}

class _TicketConsultationBlockState extends State<TicketConsultationBlock> {
  late Map<String, String> ticketInformationData;
  bool _hasLoadedOnce = false;
  String creator = '';
  List<ConsultationOpinionModel> consultationOpinions = [];

  @override
  void initState() {
    super.initState();
    _loadConsultationOpinions();
  }

  void _loadConsultationOpinions() async {
    final int typedTicketId = widget.ticketId is int ? widget.ticketId : int.tryParse(widget.ticketId.toString()) ?? 0;
    context.read<ConsultationOpinionBloc>().add(
          GetConsultationOpinions(
            procInstId: widget.procInstId,
            ticketId: typedTicketId,
          ),
        );
    context.read<TicketProcessDetailBloc>().add(
          LoadUserTaskInfo(ticketId: typedTicketId.toString()),
        );
  }

  Color _getAvatarColor(String username) {
    final colors = [
      getColorSkin().primaryBlue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    int hash = 0;
    for (int i = 0; i < username.length; i++) {
      hash = username.codeUnitAt(i) + ((hash << 5) - hash);
    }

    return colors[hash.abs() % colors.length];
  }

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
          buildWhen: (previous, current) {
            return current is UserTaskInfoLoaded;
          },
          builder: (context, detailState) {
            // Set creator when UserTaskInfoLoaded
            if (detailState is UserTaskInfoLoaded) {
              final c = detailState.getCreator();
              if (c != null) {
                creator = c.getFormattedInfo();
              }
            }

            return BlocBuilder<ConsultationOpinionBloc, ConsultationOpinionState>(
              buildWhen: (previous, current) {
                return current.status == ServiceStatus.loading ||
                    current.status == ServiceStatus.success ||
                    current.status == ServiceStatus.failure;
              },
              builder: (context, state) {
                log('State: ${state.status}, Opinions: ${state.opinions}');

                if (state.status == ServiceStatus.loading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state.status == ServiceStatus.success) {
                  if (!_hasLoadedOnce) {
                    _hasLoadedOnce = true;
                    consultationOpinions = state.opinions;

                    // Sort và giữ lại bài mới nhất
                    consultationOpinions.sort((a, b) {
                      final aDate = a.createdDate ?? 0;
                      final bDate = b.createdDate ?? 0;
                      return bDate.compareTo(aDate);
                    });

                    if (consultationOpinions.isNotEmpty) {
                      consultationOpinions = [consultationOpinions.first]; // Chỉ lấy bài mới nhất
                    }
                  }

                  if (consultationOpinions.isEmpty) {
                    return const SizedBox.shrink();
                  }

                  return CustomExpandedList<String>(
                    expandedSvgIconPath: StringImage.ic_arrow_up,
                    collapsedSvgIconPath: StringImage.ic_arrow_right,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    title: widget.title,
                    cardColor: Colors.white,
                    isBuildLeading: true,
                    svgIconPath: StringImage.ic_ticket_message,
                    iconWidth: 24.w,
                    iconHeight: 24.h,
                    isExpanded: true,
                    titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
                    child: _buildFormContent(consultationOpinions, detailState),
                  );
                } else if (state.status == ServiceStatus.failure) {
                  return Center(child: Text('Error: ${state.errorMessage ?? 'Failed to load opinions'}'));
                }

                return const Center(child: Text('No data available'));
              },
            );
          },
        );
      },
    );
  }

  Widget _buildFormContent(List<ConsultationOpinionModel> opinions, TicketProcessDetailState detailState) {
    if (opinions.isEmpty) return const SizedBox.shrink();

    // Sắp xếp theo thời gian giảm dần (mới nhất trước)
    opinions.sort((a, b) {
      final aDate = a.createdDate ?? 0;
      final bDate = b.createdDate ?? 0;
      return bDate.compareTo(aDate);
    });

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: opinions.map((opinion) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 4.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main comment
                ..._buildOpinionContent(opinion, detailState),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getDisplayUsername(String createdUser, TicketProcessDetailState detailState) {
    String displayUsername = createdUser.isNotEmpty ? createdUser : 'Unknown user';
    if (detailState is UserTaskInfoLoaded) {
      for (final task in detailState.userTaskInfo.data ?? []) {
        for (final assignee in task.lstAssigneeInfo) {
          if (assignee.username?.trim() == createdUser.trim()) {
            displayUsername =
                '${assignee.username} - ${assignee.fullName ?? 'Unknown'} - ${assignee.title ?? 'No Title'}';
            break;
          }
        }
      }
    }
    return displayUsername;
  }

  List<Widget> _buildOpinionContent(ConsultationOpinionModel opinion, TicketProcessDetailState detailState,
      {bool isReply = false}) {
    final displayContent = opinion.content != null ? CommentUtils.convertHtmlToText(opinion.content!) : 'N/A';
    final displayUsername = _getDisplayUsername(opinion.createdUser ?? '', detailState);
    String initial = '';
    if (displayUsername.isNotEmpty) {
      if (displayUsername.contains('-')) {
        final parts = displayUsername.split('-');
        if (parts.length > 1 && parts[1].trim().isNotEmpty) {
          initial = parts[1].trim()[0];
        } else {
          initial = displayUsername[0];
        }
      } else {
        initial = displayUsername[0];
      }
    }
    return [
      GestureDetector(
        onTap: () {
          // Navigate to ConsultationOpinionScreen
          context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ConsultationOpinionScreen(
                  ticketId: widget.ticketId,
                  ticketProcId: widget.procInstId,
                ),
              )).then((_) {
            if (!context.mounted) return;
            context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
          });
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 16.r,
              backgroundColor: _getAvatarColor(opinion.createdUser ?? ''),
              child: Text(
                initial.toUpperCase(),
                style: getTypoSkin().title5Medium.copyWith(
                      color: getColorSkin().white,
                      fontSize: 14.sp,
                    ),
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    displayUsername,
                    softWrap: true,
                    style: getTypoSkin().title5Medium.copyWith(
                          color: getColorSkin().ink1,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    textAlign: TextAlign.start,
                    formatDate(opinion.createdDate ?? 0),
                    style: getTypoSkin().title5Medium.copyWith(
                          color: getColorSkin().ink3,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                        ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    textAlign: TextAlign.start,
                    displayContent,
                    style: getTypoSkin().title5Medium.copyWith(
                          color: getColorSkin().ink1,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ];
  }

  String formatDate(int? timestamp) {
    if (timestamp == null) return '';
    final dt = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat('dd/MM/yyyy HH:mm').format(dt);
  }
}
