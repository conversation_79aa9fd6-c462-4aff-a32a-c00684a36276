import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';

class UserInformationScreen extends StatefulWidget {
  const UserInformationScreen({super.key});

  @override
  State<UserInformationScreen> createState() => _UserInformationScreenState();
}

class _UserInformationScreenState extends State<UserInformationScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: getColorSkin().white,
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: FIcon(icon: FIconData.icBack),
        ),
        title: Text(
          "Thông tin tài khoản",
          style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
        ),
        centerTitle: true,
      ),
      body: Container(
        color: const Color(0xFFF6F6F6), // Màu nền của toàn màn hình
        child: Column(children: [
          Container(
            margin: EdgeInsets.only(top: 10.h),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: getColorSkin().white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.r),
                topRight: Radius.circular(8.r),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize:
                  MainAxisSize.min, // Chỉ chiếm đúng kích thước cần thiết
              children: [
                _buildLabel("Account"),
                _buildReadOnlyField("Nve100000001"),
                _buildLabel("Email"),
                _buildReadOnlyField("<EMAIL>"),
                _buildLabel("Số điện thoại"),
                _buildReadOnlyField("**********"),
                _buildLabel("Chức vụ"),
                _buildReadOnlyField("Lãnh đạo"),
              ],
            ),
          ),
        ]),
      ),
      backgroundColor: Colors.grey[100],
    );
  }
}

Widget _buildLabel(String text) {
  return Padding(
    padding: EdgeInsets.only(top: 10.h, bottom: 5.h),
    child: Text(
      text,
      style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
    ),
  );
}

Widget _buildReadOnlyField(String value) {
  return TextField(
    readOnly: true,
    decoration: InputDecoration(
      filled: true,
      fillColor: getColorSkin().whiteSmoke,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(
          color: getColorSkin().grey4Background,
          width: 1.w,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(
          color: getColorSkin().grey4Background,
          width: 1.w,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.r),
        borderSide: BorderSide(
          color: getColorSkin().grey4Background,
          width: 1.w,
        ),
      ),
      contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      hintText: value,
      hintStyle: getTypoSkin()
          .bodyRegular14
          .copyWith(color: getColorSkin().secondaryText),
    ),
  );
}
