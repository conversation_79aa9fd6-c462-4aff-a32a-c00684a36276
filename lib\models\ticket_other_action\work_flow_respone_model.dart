class WorkflowResponse {
  final int? code;
  final String? message;
  final List<Group>? data;

  WorkflowResponse({
    this.code,
    this.message,
    this.data,
  });

  factory WorkflowResponse.fromJson(Map<String, dynamic> json) {
    return WorkflowResponse(
      code: json['code'] as int?,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Group.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'message': message,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}

class Group {
  final String? group;
  final List<Node>? nodes;

  Group({
    this.group,
    this.nodes,
  });

  factory Group.fromJson(Map<String, dynamic> json) {
    return Group(
      group: json['group'] as String?,
      nodes: (json['nodes'] as List<dynamic>?)
          ?.map((e) => Node.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'group': group,
        'nodes': nodes?.map((e) => e.toJson()).toList(),
      };
}

class Node {
  final String? id;
  final String? name;
  final String? type;
  final int? position;
  final List<PrevNode>? prevNodes;
  final List<NextNode>? nextNodes;
  final String? formKey;
  final bool? sequential;
  final bool? multiInstance;
  final bool? requestUpdate;
  final bool? candidateTask;
  final List<String>? assignee;

  Node({
    this.id,
    this.name,
    this.type,
    this.position,
    this.prevNodes,
    this.nextNodes,
    this.formKey,
    this.sequential,
    this.multiInstance,
    this.requestUpdate,
    this.candidateTask,
    this.assignee,
  });

  factory Node.fromJson(Map<String, dynamic> json) {
    List<String>? assignees;
    if (json['assignee'] != null) {
      if (json['assignee'] is String) {
        assignees = (json['assignee'] as String)
            .replaceAll('[', '')
            .replaceAll(']', '')
            .replaceAll('"', '')
            .split(',')
            .map((e) => e.trim())
            .toList();
      } else if (json['assignee'] is List) {
        assignees = List<String>.from(json['assignee'] as List);
      }
    }

    return Node(
      id: json['id'] as String?,
      name: json['name'] as String?,
      type: json['type'] as String?,
      position: json['position'] as int?,
      prevNodes: (json['prevNodes'] as List<dynamic>?)
          ?.map((e) => PrevNode.fromJson(e as Map<String, dynamic>))
          .toList(),
      nextNodes: (json['nextNodes'] as List<dynamic>?)
          ?.map((e) => NextNode.fromJson(e as Map<String, dynamic>))
          .toList(),
      formKey: json['formKey'] as String?,
      sequential: json['sequential'] as bool?,
      multiInstance: json['multiInstance'] as bool?,
      requestUpdate: json['requestUpdate'] as bool?,
      candidateTask: json['candidateTask'] as bool?,
      assignee: assignees,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'type': type,
        'position': position,
        'prevNodes': prevNodes?.map((e) => e.toJson()).toList(),
        'nextNodes': nextNodes?.map((e) => e.toJson()).toList(),
        'formKey': formKey,
        'sequential': sequential,
        'multiInstance': multiInstance,
        'requestUpdate': requestUpdate,
        'candidateTask': candidateTask,
        'assignee': assignee,
      };
}

class PrevNode {
  final String? id;
  final String? incoming;

  PrevNode({
    this.id,
    this.incoming,
  });

  factory PrevNode.fromJson(Map<String, dynamic> json) {
    return PrevNode(
      id: json['id'] as String?,
      incoming: json['incoming'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'incoming': incoming,
      };
}

class NextNode {
  final String? id;
  final String? incoming;

  NextNode({
    this.id,
    this.incoming,
  });

  factory NextNode.fromJson(Map<String, dynamic> json) {
    return NextNode(
      id: json['id'] as String?,
      incoming: json['incoming'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'incoming': incoming,
      };
}
