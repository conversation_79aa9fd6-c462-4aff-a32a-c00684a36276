import 'package:eapprove/utils/expression_evaluator.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';

import 'dart:developer' as developer;

dynamic getNestedValue(Map<String, dynamic> map, String fieldName) {
  final parts = fieldName.split('.');
  dynamic current = map;
  
  for (final part in parts) {
    if (current is Map && current.containsKey(part)) {
      current = current[part];
    } else {
      return null;
    }
  }
  return current;
}

Map<String, dynamic> evaluateConditions(
  List<Map<String, dynamic>> eventExpressions,
  Map<String, dynamic> formValues,
  String? fromFunction,
  String? name,
) {
  // Duyệt từng rule
  for (final rule in eventExpressions) {
    final String condition = rule['condition'] ?? '';
    final String rawExp = rule['expression'] ?? '';
    

    bool matched = false;
    // debugPrint("evaluateConditions ${condition}");
    try {
      if (condition == 'if' || condition == 'elseif') {
        matched = ExpressionEvaluator.evaluateExpression(rawExp, formValues);
        // developer.log('matched: $name $matched', name: 'evaluateConditions');
      } else if (condition == 'else') {
        matched = true;
      }
    } catch (e, stacktrace) {
      // print('[ExpressionEvaluator] Error evaluating: $rawExp');
    }

    if (matched) {
      // developer.log('evaluateConditionsmatched: $name = $matched', name: 'evaluateConditions');
      final actions = <String, dynamic>{};

      // Sửa lỗi ép kiểu: kiểm tra từng phần tử là Map<String, dynamic>
      final rawProperties = rule['properties'];
      final List<Map<String, dynamic>> properties = (rawProperties is List)
          ? rawProperties
              .where((e) => e is Map<String, dynamic>)
              .map((e) => e as Map<String, dynamic>)
              .toList()
          : <Map<String, dynamic>>[];

      for (final prop in properties) {
        final fieldStr = prop['field'] as String? ?? '';
        final parts = fieldStr.replaceAll('@', '').split('=');

        if (parts.length == 2) {
          final key = parts[0].trim();
          final rawValue = parts[1].trim();
          final value = (rawValue == 'true')
              ? true
              : (rawValue == 'false')
                  ? false
                  : rawValue;

          actions[key] = value;
        }
      }

      // Check if there are no properties but has conditionResult
      if (properties.isEmpty && rule['conditionResult'] != null) {
        final conditionResult = rule['conditionResult'] as String;
        // developer.log('conditionResultconditionResult: $conditionResult', name: 'evaluateConditions');
        if (conditionResult.startsWith('@')) {
          final fieldName = conditionResult.replaceAll('@', '');
          final parts = fieldName.split('.');
          if (parts.length > 1) {
            // Nếu là field từ table
            final tableName = parts[0];
            final fieldName = parts[1];
            // Lấy giá trị từ formValues trực tiếp vì không có access tới FormStateManager instance
            final tableData = formValues[tableName];
            // developer.log('Table data: $tableData', name: 'evaluateConditions');
            
            if (tableData is List && tableData.isNotEmpty) {
              // Lấy tất cả giá trị từ các row
              final values = tableData.map((row) => row[fieldName]).toList();
              // developer.log('Table field values: $values', name: 'evaluateConditions');
              
              // Nếu chỉ có một giá trị, trả về giá trị đó
              if (values.length == 1) {
                actions['value'] = values[0];
              } else {
                // Nếu có nhiều giá trị, trả về mảng các giá trị
                actions['value'] = values;
              }
            } else {
              actions['value'] = null;
            }
            // developer.log('Table field actions: $actions', name: 'evaluateConditions');
          } else {
            // Nếu là field thông thường
            actions['value'] = formValues[fieldName];
            // developer.log('Normal field actions: $actions', name: 'evaluateConditions');
          }
        } else {
          actions['value'] = conditionResult == 'null' ? null : conditionResult;
          // developer.log('Static value actions: $actions', name: 'evaluateConditions');
        }
        return actions;
      }

      return actions;
    }
  }

  // Không có rule nào match
  return {};
}
