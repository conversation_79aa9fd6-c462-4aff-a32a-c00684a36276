import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_other_action/state/work_flow_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/models/pyc/ticket_process_detail_model.dart';
import 'package:eapprove/models/ticket_other_action/work_flow_respone_model.dart';
import 'package:eapprove/screens/common/shimmer_row_loading.dart';
import 'package:eapprove/widgets/custom_expanded_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_info_row.dart';
import 'package:intl/intl.dart';
import 'dart:developer' as developer;

class TicketProcessDetailBlock extends StatefulWidget {
  final String title;
  final dynamic ticketId;
  final dynamic ticket;
  const TicketProcessDetailBlock({
    super.key,
    required this.title,
    required this.ticketId,
    required this.ticket,
  });

  @override
  State<TicketProcessDetailBlock> createState() => TicketProcessDetailBlockState();
}

class TicketProcessDetailBlockState extends State<TicketProcessDetailBlock> {
  UserTaskInfoResponse? _userTaskInfo;
  ThongTinChungModel? _thongTinChungModel;
  Map<String, Map<String, String>> ticketInformationData = {};
  List<StatusContentModel> taskStatusList = [];
  bool _hasLoadedProcessDetail = false;
  String? _currentNodeType;

  void updateStepByNode({
    required String nodeId,
    required String nodeType,
    TicketProcessDetailModel? ticketProcessDetailModel,
    ThongTinChungModel? thongTinChungModel,
    required List<Group> groups,
  }) {
    setState(() {
      _currentNodeType = nodeType;
    });
    developer.log('🔄 [TicketProcessDetailBlock] updateStepByNode called with nodeId: $nodeId');
    developer.log('🔄 Groups count: ${groups.length}');
    developer.log('🔄 Node type: $nodeType');
    final allNodes = groups.expand((g) => g.nodes ?? []).toList();
    developer.log('🔄 All nodes count: ${allNodes.length}');
    final selectedNode = allNodes.firstWhere((n) => n.id == nodeId, orElse: () {
      developer.log('⚠️ Node with id $nodeId not found!');
      return Node();
    });

    developer.log('🔄 Selected node type: ${selectedNode.type}');

    if (nodeType == 'endEvent') {
      developer.log('✅ Calling _updateFormFinishDetail for endEvent');
      _updateFormFinishDetail(thongTinChungModel ?? ThongTinChungModel());
    } else if (nodeType == 'userTask') {
      developer.log('✅ Calling _updateFromTicketDetail for userTask');
      _updateFromTicketDetail(ticketProcessDetailModel ?? TicketProcessDetailModel(code: '', message: ''));
    } else {
      developer.log('⚠️ Unknown node type: $nodeType');
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeData();
    _fetchTaskStatus();

    int? ticketIdInt = int.tryParse(widget.ticketId);
    if (ticketIdInt != null) {
      context.read<TicketProcessDetailBloc>().add(
            LoadTicketProcessDetailById(ticketId: ticketIdInt),
          );
    }

    context.read<TicketProcessDetailBloc>().add(
          LoadUserTaskInfo(ticketId: widget.ticketId),
        );

    developer.log("TicketProcessDetailBlock initialized with ticketId: ${widget.ticketId}");
  }

  void _fetchTaskStatus() {
    context.read<PycBloc>().add(FetchStatusTask(
          StatusTicketRequest(
            code: "STATUS_TASK",
            type: "system",
            page: 1,
            limit: 9999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));
  }

  String formatDateTime(int? timestamp) {
    if (timestamp == null) return '';
    try {
      final dtUtc = DateTime.fromMillisecondsSinceEpoch(timestamp, isUtc: true);
      final dtLocal = dtUtc.toLocal();
      return DateFormat('dd/MM/yyyy HH:mm').format(dtLocal);
    } catch (e) {
      developer.log('Error formatting timestamp $timestamp: $e');
      return '';
    }
  }

  void _initializeData() {
    ticketInformationData = {
      'tenBuoc': {'key': 'tenBuoc', 'label': 'Tên bước', 'value': 'Đang tải...'},
      'trangThai': {'key': 'trangThai', 'label': 'Trạng thái', 'value': 'Đang tải...'},
      'doUuTien': {'key': 'doUuTien', 'label': 'Độ ưu tiên', 'value': 'Đang tải...'},
      'nguoiThucHien': {'key': 'nguoiThucHien', 'label': 'Người thực hiện', 'value': 'Đang tải...'},
      'maCongTy': {'key': 'maCongTy', 'label': 'Mã công ty', 'value': 'Đang tải...'},
      'thoiGianNhan': {'key': 'thoiGianNhan', 'label': 'Thời gian nhận', 'value': 'Đang tải...'},
      'camKetHoanThanh': {'key': 'camKetHoanThanh', 'label': 'Cam kết hoàn thành', 'value': 'Đang tải...'},
      'duKienHoanThanh': {'key': 'duKienHoanThanh', 'label': 'Dự kiến hoàn thành', 'value': 'Đang tải...'},
      'hoanThanhThucTe': {'key': 'hoanThanhThucTe', 'label': 'Hoàn thành thực tế', 'value': 'Đang tải...'},
    };
  }

  void _updateFromTicketDetail(TicketProcessDetailModel data) {
    final taskStatus = data.data?.taskStatus;
    final taskName = data.data?.taskName ?? 'kết thúc';
    final taskAssignee = data.data?.taskAssignee ?? '---';
    final formattedStart = formatDateTime(data.data?.taskCreatedTime);
    final formattedExpectedFinish = formatDateTime(data.data?.finishTime?.expected);
    final taskDefKey = data.data?.taskDefKey;
    String assignee = taskAssignee;
    String? companyCode = data.data?.taskOrgAssignee?['companyCode'] ?? '';

    if (_userTaskInfo != null && taskDefKey != null) {
      final user = UserTaskInfoLoaded(_userTaskInfo!).getAssigneeByTaskDefKey(taskDefKey);
      if (user != null) {
        assignee = '${user.username} - ${user.fullName} - ${user.title}';
        companyCode = '${user.companyCode ?? ''} - ${user.chartShortName ?? ''}';
      }
    }

    final taskStatusName = taskStatus ?? '';
    final slaVal = data.data?.finishTime?.sla;
    String slaFinishHours = '';
    if (slaVal != null) {
      try {
        slaFinishHours = slaVal % 1 == 0 ? '${slaVal.toInt()}h' : '${slaVal}h';
      } catch (e) {
        developer.log('Error parsing SLA: $e');
      }
    }

    setState(() {
      ticketInformationData = {
        'tenBuoc': {
          'key': 'tenBuoc',
          'label': 'Tên bước',
          'value': taskName,
        },
        'trangThai': {
          'key': 'trangThai',
          'label': 'Trạng thái',
          'value': taskStatusName,
        },
        'doUuTien': {
          'key': 'doUuTien',
          'label': 'Độ ưu tiên',
          'value': data.data?.taskPriority ?? 'Trung bình',
        },
        'nguoiThucHien': {
          'key': 'nguoiThucHien',
          'label': 'Người thực hiện',
          'value': assignee,
        },
        'maCongTy': {
          'key': 'maCongTy',
          'label': 'Mã công ty',
          'value': companyCode ?? '',
        },
        'thoiGianNhan': {
          'key': 'thoiGianNhan',
          'label': 'Thời gian nhận',
          'value': formattedStart,
        },
        'camKetHoanThanh': {
          'key': 'camKetHoanThanh',
          'label': 'Cam kết hoàn thành',
          'value': slaFinishHours,
        },
        'duKienHoanThanh': {
          'key': 'duKienHoanThanh',
          'label': 'Dự kiến hoàn thành',
          'value': formattedExpectedFinish,
        },
        'hoanThanhThucTe': {
          'key': 'hoanThanhThucTe',
          'label': 'Hoàn thành thực tế',
          'value': formatDateTime(data.data?.finishTime?.actual)
        },
      };
      _hasLoadedProcessDetail = true;
    });
  }

  void _updateFormFinishDetail(ThongTinChungModel data) {
    const fixedTaskName = 'Kết thúc';
    final rawStatus = data.ticketStatus?.toUpperCase() ?? '';
    final taskStatus = (rawStatus == 'COMPLETED' || rawStatus == 'CLOSED') ? rawStatus : 'WAIT';
    final taskPriority = data.priority ?? '';

    setState(() {
      ticketInformationData = {
        'tenBuoc': {
          'key': 'tenBuoc',
          'label': 'Tên bước',
          'value': fixedTaskName,
        },
        'trangThai': {
          'key': 'trangThai',
          'label': 'Trạng thái',
          'value': taskStatus,
        },
        'doUuTien': {
          'key': 'doUuTien',
          'label': 'Mức độ ưu tiên',
          'value': taskPriority,
        },
        'hoanThanhThucTe': {
          'key': 'hoanThanhThucTe',
          'label': 'Thời gian hoàn thành',
          'value': '',
        },
      };
      _hasLoadedProcessDetail = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        MultiBlocListener(
          listeners: [
            BlocListener<PycBloc, PycState>(
              listenWhen: (prev, curr) =>
                  prev.statusTaskStatus != curr.statusTaskStatus ||
                  prev.statusTaskResponseModel != curr.statusTaskResponseModel,
              listener: (context, state) {
                if (state.statusTaskStatus == PycStatus.loaded &&
                    state.statusTaskResponseModel != null &&
                    state.statusTaskRequestModel != null) {
                  final code = state.statusTaskRequestModel!.code;
                  final list = state.statusTaskResponseModel!.data.content ?? [];
                  if (code == "STATUS_TASK") {
                    setState(() {
                      taskStatusList = list;
                    });
                  }
                }
              },
            ),
            BlocListener<TicketProcessDetailBloc, TicketProcessDetailState>(
              listenWhen: (previous, current) {
                return (current is TicketProcessDetailLoaded && current.ticketProcessDetail.data != null) ||
                    (current is UserTaskInfoLoaded && current.userTaskInfo.data != null) ||
                    (current is TicketProcessDetailByIdLoaded) ||
                    (current is TicketProcessDetailError);
              },
              listener: (context, state) {
                developer.log('[BlocListener] STATE: ${state.runtimeType}');

                if (state is TicketProcessDetailLoaded) {
                  _updateFromTicketDetail(state.ticketProcessDetail);
                }

                if (state is UserTaskInfoLoaded) {
                  setState(() {
                    _userTaskInfo = state.userTaskInfo;
                  });
                }

                // ✅ Lưu trữ thongTinChungModel khi có TicketProcessDetailByIdLoaded
                if (state is TicketProcessDetailByIdLoaded) {
                  setState(() {
                    _thongTinChungModel = state.thongTinChungModel;
                  });

                  // Nếu cũng là WorkflowLoaded thì update form finish detail
                  if (state is WorkflowLoaded) {
                    _updateFormFinishDetail(state.thongTinChungModel);
                  }
                }

                if (state is TicketProcessDetailError && state.errorCode == 400) {
                  developer.log('⚠️ [TicketProcessDetailBlock] API Error 400: ${state.message}');

                  if (_userTaskInfo != null && _currentNodeType != 'endEvent') {
                    developer.log('🔄 [TicketProcessDetailBlock] Using UserTaskInfo as fallback for 400 error');
                    _updateFromUserTaskInfoFallback(_userTaskInfo!, _thongTinChungModel);
                  } else {
                    _updateFormFinishDetail(_thongTinChungModel!);
                  }
                }
              },
            )
          ],
          child: SizedBox.shrink(),
        ),
      
        // The actual UI component
        CustomExpandedList<String>(
          expandedSvgIconPath: StringImage.ic_arrow_up,
          collapsedSvgIconPath: StringImage.ic_arrow_right,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          title: widget.title,
          isProcessDetail: true,
          onResubmitPressed: () {},
          cardColor: Colors.white,
          isBuildLeading: true,
          svgIconPath: StringImage.ic_ticket_info_process,
          iconWidth: 24.w,
          iconHeight: 24.h,
          isExpanded: true,
          titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
          child: _hasLoadedProcessDetail ? _buildFormContent() : buildShimmerRowLoading(),
        ),
      ],
    );
  }

  // ✅ Method để show error state
  void _showErrorState() {
    setState(() {
      ticketInformationData = {
        'tenBuoc': {'key': 'tenBuoc', 'label': 'Tên bước', 'value': 'Lỗi tải dữ liệu'},
        'trangThai': {'key': 'trangThai', 'label': 'Trạng thái', 'value': 'Lỗi'},
        'doUuTien': {'key': 'doUuTien', 'label': 'Độ ưu tiên', 'value': '---'},
        'nguoiThucHien': {'key': 'nguoiThucHien', 'label': 'Người thực hiện', 'value': '---'},
        'maCongTy': {'key': 'maCongTy', 'label': 'Mã công ty', 'value': '---'},
        'thoiGianNhan': {'key': 'thoiGianNhan', 'label': 'Thời gian nhận', 'value': '---'},
        'camKetHoanThanh': {'key': 'camKetHoanThanh', 'label': 'Cam kết hoàn thành', 'value': '---'},
        'duKienHoanThanh': {'key': 'duKienHoanThanh', 'label': 'Dự kiến hoàn thành', 'value': '---'},
        'hoanThanhThucTe': {'key': 'hoanThanhThucTe', 'label': 'Hoàn thành thực tế', 'value': '---'},
      };
      _hasLoadedProcessDetail = true;
    });
  }

  // ✅ Cập nhật method _updateFromUserTaskInfoFallback để handle null
  void _updateFromUserTaskInfoFallback(UserTaskInfoResponse userTaskInfo, ThongTinChungModel? thongTinChungModel) {
    developer.log('🔄 [TicketProcessDetailBlock] Fallback to UserTaskInfo data due to API error');

    final taskName = userTaskInfo.data?.isNotEmpty == true ? userTaskInfo.data!.first.taskName ?? 'N/A' : 'N/A';
    const taskStatusName = 'WAIT'; // Fixed status as requested
    final taskPriority = thongTinChungModel?.priority ?? 'Trung bình';

    // Build assignee info
    String assignee = '';

    if (userTaskInfo.data?.isNotEmpty == true && userTaskInfo.data!.first.lstAssigneeInfo?.isNotEmpty == true) {
      final user = userTaskInfo.data!.first.lstAssigneeInfo!.first;
      assignee = '${user.username ?? ''} - ${user.fullName ?? ''} - ${user.title ?? ''}';
    }

    setState(() {
      ticketInformationData = {
        'tenBuoc': {
          'key': 'tenBuoc',
          'label': 'Tên bước',
          'value': taskName,
        },
        'trangThai': {
          'key': 'trangThai',
          'label': 'Trạng thái',
          'value': taskStatusName,
        },
        'doUuTien': {
          'key': 'doUuTien',
          'label': 'Độ ưu tiên',
          'value': taskPriority,
        },
        'nguoiThucHien': {
          'key': 'nguoiThucHien',
          'label': 'Người thực hiện',
          'value': assignee,
        },
        'thoiGianNhan': {
          'key': 'thoiGianNhan',
          'label': 'Thời gian nhận',
          'value': '',
        },
        'camKetHoanThanh': {
          'key': 'camKetHoanThanh',
          'label': 'Cam kết hoàn thành',
          'value': '',
        },
        'duKienHoanThanh': {
          'key': 'duKienHoanThanh',
          'label': 'Dự kiến hoàn thành',
          'value': '',
        },
        'hoanThanhThucTe': {'key': 'hoanThanhThucTe', 'label': 'Hoàn thành thực tế', 'value': ''},
      };
      _hasLoadedProcessDetail = true;
    });
  }

  Widget _buildStatusBadge(String status) {
    if (status.isEmpty) return Container();

    final blocState = context.read<PycBloc>().state;

    // Áp dụng map chuyển status
    final fixedStatus = PycStateExt.fixStatusCode(status);
    final matched = taskStatusList.firstWhere(
      (item) => item.configValue?.toUpperCase() == fixedStatus,
      orElse: () => StatusContentModel(),
    );

    final statusName = matched.configName ?? fixedStatus;

    final textColor = blocState.getStatusTaskColor(fixedStatus);
    final bgColor = blocState.getStatusTaskBackgroundColor(fixedStatus);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusName,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 12.sp,
          color: textColor,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildFormContent() {
    return Container(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...ticketInformationData.entries.map((entry) => Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: InfoRow(
                  textAlign: TextAlign.left,
                  label: entry.value['label'] ?? '',
                  value: entry.value['value'] ?? '',
                  textStyle: getTypoSkin().label4Regular.copyWith(color: getColorSkin().ink1),
                  widgetValue: entry.key == 'trangThai'
                      ? Expanded(
                          flex: 5,
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: _buildStatusBadge(entry.value['value'].toString()),
                          ))
                      : null,
                ),
              )),
        ],
      ),
    );
  }
}
