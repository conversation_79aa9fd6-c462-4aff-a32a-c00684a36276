import 'package:eapprove/models/authorize_management/authorize_condition_model.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/screens/authorize_management/widgets/authorize_condition_card.dart';
import 'package:eapprove/screens/authorize_management/widgets/authorize_document_card.dart';
import 'package:eapprove/screens/authorize_management/widgets/authorize_information_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';

class AuthorizationDetail extends StatefulWidget {
  final String title;
  final int initialTabIndex;
  final AuthorizeItemData request;

  const AuthorizationDetail({
    super.key,
    required this.title,
    required this.request,
    this.initialTabIndex = 0,
  });

  @override
  State<AuthorizationDetail> createState() => _AuthorizationDetailState();
}

class _AuthorizationDetailState extends State<AuthorizationDetail> {
  final isTablet = DeviceUtils.isTablet;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: GradientBackground(
        child: Column(
          children: [
            // Header
            CustomHeader(
              title: widget.title,
              onBack: () {
                Navigator.pop(context); // Quay lại màn hình trước
              },
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    children: [
                      CardInformationDetail(
                        isDisabled: true,
                        request: widget.request,
                      ),
                      SizedBox(height: 10.h),
                      if (widget.request.authorityConditions != null &&
                          widget.request.authorityConditions!.isNotEmpty)
                        CardConditionAuthorize(
                          condition: AuthorizeConditionResponse.fromJson(
                              widget.request.authorityConditions!),
                        ),
                      if (widget.request.authorityConditions != null &&
                          widget.request.authorityConditions!.isNotEmpty)
                        SizedBox(height: 10.h),
                      CardDocumentAuthorize(
                          request: widget.request,
                          onDownload: (document) async {
                            await document.downloadFile();
                          },
                          document: DocumentModel(
                              url: widget.request.fileName,
                              id: widget.request.id.toString(),
                              extension:
                                  widget.request.fileName.split('.').last,
                              name: widget.request.fileName.split('/').last,
                              type: FileType.pdf,
                              size: '')),
                      SizedBox(height: 40.h),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomHeader extends StatefulWidget {
  final String title;
  final VoidCallback onBack;

  const CustomHeader({
    super.key,
    required this.title,
    required this.onBack,
  });

  @override
  State<CustomHeader> createState() => _CustomHeaderState();
}

class _CustomHeaderState extends State<CustomHeader> {
  bool _isExpanded = false; // Trạng thái để kiểm soát việc mở rộng tiêu đề

  @override
  Widget build(BuildContext context) {
    final double maxHeight = MediaQuery.of(context).size.height * 0.3;

    return Container(
      color: const Color(0xFF30AEEF), // Nền màu xanh
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                    onPressed: widget.onBack, // Gọi callback khi nhấn nút
                  ),
                  const Text(
                    'Trở lại',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white, // Chữ màu trắng
                    ),
                  ),
                ],
              ),
              LayoutBuilder(
                builder: (context, constraints) {
                  final textSpan = TextSpan(
                    text: widget.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white, // Chữ màu trắng
                    ),
                  );

                  final textPainter = TextPainter(
                    text: textSpan,
                    maxLines: 2,
                    textDirection: TextDirection.ltr,
                  )..layout(maxWidth: constraints.maxWidth);

                  final isOverflowing = textPainter.didExceedMaxLines;

                  return ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: maxHeight,
                    ),
                    child: SingleChildScrollView(
                      physics: _isExpanded
                          ? const BouncingScrollPhysics()
                          : const NeverScrollableScrollPhysics(),
                      child: Padding(
                        padding:
                            const EdgeInsets.only(left: 8, right: 8, bottom: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.title,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.white, // Chữ màu trắng
                              ),
                              maxLines: _isExpanded
                                  ? null
                                  : 2, // Hiển thị 2 dòng hoặc toàn bộ
                              overflow: _isExpanded
                                  ? TextOverflow.visible
                                  : TextOverflow.ellipsis,
                            ),
                            if (isOverflowing) // Hiển thị nút "Xem thêm" nếu tiêu đề dài
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _isExpanded =
                                        !_isExpanded; // Đổi trạng thái mở rộng
                                  });
                                },
                                child: Text(
                                  _isExpanded ? 'Thu gọn' : 'Xem thêm',
                                  style: const TextStyle(
                                      fontSize: 16,
                                      color: Color.fromRGBO(79, 70, 229, 0.7)),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
