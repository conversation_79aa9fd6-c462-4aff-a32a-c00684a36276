import 'package:equatable/equatable.dart';

enum AssistantOpinionStatus { initial, loading, success, failure }

class AssistantOpinionState extends Equatable {
  final AssistantOpinionStatus status;
  final List<Object> url;
  final String? error;

  const AssistantOpinionState({
    required this.status,
    required this.url,
    this.error,
  });

  factory AssistantOpinionState.initial() => AssistantOpinionState(
        status: AssistantOpinionStatus.initial,
        url: [],
        error: null,
      );

  AssistantOpinionState copyWith({
    AssistantOpinionStatus? status,
    List<Object>? url,
    String? error,
  }) {
    return AssistantOpinionState(
      status: status ?? this.status,
      url: url ?? this.url,
      error: error,
    );
  }

  @override
  List<Object?> get props => [status, url, error];
}
