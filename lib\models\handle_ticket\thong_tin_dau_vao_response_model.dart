class ThongTinDauVaoResponseModel {
  final dynamic code;
  final Data data;
  final dynamic message;

  ThongTinDauVaoResponseModel({
    required this.code,
    required this.data,
    required this.message,
  });

  factory ThongTinDauVaoResponseModel.fromJson(Map<dynamic, dynamic> json) {
    return ThongTinDauVaoResponseModel(
      code: json['code'],
      data: Data.fromJson(json['data'] as Map<dynamic, dynamic>),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'data': data.toJson(),
      'message': message,
    };
  }
}

class Data {
  final List<Variable> listVariables;

  Data({
    required this.listVariables,
  });

  factory Data.fromJson(Map<dynamic, dynamic> json) {
    return Data(
      listVariables: (json['listVariables'] as List<dynamic>).map((e) => Variable.fromJson(e as Map<dynamic, dynamic>)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'listVariables': listVariables.map((e) => e.toJson()).toList(),
    };
  }
}

class Variable {
  final dynamic additionalVal;
  final dynamic name;
  final dynamic type;
  final dynamic value;
  final dynamic taskId;

  Variable({
    required this.additionalVal,
    required this.name,
    required this.type,
    required this.value,
    required this.taskId,
  });

  factory Variable.fromJson(Map<dynamic, dynamic> json) {
    return Variable(
      additionalVal: json['additionalVal'],
      name: json['name'],
      type: json['type'],
      value: json['value'],
      taskId: json['taskId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'additionalVal': additionalVal,
      'name': name,
      'type': type,
      'value': value,
      'taskId': taskId,
    };
  }
}