import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:eapprove/module_main.dart';
import 'package:eapprove/services/network_manager.dart';
import 'package:eapprove/services/token.dart';
import 'package:flutter/foundation.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:eapprove/screens/common/error_handler.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'package:http_parser/http_parser.dart';
import 'dart:developer' as developer;


class NoInternetException implements Exception {
  final String message;
  NoInternetException(this.message);
  @override
  String toString() => message;
}

class ApiService {
  final http.Client httpClient;
  final String baseUrl;
  final String adminUrl = 'https://uat-admin.datxanh.com.vn/';
  final String getListUrl = 'https://api.eapprove.online';
  static const String _notiBaseUrl = 'https://uat-noti.datxanh.com.vn';
  final String sso = 'http://uat-api-sso.datxanh.com.vn/';
  final Logger _logger = LoggerConfig.logger;
  final TokenManager _tokenManager;
  final NetworkManager _networkManager = NetworkManager();

  ApiService(
      {required this.httpClient,
      required TokenManager tokenManager,
      this.baseUrl = 'https://uat-api-eapp.datxanh.com.vn/'})
      : _tokenManager = tokenManager;

  Future<Uint8List> getBinary(String endpoint,
      {Map<String, String>? headers, String? customBaseUrl}) async {
    return _networkManager.executeWithRetry<Uint8List>(
      action: () async {
        try {
          final finalHeaders = _getDefaultHeaders();
          if (headers != null) {
            finalHeaders.addAll(headers);
          }
          finalHeaders.remove('Content-Type');

          final url = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');
          _logger.d('GET Binary Request: $url');

          final token = await _tokenManager.getFormattedToken();
          if (token != null) {
            finalHeaders['Authorization'] = token;
          }
          debugPrint('token from get binary: $token');
          // Create request with headers
          final request = http.Request('GET', url);
          request.headers.addAll(finalHeaders);

          // Use a direct client for better performance
          final client = http.Client();
          try {
            final streamedResponse = await client.send(request);

            if (streamedResponse.statusCode == 200) {
              // Use a buffer to collect bytes more efficiently
              final bytes = <int>[];
              await for (final chunk in streamedResponse.stream) {
                bytes.addAll(chunk);
              }
              return Uint8List.fromList(bytes);
            } else {
              final responseString = await streamedResponse.stream.bytesToString();
              _logger.e(
                  'GET binary request failed: Status ${streamedResponse.statusCode}, Response: $responseString');
              throw Exception(
                  'Failed to download file: ${streamedResponse.statusCode}');
            }
          } finally {
            client.close();
          }
        } on NetworkException {
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } on SocketException catch (e) {
          _logger.e('Network error in getBinary: $e');
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } catch (e, stackTrace) {
          _logger.e('GET binary request error: $e, stackTrace: $stackTrace');
          rethrow;
        }
      },
      requestIdentifier: 'BINARY:${_getBaseUrl(customBaseUrl)}$endpoint',
    );
  }

  Future<http.Response> get(String endpoint,
      {Map<String, String>? headers, String? customBaseUrl}) async {
    return _networkManager.executeWithRetry<http.Response>(
      action: () async {
        try {
          final finalHeaders = _getDefaultHeaders();
          if (headers != null) {
            finalHeaders.addAll(headers);
          }
          finalHeaders.remove('Content-Type');
          debugPrint('start get api service');

          // Get token before creating request
          final token = await _tokenManager.getFormattedToken();
          if (token != null) {
            finalHeaders['Authorization'] = token;
          }

          final url = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');

          _logger.d('GET Request: $url');
          _logger.d('Headers: $finalHeaders');

          final response = await httpClient.get(url, headers: finalHeaders);

          // Add detailed logging of response
          _logger.d('Response Status Code: ${url} ${response.statusCode}');
          // _logger.d('Response Body: ${response.body}');
          // _logger.d('Response Headers: ${response.headers}');

          return response;
        } on NetworkException {
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } on SocketException catch (e) {
          _logger.e('Network error in GET request: $e');
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } catch (e) {
          _logger.e('GET request error: $e');
          throw Exception(ErrorHandler.getErrorMessage(e));
        }
      },
      requestIdentifier: 'GET:${_getBaseUrl(customBaseUrl)}$endpoint',
    );
  }

  Future<http.Response> post(String endpoint, dynamic body,
      {Map<String, String>? headers, String? customBaseUrl}) async {
    return _networkManager.executeWithRetry<http.Response>(action: () async {
      try {
        final finalHeaders = _getDefaultHeaders();
        if (headers != null) {
          finalHeaders.addAll(headers);
        }

        // Get token before creating request
        final token = await _tokenManager.getFormattedToken();
        if (token != null) {
          finalHeaders['Authorization'] = token;
        }

        final url = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');

        _logger.d('POST Request: $url');
        _logger.d('Headers: $finalHeaders');
        _logger.d('POST - Body: $body');

        final String jsonBody = jsonEncode(body);

        // Create a new client for each request to ensure isolation
        final client = http.Client();
        try {
          final response =
              await client.post(url, headers: finalHeaders, body: jsonBody);

          // _logger.d('POST - Response Status: ${url} ${response.statusCode}');
          // _logger.d('POST - Response Body: ${url}  ${response.body}');

          // developer.log('POST - Response Status: ${response.statusCode} ${response.body}', name: 'post');

          return response;
        } finally {
          client.close();
        }
      } on NoInternetException {
        rethrow;
      } on SocketException catch (e) {
        _logger.e('Network error in POST request: $e');
        throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
      } catch (e) {
        _logger.e('POST request error: $e');
        throw Exception(ErrorHandler.getErrorMessage(e));
      }
    });
  }

  Future<http.Response> put(String endpoint, dynamic body,
      {Map<String, String>? headers, String? customBaseUrl}) async {
    return _networkManager.executeWithRetry<http.Response>(
      action: () async {
        try {
          final finalHeaders = _getDefaultHeaders();
          if (headers != null) {
            finalHeaders.addAll(headers);
          }

          final url = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');

          _logger.d('PUT Request: $url');
          _logger.d('Headers: $finalHeaders');
          _logger.d('Body: $body');

          final String jsonBody = jsonEncode(body);
          final response =
              await httpClient.put(url, headers: finalHeaders, body: jsonBody);

          return response;
        } on NetworkException {
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } on SocketException catch (e) {
          _logger.e('Network error in PUT request: $e');
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } catch (e) {
          _logger.e('PUT request error: $e');
          throw Exception(ErrorHandler.getErrorMessage(e));
        }
      },
      requestIdentifier: 'PUT:${_getBaseUrl(customBaseUrl)}$endpoint',
    );
  }

  Future<http.Response> delete(String endpoint,
      {Map<String, String>? headers, String? customBaseUrl, dynamic body, Map<String, String>? queryParams}) async {
    return _networkManager.executeWithRetry<http.Response>(
      action: () async {
        try {
          final finalHeaders = _getDefaultHeaders();
          if (headers != null) {
            finalHeaders.addAll(headers);
          }

          var uri = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');
          
          if (queryParams != null && queryParams.isNotEmpty) {
            uri = uri.replace(
              queryParameters: queryParams.map((key, value) => MapEntry(key, value.toString())),
            );
          }

          _logger.d('DELETE Request: $uri');
          _logger.d('Headers: $finalHeaders');
          _logger.d('Body: $body');

          final request = http.Request('DELETE', uri);
          request.headers.addAll(finalHeaders);
          
          if (body != null) {
            request.body = jsonEncode(body);
          }
          
          final streamedResponse = await httpClient.send(request);
          final response = await http.Response.fromStream(streamedResponse);

          return response;
        } on NetworkException {
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } on SocketException catch (e) {
          _logger.e('Network error in DELETE request: $e');
          throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
        } catch (e) {
          _logger.e('DELETE request error: $e');
          throw Exception(ErrorHandler.getErrorMessage(e));
        }
      },
      requestIdentifier: 'DELETE:${_getBaseUrl(customBaseUrl)}$endpoint',
    );
  }
  Future<http.Response> uploadSingleFile(String endpoint, Map<String, dynamic> fileData, {Map<String, dynamic>? extraData}) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      // Create multipart request
      final request = http.MultipartRequest('POST', uri);
      final finalHeaders = _getDefaultHeaders();
      final token = await _tokenManager.getFormattedToken();
      if (token != null) {
        finalHeaders['Authorization'] = token;
      }
      request.headers.addAll(finalHeaders);

      // Add any extra form fields
      if (extraData != null) {
        extraData.forEach((key, value) {
          if (value != null) {
            request.fields[key] = value.toString();
          }
        });
      }

      final fileName = fileData['name'];

      // Check if we have bytes or need to read from path
      if (fileData['bytes'] != null) {
        // Use bytes directly
        request.files.add(
            http.MultipartFile.fromBytes(
              'file', // field name - adjust as needed for your API
              fileData['bytes'],
              filename: fileName,
              contentType: MediaType.parse(fileData['mimeType'] ?? 'application/octet-stream'),
            )
        );
      } else if (fileData['path'] != null) {
        // Read from file path
        final file = File(fileData['path']);
        request.files.add(
            await http.MultipartFile.fromPath(
              'file', // field name
              file.path,
              filename: fileName,
              contentType: MediaType.parse(fileData['mimeType'] ?? 'application/octet-stream'),
            )
        );
      }

      // Add note or other metadata if needed
      if (fileData['note'] != null && fileData['note'].isNotEmpty) {
        request.fields['note'] = fileData['note'];
      }

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return response;
    } catch (e) {
      log('⛔ Upload single file error: $e');
      throw Exception('Error uploading file: $e');
    }
  }

  Future<http.Response> uploadFiles(String endpoint, List<Map<String, dynamic>> files, {Map<String, dynamic>? extraData}) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      // Create multipart request
      final request = http.MultipartRequest('POST', uri);
      final finalHeaders = _getDefaultHeaders();
      final token = await _tokenManager.getFormattedToken();
      if (token != null) {
        finalHeaders['Authorization'] = token;
      }
      request.headers.addAll(finalHeaders);

      if (extraData != null) {
        extraData.forEach((key, value) {
          if (value != null) {
            request.fields[key] = value.toString();
          }
        });
      }

      for (int i = 0; i < files.length; i++) {
        final fileData = files[i];
        final fileName = fileData['name'];

        // Check if we have bytes or need to read from path
        if (fileData['bytes'] != null) {
          // Use bytes directly
          request.files.add(
              http.MultipartFile.fromBytes(
                'files', // Changed from 'files[$i]' to just 'files'
                fileData['bytes'],
                filename: fileName,
                contentType: MediaType.parse(fileData['mimeType'] ?? 'application/octet-stream'),
              )
          );
        } else if (fileData['path'] != null) {
          // Read from file path
          final file = File(fileData['path']);
          request.files.add(
              await http.MultipartFile.fromPath(
                'files', // Changed from 'files[$i]' to just 'files'
                file.path,
                filename: fileName,
                contentType: MediaType.parse(fileData['mimeType'] ?? 'application/octet-stream'),
              )
          );
        }

        // Add note or other metadata if needed
        if (fileData['note'] != null && fileData['note'].isNotEmpty) {
          request.fields['notes[$i]'] = fileData['note']; // Notes might still be indexed
        }
      }

      debugPrint('token from get binary: $token');
      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      debugPrint('response Upload from get binary: ${response.statusCode} - ${response.body}');

      return response;
    } catch (e) {
      log('⛔ Upload files error: $e');
      throw Exception('Error uploading files: $e');
    }
  }

  Map<String, String> _getDefaultHeaders() {
    return {
      'Content-Type': 'application/json',
      'accept': 'application/json, text/plain, */*',
    };
  }

  String _getBaseUrl(String? customBaseUrl) {
    if (customBaseUrl == 'admin') {
      return adminUrl;
    }
    if (customBaseUrl == 'getList') {
      return getListUrl;
    }
    if (customBaseUrl == 'noti') {
      return _notiBaseUrl;
    }
    if (customBaseUrl == 'sso') {
      return sso;
    }
    return customBaseUrl ?? baseUrl;
  }

  Future<http.Response> multipartRequest(
    String endpoint, {
    Map<String, String>? fields,
    Map<String, String>? files,
    String? customBaseUrl,
  }) async {
    try {
      final uri = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');
      
      final request = http.MultipartRequest('POST', uri);
      final finalHeaders = _getDefaultHeaders();
      
      final token = await _tokenManager.getFormattedToken();
      if (token != null) {
        finalHeaders['Authorization'] = token;
      }
      
      finalHeaders.remove('Content-Type');
      request.headers.addAll(finalHeaders);

      if (fields != null) {
        fields.forEach((key, value) {
          request.fields[key] = value;
        });
      }

      if (files != null) {
        for (var entry in files.entries) {
          final filePath = entry.value;
          final file = File(filePath);
          final fileName = file.path.split('/').last;
          
          request.files.add(
            await http.MultipartFile.fromPath(
              entry.key, 
              file.path,
              filename: fileName,
            ),
          );
        }
      }

      _logger.d('MULTIPART Request: $uri');
      _logger.d('Headers: ${request.headers}');
      _logger.d('Fields: ${request.fields}');
      
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      _logger.d('Response Status Code: ${response.statusCode}');
      _logger.d('Response Body: ${response.body}');
      
      return response;
    } on SocketException catch (e) {
      _logger.e('Network error in MULTIPART request: $e');
      throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
    } catch (e) {
      _logger.e('MULTIPART request error: $e');
      throw Exception('Lỗi khi gửi file: ${ErrorHandler.getErrorMessage(e)}');
    }
  }
  
  Future<http.Response> multipartRequestWithMultipleFiles(
    String endpoint, {
    Map<String, String>? fields,
    required List<String> filePaths,
    String? customBaseUrl,
  }) async {
    try {
      final uri = Uri.parse('${_getBaseUrl(customBaseUrl)}$endpoint');
      
      final request = http.MultipartRequest('POST', uri);
      final finalHeaders = _getDefaultHeaders();
      
      final token = await _tokenManager.getFormattedToken();
      if (token != null) {
        finalHeaders['Authorization'] = token;
      }
      
      finalHeaders.remove('Content-Type');
      request.headers.addAll(finalHeaders);

      if (fields != null) {
        fields.forEach((key, value) {
          request.fields[key] = value;
        });
      }

      for (var filePath in filePaths) {
        final file = File(filePath);
        final fileName = file.path.split('/').last;
        
        request.files.add(
          await http.MultipartFile.fromPath(
            'file', 
            file.path,
            filename: fileName,
          ),
        );
      }

      _logger.d('MULTIPART Request with multiple files: $uri');
      _logger.d('Headers: ${request.headers}');
      _logger.d('Fields: ${request.fields}');
      _logger.d('Files count: ${filePaths.length}');
      
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      _logger.d('Response Status Code: ${response.statusCode}');
      _logger.d('Response Body: ${response.body}');
      
      return response;
    } on SocketException catch (e) {
      _logger.e('Network error in MULTIPART request with multiple files: $e');
      throw NoInternetException('Vui lòng kiểm tra kết nối Internet');
    } catch (e) {
      _logger.e('MULTIPART request with multiple files error: $e');
      throw Exception('Lỗi khi gửi nhiều file: ${ErrorHandler.getErrorMessage(e)}');
    }
  }
}
