[{"id": "fieldItem_375f04d0-8055-4ef5-b153-e56565f88ddb", "step": "", "row": "row_47e557a8-5ed7-43c2-9036-059fab6b7cf3", "col": "column_51574617-a581-4399-ac6a-2fb459d65b8f", "rowSortOrder": 3, "colSortOrder": 0, "fieldSortOrder": 1, "tab": "", "tabItem": "", "parentName": "", "splitter": "spl__910d7c81-8f23-4a83-bcfd-3d87f84b51b9", "validations": {"required": false}, "type": "select", "label": "iD chi nhánh", "name": "slt_idChiNhanh", "eventExpression": [], "isHorizontal": 2, "fontWeight": "", "display": false, "displayName": "iD chi nhánh", "typeOption": "api_link", "optionType": "api_link", "option": [{"label": "option 01", "value": "option 01"}, {"label": "option 02", "value": "option 02"}], "value": "", "optionConfig": {"isMulti": false, "orgchart": [], "orgchartType": "", "valueField": "", "feedDataTo": [], "orgchartExpression": [], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "", "masterDataName": "", "masterDataId": "", "masterDataValue": "", "mdExpression": [], "apiURLBase": 392, "apiURL": "/customer/chart/loadChartFilter", "apiMethod": "POST", "apiDisplay": ["code", "name"], "apiValue": "code", "fieldGetData": "", "addHeaderTemplate": "", "responseTemplate": "{\"fieldData\":\"data\"}", "apiExpression": [{"input": "{ \"search\": \"\", \"page\": \"1\", \"limit\": \"9999999\", \"listType\": [ \"import\" ], \"sortBy\": \"id\", \"sortType\": \"ASC\"}", "expression": "=", "condition": "and", "fieldType": "", "type": "formData", "defaultValue": "", "objectInput": [{"type": "paragraph", "children": [{"text": "{"}]}, {"type": "paragraph", "children": [{"text": " \"search\": \"\","}]}, {"type": "paragraph", "children": [{"text": " \"page\": \"1\","}]}, {"type": "paragraph", "children": [{"text": " \"limit\": \"9999999\","}]}, {"type": "paragraph", "children": [{"text": " \"listType\": ["}]}, {"type": "paragraph", "children": [{"text": " \"import\""}]}, {"type": "paragraph", "children": [{"text": " ],"}]}, {"type": "paragraph", "children": [{"text": " \"sortBy\": \"id\","}]}, {"type": "paragraph", "children": [{"text": " \"sortType\": \"ASC\""}]}, {"type": "paragraph", "children": [{"text": "}"}]}]}], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": false, "valueField": "", "readonly": true, "defaultSelect": true, "autoLoadAll": false}, {"id": "fieldItem_d2859fc9-93f9-4bdd-9dd8-e75039177de9", "step": "", "row": "row_0fb1487e-9203-452d-9354-0f90aa5651f2", "col": "column_73201d8a-3e69-4c4d-b18d-5fbdcf4e6362", "rowSortOrder": 2, "colSortOrder": 0, "fieldSortOrder": 3, "tab": "", "tabItem": "", "parentName": "", "splitter": "spl__e923daa5-c0e7-4042-9290-6031350cdb72", "validations": {"required": true}, "type": "select", "label": "Ban tổng giám đốc phê duyệt", "name": "slt_banTongGiamDocPheDuyet", "tooltip": "", "eventExpression": [], "isHorizontal": 2, "fontWeight": "", "display": true, "displayName": "Ban tổng giám đốc phê duyệt", "isClearOnClone": true, "isConditionAuth": false, "typeOption": "masterData", "optionType": "masterData", "option": [{"label": "employee", "value": "employee"}], "value": "", "optionConfig": {"isMulti": false, "orgchart": [], "isLoadAll": false, "orgchartType": "user", "displayField": ["username", "fullname", "title"], "valueField": "username", "feedDataTo": [{"input": "@txt_feedToAccountBanTgd", "fieldFeed": "maNhan<PERSON><PERSON>", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_feedToAccountBanTgd", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_feedToAccountBanTgd2", "fieldFeed": "maNhan<PERSON><PERSON>", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_feedToAccountBanTgd2", "children": [{"text": ""}]}, {"text": ""}]}]}], "orgchartExpression": [{"input": "Tổng <PERSON> đốc", "expression": "like", "condition": "and", "fieldType": "title", "type": "user", "objectInput": [{"type": "paragraph", "children": [{"text": "Tổng <PERSON> đốc"}]}], "orgchartType": "user"}, {"input": "********", "expression": "=", "condition": "and", "fieldType": "code", "type": "user", "objectInput": [{"type": "paragraph", "children": [{"text": "********"}]}], "orgchartType": "user"}], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "createMaster", "masterDataName": "", "masterDataId": "-1377548921987754802", "masterDataDisplay": ["truong<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tenDayDuNhanVien"], "masterDataValue": "truong<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mdExpression": [], "masterDataFilterId": "0dr0U", "apiURLBase": 1, "apiURL": "/customer/userInfo/getUserInfoByChartNodeLevel", "apiMethod": "GET", "apiDisplay": ["username", "name", "<PERSON><PERSON><PERSON>"], "apiValue": "username", "fieldGetData": "", "addHeaderTemplate": "{}", "responseTemplate": "{\"fieldData\":\"data.data\", \"fieldStatus\":\"data.code\", \"fieldMessage\":\"data.message\", \"codeSuccess\":\"1\"}", "apiExpression": [{"input": "@system_chartId_service", "expression": "=", "condition": "and", "fieldType": "", "type": "chartId", "defaultValue": "1632", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "system_chartId_service", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@slt_nguoiDeNghi", "expression": "=", "condition": "and", "fieldType": "", "type": "username", "defaultValue": "employee", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "slt_nguoiDeNghi", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "2", "expression": "=", "condition": "and", "fieldType": "", "type": "level", "defaultValue": "2", "objectInput": [{"type": "paragraph", "children": [{"text": "2"}]}]}], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": false, "displayField": ["username", "fullname", "title"], "valueField": "username", "readonly": false, "defaultSelect": true, "autoLoadAll": false, "isAutoGetLogo": false, "isApprovedBudget": false, "isBusinessPermit": false, "isApprovedBudgetChart": false, "approvedBudgetChartType": "0"}, {"id": "fieldItem_41c85f2e-e948-46c9-96b6-f9a89530e6e1", "step": "", "row": "row_47e557a8-5ed7-43c2-9036-059fab6b7cf3", "col": "column_51574617-a581-4399-ac6a-2fb459d65b8f", "rowSortOrder": 3, "colSortOrder": 0, "fieldSortOrder": 0, "tab": "", "tabItem": "", "parentName": "", "splitter": "spl__910d7c81-8f23-4a83-bcfd-3d87f84b51b9", "validations": {"required": true}, "type": "select", "label": "<PERSON><PERSON> chi nha<PERSON>nh", "name": "slt_maChiNhanh", "eventExpression": [{"condition": "if", "expression": "@txt_companycode!=@txt_parentcode&&@txt_parentcode!=''&&@txt_parentcode!=null", "expressionObject": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_companycode", "children": [{"text": ""}]}, {"text": "!="}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": "&&"}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": "!=''&&"}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": "!=null"}]}], "conditionResult": "", "conditionResultObject": [], "properties": [{"field": "@switchField=CN", "fieldObject": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "switchField", "children": [{"text": ""}]}, {"text": "=CN"}]}]}]}, {"condition": "elseif", "expression": "@txt_companycode==@txt_parentcode&&@txt_parentcode!=''&&@txt_parentcode!=null", "expressionObject": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_companycode", "children": [{"text": ""}]}, {"text": "=="}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": "&&"}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": "!=''&&"}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": "!=null"}]}], "conditionResult": "", "conditionResultObject": [], "properties": [{"field": "@switchField=Cty", "fieldObject": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "switchField", "children": [{"text": ""}]}, {"text": "=Cty"}]}]}]}, {"condition": "else", "expression": "", "expressionObject": [], "conditionResult": "", "conditionResultObject": [], "properties": []}], "isHorizontal": 2, "fontWeight": "", "display": true, "switchField": true, "switchFieldConfig": [{"validations": {"required": true}, "type": "select", "label": "<PERSON><PERSON> chi nha<PERSON>nh", "name": "slt_maChiNhanh", "eventExpression": [], "isHorizontal": 2, "fontWeight": "", "display": true, "displayName": "<PERSON><PERSON> ty/ Chi n<PERSON>nh", "isClearOnClone": false, "typeOption": "api_link", "optionType": "api_link", "option": [{"label": "option 01", "value": "option 01", "tooltip": ""}, {"label": "option 02", "value": "option 02", "tooltip": ""}], "value": "", "optionConfig": {"isMulti": false, "orgchart": [], "isLoadAll": false, "orgchartType": "", "valueField": "", "feedDataTo": [], "orgchartExpression": [], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "", "masterDataName": "", "masterDataId": "", "masterDataValue": "", "mdExpression": [], "apiURLBase": 379, "apiURL": "/api/master-data/general/sapBusinessPlace/getByEntity", "apiMethod": "POST", "apiDisplay": ["businessPlace"], "apiValue": "businessPlace", "fieldGetData": "", "addHeaderTemplate": "", "responseTemplate": "{\"fieldData\":\"data.data\", \"fieldStatus\":\"data.code\", \"fieldMessage\":\"data.message\", \"codeSuccess\":\"API-000\"}", "apiExpression": [{"input": "@txt_companycode", "expression": "=", "condition": "and", "fieldType": "", "type": "businessPlace", "defaultValue": "1000", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_companycode", "children": [{"text": ""}]}, {"text": ""}]}]}], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": false, "valueField": "", "readonly": false, "defaultSelect": true, "autoLoadAll": false, "switchFieldType": "select", "parentName": "", "switchFieldName": "CN"}, {"validations": {"required": true}, "type": "select", "label": "<PERSON><PERSON> chi nha<PERSON>nh", "name": "slt_maChiNhanh", "eventExpression": [], "isHorizontal": 2, "fontWeight": "", "display": true, "displayName": "<PERSON><PERSON> ty/ Chi n<PERSON>nh", "isClearOnClone": false, "typeOption": "api_link", "optionType": "api_link", "option": [{"label": "option 01", "value": "option 01", "tooltip": ""}, {"label": "option 02", "value": "option 02", "tooltip": ""}], "value": "", "optionConfig": {"isMulti": false, "orgchart": [], "isLoadAll": false, "orgchartType": "", "valueField": "", "feedDataTo": [], "orgchartExpression": [], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "", "masterDataName": "", "masterDataId": "", "masterDataValue": "", "mdExpression": [], "apiURLBase": 379, "apiURL": "/api/master-data/general/sapBusinessPlace/getByEntity", "apiMethod": "POST", "apiDisplay": ["businessPlace"], "apiValue": "businessPlace", "fieldGetData": "", "addHeaderTemplate": "", "responseTemplate": "{\"fieldData\":\"data.data\", \"fieldStatus\":\"data.code\", \"fieldMessage\":\"data.message\", \"codeSuccess\":\"API-000\"}", "apiExpression": [{"input": "@txt_parentcode", "expression": "=", "condition": "and", "fieldType": "", "type": "companyCode", "defaultValue": "1000", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_parentcode", "children": [{"text": ""}]}, {"text": ""}]}]}], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": false, "valueField": "", "readonly": false, "defaultSelect": true, "autoLoadAll": false, "switchFieldType": "select", "parentName": "", "switchFieldName": "Cty"}], "displayName": "<PERSON><PERSON> ty/ Chi n<PERSON>nh", "isClearOnClone": true, "typeOption": "api_link", "optionType": "api_link", "option": [{"label": "503A", "value": "503A"}], "value": "", "optionConfig": {"isMulti": false, "orgchart": [], "isLoadAll": false, "orgchartType": "", "valueField": "", "feedDataTo": [{"input": "@slt_idChiNhanh", "fieldFeed": "businessPlace", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "slt_idChiNhanh", "children": [{"text": ""}]}, {"text": ""}]}]}], "orgchartExpression": [], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "", "masterDataName": "", "masterDataId": "", "masterDataValue": "", "mdExpression": [], "apiURLBase": 379, "apiURL": "/api/master-data/general/sapBusinessPlace/getByEntity", "apiMethod": "POST", "apiDisplay": ["businessPlace"], "apiValue": "businessPlace", "fieldGetData": "", "addHeaderTemplate": "{\"Accept\": \"application/json, application/*+json\",\"Content-Type\": \"application/json\",\"Content-Length\": \"0\"}", "responseTemplate": "{\"fieldData\":\"data.data\", \"fieldStatus\":\"data.code\", \"fieldMessage\":\"data.message\", \"codeSuccess\":\"API-000\"}", "apiExpression": [{"input": "@txt_companyCode", "expression": "=", "condition": "and", "fieldType": "", "type": "companyCode", "defaultValue": "1000", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_companycode", "children": [{"text": ""}]}, {"text": ""}]}]}], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": false, "valueField": "", "readonly": false, "defaultSelect": true}, {"id": "fieldItem_5860756b-5b41-4131-925a-71ef4e00ffe9", "step": "", "row": "row_5b63c2eb-b686-4399-9e6c-c59541ba978c", "col": "column_35d4332d-54ca-468b-9333-b5c1ed318827", "rowSortOrder": 1, "colSortOrder": 0, "fieldSortOrder": 0, "tab": "", "tabItem": "", "parentName": "", "splitter": "spl__910d7c81-8f23-4a83-bcfd-3d87f84b51b9", "validations": {"required": true}, "type": "select", "label": "<PERSON><PERSON><PERSON><PERSON> đề nghị", "name": "slt_nguoiDeNghi", "eventExpression": [], "isHorizontal": 2, "fontWeight": "", "display": true, "switchField": false, "switchFieldConfig": [], "displayName": "<PERSON><PERSON><PERSON><PERSON> đề nghị", "isClearOnClone": true, "isConditionAuth": false, "typeOption": "orgchart", "optionType": "orgchart", "option": [{"label": "nve100000053", "value": "nve100000053"}, {"label": "employee", "value": "employee"}], "value": "nve100000053", "optionConfig": {"isMulti": false, "orgchart": [], "isLoadAll": false, "orgchartType": "user", "displayField": ["username", "fullname", "title"], "valueField": "username", "feedDataTo": [{"input": "@txt_ma<PERSON>han<PERSON><PERSON>", "fieldFeed": "staffCode", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_maNhan<PERSON><PERSON>", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_tenNhanVien", "fieldFeed": "fullname", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_tenNhanVien", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_chucDanh", "fieldFeed": "title", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_chucDanh", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_idSdtc", "fieldFeed": "chartId", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_idSdtc", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_maPhongBan", "fieldFeed": "code", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_ma<PERSON>hongBan", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_tenPhongBan", "fieldFeed": "name", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_tenPhongBan", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_tenCongTyChiNhanh", "fieldFeed": "chartName", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_tenCongTyChiNhanh", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_companycode", "fieldFeed": "chartCode", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_companycode", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_maChiNhanhCuaUserNguoiDeNghi", "fieldFeed": "chartCode", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_maChiNhanhCuaUserNguoiDeNghi", "children": [{"text": ""}]}, {"text": ""}]}]}], "orgchartExpression": [], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "", "masterDataName": "", "masterDataId": "", "masterDataValue": "", "mdExpression": [], "apiURLBase": "", "apiURL": "", "apiMethod": "POST", "apiValue": "", "fieldGetData": "", "addHeaderTemplate": "", "responseTemplate": "", "apiExpression": [], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": true, "displayField": ["username", "fullname", "title"], "valueField": "username", "readonly": false, "defaultSelect": true, "autoLoadAll": false, "specialField": "nguoiDeTrinh", "isAutoGetLogo": false}, {"id": "fieldItem_b32dbe71-c058-4e84-945b-cb9ffa422d99", "step": "", "row": "row_5b63c2eb-b686-4399-9e6c-c59541ba978c", "col": "column_0657801a-3e32-4b8e-83ce-73dd5f690135", "rowSortOrder": 1, "colSortOrder": 1, "fieldSortOrder": 0, "tab": "", "tabItem": "", "parentName": "", "splitter": "spl__910d7c81-8f23-4a83-bcfd-3d87f84b51b9", "validations": {"required": true}, "type": "select", "label": "Phòng ban", "name": "slt_phongBan", "eventExpression": [], "isHorizontal": 2, "fontWeight": "", "display": true, "displayName": "Phòng ban", "isClearOnClone": true, "typeOption": "orgchart", "optionType": "orgchart", "option": [{"label": "option 01", "value": "option 01"}, {"label": "option 02", "value": "option 02"}], "value": "", "optionConfig": {"isMulti": false, "orgchart": [], "isLoadAll": false, "orgchartType": "user", "displayField": ["name"], "valueField": "code", "feedDataTo": [{"input": "@txt_maPhongBan", "fieldFeed": "code", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_ma<PERSON>hongBan", "children": [{"text": ""}]}, {"text": ""}]}]}, {"input": "@txt_tenPhongBan", "fieldFeed": "name", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "txt_tenPhongBan", "children": [{"text": ""}]}, {"text": ""}]}]}], "orgchartExpression": [{"input": "@slt_nguoiDeNghi", "expression": "=", "condition": "and", "fieldType": "username", "type": "user", "objectInput": [{"type": "paragraph", "children": [{"text": ""}, {"type": "mention", "character": "slt_nguoiDeNghi", "children": [{"text": ""}]}, {"text": ""}]}], "orgchartType": "user"}], "onlyLoadOrgChartByUserLogin": false, "masterDataType": "", "masterDataName": "", "masterDataId": "", "masterDataValue": "", "mdExpression": [], "apiURLBase": "", "apiURL": "", "apiMethod": "POST", "apiValue": "", "fieldGetData": "", "addHeaderTemplate": "", "responseTemplate": "", "apiExpression": [], "selectInherit": [{"processId": "", "ticketProcDefId": "", "ticketId": "", "taskId": "", "taskKey": "", "formKey": "", "inputName": ""}]}, "isMulti": false, "displayFieldObject": [], "isUserCurrentUserLogin": false, "displayField": ["name"], "valueField": "code", "readonly": false, "defaultSelect": true, "autoLoadAll": false}]