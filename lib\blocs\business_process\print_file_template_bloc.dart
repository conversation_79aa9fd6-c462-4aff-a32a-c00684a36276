import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/business_process_repository.dart';

part 'print_file_template_event.dart';
part 'print_file_template_state.dart';

class PrintFileTemplateBloc
    extends Bloc<PrintFileTemplateEvent, PrintFileTemplateState> {
  final BusinessProcessRepository _repository;

  PrintFileTemplateBloc({required BusinessProcessRepository repository})
      : _repository = repository,
        super(PrintFileTemplateInitial()) {
    on<PrintFileTemplateRequested>(_onPrintFileTemplateRequested);
    on<ClearPrintFileState>(_onClearPrintFileState);
  }

  void _onClearPrintFileState(ClearPrintFileState event, Emitter<PrintFileTemplateState> emit) {
    emit(PrintFileTemplateInitial());
  }

  Future<void> _onPrintFileTemplateRequested(
    PrintFileTemplateRequested event,
    Emitter<PrintFileTemplateState> emit,
  ) async {
    try {
      emit(PrintFileTemplateLoading());

      final response =
          await _repository.getPrintFileTemplate(event.requestBody);

      emit(PrintFileTemplateSuccess(response));
    } catch (e) {
      emit(PrintFileTemplateFailure(e.toString()));
    }
  }
}
