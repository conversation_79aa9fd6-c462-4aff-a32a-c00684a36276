import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;

import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:eapprove/models/ticket_other_action/work_flow_requestmodel.dart';
import 'package:eapprove/models/ticket_other_action/work_flow_respone_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

class TicketOtherActionRepository {
  final ApiService _apiService;

  TicketOtherActionRepository({required ApiService apiService}) : _apiService = apiService;

  //Lấy danh sách action của phiếu
  Future<dynamic> getFilterHistory(String procInstId, int ticketId, int page, int pageSize) async {
    final response = await _apiService.get('business-process/history/getFilterHistory?ticketId=$ticketId');
    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return jsonData['data']['listAction'];
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      throw Exception(jsonData['message'] ?? 'Lỗi khi lấy dữ liệu lịch sử phiếu');
    }
  }

  //Lấy lịch sử phiếu (không có taskDefKey) , lịch sử bước (có taskDefKey)
  Future<TicketOtherActionResponse> getTicketHistory(
      String procInstId, int ticketId, int page, int pageSize, List<dynamic> listAction, String? taskDefKey) async {
    final response = await _apiService.post('business-process/history/getHistoryByProcess', {
      "page": page,
      "limit": pageSize,
      "sortBy": "time",
      "sortType": "DESC",
      "search": "",
      "procInstId": procInstId,
      "ticketId": ticketId,
      "listAction": listAction,
      "taskDefKey": taskDefKey ?? ''
    });
    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return TicketOtherActionResponse.mappingFromTicketHistoryJSON(jsonData['data']);
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      throw Exception(jsonData['message'] ?? 'Lỗi khi lấy dữ liệu lịch sử phiếu');
    }
  }

  //Lấy danh sách phiếu yêu cầu liên quan - Liên kêt thông tin đến
  Future<TicketOtherActionResponse> getTicketLink(int ticketId) async {
    final response = await _apiService.get('business-process/bpmProcInst/getTicketLink?ticketId=$ticketId');
    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return TicketOtherActionResponse.mappingFromRequestRelationTicketHistoryJSON(jsonData);
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      throw Exception(jsonData['message'] ?? 'Lỗi khi lấy dữ liệu lịch sử phiếu');
    }
  }

//Lấy danh sách phiếu yêu cầu liên quan - Liên kêt thông tin từ
  Future<TicketOtherActionResponse> getTicketFrom(int ticketId) async {
    final response = await _apiService.get('business-process/bpmProcInst/getTicket?ticketId=$ticketId');
    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return TicketOtherActionResponse.mappingFromRequestRelationTicketHistoryJSON(jsonData);
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      throw Exception(jsonData['message'] ?? 'Lỗi khi lấy dữ liệu lịch sử phiếu');
    }
  }

  Future<TicketOtherActionResponse> getTicketAttachmentList(int ticketId) async {
    final response = await _apiService.get('business-process/bpmProcInst/getAllFileByTicketId?ticketId=$ticketId');
    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return TicketOtherActionResponse.mappingFromTicketAttachmentListJSON(jsonData);
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      throw Exception(jsonData['message'] ?? 'Lỗi khi lấy dữ liệu lịch sử phiếu');
    }
  }

  Future<TicketOtherActionResponse> getConsultationOpinionList({
    required String procInstId,
    required int ticketId,
    bool isAdditionalRequest = false,
  }) async {
    final response = await _apiService.post(
      'business-process/bpmProcInst/getDiscussion',
      {
        "procInstId": procInstId,
        "ticketId": ticketId,
        "page": 1,
        "limit": 99,
        "size": 9999,
        "search": "",
        "isAdditionalRequest": isAdditionalRequest.toString(),
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      return TicketOtherActionResponse.fromConsultationJson(jsonData);
    } else {
      final Map<String, dynamic> jsonData = json.decode(response.body);
      throw Exception(jsonData['message'] ?? 'Lỗi khi lấy Thông tin tham vấn');
    }
  }

  Future<bool> deleteConsultationOpinion({required int discusId}) async {
    try {
      final response = await _apiService
          .delete('business-process/bpmProcInst/deleteDiscussion', queryParams: {'discusId': discusId.toString()});
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData['code'] == 1 && jsonData['message'] == "Success";
      } else {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        throw Exception(jsonData['message'] ?? 'Lỗi khi xóa Thông tin tham vấn');
      }
    } catch (e) {
      throw Exception('Lỗi khi xóa Thông tin tham vấn: $e');
    }
  }

  Future<WorkflowResponse> workFlow({required WorkFlowRequestmodel requestBody}) async {
    try {
      final response = await _apiService.post(
        'business-process/spro/work-flow',
        requestBody.toJson(),
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        log('workFlow: ${jsonData.toString()}');
        return WorkflowResponse.fromJson(jsonData);
      } else {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        throw Exception(jsonData['message'] ?? 'Lỗi khi lấy dữ liệu luồng nghiệp vụ');
      }
    } catch (e) {
      throw Exception('Lỗi khi lấy dữ liệu luồng nghiệp vụ: $e');
    }
  }

  Future<bool> addConsultationOpinion({
    required String procInstId,
    required dynamic ticketId,
    required String content,
    required bool isPrivate,
    List<String>? filePaths,
    bool isAdditionalRequest = false,
    bool isCreateUserAdditionalRequest = false,
    int? discusId,
    List<int>? idFilesDelete,
    int? groupId,
  }) async {
    try {
      if (filePaths == null || filePaths.isEmpty) {
        final ticketIdString = ticketId.toString();
        final ticketIntId = ticketId is int ? ticketId : int.tryParse(ticketIdString) ?? 0;

        final fields = {
          'procInstId': procInstId,
          'isAdditionalRequest': isAdditionalRequest.toString(),
          'isCreateUserAdditionalRequest': isCreateUserAdditionalRequest.toString(),
          'typeDiscussion': isPrivate ? '0' : '1', // 0: private, 1: public
          'content': content,
          'ticketId': ticketIdString,
          'ticketIntId': ticketIntId.toString(),
        };

        if (discusId != null) {
          fields['discusId'] = discusId.toString();
        }

        if (idFilesDelete != null && idFilesDelete.isNotEmpty) {
          fields['idFilesDelete'] = idFilesDelete.join(',');
        }

        if (groupId != null) {
          fields['groupId'] = groupId.toString();
        }

        // log('│ 🐛 Fields: $fields');

        final response = await _apiService.multipartRequest(
          'business-process/bpmProcInst/discussion',
          fields: fields,
        );

        if (response.statusCode == 200) {
          final Map<String, dynamic> jsonData = json.decode(response.body);
          return jsonData['code'] == 1 && jsonData['message'] == "Success";
        } else {
          final Map<String, dynamic> jsonData = json.decode(response.body);
          throw Exception(jsonData['message'] ?? 'Lỗi khi ${discusId != null ? "sửa" : "thêm"} Thông tin tham vấn');
        }
      } else {
        final ticketIdString = ticketId.toString();
        final ticketIntId = ticketId is int ? ticketId : int.tryParse(ticketIdString) ?? 0;

        Map<String, String> additionalFields = {};
        if (discusId != null) {
          additionalFields['discusId'] = discusId.toString();
        }

        if (idFilesDelete != null && idFilesDelete.isNotEmpty) {
          additionalFields['idFilesDelete'] = idFilesDelete.join(',');
        }

        if (groupId != null) {
          additionalFields['groupId'] = groupId.toString();
        }

        if (filePaths.length == 1) {
          final response = await _apiService.multipartRequest(
            'business-process/bpmProcInst/discussion',
            fields: {
              'isAdditionalRequest': isAdditionalRequest.toString(),
              'isCreateUserAdditionalRequest': isCreateUserAdditionalRequest.toString(),
              'typeDiscussion': isPrivate ? '0' : '1', // 0: private, 1: public
              'content': content,
              'ticketId': ticketIdString,
              'ticketIntId': ticketIntId.toString(),
              ...additionalFields,
            },
            files: {'file': filePaths.first},
          );

          if (response.statusCode == 200) {
            final Map<String, dynamic> jsonData = json.decode(response.body);
            return jsonData['code'] == 1 && jsonData['message'] == "Success";
          } else {
            final Map<String, dynamic> jsonData = json.decode(response.body);
            throw Exception(jsonData['message'] ??
                'Lỗi khi ${discusId != null ? "sửa" : "thêm"} Thông tin tham vấn với tệp đính kèm');
          }
        } else {
          final response = await _apiService.multipartRequestWithMultipleFiles(
            'business-process/bpmProcInst/discussion',
            fields: {
              'isAdditionalRequest': isAdditionalRequest.toString(),
              'isCreateUserAdditionalRequest': isCreateUserAdditionalRequest.toString(),
              'typeDiscussion': isPrivate ? '0' : '1',
              'content': content,
              'ticketId': ticketIdString,
              'ticketIntId': ticketIntId.toString(),
              ...additionalFields,
            },
            filePaths: filePaths,
          );

          if (response.statusCode == 200) {
            final Map<String, dynamic> jsonData = json.decode(response.body);
            return jsonData['code'] == 1 && jsonData['message'] == "Success";
          } else {
            final Map<String, dynamic> jsonData = json.decode(response.body);
            throw Exception(jsonData['message'] ??
                'Lỗi khi ${discusId != null ? "sửa" : "thêm"} Thông tin tham vấn với nhiều tệp đính kèm');
          }
        }
      }
    } catch (e) {
      throw Exception('Lỗi khi ${discusId != null ? "sửa" : "thêm"} Thông tin tham vấn: $e');
    }
  }

  Future<List<String>> uploadlFile(List<PlatformFile> files) async {
    final fileData = files
        .map((file) => {
              'path': file.path,
              'name': file.name,
              'mimeType': 'application/octet-stream',
            })
        .toList();

    final response = await _apiService.uploadFiles(
      'business-process/file/upload-multi?type=ticket',
      fileData,
    );

    if (response.statusCode == 200) {
      final jsonBody = json.decode(response.body);
      final data = jsonBody['data'];
      if (data != null && data is List) {
        return data.map<String>((e) => e['fileName'] as String).toList();
      }
      throw Exception('Upload thành công nhưng dữ liệu trả về không hợp lệ');
    } else {
      throw Exception('Upload file thất bại: ${response.body}');
    }
  }

  Future<List<Object>> uploadlFileAssistantOpinion(List<PlatformFile> files) async {
    final fileData = files
        .map((file) => {
              'path': file.path,
              'name': file.name,
              'mimeType': 'application/octet-stream',
            })
        .toList();

    final response = await _apiService.uploadFiles(
      'business-process/file/upload-multi?type=ticket',
      fileData,
    );

    if (response.statusCode == 200) {
      final jsonBody = json.decode(response.body);
      final data = jsonBody['data'];
      if (data != null && data is List) {
        return List<Object>.from(data);
      }
      throw Exception('Upload thành công nhưng dữ liệu trả về không hợp lệ');
    } else {
      throw Exception('Upload file thất bại: ${response.body}');
    }
  }

  Future<void> saveCancelDraft({
    required String ticketProcId,
    required String reason,
    String? filePath,
  }) async {
    final response = await _apiService.post(
      'business-process/task/cancelDraft/$ticketProcId/draft',
      {
        "variables": {
          "__cancel_reason": {"value": reason, "type": "String"},
          if (filePath != null) "__cancel_file": {"value": filePath, "type": "String"},
        },
        "businessKey": null,
        "isCancelDraft": true,
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Lưu nháp thất bại: ${response.body}');
    }
  }

  Future<void> saveAssistantOpinionDraft({
    required String ticketProcId,
    required String reason,
    String? filePath,
  }) async {
    final response = await _apiService.post(
      'business-process/task/cancelDraft/$ticketProcId/draft',
      {
        "variables": {
          "__cancel_reason": {"value": reason, "type": "String"},
          if (filePath != null) "__cancel_file": {"value": filePath, "type": "String"},
        },
        "businessKey": null,
        "isCancelDraft": true,
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Lưu nháp thất bại: ${response.body}');
    }
  }

  Future<void> submitCancelTicket({
    required String procInstId,
    required String taskDefKey,
    required int ticketId,
    required String reason,
    required List<String> filePaths,
  }) async {
    print("ABCCCC" + jsonEncode({
      "reason": reason,
      "ticketId": ticketId,
      "attachFiles": filePaths,
      "attachFilesName": List.filled(filePaths.length, null),
      "attachFilesSize": List.filled(filePaths.length, 0),
    }));
    print("ABCCCC" + 'business-process/cancel/$procInstId?taskDefKey=$taskDefKey');
    final response = await _apiService.post(
      'business-process/cancel/$procInstId?taskDefKey=$taskDefKey',
      {
        "reason": reason,
        "ticketId": ticketId,
        "attachFiles": filePaths,
        "attachFilesName": List.filled(filePaths.length, null),
        "attachFilesSize": List.filled(filePaths.length, 0),
      },
    );
    log('Response submitCancelTicket: ${response.body}');
    log('Response submitCancelTicket: ${response.statusCode}');
    if (response.statusCode != 200) {
      throw Exception('Gửi hủy phiếu thất bại: ${response.body}');
    }
  }

  Future<void> submitAssistantOpinionTicket({
    required String opinion,
    required int ticketId,
    required int status,
    required String assistantEmail,
    required List<Object> url,
  }) async {
    final response = await _apiService.post(
      'business-process/bpmProcInst/create-opinion',
      [
        {
          "assistantEmail": assistantEmail,
          "ticketId": ticketId,
          "status": status,
          "opinion": opinion,
          "url": url.length > 0 ? url.toString() : "[]",
        }
      ],
    );

    if (response.statusCode != 200) {
      throw Exception('Gửi ý kiến thất bại: ${response.body}');
    }
  }

  Future<void> submitRecoverRequest({
    required String taskDefKey,
    required int ticketId,
    required String reason,
    required List<String> filePaths,
  }) async {
    final response = await _apiService.post(
      'business-process/bpmProcInst/$taskDefKey/recall',
      {
        "reason": reason,
        "ticketId": ticketId,
        "attachFiles": filePaths,
        "attachFilesName": List.filled(filePaths.length, null),
        "attachFilesSize": List.filled(filePaths.length, 0),
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Gửi hủy phiếu thất bại: ${response.body}');
    }
  }
}
