import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/ticket_other_action/ticket_history_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/flutter_svg.dart';

class TicketHistoryItem extends StatelessWidget {
  const TicketHistoryItem({super.key, required this.ticketHistory});
  final TicketHistoryModel ticketHistory;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(22.w, 4.h, 22.w, 4.h),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${ticketHistory.username} - ${ticketHistory.position}',
            style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
          ),
          Sized<PERSON>ox(height: 4.h),
          Row(
            children: [
              Text(
                'Hành động: ',
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
              ),
              Text(
                ticketHistory.action,
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            children: [
              Text(
                'Thời gian: ',
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
              ),
              Text(
                ticketHistory.date,
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Nội dung: ',
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
              ),
              Expanded(
                child: Text(
                  ticketHistory.description ?? '',
                  style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
                ),
              ),
            ],
          ),
          if (ticketHistory.attachments != null && ticketHistory.attachments!.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 8.h),
                Text(
                  'Chứng từ đính kèm:',
                  style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
                ),
                SizedBox(height: 8.h),
                ...ticketHistory.attachments!.map(
                  (file) => Padding(
                    padding: EdgeInsets.only(bottom: 8.h),
                    child: InkWell(
                      onTap: () {
                        // Handle file view/download
                      },
                      child: Row(
                        children: [
                          Text(
                            '${file.name}.${file.extension}',
                            style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue),
                          ),
                          SizedBox(width: 8.w),
                         SvgPicture.asset(
                            StringImage.ic_eye,
                            width: 24.w,
                            height: 24.h,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
