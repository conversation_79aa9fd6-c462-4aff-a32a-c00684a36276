import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:equatable/equatable.dart';

class AttachmentListState extends Equatable {

  final ServiceStatus status;
  final String? errorMessage;
  final List<DocumentModel> contentFile;
  final List<DocumentModel> contentFileOther;

  const AttachmentListState({
    this.status = ServiceStatus.initial,
    this.errorMessage,
    this.contentFile = const [],
    this.contentFileOther = const [],
  });

  AttachmentListState copyWith({
    ServiceStatus? status,
    String? errorMessage,
    List<DocumentModel>? contentFile,
    List<DocumentModel>? contentFileOther,
  }) {
    return AttachmentListState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      contentFile: contentFile ?? this.contentFile,
      contentFileOther: contentFileOther ?? this.contentFileOther,
    );
  }

  @override
  List<Object?> get props => [status, errorMessage, contentFile, contentFileOther];
}
