// import 'package:eapprove/screens/pyc_screen/overview_ticket_board_screen.dart';
// import 'package:eapprove/screens/ticket_screen/create_ticket_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'package:flutter_sdk/widgets/custom_bottom_navigation_bar.dart';

// import 'package:eapprove/common/bottom_navigation_item.dart';
// import 'notification_screen.dart';

// class HomeScreen extends StatefulWidget {
//   final Widget child;
//   final int currentIndex;

//   const HomeScreen({
//     super.key,
//     required this.child,
//     required this.currentIndex,
//   });

//   @override
//   State<HomeScreen> createState() => _HomeScreenState();
// }

// class _HomeScreenState extends State<HomeScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: widget.child,
//       bottomNavigationBar: CustomBottomNavigationBar(
//         selectedIndex: widget.currentIndex,
//         onItemSelected: (index) {
//           if (index == widget.currentIndex) {
//             if (Navigator.of(context).canPop()) {
//               Navigator.of(context).pop();
//             }
//             return;
//           }
//           final navigator = Navigator.of(context, rootNavigator: true);
//           switch (index) {
//             case 0:
//               navigator.pushAndRemoveUntil(
//                 MaterialPageRoute(
//                   builder: (context) => const HomeScreen(
//                     currentIndex: 0,
//                     child: OverviewTicketScreen(),
//                   ),
//                 ),
//                 (route) => false,
//               );
//               break;
//             case 1:
//               navigator.pushAndRemoveUntil(
//                 MaterialPageRoute(
//                   builder: (context) => const HomeScreen(
//                     currentIndex: 1,
//                     child: CreateTicketScreen(),
//                   ),
//                 ),
//                 (route) => false,
//               );
//               break;
//             case 2:
//               navigator.pushAndRemoveUntil(
//                 MaterialPageRoute(
//                   builder: (context) => const HomeScreen(
//                     currentIndex: 2,
//                     child: NotificationScreen(),
//                   ),
//                 ),
//                 (route) => false,
//               );
//               break;
//             case 3:
//               break;
//           }
//         },
//         items: navigationItems,
//         selectedBackgroundColor: getColorSkin().blue2,
//         backgroundColor: getColorSkin().white,
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';

class HomeScreen extends StatelessWidget {
  final Widget? child;
  final int? currentIndex;

  const HomeScreen({
    super.key,
    this.child,
    this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Home")),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.of(context)
                .pushNamed('listTicket');
          },
          child: Text("Go to List Ticket"),
        ),
      ),
    );
  }
}
