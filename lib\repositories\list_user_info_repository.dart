  import 'dart:convert';
  import 'package:eapprove/models/authorize_management/list_user_info_response.dart';
  import 'package:eapprove/services/api_services.dart';

class UserInfoRepository {
  final ApiService _apiService;

  UserInfoRepository(this._apiService);

  Future<UserInfoResponse> getUserInfo({
    List<int> concurrently = const [0],
    List<String> managerLevel = const ['primary', 'secondary', 'staff'],
    String search = '',
    String sortBy = 'id',
    String sortType = 'DESC',
    int page = 1,
    int limit = 99999999,
    int size = 99999999,
  }) async {
    try {
      final response = await _apiService.post(
        'customer/userInfo/search',
        {
          'concurrently': concurrently,
          'managerLevel': managerLevel,
          'search': search,
          'sortBy': sortBy,
          'sortType': sortType,
          'page': page,
          'limit': limit,
          'size': size,
        },
      );
      print('UserInfo Response: ${response.body}');
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return UserInfoResponse.fromJson(responseData);
      } else {
        throw Exception('Failed to load user info: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to load user info: $e');
    }
  }
} 