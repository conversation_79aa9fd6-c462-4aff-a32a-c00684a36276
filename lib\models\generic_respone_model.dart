class GenericResponseModel<T> {
  final int code;
  final String message;
  final T data;

  const GenericResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory GenericResponseModel.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return GenericResponseModel<T>(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: fromJsonT(json['data']),
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'code': code,
      'message': message,
      'data': toJsonT(data),
    };
  }

  GenericResponseModel<T> copyWith({
    int? code,
    String? message,
    T? data,
  }) {
    return GenericResponseModel<T>(
      code: code ?? this.code,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}
