import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/widgets/custom_datepicker.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:intl/intl.dart';
import 'dart:developer' as developer;

class DatePickerWidget extends StatefulWidget {
  final FormItemInfo data;
  final FormStateManager stateManager;
  final int? rowIndex;
  final bool? isShowLabel;

  const DatePickerWidget({
    Key? key,
    required this.data,
    required this.stateManager,
    this.rowIndex,
    this.isShowLabel = true,
  }) : super(key: key);

  @override
  State<DatePickerWidget> createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends State<DatePickerWidget> {
  DateTime? _selectedDate;
  late DateFormat _dateFormat;
  late bool _isChooseOldDays;
  late bool _isChooseNow;
  late bool _isChooseFutureDays;

  @override
  void initState() {
    super.initState();
    _initializeDateSettings();
    _initializeDate();
  }

  @override
  void didUpdateWidget(DatePickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    final newValue = widget.stateManager.getFieldValueByName(widget.data.name ?? '', null);
    final oldValue = oldWidget.stateManager.getFieldValueByName(oldWidget.data.name ?? '', null);
    if (newValue != oldValue) {
      _initializeDate();
    }
  }

  void _initializeDateSettings() {
    // Set date format based on the form data
    final dateFormatStr = widget.data.dateFormat ?? 'DD/MM/YYYY';

    // Convert from moment.js format (DD/MM/YYYY) to intl package format (dd/MM/yyyy)
    String intlFormat = dateFormatStr
        .replaceAll('DD', 'dd')
        .replaceAll('MM', 'MM')
        .replaceAll('YYYY', 'yyyy');

    _dateFormat = DateFormat(intlFormat);

    // Initialize date range constraints
    _isChooseOldDays = widget.data.isChooseOldDays ?? true;
    _isChooseNow = widget.data.isChooseNow ?? true;
    _isChooseFutureDays = widget.data.isChooseFutureDays ?? true;

    developer.log(
        'Date settings initialized: format=$intlFormat, oldDays=$_isChooseOldDays, today=$_isChooseNow, futureDays=$_isChooseFutureDays',
        name: 'DatePickerWidget');
  }

  void _initializeDate() {
    final value = widget.stateManager.getFieldValueByName(widget.data.name ?? '', null);
    if (value != null && value.toString().isNotEmpty) {
      // Parse existing value
      _selectedDate = _parseDate(value);
      developer.log('Initialized with provided date: $_selectedDate',
          name: 'DatePickerWidget');
    } else if (widget.data.readonly == true && _isChooseNow) {
      // Auto-fill with current date if isChooseNow is true and no value provided
      _selectedDate = DateTime.now();
      widget.data.value = _selectedDate;
      developer.log('Auto-filled with current date: $_selectedDate',
          name: 'DatePickerWidget');

      // Notify about the default value
      Future.microtask(() {
        widget.stateManager.setFieldValue(widget.data.name ?? '', _selectedDate,
            parentName: widget.data.parentName, rowIndex: widget.rowIndex);
      });
    }

    // Validate the date against range constraints
    _validateDateRange();
  }

  DateTime? _parseDate(dynamic dateValue) {
    if (dateValue == null) return null;

    try {
      if (dateValue is DateTime) return dateValue;
      if (dateValue is! String || dateValue.isEmpty) return null;

      // First try parsing as ISO 8601 format
      try {
        return DateTime.parse(dateValue);
      } catch (_) {
        // If that fails, try parsing with the configured format
        try {
          return _dateFormat.parse(dateValue);
        } catch (e) {
          developer.log('Error parsing date with configured format: $e',
              name: 'DatePickerWidget', error: e);
          return null;
        }
      }
    } catch (e) {
      developer.log('Error parsing date: $e',
          name: 'DatePickerWidget', error: e);
      return null;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    if (widget.data.readonly == true) return;

    // Calculate available date range
    DateTime firstDate, lastDate;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Set first available date
    if (_isChooseOldDays) {
      firstDate = DateTime(1900);
    } else if (_isChooseNow) {
      firstDate = today;
    } else {
      firstDate = DateTime(now.year, now.month, now.day + 1);
    }

    // Set last available date
    if (_isChooseFutureDays) {
      lastDate = DateTime(2100);
    } else if (_isChooseNow) {
      lastDate = today;
    } else {
      lastDate = DateTime(now.year, now.month, now.day - 1);
    }

    // Ensure lastDate is not before firstDate
    if (lastDate.isBefore(firstDate)) {
      lastDate = firstDate;
    }

    final DateTime? picked = await CustomDatePicker.show(
      context: context,
      initialDate: _getValidInitialDate(firstDate, lastDate),
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (picked != null && picked != _selectedDate) {
      _updateSelectedDate(picked);
    }
  }

  DateTime _getValidInitialDate(DateTime firstDate, DateTime lastDate) {
    // Use the selected date if available and valid
    if (_selectedDate != null) {
      final selectedDay = DateTime(
          _selectedDate!.year, _selectedDate!.month, _selectedDate!.day);
      if (!selectedDay.isBefore(firstDate) && !selectedDay.isAfter(lastDate)) {
        return _selectedDate!;
      }
    }

    // Otherwise use current date if valid
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    if (!today.isBefore(firstDate) && !today.isAfter(lastDate)) {
      return now;
    }

    // Default to first available date
    return firstDate;
  }

  void _updateSelectedDate(DateTime date) {
    setState(() {
      _selectedDate = date;
      widget.data.value = date;
    });
    developer.log('Selected date: $_selectedDate');
    Future.microtask(() {
      widget.stateManager.setFieldValue(widget.data.name ?? '', date,
          parentName: widget.data.parentName, rowIndex: widget.rowIndex);
    });

    _validateDateRange();
  }

  void _clearDate() {
    setState(() {
      _selectedDate = null;
      widget.data.value = null;
    });
    Future.microtask(() {
      widget.stateManager.setFieldValue(widget.data.name ?? '', null,
          parentName: widget.data.parentName, rowIndex: widget.rowIndex);
    });

    // Check if field is required
    _validateRequired();
  }

  // Validate date against configured date range constraints
  void _validateDateRange() {
    if (_selectedDate == null) {
      _validateRequired();
      return;
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay =
        DateTime(_selectedDate!.year, _selectedDate!.month, _selectedDate!.day);

    bool isValid = true;
    String errorMessage = '';

    // Check if date is in the past when not allowed
    if (!_isChooseOldDays && selectedDay.isBefore(today)) {
      isValid = false;
      errorMessage = 'Past dates are not allowed';
    }

    // Check if date is today when not allowed
    if (!_isChooseNow && selectedDay.isAtSameMomentAs(today)) {
      isValid = false;
      errorMessage = 'Current date is not allowed';
    }

    // Check if date is in the future when not allowed
    if (!_isChooseFutureDays && selectedDay.isAfter(today)) {
      isValid = false;
      errorMessage = 'Future dates are not allowed';
    }

    if (!isValid) {
      setError(errorMessage);
    } else {
      resetError();
    }
  }

  // Validate required field
  void _validateRequired() {
    if (widget.data.validations?['required'] == true && _selectedDate == null) {
      setError('This field is required');
    } else {
      resetError();
    }
  }

  void setError(String error) {
    setState(() => widget.data.error = error);
  }

  void resetError() {
    setState(() => widget.data.error = '');
  }

  dynamic getValue() => _selectedDate;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if(widget.isShowLabel == true)
            FormLabel(
              displayName: widget.data.displayName,
              label: widget.data.label,
            suggestText: widget.data.suggestText,
            isRequired: widget.data.validations?['required'] == true,
          ),
          Container(
            margin: EdgeInsets.only(top: 8.h),
            child: _buildDateField(),
          ),
        ],
      ),
    );
  }
  
  

  Widget _buildDateField() {
    final displayText = _selectedDate != null
        ? _dateFormat.format(_selectedDate!)
        : widget.data.placeholder?.isNotEmpty == true
            ? widget.data.placeholder!
            : 'Select a date';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap:
              widget.data.readonly == true ? null : () => _selectDate(context),
          child: Container(
            height: 40.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
                border: Border.all(
                    color: widget.data.error?.isNotEmpty == true
                        ? Colors.red
                        : getColorSkin().ink5),
                borderRadius: BorderRadius.circular(8.r),
                color: widget.data.readonly == true
                    ? getColorSkin().whiteSmoke
                    : getColorSkin().white),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayText,
                        style: getTypoSkin().title6Regular.copyWith(
                              color: _selectedDate != null
                                  ? getColorSkin().black
                                  : getColorSkin().ink3,
                            ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: widget.data.readonly == true
                      ? null
                      : () => _selectedDate != null
                          ? _clearDate()
                          : _selectDate(context),
                  child: _selectedDate != null
                      ? Icon(Icons.highlight_remove_outlined,
                          color: getColorSkin().secondaryText)
                      : Icon(Icons.calendar_today, color: getColorSkin().ink1),
                ),
              ],
            ),
          ),
        ),
        if (widget.data.error?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.only(top: 4.h, left: 8.w),
            child: Text(
              widget.data.error!,
              style: TextStyle(
                color: Colors.red,
                fontSize: 12.sp,
              ),
            ),
          ),
      ],
    );
  }
}
