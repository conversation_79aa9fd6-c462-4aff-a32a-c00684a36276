import 'package:eapprove/models/authorize_management/filter_data_authorize_response.dart';
import 'package:eapprove/repositories/filter_data_authorize_repository.dart';

class FilterDataAuthorizeService {
  final FilterDataAuthorizeRepository _repository;

  FilterDataAuthorizeService(this._repository);

  Future<FilterDataAuthorizeResponse> getFilterData({
    String search = '',
    String sortBy = 'createdDate',
    String sortType = 'DESC',
    String limit = '10',
    String page = '1',
    int totalPages = 0,
    int totalElements = 0,
    List<String> listAssignUser = const ['-1'],
    List<String> listAssignedUser = const ['-1'],
    List<int> status = const [-3, -2, -1, 0, 1, -4],
    List<String> listCreatedUser = const ['-1'],
    List<String> listUpdatedUser = const ['-1'],
    List<Map<String, String>> listDateFilter = const [{'fromDate': '', 'toDate': '', 'type': ''}],
    String userLogin = 'employee',
  }) async {
    try {
      return await _repository.getFilterData(
        search: search,
        sortBy: sortBy,
        sortType: sortType,
        limit: limit,
        page: page,
        totalPages: totalPages,
        totalElements: totalElements,
        listAssignUser: listAssignUser,
        listAssignedUser: listAssignedUser,
        status: status,
        listCreatedUser: listCreatedUser,
        listUpdatedUser: listUpdatedUser,
        listDateFilter: listDateFilter,
        userLogin: userLogin,
      );
    } catch (e) {
      throw Exception('Failed to get filter data: $e');
    }
  }
} 