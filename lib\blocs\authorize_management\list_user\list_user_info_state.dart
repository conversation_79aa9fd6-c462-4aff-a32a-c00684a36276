import 'package:eapprove/models/authorize_management/list_user_info_response.dart';

abstract class UserInfoState {}

class UserInfoInitial extends UserInfoState {}

class UserInfoLoading extends UserInfoState {}

class UserInfoLoaded extends UserInfoState {
  final UserInfoResponse response;

  UserInfoLoaded(this.response);
}

class UserInfoError extends UserInfoState {
  final String message;

  UserInfoError(this.message);
}
