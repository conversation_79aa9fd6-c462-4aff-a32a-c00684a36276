class CheckToTrinhResponseModel {
  final dynamic code;
  final String message;
  final Data data;

  CheckToTrinhResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory CheckToTrinhResponseModel.fromJson(Map<String, dynamic> json) {
    return CheckToTrinhResponseModel(
      code: json['code'] as dynamic,
      message: json['message'] as String,
      data: Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }
}

class Data {
  final dynamic processType;
  final bool? informTo;
  final List<UserTask>? listUserTask;
  final List<SignForm>? listSignForm;

  Data({
    this.processType,
    this.informTo,
    this.listUserTask,
    this.listSignForm,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      processType: json['processType'],
      informTo: json['informTo'] as bool?,
      listUserTask: json['listUserTask'] != null
          ? (json['listUserTask'] as List<dynamic>)
          .map((e) => UserTask.fromJson(e as Map<String, dynamic>))
          .toList()
          : null,
      listSignForm: json['listSignForm'] != null
          ? (json['listSignForm'] as List<dynamic>)
          .map((e) => SignForm.fromJson(e as Map<String, dynamic>))
          .toList()
          : null,
    );
  }
}

class UserTask {
  final String taskDefKey;
  final String? zoomeField;
  final String taskName;

  UserTask({
    required this.taskDefKey,
    this.zoomeField,
    required this.taskName,
  });

  factory UserTask.fromJson(Map<String, dynamic> json) {
    return UserTask(
      taskDefKey: json['taskDefKey'] as String,
      zoomeField: json['zoomeField'] as String?,
      taskName: json['taskName'] as String,
    );
  }
}

class SignForm {
  final String taskDefKey;
  final dynamic prdynamicType;
  final dynamic pdfContent;
  final String? templateName;
  final dynamic cfId;
  final String? conditionText;
  final String? uploadWordsChange;
  final dynamic id;
  final dynamic content;
  final String? status;
  final String? uploadWords;

  SignForm({
    required this.taskDefKey,
    this.prdynamicType,
    this.pdfContent,
    this.templateName,
    this.cfId,
    this.conditionText,
    this.uploadWordsChange,
    this.id,
    this.content,
    this.status,
    this.uploadWords,
  });

  factory SignForm.fromJson(Map<String, dynamic> json) {
    return SignForm(
      taskDefKey: json['taskDefKey'] as String,
      prdynamicType: json['prdynamicType'],
      pdfContent: json['pdfContent'],
      templateName: json['templateName'] as String?,
      cfId: json['cf_id'],
      conditionText: json['conditionText'] as String?,
      uploadWordsChange: json['uploadWordsChange'] as String?,
      id: json['id'],
      content: json['content'],
      status: json['status'] as String?,
      uploadWords: json['uploadWords'] as String?,
    );
  }
}