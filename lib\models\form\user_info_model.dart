
//danh cho form, response khac voi UserInfoResponse
class UserInfoByUserNameResponse {
  final int code;
  final String message;
  final UserInfoData data;

  UserInfoByUserNameResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory UserInfoByUserNameResponse.fromJson(Map<String, dynamic> json) {
    return UserInfoByUserNameResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: UserInfoData.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }

  bool get isSuccess => code == 1;
}

class UserInfoData {
  final int? id;
  final int? chartNodeId;
  final String? chartNodeName;
  final int? importChartId;
  final int? locationId;
  final String? staffCode;
  final String? firstname;
  final String? lastname;
  final String? fullName;
  final String? username;
  final String? usernames;
  final String? name;
  final String? email;
  final List<String>? emailList;
  final List<int>? idList;
  final String? managerLevel;
  final String? phone;
  final String? status;
  final String? handoverStatus;
  final bool available;
  final String? addition;
  final String? directManager;
  final String? viceManager;
  final String? description;
  final String? createdUser;
  final DateTime? createdDate;
  final DateTime? modifiedDate;
  final String? modifiedUser;
  final bool? hasSecretary;
  final bool assignment;
  final bool? multiChartApproval;
  final String? workingType;
  final List<String>? userNameList;
  final bool? activeUser;
  final bool? inactiveUser;
  final bool? activeNode;
  final bool? inactiveNode;
  final List<int>? chartNodeIds;
  final List<int>? chartIds;
  final int? chartId;
  final int? userTitleId;
  final List<dynamic>? interactiveUserDtos;
  final List<dynamic>? interactiveNodeDtos;
  final List<UserTitleDto> userTitleDtos;
  final String? positionCode;
  final String? positionName;
  final String? titleCode;
  final String? titleName;
  final int? concurrently;
  final int? chartNodeIdSecretary;
  final DateTime? secretaryBeginDate;
  final DateTime? secretaryEndDate;

  UserInfoData({
    this.id,
    this.chartNodeId,
    this.chartNodeName,
    this.importChartId,
    this.locationId,
    this.staffCode,
    this.firstname,
    this.lastname,
    this.fullName,
    this.username,
    this.usernames,
    this.name,
    this.email,
    this.emailList,
    this.idList,
    this.managerLevel,
    this.phone,
    this.status,
    this.handoverStatus,
    this.available = false,
    this.addition,
    this.directManager,
    this.viceManager,
    this.description,
    this.createdUser,
    this.createdDate,
    this.modifiedDate,
    this.modifiedUser,
    this.hasSecretary,
    this.assignment = false,
    this.multiChartApproval,
    this.workingType,
    this.userNameList,
    this.activeUser,
    this.inactiveUser,
    this.activeNode,
    this.inactiveNode,
    this.chartNodeIds,
    this.chartIds,
    this.chartId,
    this.userTitleId,
    this.interactiveUserDtos,
    this.interactiveNodeDtos,
    required this.userTitleDtos,
    this.positionCode,
    this.positionName,
    this.titleCode,
    this.titleName,
    this.concurrently,
    this.chartNodeIdSecretary,
    this.secretaryBeginDate,
    this.secretaryEndDate,
  });

  factory UserInfoData.fromJson(Map<String, dynamic> json) {
    return UserInfoData(
      id: json['id'],
      chartNodeId: json['chartNodeId'],
      chartNodeName: json['chartNodeName'],
      importChartId: json['importChartId'],
      locationId: json['locationId'],
      staffCode: json['staffCode'],
      firstname: json['firstname'],
      lastname: json['lastname'],
      fullName: json['fullName'],
      username: json['username'],
      usernames: json['usernames'],
      name: json['name'],
      email: json['email'],
      emailList: json['emailList'] != null ? List<String>.from(json['emailList']) : null,
      idList: json['idList'] != null ? List<int>.from(json['idList']) : null,
      managerLevel: json['managerLevel'],
      phone: json['phone'],
      status: json['status'],
      handoverStatus: json['handoverStatus'],
      available: json['available'] ?? false,
      addition: json['addition'],
      directManager: json['directManager'],
      viceManager: json['viceManager'],
      description: json['description'],
      createdUser: json['createdUser'],
      createdDate: json['createdDate'] != null ? DateTime.parse(json['createdDate']) : null,
      modifiedDate: json['modifiedDate'] != null ? DateTime.parse(json['modifiedDate']) : null,
      modifiedUser: json['modifiedUser'],
      hasSecretary: json['hasSecretary'],
      assignment: json['assignment'] ?? false,
      multiChartApproval: json['multiChartApproval'],
      workingType: json['workingType'],
      userNameList: json['userNameList'] != null ? List<String>.from(json['userNameList']) : null,
      activeUser: json['activeUser'],
      inactiveUser: json['inactiveUser'],
      activeNode: json['activeNode'],
      inactiveNode: json['inactiveNode'],
      chartNodeIds: json['chartNodeIds'] != null ? List<int>.from(json['chartNodeIds']) : null,
      chartIds: json['chartIds'] != null ? List<int>.from(json['chartIds']) : null,
      chartId: json['chartId'],
      userTitleId: json['userTitleId'],
      interactiveUserDtos: json['interactiveUserDtos'],
      interactiveNodeDtos: json['interactiveNodeDtos'],
      userTitleDtos: json['userTitleDtos'] != null
          ? List<UserTitleDto>.from(
          json['userTitleDtos'].map((dto) => UserTitleDto.fromJson(dto)))
          : [],
      positionCode: json['positionCode'],
      positionName: json['positionName'],
      titleCode: json['titleCode'],
      titleName: json['titleName'],
      concurrently: json['concurrently'],
      chartNodeIdSecretary: json['chartNodeIdSecretary'],
      secretaryBeginDate: json['secretaryBeginDate'] != null ? DateTime.parse(json['secretaryBeginDate']) : null,
      secretaryEndDate: json['secretaryEndDate'] != null ? DateTime.parse(json['secretaryEndDate']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chartNodeId': chartNodeId,
      'chartNodeName': chartNodeName,
      'importChartId': importChartId,
      'locationId': locationId,
      'staffCode': staffCode,
      'firstname': firstname,
      'lastname': lastname,
      'fullName': fullName,
      'username': username,
      'usernames': usernames,
      'name': name,
      'email': email,
      'emailList': emailList,
      'idList': idList,
      'managerLevel': managerLevel,
      'phone': phone,
      'status': status,
      'handoverStatus': handoverStatus,
      'available': available,
      'addition': addition,
      'directManager': directManager,
      'viceManager': viceManager,
      'description': description,
      'createdUser': createdUser,
      'createdDate': createdDate?.toIso8601String(),
      'modifiedDate': modifiedDate?.toIso8601String(),
      'modifiedUser': modifiedUser,
      'hasSecretary': hasSecretary,
      'assignment': assignment,
      'multiChartApproval': multiChartApproval,
      'workingType': workingType,
      'userNameList': userNameList,
      'activeUser': activeUser,
      'inactiveUser': inactiveUser,
      'activeNode': activeNode,
      'inactiveNode': inactiveNode,
      'chartNodeIds': chartNodeIds,
      'chartIds': chartIds,
      'chartId': chartId,
      'userTitleId': userTitleId,
      'interactiveUserDtos': interactiveUserDtos,
      'interactiveNodeDtos': interactiveNodeDtos,
      'userTitleDtos': userTitleDtos.map((dto) => dto.toJson()).toList(),
      'positionCode': positionCode,
      'positionName': positionName,
      'titleCode': titleCode,
      'titleName': titleName,
      'concurrently': concurrently,
      'chartNodeIdSecretary': chartNodeIdSecretary,
      'secretaryBeginDate': secretaryBeginDate?.toIso8601String(),
      'secretaryEndDate': secretaryEndDate?.toIso8601String(),
    };
  }
}

class UserTitleDto {
  final int id;
  final int userInfoId;
  final int chartNodeId;
  final String? positionCode;
  final String? positionName;
  final String? titleCode;
  final String? titleName;
  final String? managerLevel;
  final int? concurrently;
  final DateTime? concurrentlyBeginDate;
  final DateTime? concurrentlyEndDate;
  final DateTime? secretaryEndDate;
  final DateTime? secretaryBeginDate;
  final int? chartNodeIdSecretary;
  final String? description;
  final String? directManager;
  final String? viceManager;
  final bool? assignment;
  final bool? hasSecretary;
  final bool applyChild;

  UserTitleDto({
    required this.id,
    required this.userInfoId,
    required this.chartNodeId,
    this.positionCode,
    this.positionName,
    this.titleCode,
    this.titleName,
    this.managerLevel,
    this.concurrently,
    this.concurrentlyBeginDate,
    this.concurrentlyEndDate,
    this.secretaryEndDate,
    this.secretaryBeginDate,
    this.chartNodeIdSecretary,
    this.description,
    this.directManager,
    this.viceManager,
    this.assignment,
    this.hasSecretary,
    this.applyChild = false,
  });

  factory UserTitleDto.fromJson(Map<String, dynamic> json) {
    return UserTitleDto(
      id: json['id'] ?? 0,
      userInfoId: json['userInfoId'] ?? 0,
      chartNodeId: json['chartNodeId'] ?? 0,
      positionCode: json['positionCode'],
      positionName: json['positionName'],
      titleCode: json['titleCode'],
      titleName: json['titleName'],
      managerLevel: json['managerLevel'],
      concurrently: json['concurrently'],
      concurrentlyBeginDate: json['concurrentlyBeginDate'] != null ? DateTime.parse(json['concurrentlyBeginDate']) : null,
      concurrentlyEndDate: json['concurrentlyEndDate'] != null ? DateTime.parse(json['concurrentlyEndDate']) : null,
      secretaryEndDate: json['secretaryEndDate'] != null ? DateTime.parse(json['secretaryEndDate']) : null,
      secretaryBeginDate: json['secretaryBeginDate'] != null ? DateTime.parse(json['secretaryBeginDate']) : null,
      chartNodeIdSecretary: json['chartNodeIdSecretary'],
      description: json['description'],
      directManager: json['directManager'],
      viceManager: json['viceManager'],
      assignment: json['assignment'],
      hasSecretary: json['hasSecretary'],
      applyChild: json['applyChild'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userInfoId': userInfoId,
      'chartNodeId': chartNodeId,
      'positionCode': positionCode,
      'positionName': positionName,
      'titleCode': titleCode,
      'titleName': titleName,
      'managerLevel': managerLevel,
      'concurrently': concurrently,
      'concurrentlyBeginDate': concurrentlyBeginDate?.toIso8601String(),
      'concurrentlyEndDate': concurrentlyEndDate?.toIso8601String(),
      'secretaryEndDate': secretaryEndDate?.toIso8601String(),
      'secretaryBeginDate': secretaryBeginDate?.toIso8601String(),
      'chartNodeIdSecretary': chartNodeIdSecretary,
      'description': description,
      'directManager': directManager,
      'viceManager': viceManager,
      'assignment': assignment,
      'hasSecretary': hasSecretary,
      'applyChild': applyChild,
    };
  }
}