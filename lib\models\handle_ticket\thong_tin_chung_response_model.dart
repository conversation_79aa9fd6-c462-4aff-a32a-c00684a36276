class ThongTinChungModel {
  final dynamic id;
  final dynamic ticketId;
  final dynamic ticketTitle;
  final dynamic ticketCreatedUser;
  final dynamic ticketProcDefId;
  final dynamic ticketSla;
  final dynamic endKey;
  final dynamic startKey;
  final dynamic ticketEndTime;
  final dynamic ticketClosedTime;
  final dynamic ticketStartedTime;
  final dynamic ticketCanceledTime;
  final dynamic ticketCreatedTime;
  final dynamic ticketFinishTime;
  final dynamic ticketStatus;
  final dynamic procServiceName;
  final dynamic ticketStartUserId;
  final dynamic ticketRating;
  final dynamic comment;
  final List<dynamic> listOwner;
  final dynamic listService;
  final dynamic listStatus;
  final dynamic listDate;
  final dynamic user;
  final List<dynamic> ruThongTinChungModel;
  final dynamic contentRequest;
  final dynamic serviceId;
  final List<TicketTaskDto> ticketTaskDtoList;
  final dynamic hideResult;
  final dynamic informTo;
  final dynamic updatePermission;
  final List<dynamic> hideRuTasks;
  final dynamic ruPermission;
  final dynamic newAndClone;
  final dynamic autoInherits;
  final dynamic offNotifications;
  final List<BpmProcdefInherit> bpmProcdefInherits;
  final dynamic slaResponse;
  final dynamic slaFinish;
  final dynamic slaResponseProcess;
  final dynamic slaFinishProcess;
  final dynamic slaResponseTimeProcess;
  final dynamic slaFinishTimeProcess;
  final dynamic slaResponseTime;
  final dynamic slaFinishTime;
  final dynamic editPermission;
  final List<dynamic> listSignForm;
  final dynamic fullName;
  final dynamic priority;
  final dynamic priorityId;
  final dynamic color;
  final Map<dynamic, List<dynamic>> taskUsers;
  final List<SignedFile> signedFiles;
  final dynamic endNodeId;
  final dynamic linkedBpmProcInstDto;
  final dynamic appCode;
  final dynamic submissionTypeName;
  final dynamic submissionType;
  final dynamic cancel;
  final dynamic hideInfo;
  final dynamic showInfo;
  final dynamic isAssistant;
  final dynamic isEditAssistant;
  final List<dynamic> hideInfoTasks;
  final List<dynamic> showInfoTasks;
  final dynamic additionalRequest;
  final dynamic recall;
  final dynamic showInputTask;
  final List<dynamic> cancelTasks;
  final List<dynamic> showInputTaskDefKeys;
  final dynamic procDefId;
  final dynamic requestCode;
  final dynamic chartId;
  final dynamic chartNodeId;
  final dynamic changeImplementerValue;
  final dynamic ticketRecall;
  final dynamic companyCode;
  final dynamic authorityOnTicketValue;
  final dynamic authorityOnTicketStep;
  final dynamic authorityOnTicket;
  final dynamic hideInherit;
  final List<dynamic> hideInheritTasks;
  final dynamic hideComment;
  final List<dynamic> hideCommentTasks;
  final dynamic hideDownload;
  final List<dynamic> hideDownloadTasks;
  final dynamic hideShareTicket;
  final List<dynamic> viewFileApi;
  final dynamic forceViewUrl;
  final List<dynamic> notifyUsers;
  final dynamic cancelDraftVariables;
  final List<dynamic> lstOrgAssignee;
  final dynamic approvedBudgetIds;

  ThongTinChungModel({
    this.id,
    this.ticketId,
    this.ticketTitle,
    this.ticketCreatedUser,
    this.ticketProcDefId,
    this.ticketSla,
    this.endKey,
    this.startKey,
    this.ticketEndTime,
    this.ticketClosedTime,
    this.ticketStartedTime,
    this.ticketCanceledTime,
    this.ticketCreatedTime,
    this.ticketFinishTime,
    this.ticketStatus,
    this.procServiceName,
    this.ticketStartUserId,
    this.ticketRating,
    this.comment,
    this.listOwner = const [],
    this.listService,
    this.listStatus,
    this.listDate,
    this.user,
    this.ruThongTinChungModel = const [],
    this.contentRequest,
    this.serviceId,
    this.ticketTaskDtoList = const [],
    this.hideResult = false,
    this.informTo = false,
    this.updatePermission = false,
    this.hideRuTasks = const [],
    this.ruPermission = false,
    this.newAndClone = false,
    this.autoInherits = false,
    this.offNotifications,
    this.bpmProcdefInherits = const [],
    this.slaResponse,
    this.slaFinish,
    this.slaResponseProcess,
    this.slaFinishProcess,
    this.slaResponseTimeProcess,
    this.slaFinishTimeProcess,
    this.slaResponseTime,
    this.slaFinishTime,
    this.editPermission = false,
    this.listSignForm = const [],
    this.fullName,
    this.priority,
    this.priorityId,
    this.color,
    this.taskUsers = const {},
    this.signedFiles = const [],
    this.endNodeId,
    this.linkedBpmProcInstDto,
    this.appCode,
    this.submissionTypeName,
    this.submissionType,
    this.cancel = false,
    this.hideInfo = false,
    this.showInfo,
    this.isAssistant = false,
    this.isEditAssistant = false,
    this.hideInfoTasks = const [],
    this.showInfoTasks = const [],
    this.additionalRequest = false,
    this.recall,
    this.showInputTask,
    this.cancelTasks = const [],
    this.showInputTaskDefKeys = const [],
    this.procDefId,
    this.requestCode,
    this.chartId,
    this.chartNodeId,
    this.changeImplementerValue,
    this.ticketRecall,
    this.companyCode,
    this.authorityOnTicketValue,
    this.authorityOnTicketStep,
    this.authorityOnTicket = false,
    this.hideInherit,
    this.hideInheritTasks = const [],
    this.hideComment,
    this.hideCommentTasks = const [],
    this.hideDownload,
    this.hideDownloadTasks = const [],
    this.hideShareTicket,
    this.viewFileApi = const [],
    this.forceViewUrl,
    this.notifyUsers = const [],
    this.cancelDraftVariables,
    this.lstOrgAssignee = const [],
    this.approvedBudgetIds,
  });
  factory ThongTinChungModel.fromJson(Map<dynamic, dynamic> json) {
    return ThongTinChungModel(
      id: json['id'],
      ticketId: json['ticketId'],
      ticketTitle: json['ticketTitle'],
      ticketCreatedUser: json['ticketCreatedUser'],
      ticketProcDefId: json['ticketProcDefId'],
      ticketSla: json['ticketSla'],
      endKey: json['endKey'],
      startKey: json['startKey'],
      ticketEndTime: json['ticketEndTime'],
      ticketClosedTime: json['ticketClosedTime'],
      ticketStartedTime: json['ticketStartedTime'],
      ticketCanceledTime: json['ticketCanceledTime'],
      ticketCreatedTime: json['ticketCreatedTime'],
      ticketFinishTime: json['ticketFinishTime'],
      ticketStatus: json['ticketStatus'],
      procServiceName: json['procServiceName'],
      ticketStartUserId: json['ticketStartUserId'],
      ticketRating: json['ticketRating'],
      comment: json['comment'],
      listOwner: json['listOwner'] != null ? List<dynamic>.from(json['listOwner'] as List) : <dynamic>[],
      listService: json['listService'],
      listStatus: json['listStatus'],
      listDate: json['listDate'],
      user: json['user'],
      ruThongTinChungModel:
          json['ruThongTinChungModel'] != null ? List<dynamic>.from(json['ruThongTinChungModel'] as List) : <dynamic>[],
      contentRequest: json['contentRequest'],
      serviceId: json['serviceId'],
      ticketTaskDtoList:
          (json['ticketTaskDtoList'] as List).map((e) => TicketTaskDto.fromJson(e as Map<dynamic, dynamic>)).toList(),
      hideResult: json['hideResult'] as dynamic,
      informTo: json['informTo'] as dynamic,
      updatePermission: json['updatePermission'] as dynamic,
      hideRuTasks: json['hideRuTasks'] != null ? List<dynamic>.from(json['hideRuTasks'] as List) : <dynamic>[],
      ruPermission: json['ruPermission'] as dynamic,
      newAndClone: json['newAndClone'] as dynamic,
      autoInherits: json['autoInherits'],
      offNotifications: json['offNotifications'],
      bpmProcdefInherits: (json['bpmProcdefInherits'] as List)
          .map((e) => BpmProcdefInherit.fromJson(e as Map<dynamic, dynamic>))
          .toList(),
      slaResponse: json['slaResponse'],
      slaFinish: json['slaFinish'],
      slaResponseProcess: json['slaResponseProcess'],
      slaFinishProcess: json['slaFinishProcess'],
      slaResponseTimeProcess: json['slaResponseTimeProcess'],
      slaFinishTimeProcess: json['slaFinishTimeProcess'],
      slaResponseTime: json['slaResponseTime'],
      slaFinishTime: json['slaFinishTIme'],
      editPermission: json['editPermission'] as dynamic,
      listSignForm: json['listSignForm'] != null ? List<dynamic>.from(json['listSignForm'] as List) : <dynamic>[],
      fullName: json['fullName'],
      priority: json['priority'],
      priorityId: json['priorityId'],
      color: json['color'],
      taskUsers: json['taskUsers'] != null
          ? (json['taskUsers'] as Map<dynamic, dynamic>).map(
              (key, value) => MapEntry(key, value != null ? List<dynamic>.from(value as List) : []),
            )
          : {},
      signedFiles: json['signedFiles'] != null
          ? (json['signedFiles'] as List).map((e) => SignedFile.fromJson(e as Map<String, dynamic>)).toList()
          : <SignedFile>[],
      endNodeId: json['endNodeId'],
      linkedBpmProcInstDto: json['linkedBpmProcInstDto'],
      appCode: json['appCode'],
      submissionTypeName: json['submissionTypeName'],
      submissionType: json['submissionType'],
      cancel: json['cancel'] as dynamic,
      hideInfo: json['hideInfo'] as dynamic,
      showInfo: json['showInfo'],
      isAssistant: json['isAssistant'] as dynamic,
      isEditAssistant: json['isEditAssistant'] as dynamic,
      hideInfoTasks: json['hideInfoTasks'] != null ? List<dynamic>.from(json['hideInfoTasks'] as List) : <dynamic>[],
      showInfoTasks: json['showInfoTasks'] != null ? List<dynamic>.from(json['showInfoTasks'] as List) : <dynamic>[],
      additionalRequest: json['additionalRequest'] as dynamic,
      recall: json['recall'],
      showInputTask: json['showInputTask'],
      cancelTasks: json['cancelTasks'] != null ? List<dynamic>.from(json['cancelTasks'] as List) : <dynamic>[],
      showInputTaskDefKeys:
          json['showInputTaskDefKeys'] != null ? List<dynamic>.from(json['showInputTaskDefKeys'] as List) : <dynamic>[],
      procDefId: json['procDefId'],
      requestCode: json['requestCode'],
      chartId: json['chartId'],
      chartNodeId: json['chartNodeId'],
      changeImplementerValue: json['changeImplementerValue'],
      ticketRecall: json['ticketRecall'],
      companyCode: json['companyCode'],
      authorityOnTicketValue: json['authorityOnTicketValue'],
      authorityOnTicketStep: json['authorityOnTicketStep'],
      authorityOnTicket: json['authorityOnTicket'],
      hideInherit: json['hideInherit'],
      hideInheritTasks:
          json['hideInheritTasks'] != null ? List<dynamic>.from(json['hideInheritTasks'] as List) : <dynamic>[],
      hideComment: json['hideComment'],
      hideCommentTasks:
          json['hideCommentTasks'] != null ? List<dynamic>.from(json['hideCommentTasks'] as List) : <dynamic>[],
      hideDownload: json['hideDownload'],
      hideDownloadTasks:
          json['hideDownloadTasks'] != null ? List<dynamic>.from(json['hideDownloadTasks'] as List) : <dynamic>[],
      hideShareTicket: json['hideShareTicket'],
      viewFileApi: json['viewFileApi'] != null ? List<dynamic>.from(json['viewFileApi'] as List) : <dynamic>[],
      forceViewUrl: json['forceViewUrl'],
      notifyUsers: json['notifyUsers'] != null ? List<dynamic>.from(json['notifyUsers'] as List) : <dynamic>[],
      cancelDraftVariables: json['cancelDraftVariables'],
      lstOrgAssignee: json['lstOrgAssignee'] != null ? List<dynamic>.from(json['lstOrgAssignee'] as List) : <dynamic>[],
      approvedBudgetIds: json['approvedBudgetIds'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'ticketTitle': ticketTitle,
      'ticketCreatedUser': ticketCreatedUser,
      'ticketProcDefId': ticketProcDefId,
      'ticketSla': ticketSla,
      'endKey': endKey,
      'startKey': startKey,
      'ticketEndTime': ticketEndTime,
      'ticketClosedTime': ticketClosedTime,
      'ticketStartedTime': ticketStartedTime,
      'ticketCanceledTime': ticketCanceledTime,
      'ticketCreatedTime': ticketCreatedTime,
      'ticketFinishTime': ticketFinishTime,
      'ticketStatus': ticketStatus,
      'procServiceName': procServiceName,
      'ticketStartUserId': ticketStartUserId,
      'ticketRating': ticketRating,
      'comment': comment,
      'listOwner': listOwner,
      'listService': listService,
      'listStatus': listStatus,
      'listDate': listDate,
      'user': user,
      'ruThongTinChungModel': ruThongTinChungModel,
      'contentRequest': contentRequest,
      'serviceId': serviceId,
      'ticketTaskDtoList': ticketTaskDtoList.map((e) => e.toJson()).toList(),
      'hideResult': hideResult,
      'informTo': informTo,
      'updatePermission': updatePermission,
      'hideRuTasks': hideRuTasks,
      'ruPermission': ruPermission,
      'newAndClone': newAndClone,
      'autoInherits': autoInherits,
      'offNotifications': offNotifications,
      'bpmProcdefInherits': bpmProcdefInherits.map((e) => e.toJson()).toList(),
      'slaResponse': slaResponse,
      'slaFinish': slaFinish,
      'slaResponseProcess': slaResponseProcess,
      'slaFinishProcess': slaFinishProcess,
      'slaResponseTimeProcess': slaResponseTimeProcess,
      'slaFinishTimeProcess': slaFinishTimeProcess,
      'slaResponseTime': slaResponseTime,
      'slaFinishTIme': slaFinishTime,
      'editPermission': editPermission,
      'listSignForm': listSignForm,
      'fullName': fullName,
      'priority': priority,
      'priorityId': priorityId,
      'color': color,
      'taskUsers': taskUsers,
      'signedFiles': signedFiles,
      'endNodeId': endNodeId,
      'linkedBpmProcInstDto': linkedBpmProcInstDto,
      'appCode': appCode,
      'submissionTypeName': submissionTypeName,
      'submissionType': submissionType,
      'cancel': cancel,
      'hideInfo': hideInfo,
      'showInfo': showInfo,
      'isAssistant': isAssistant,
      'isEditAssistant': isEditAssistant,
      'hideInfoTasks': hideInfoTasks,
      'showInfoTasks': showInfoTasks,
      'additionalRequest': additionalRequest,
      'recall': recall,
      'showInputTask': showInputTask,
      'cancelTasks': cancelTasks,
      'showInputTaskDefKeys': showInputTaskDefKeys,
      'procDefId': procDefId,
      'requestCode': requestCode,
      'chartId': chartId,
      'chartNodeId': chartNodeId,
      'changeImplementerValue': changeImplementerValue,
      'ticketRecall': ticketRecall,
      'companyCode': companyCode,
      'authorityOnTicketValue': authorityOnTicketValue,
      'authorityOnTicketStep': authorityOnTicketStep,
      'authorityOnTicket': authorityOnTicket,
      'hideInherit': hideInherit,
      'hideInheritTasks': hideInheritTasks,
      'hideComment': hideComment,
      'hideCommentTasks': hideCommentTasks,
      'hideDownload': hideDownload,
      'hideDownloadTasks': hideDownloadTasks,
      'hideShareTicket': hideShareTicket,
      'viewFileApi': viewFileApi,
      'forceViewUrl': forceViewUrl,
      'notifyUsers': notifyUsers,
      'cancelDraftVariables': cancelDraftVariables,
      'lstOrgAssignee': lstOrgAssignee,
      'approvedBudgetIds': approvedBudgetIds,
    };
  }
}

class TicketTaskDto {
  final dynamic id;
  final dynamic taskId;
  final dynamic taskDefKey;
  final dynamic taskName;
  final dynamic taskPriority;
  final dynamic taskAssignee;
  final dynamic taskCreatedTime;
  final dynamic taskStartedTime;
  final dynamic taskSla;
  final dynamic taskFinishedTime;
  final dynamic taskDoneTime;
  final dynamic taskStatus;
  final dynamic procInstId;
  final dynamic endKey;
  final dynamic procDefId;
  final dynamic taskType;
  final dynamic affected;
  final dynamic listStatus;
  final dynamic listVariables;
  final dynamic responseTime;
  final dynamic finishTime;
  final dynamic newTaskId;
  final dynamic startPermission;
  final dynamic newStatus;
  final dynamic prdynamicId;
  final dynamic ticketId;
  final dynamic listSignForm;
  final dynamic editPermission;
  final dynamic ticketProcDefId;
  final dynamic fullName;
  final dynamic taskUsers;
  final dynamic priorityId;
  final dynamic color;
  final dynamic signedFiles;
  final dynamic newId;
  final dynamic draftVariables;
  final dynamic taskActionUser;
  final dynamic taskOrgAssignee;
  final dynamic taskProcDefId;

  TicketTaskDto({
    required this.id,
    required this.taskId,
    required this.taskDefKey,
    required this.taskName,
    required this.taskPriority,
    required this.taskAssignee,
    required this.taskCreatedTime,
    required this.taskStartedTime,
    required this.taskSla,
    this.taskFinishedTime,
    this.taskDoneTime,
    required this.taskStatus,
    required this.procInstId,
    this.endKey,
    required this.procDefId,
    required this.taskType,
    required this.affected,
    required this.listStatus,
    required this.listVariables,
    required this.responseTime,
    required this.finishTime,
    required this.newTaskId,
    required this.startPermission,
    required this.newStatus,
    required this.prdynamicId,
    required this.ticketId,
    required this.listSignForm,
    required this.editPermission,
    required this.ticketProcDefId,
    required this.fullName,
    required this.taskUsers,
    required this.priorityId,
    required this.color,
    required this.signedFiles,
    required this.newId,
    required this.draftVariables,
    required this.taskActionUser,
    required this.taskOrgAssignee,
    required this.taskProcDefId,
  });

  factory TicketTaskDto.fromJson(Map<dynamic, dynamic> json) {
    return TicketTaskDto(
      id: json['id'],
      taskId: json['taskId'],
      taskDefKey: json['taskDefKey'],
      taskName: json['taskName'],
      taskPriority: json['taskPriority'],
      taskAssignee: json['taskAssignee'],
      taskCreatedTime: json['taskCreatedTime'],
      taskStartedTime: json['taskStartedTime'],
      taskSla: json['taskSla'],
      taskFinishedTime: json['taskFinishedTime'],
      taskDoneTime: json['taskDoneTime'],
      taskStatus: json['taskStatus'],
      procInstId: json['procInstId'],
      endKey: json['endKey'],
      procDefId: json['procDefId'],
      taskType: json['taskType'],
      affected: json['affected'],
      listStatus: json['listStatus'],
      listVariables: json['listVariables'],
      responseTime: json['responseTime'],
      finishTime: json['finishTime'],
      newTaskId: json['newTaskId'],
      startPermission: json['startPermission'],
      newStatus: json['newStatus'],
      prdynamicId: json['prdynamicId'],
      ticketId: json['ticketId'],
      listSignForm: json['listSignForm'],
      editPermission: json['editPermission'],
      ticketProcDefId: json['ticketProcDefId'],
      fullName: json['fullName'],
      taskUsers: json['taskUsers'],
      priorityId: json['priorityId'],
      color: json['color'],
      signedFiles: json['signedFiles'],
      newId: json['newId'],
      draftVariables: json['draftVariables'],
      taskActionUser: json['taskActionUser'],
      taskOrgAssignee: json['taskOrgAssignee'],
      taskProcDefId: json['taskProcDefId'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'taskDefKey': taskDefKey,
      'taskName': taskName,
      'taskPriority': taskPriority,
      'taskAssignee': taskAssignee,
      'taskCreatedTime': taskCreatedTime,
      'taskStartedTime': taskStartedTime,
      'taskSla': taskSla,
      'taskFinishedTime': taskFinishedTime,
      'taskDoneTime': taskDoneTime,
      'taskStatus': taskStatus,
      'procInstId': procInstId,
      'endKey': endKey,
      'procDefId': procDefId,
      'taskType': taskType,
      'affected': affected,
      'listStatus': listStatus,
      'listVariables': listVariables,
      'responseTime': responseTime,
      'finishTime': finishTime,
      'newTaskId': newTaskId,
      'startPermission': startPermission,
      'newStatus': newStatus,
      'prdynamicId': prdynamicId,
      'ticketId': ticketId,
      'listSignForm': listSignForm,
      'editPermission': editPermission,
      'ticketProcDefId': ticketProcDefId,
      'fullName': fullName,
      'taskUsers': taskUsers,
      'priorityId': priorityId,
      'color': color,
      'signedFiles': signedFiles,
      'newId': newId,
      'draftVariables': draftVariables,
      'taskActionUser': taskActionUser,
      'taskOrgAssignee': taskOrgAssignee,
      'taskProcDefId': taskProcDefId,
    };
  }
}

class BpmProcdefInherit {
  final dynamic id;
  final dynamic bpmProcdefId;
  final dynamic procDefId;
  final dynamic taskDefKey;
  final dynamic taskDefKeyInherits;
  final dynamic fieldInherits;
  final dynamic status;
  final List<dynamic> createdTime;
  final dynamic createdUser;
  final dynamic updatedTime;
  final dynamic updatedUser;

  BpmProcdefInherit({
    required this.id,
    required this.bpmProcdefId,
    required this.procDefId,
    required this.taskDefKey,
    required this.taskDefKeyInherits,
    required this.fieldInherits,
    required this.status,
    required this.createdTime,
    required this.createdUser,
    required this.updatedTime,
    required this.updatedUser,
  });

  factory BpmProcdefInherit.fromJson(Map<dynamic, dynamic> json) {
    return BpmProcdefInherit(
      id: json['id'],
      bpmProcdefId: json['bpmProcdefId'],
      procDefId: json['procDefId'],
      taskDefKey: json['taskDefKey'],
      taskDefKeyInherits: json['taskDefKeyInherits'],
      fieldInherits: json['fieldInherits'],
      status: json['status'],
      createdTime: List<dynamic>.from(json['createdTime'] as List),
      createdUser: json['createdUser'],
      updatedTime: json['updatedTime'],
      updatedUser: json['updatedUser'],
    );
  }

  Map<dynamic, dynamic> toJson() {
    return {
      'id': id,
      'bpmProcdefId': bpmProcdefId,
      'procDefId': procDefId,
      'taskDefKey': taskDefKey,
      'taskDefKeyInherits': taskDefKeyInherits,
      'fieldInherits': fieldInherits,
      'status': status,
      'createdTime': createdTime,
      'createdUser': createdUser,
      'updatedTime': updatedTime,
      'updatedUser': updatedUser,
    };
  }
}

class SignedFile {
  final String taskDefKey;
  final String signedFile;
  final String fileName;
  final int bpmTemplatePrintId;

  SignedFile({
    required this.taskDefKey,
    required this.signedFile,
    required this.fileName,
    required this.bpmTemplatePrintId,
  });

  factory SignedFile.fromJson(Map<String, dynamic> json) {
    return SignedFile(
      taskDefKey: json['taskDefKey'] as String,
      signedFile: json['signedFile'] as String,
      fileName: json['fileName'] as String,
      bpmTemplatePrintId: json['bpmTemplatePrintId'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskDefKey': taskDefKey,
      'signedFile': signedFile,
      'fileName': fileName,
      'bpmTemplatePrintId': bpmTemplatePrintId,
    };
  }
}
