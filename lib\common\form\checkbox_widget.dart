import 'package:eapprove/common/form/base_form_item.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'dart:developer' as developer;

/// A widget that renders a group of checkboxes with optional "Select All" and "Other" options.
/// Supports both horizontal and vertical layouts.
class CheckboxWidget extends StatefulWidget {
  final FormItemInfo data;
  final FormStateManager stateManager;
  final int? rowIndex;

  const CheckboxWidget({
    Key? key,
    required this.data,
    required this.stateManager,
    this.rowIndex,
  }) : super(key: key);

  @override
  State<CheckboxWidget> createState() => _CheckboxWidgetState();
}

class _CheckboxWidgetState extends State<CheckboxWidget> {
  late List<String> _selectedValues;
  late final bool _isReadOnly;
  late final bool _isHorizontal;
  late final bool _isHorizontalItem;

  @override
  void initState() {
    super.initState();
    _initializeState();
  }

  void _initializeState() {
    _isReadOnly = widget.data.readonly == true;
    _isHorizontal = DeviceUtils.isTablet == true;
    _isHorizontalItem = widget.data.isHorizontalItem == 1;
    _selectedValues = _parseInitialValue();
  }

  List<String> _parseInitialValue() {
    if (widget.data.value == null) return [];
    if (widget.data.value is String) return [widget.data.value as String];
    if (widget.data.value is List) {
      return List<String>.from(
          widget.data.value.map((item) => item.toString()));
    }
    return [widget.data.value.toString()];
  }

  void _updateSelectedValues(List<String> newValues) {
    setState(() => _selectedValues = newValues);
    widget.data.value = newValues;
    widget.stateManager.setFieldValue(
      widget.data.name ?? '',
      newValues,
      rowIndex: widget.rowIndex,
    );
  }

  bool _isAllSelected(List<Map<String, dynamic>> options) {
    if (options.isEmpty) return false;
    return options.every((option) =>
        _selectedValues.contains(option['value']?.toString() ?? ''));
  }

  Widget _buildCheckboxItem({
    required String value,
    required String label,
    required bool isSelected,
    required ValueChanged<bool?> onChanged,
  }) {
    developer.log(
        "_isReadOnly_isReadOnly_isReadOnly:: ${_isReadOnly} ${widget.data.readonly}");
    return Container(
      // padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Checkbox(
            value: isSelected,
            onChanged: widget.data.readonly == true ? null : onChanged,
          ),
          Flexible(
            child: GestureDetector(
              onTap: widget.data.readonly == true
                  ? null
                  : () => onChanged(!isSelected),
              child: Text(label),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOtherCheckbox() {
    final options = widget.data.option ?? [];
    final isOtherSelected = _selectedValues.any((value) =>
        !options.any((o) => (o['value']?.toString() ?? '') == value));

    final otherValue = _selectedValues.firstWhere(
      (value) => !options.any((o) => (o['value']?.toString() ?? '') == value),
      orElse: () => '',
    );

    return Row(
      children: [
        Checkbox(
          value: isOtherSelected,
          onChanged: widget.data.readonly == true
              ? null
              : (value) {
                  final newValues = List<String>.from(_selectedValues);
                  if (value == true) {
                    if (!isOtherSelected) {
                      newValues.add('other');
                    }
                  } else {
                    newValues.removeWhere((val) => !options
                        .any((o) => (o['value']?.toString() ?? '') == val));
                  }
                  _updateSelectedValues(newValues);
                },
        ),
        const Text('Other: '),
        Expanded(
          child: TextField(
            enabled: isOtherSelected && widget.data.readonly == false,
            onChanged: (value) {
              final newValues = List<String>.from(_selectedValues);
              if (isOtherSelected) {
                newValues.removeWhere((val) =>
                    !options.any((o) => (o['value']?.toString() ?? '') == val));
                newValues.add(value);
              }
              _updateSelectedValues(newValues);
            },
            decoration: const InputDecoration(
              isDense: true,
              border: OutlineInputBorder(),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final options = widget.data.option ?? [];
    final hasOtherOption = widget.data.isCheckDifference ?? false;
    final hasSelectAllOption = widget.data.isCheckAll ?? false;

    final checkboxes = <Widget>[];

    if (hasSelectAllOption) {
      checkboxes.add(
        _buildCheckboxItem(
          value: 'select_all',
          label: 'Select All',
          isSelected: _isAllSelected(options),
          onChanged: (value) {
            final newValues = value == true
                ? options
                    .map((o) => o['value'].toString())
                    .toList()
                    .cast<String>()
                : <String>[];
            _updateSelectedValues(newValues);
          },
        ),
      );
    }

    for (var option in options) {
      checkboxes.add(
        _buildCheckboxItem(
          value: option['value']?.toString() ?? '',
          label: option['label']?.toString() ?? '',
          isSelected:
              _selectedValues.contains(option['value']?.toString() ?? ''),
          onChanged: (value) {
            final newValues = List<String>.from(_selectedValues);
            final optionValue = option['value']?.toString() ?? '';

            if (value == true) {
              if (!newValues.contains(optionValue)) {
                newValues.add(optionValue);
              }
            } else {
              newValues.remove(optionValue);
            }
            _updateSelectedValues(newValues);
          },
        ),
      );
    }

    if (hasOtherOption) {
      checkboxes.add(_buildOtherCheckbox());
    }

    final checkboxGroup = _isHorizontalItem
        ? Wrap(spacing: 16, children: checkboxes)
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: checkboxes,
          );

    return Container(
      decoration: BoxDecoration(
          // border: Border.all(color: Colors.grey),
          // borderRadius: BorderRadius.circular(8),
          // color: Colors.red,
          ),
      child: _isHorizontal
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: checkboxGroup),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // const SizedBox(height: 8),
                checkboxGroup,
              ],
            ),
    );
  }
}
