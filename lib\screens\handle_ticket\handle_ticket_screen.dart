import 'dart:collection';
import 'dart:developer' as developer;
import 'dart:developer';
import 'package:collection/collection.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/ticket_input/ticket_input_bloc.dart';
import 'package:eapprove/blocs/ticket_input/ticket_input_event.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/state/work_flow_state.dart';
import 'package:eapprove/models/form/check_to_trinh_response_model.dart' as checkToTrinh;
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:eapprove/models/pyc/ticket_process_detail_model.dart';
import 'package:eapprove/models/ticket_other_action/work_flow_respone_model.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_comment.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_common.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_consultation.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_information_block.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_information_detail.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_input.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_output.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_process_detail.dart';
import 'package:eapprove/screens/payment_step/widgets/bottom_bar.dart';
import 'package:eapprove/screens/payment_step/widgets/pdf_view.dart';
import 'package:eapprove/screens/ticket_other_action/widget/work_flow_widget.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:eapprove/widgets/header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:flutter_sdk/utils/device_utils.dart';

class HandleTicketScreen extends StatefulWidget {
  static const routeName = "/handle-ticket";
  final dynamic ticket;
  final bool isEmbedded;
  final Function(ThongTinChungModel)? onTicketDataLoaded;
  final Function(bool)? onPdfViewed;
  final bool showWorkflow;
  final Function(bool)? onWorkflowChanged;
  const HandleTicketScreen({
    super.key,
    required this.ticket,
    this.isEmbedded = false,
    this.onTicketDataLoaded,
    this.onPdfViewed,
    this.showWorkflow = false,
    this.onWorkflowChanged,
  });

  @override
  State<HandleTicketScreen> createState() => HandleTicketScreenState();
}

class HandleTicketScreenState extends State<HandleTicketScreen> {
  late String ticketTitle;
  late String? userId = '';
  String? _assignee;
  String? selectedNodeId;
  WorkflowResponse? _currentWorkflowData;
  UserTaskInfoResponse? _userTaskInfo;
  int? _processType;
  String? selectedNodeType;
  List<checkToTrinh.SignForm>? _listSignForm;
  bool _hasCheckedSignedFiles = false;
  ThongTinChungModel? _thongTinChungModel;
  ThongTinDauVaoResponseModel? _thongTinDauVao;
  final GlobalKey<TicketProcessDetailBlockState> _ticketDetailBlockKey = GlobalKey();
  TicketProcessDetailModel? _currentTicketProcessDetailModel;
  bool _isInitialLoadDone = false;
  bool _hasLoadedTicketDetail = false;
  bool _hasNavigatedToPdf = false;

  // final GlobalKey<NavigatorState> _rightColumnNavigatorKey = GlobalKey<NavigatorState>();

  String? prevTask;
  String? _pdfUrl;
  String? _pdfName;
  final isTablet = DeviceUtils.isTablet;
  bool _hasHandledTicketDetail = false;
  bool _hasHandledProcessType = false;
  String? _lastHandledTicketId;
  String? taskId = '';

  void _notifyPdfViewChanged(bool isPdfShowing) {
    if (widget.onPdfViewed != null) {
      widget.onPdfViewed!(isPdfShowing);
    }
  }

  @override
  void initState() {
    super.initState();
    ticketTitle = TicketUtils.getTicketTitle(widget.ticket);
    TicketUtils.logTicketDetails(widget.ticket);
    userId = "employee";

    context.read<BottomNavBloc>().add(
          SetBottomNavVisibility(true),
        );
    final blocState = context.read<TicketProcessDetailBloc>().state;
    if (blocState is TicketProcessDetailLoaded) {
      _currentTicketProcessDetailModel = blocState.ticketProcessDetail;
    }
    if (blocState is TicketProcessDetailByIdLoaded) {
      _thongTinChungModel = blocState.thongTinChungModel;
    }
    _loadInitialData();
  }

  void _onWorkflowNodeSelected(String nodeId, String nodeType) {
    setState(() {
      selectedNodeId = nodeId;
      selectedNodeType = nodeType;
      developer.log('🔄 [HandleTicketScreen] Updating selectedNodeId = $nodeId, nodeType = $nodeType');
    });

    if (_thongTinChungModel == null) {
      developer.log('⚠️ ThongTinChungModel chưa có dữ liệu');
      return;
    }

    _handleDifferentNodeTypes(nodeId, nodeType);

    _reloadDataWithNewTaskDefKey(nodeId);

    if (_currentTicketProcessDetailModel != null) {
      _ticketDetailBlockKey.currentState?.updateStepByNode(
        nodeId: nodeId,
        nodeType: nodeType,
        ticketProcessDetailModel: _currentTicketProcessDetailModel!,
        thongTinChungModel: _thongTinChungModel!,
        groups: _currentWorkflowData?.data ?? [],
      );
    }
  }

  void _handleDifferentNodeTypes(String nodeId, String nodeType) {
    developer.log(' Handling nodeType: $nodeType for nodeId: $nodeId');

    switch (nodeType) {
      case 'startEvent':
        developer.log(' Selected start event node');
        break;
      case 'endEvent':
        developer.log(' Selected end event node');
        break;
      case 'userTask':
        developer.log(' Selected user task node');
        break;

      default:
        selectedNodeType = 'userTask';
        break;
    }
  }

// Thêm hàm mới để reload data
  void _reloadDataWithNewTaskDefKey(String newTaskDefKey) {
    developer.log('🔄 Reloading data with new taskDefKey: $newTaskDefKey');

    final ticketId = getCorrectTicketId();
    final procInstId = getProcInstId()?.toString() ?? '';
    final status = _safeGetProperty('status') ?? '';
    final taskInfo = _userTaskInfo?.data?.firstWhereOrNull((t) => t.taskDefKey == newTaskDefKey);
    final user = taskInfo?.lstAssigneeInfo?.first.username ?? '';

    // 1. Reload TicketProcessDetail với taskDefKey mới
    if (procInstId.isNotEmpty && newTaskDefKey.isNotEmpty) {
      context.read<TicketProcessDetailBloc>().add(
            LoadTicketProcessDetail(
              procInstId: procInstId,
              taskDefKey: newTaskDefKey,
              user: user,
              status: status,
            ),
          );
    }
    if (procInstId.isNotEmpty && newTaskDefKey.isNotEmpty) {
      context.read<TicketProcessDetailBloc>().add(
            LoadTicketProcessDetailById(
              ticketId: ticketId?.toString() ?? '',
            ),
          );
    }
    final taskId = TicketUtils.getTaskId(widget.ticket);
    // 2. Reload TicketInput data với taskId mới
    context.read<TicketInputBloc>().add(
          LoadTicketInput(
            taskId: taskId?.toString() ?? '',
            type: 'input',
          ),
        );

    // 3. Reload CheckToTrinh nếu cần
    final procDefId = _getProcDefId();
    if (procDefId != null && _thongTinChungModel != null) {
      context.read<TicketProcessDetailBloc>().add(
            CheckToTrinhHandleTicket(
              procDefId: procDefId.toString(),
              serviceId: int.tryParse(_thongTinChungModel!.serviceId?.toString() ?? '') ?? 0,
              taskDefKey: newTaskDefKey,
            ),
          );
    }

    // 4. Reset các state flag để cho phép xử lý lại
    setState(() {
      _hasHandledProcessType = false;
      _hasHandledTicketDetail = false;
    });
  }

  void _getTicketInputData() {
    final taskId = TicketUtils.getTaskId(widget.ticket);
    if (taskId != null && taskId.isNotEmpty) {
      context.read<TicketInputBloc>().add(
            LoadTicketInput(
              taskId: taskId,
              type: 'input',
            ),
          );
    } else {
      developer.log('⚠️ Không tìm được taskId hợp lệ để gọi getTicketInputData');
    }
  }

  void _loadInitialData() {
    if (_isInitialLoadDone) return;
    _isInitialLoadDone = true;

    developer.log('=== LOAD INITIAL DATA ===');
    developer.log('widget.ticketwidget.ticket ${widget.ticket}');
    developer.log('ticket status: ${TicketUtils.safeGetProperty(widget.ticket, "status")}');

    final ticketId = TicketUtils.getCorrectTicketId(widget.ticket);
    developer.log('Received ticketId: $ticketId', name: 'HandleTicketScreen');
    _getTicketInputData();
    if (!_hasLoadedTicketDetail && ticketId != null) {
      _hasLoadedTicketDetail = true;
      developer.log('🔄 Loading ticket detail for ticketId: $ticketId');
      context.read<TicketProcessDetailBloc>().add(LoadTicketProcessDetailById(ticketId: ticketId));
    }
    context.read<TicketProcessDetailBloc>().add(LoadUserTaskInfo(ticketId: ticketId));
  }

  Widget _buildWorkflowView() {
    if (_thongTinChungModel == null) {
      return Container(
        height: 200.h,
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(child: AppConstraint.buildLoading(context)),
      );
    }

    final procDefId = TicketUtils.getProcDefId(widget.ticket) ?? '';
    final procInstId = TicketUtils.getProcInstId(widget.ticket)?.toString() ?? '';
    final ticketId = TicketUtils.getCorrectTicketId(widget.ticket)?.toString() ?? '';

    developer.log('TicketId1231231: $ticketId');

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Container(
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
              ),
              child: Text(
                'Luồng nghiệp vụ',
                style: getTypoSkin().medium14.copyWith(
                      color: getColorSkin().black,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.8,
              child: BlocProvider.value(
                value: context.read<WorkflowBloc>(),
                child: WorkflowWidget(
                  procDefId: procDefId,
                  procInstId: procInstId,
                  data: _thongTinChungModel,
                  ticketId: ticketId,
                  isEmbedded: true,
                  showBottomInfo: false,
                  // onNodeSelected: (index) {
                  //   final selectedNode = context.read<WorkflowBloc>().state;
                  //   String? nodeId;

                  //   if (selectedNode is WorkflowLoaded) {
                  //     final allNodes = selectedNode.workflow.data?.expand((e) => e.nodes ?? []).toList();
                  //     final sortedNodes = allNodes?..sort((a, b) => (a.position ?? 0).compareTo(b.position ?? 0));
                  //     _currentWorkflowData = selectedNode.workflow;
                  //     if (index < (sortedNodes?.length ?? 0)) {
                  //       nodeId = sortedNodes?[index].id;
                  //     }
                  //   }

                  //   developer.log('📌 [HandleTicketScreen] Node selected from workflow: index=$index, id=$nodeId',
                  //       name: 'HandleTicketScreen');

                  //   setState(() {
                  //     selectedNodeId = nodeId;
                  //   });
                  // },
                  onNodeSelected: (nodeId, nodeType) {
                    _onWorkflowNodeSelected(nodeId, nodeType);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  dynamic getCorrectTicketId() {
    return TicketUtils.getCorrectTicketId(widget.ticket);
  }

  dynamic getTicketId() {
    return TicketUtils.getTicketId(widget.ticket);
  }

  dynamic getProcInstId() {
    return TicketUtils.getProcInstId(widget.ticket);
  }

  String? _getTaskDefKey() {
    return TicketUtils.getTaskDefKey(widget.ticket);
  }

  String? _getProcDefId() {
    return TicketUtils.getProcDefId(widget.ticket);
  }

  String _getTicketTitle() {
    return TicketUtils.getTicketTitle(widget.ticket);
  }

  dynamic _safeGetProperty(String propertyName) {
    return TicketUtils.safeGetProperty(widget.ticket, propertyName);
  }

  Widget _buildScreenContent(TicketProcessDetailState state) {
    TicketUtils.logTicketDetails(widget.ticket);
    // developer.log('=== BUILD SCREEN CONTENT ===');
    // developer.log('State Type: ${state.runtimeType}', name: 'BuildScreenContent');
    // developer.log('Process Type: $_processType', name: 'BuildScreenContent');
    // developer.log('List Sign Form: ${_listSignForm?.length}', name: 'BuildScreenContent');
    // developer.log('Has Handled Ticket Detail: $_hasHandledTicketDetail', name: 'BuildScreenContent');
    // developer.log('Last Handled Ticket ID: $_lastHandledTicketId', name: 'BuildScreenContent');

    if (_processType == null && state is TicketProcessDetailLoading) {
      developer.log('🔄 Showing loading state');
      return Center(child: AppConstraint.buildLoading(context));
    }

    final ticketId = getCorrectTicketId();
    final procInstId = getProcInstId()?.toString() ?? '';
    final taskDefKey = _safeGetTaskDefKey() ?? '';
    // developer.log('Building content with:', name: 'BuildScreenContent');
    // developer.log('Ticket ID: $ticketId', name: 'BuildScreenContent');
    // developer.log('Process Instance ID: $procInstId', name: 'BuildScreenContent');
    developer.log('Task Def Key: $taskDefKey', name: 'BuildScreenContent');
    if (widget.showWorkflow) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 280.w,
            child: _buildWorkflowView(),
          ),
          Expanded(
            child: _buildTicketContent(ticketId, procInstId, taskDefKey),
          ),
        ],
      );
    }

    return _buildTicketContent(ticketId, procInstId, taskDefKey);
  }

  Widget _buildTicketContent(dynamic ticketId, String procInstId, String taskDefKey) {
    return SingleChildScrollView(
      key: const ValueKey('scroll_ticket_content'),
      padding: EdgeInsets.all(16.w),
      child: Column(
        key: const ValueKey('col_ticket_content'),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _buildContentWidgets(ticketId, procInstId, taskDefKey),
      ),
    );
  }

  List<Widget> _buildContentWidgets(dynamic ticketId, String procInstId, String taskDefKey) {
    final hasCreator = widget.ticket.ticketStartUserId?.isNotEmpty == true;
    final hasAssignee = _assignee?.isNotEmpty == true;
    return [
      if (hasCreator) ...[
        TicketInformationBlock(
          key: const ValueKey('info_detail'),
          title: 'Thông tin chi tiết phiếu',
          ticket: widget.ticket,
        ),
      ],
      SizedBox(height: 12.h),
      // Thông tin chung - chỉ hiện khi có creator nhưng không có approver
      if (hasCreator && !hasAssignee) ...[
        TicketCommonInformationBlock(
          key: const ValueKey('common_info'),
          title: 'Thông tin chung',
          userId: userId ?? '',
          ticket: widget.ticket,
          assignee: _assignee,
        ),
      ],
      TicketCommentBlock(
        key: const ValueKey('comment_block'),
        title: 'Ý kiến trợ lý',
      ),
      SizedBox(height: 12.h),
      TicketInformationDetailBlock(
        key: const ValueKey('info_block_detail'),
        title: 'Thông tin phiếu',
        prevTask: prevTask ?? '',
        procDefId: _getProcDefId() ?? '',
        taskId: getCorrectTicketId()?.toString() ?? '',
        ticketId: _safeGetProperty('ticketId')?.toString() ?? '',
        ticket: widget.ticket,
        taskDefKey: taskDefKey,
      ),

      SizedBox(height: 16.h),
      if (hasCreator && hasAssignee && selectedNodeType != 'startEvent') ...[
        TicketProcessDetailBlock(
          key: _ticketDetailBlockKey,
          title: 'Thông tin chi tiết bước',
          ticketId: ticketId?.toString() ?? '',
          ticket: widget.ticket,
        ),
        SizedBox(height: 12.h),
      ],
      SizedBox(height: 12.h),
      if (hasCreator && hasAssignee) ...[
        TicketInputBlock(
          key: const ValueKey('input_block'),
          title: 'Thông tin đầu vào',
          ticket: widget.ticket,
        ),
      ],
      if (ticketId != null && hasCreator && hasAssignee) ...[
        SizedBox(height: 12.h),
        TicketOutputBlock(
          key: const ValueKey('output_block'),
          title: 'Thông tin đầu ra',
          ticketId: ticketId,
          taskDefKey: taskDefKey,
          user: userId,
          status: '',
          procDefId: _getProcDefId(),
        ),
      ],
      if (ticketId != null && procInstId.isNotEmpty) ...[
        SizedBox(height: 12.h),
        TicketConsultationBlock(
          key: const ValueKey('consultation_block'),
          title: 'Tham vấn',
          ticketId: ticketId,
          procInstId: procInstId,
        ),
      ],
    ];
  }

  String? _safeGetTaskDefKey() {
    developer.log('widget.ticket: ${widget.ticket}', name: 'HandleTicketScreen');
    return TicketUtils.safeGetTaskDefKey(widget.ticket);
  }

  void _handleSignedFileUrl(SignedFileUrlLoaded state) {
    developer.log('✅ Received signed file URL: ${state.fileUrl}');
    developer.log('✅ Original file name: ${state.originalFileName}');

    setState(() {
      _pdfUrl = state.fileUrl;
      _pdfName = state.originalFileName;
    });

    if (widget.isEmbedded && isTablet) {
      setState(() {
        _hasNavigatedToPdf = true;
      });
      _notifyPdfViewChanged(true);
    } else {
      if (!widget.isEmbedded) {
        context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PdfViewerPage(
              pdfName: _pdfName ?? '',
              pdfUrl: _pdfUrl!,
              source: 'handle_ticket',
              title: _pdfName ?? '',
              nodeType: selectedNodeType,
              ticket: widget.ticket,
              data: _thongTinChungModel,
              processType: _processType,
              taskIdValue: taskId,
              thongTinDauVao: _thongTinDauVao,
              ticketStatus: _safeGetProperty('status') ?? '',
              assignee: _assignee,
              onWorkflowNodeSelected: _onWorkflowNodeSelected,
            ),
          ),
        ).then((_) {
          if (!widget.isEmbedded && context.mounted) {
            context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
          }
        });
      });
    }
  }

  void _handleProcessTypeLoaded(TicketProcessDetailCheckToTrinhLoaded state) {
    // Prevent duplicate handling
    if (_hasHandledProcessType && _processType == state.checkToTrinhResponseModel.data.processType) {
      developer.log('⚠️ ProcessType already handled, skipping...');
      return;
    }

    developer.log('=== HANDLE PROCESS TYPE LOADED ===');
    developer.log('Status = "${_safeGetProperty('status')}"');

    setState(() {
      _hasHandledProcessType = true;
      _processType = state.checkToTrinhResponseModel.data.processType;
      _listSignForm = state.checkToTrinhResponseModel.data.listSignForm;
    });

    developer.log('processType: $_processType');
    developer.log('listSignForm length: ${_listSignForm?.length}');
  }

  void _handleTicketDetailLoaded(TicketProcessDetailByIdLoaded state) {
    // Prevent duplicate handling for same ticket
    final currentTicketId = state.thongTinChungModel.ticketId;
    if (_hasHandledTicketDetail && _lastHandledTicketId == currentTicketId) {
      developer.log('⚠️ Ticket detail already handled for ticketId: $currentTicketId, skipping...');
      return;
    }

    developer.log('=== HANDLE TICKET DETAIL LOADED ===');
    developer.log('ticketId: $currentTicketId');
    developer.log('_processType: $_processType');

    _hasHandledTicketDetail = true;
    _lastHandledTicketId = currentTicketId;
    _thongTinChungModel = state.thongTinChungModel;

    if (widget.onTicketDataLoaded != null && _thongTinChungModel != null) {
      widget.onTicketDataLoaded!(_thongTinChungModel!);
    }
    if (state.thongTinChungModel.ticketStatus == 'COMPLETED') {
      setState(() {
        selectedNodeType = 'startEvent';
        selectedNodeId = null; // Có thể reset selectedNodeId nếu cần
      });
    }
    final procDefId = _getProcDefId();
    final taskDefKey = _getTaskDefKey();

    // Chỉ load CheckToTrinh nếu chưa có processType
    if (_processType == null && procDefId != null) {
      developer.log('🔄 Loading CheckToTrinh...');
      context.read<TicketProcessDetailBloc>().add(
            CheckToTrinhHandleTicket(
              procDefId: procDefId.toString(),
              serviceId: int.tryParse(state.thongTinChungModel.serviceId?.toString() ?? '') ?? 0,
              taskDefKey: taskDefKey ?? '',
            ),
          );
    }

    // Check và load signed files
    if (!_hasCheckedSignedFiles && state.thongTinChungModel.signedFiles.isNotEmpty) {
      developer.log('🔄 Loading signed files...');
      setState(() {
        _hasCheckedSignedFiles = true;
      });

      final firstFile = state.thongTinChungModel.signedFiles.first;
      context.read<TicketProcessDetailBloc>().add(
            GetSignedFileUrl(
              fileName: firstFile.signedFile,
              originalFileName: firstFile.fileName,
            ),
          );
    } else {
      developer.log('⚠️ No signed files found or already checked');
    }
  }

  void _handleUserTaskInfoLoaded(UserTaskInfoLoaded state) {
    String? assignee;

    final taskDefkey = TicketUtils.safeGetTaskDefKey(widget.ticket);

    if (state.userTaskInfo.data != null) {
      for (final task in state.userTaskInfo.data!) {
        if (task.taskDefKey == taskDefkey) {
          if (task.lstAssigneeInfo != null && task.lstAssigneeInfo!.isNotEmpty) {
            assignee = task.lstAssigneeInfo!.first.username;
            break;
          }
        }
      }
    }

    final prevTaskDefKey = state.getPrevTaskDefKey(taskDefkey ?? '');

    setState(() {
      prevTask = prevTaskDefKey;
      _assignee = assignee;
    });
  }

  @override
  Widget build(BuildContext context) {
    developer.log('=== BUILD HANDLE TICKET SCREEN ===');
    developer.log('Is Embedded: ${widget.isEmbedded}');
    developer.log('Is Initial Load Done: $_isInitialLoadDone');
    developer.log('Process Type: $_processType');
    developer.log('Has Handled Ticket Detail: $_hasHandledTicketDetail');

    return widget.isEmbedded
        ? BlocConsumer<TicketProcessDetailBloc, TicketProcessDetailState>(
            buildWhen: (previous, current) {
              // Chỉ rebuild khi state thực sự thay đổi
              if (previous.runtimeType != current.runtimeType) {
                // Không rebuild khi đang loading
                if (current is TicketProcessDetailLoading) {
                  developer.log('❌ Skip rebuild: Loading state');
                  return false;
                }
                developer.log('✅ Rebuild: State type changed');
                return true;
              }

              // Rebuild khi processType thay đổi
              if (current is TicketProcessDetailCheckToTrinhLoaded) {
                final newProcessType = current.checkToTrinhResponseModel.data.processType;
                if (_processType != newProcessType) {
                  developer.log('✅ Rebuild: Process type changed from $_processType to $newProcessType');
                  return true;
                }
              }

              developer.log('❌ Skip rebuild: No meaningful change');
              return false;
            },
            listenWhen: (previous, current) {
              // Chỉ lắng nghe các state cần thiết và chưa xử lý
              if (current is TicketProcessDetailByIdLoaded) {
                final ticketId = current.thongTinChungModel.ticketId;
                final shouldListen = ticketId != _lastHandledTicketId;
                developer.log('Ticket ID: $ticketId');
                developer.log('Last Handled Ticket ID: $_lastHandledTicketId');
                developer.log('Should Listen: $shouldListen');
                return shouldListen;
              }

              if (current is TicketProcessDetailCheckToTrinhLoaded) {
                final newProcessType = current.checkToTrinhResponseModel.data.processType;
                final shouldListen = _processType != newProcessType;
                developer.log('Process Type: $_processType');
                developer.log('New Process Type: $newProcessType');
                developer.log('Should Listen: $shouldListen');
                return shouldListen;
              }

              // Luôn lắng nghe UserTaskInfoLoaded, SignedFileUrlLoaded và Error
              return current is UserTaskInfoLoaded ||
                  current is SignedFileUrlLoaded ||
                  current is TicketProcessDetailError;
            },
            listener: (context, state) {
              developer.log('=== LISTENER TRIGGERED ===');
              developer.log('State Type: ${state.runtimeType}');

              if (state is TicketProcessDetailCheckToTrinhLoaded) {
                _handleProcessTypeLoaded(state);
              } else if (state is TicketProcessDetailByIdLoaded) {
                _handleTicketDetailLoaded(state);
                _thongTinChungModel = state.thongTinChungModel;
              } else if (state is SignedFileUrlLoaded) {
                _handleSignedFileUrl(state);
              } else if (state is UserTaskInfoLoaded) {
                _handleUserTaskInfoLoaded(state);
              } else if (state is TicketProcessDetailLoaded) {
                _currentTicketProcessDetailModel = state.ticketProcessDetail;
                developer.log('✅ TicketProcessDetailModel updated');
              }
            },
            builder: (context, state) {
              if (!_isInitialLoadDone || state is TicketProcessDetailLoading) {
                return Center(child: AppConstraint.buildLoading(context));
              }

              return IndexedStack(
                index: _hasNavigatedToPdf ? 1 : 0,
                children: [
                  _buildScreenContent(state),
                  if (_pdfUrl != null)
                    PdfViewerPage(
                      pdfName: _pdfName ?? '',
                      pdfUrl: _pdfUrl!,
                      source: 'handle_ticket',
                      title: _pdfName ?? '',
                      ticket: widget.ticket,
                      data: _thongTinChungModel,
                      processType: _processType,
                      taskIdValue: taskId,
                      thongTinDauVao: _thongTinDauVao,
                      ticketStatus: _safeGetProperty('status') ?? '',
                      assignee: _assignee,
                      onWorkflowNodeSelected: _onWorkflowNodeSelected,
                    )
                  else
                    Container(),
                ],
              );
            },
          )
        : Scaffold(
            backgroundColor: Colors.white,
            body: BlocConsumer<TicketProcessDetailBloc, TicketProcessDetailState>(
              buildWhen: (previous, current) {
                if (previous is TicketProcessDetailLoading && current is! TicketProcessDetailLoading) {
                  return true;
                }
                if (previous.runtimeType != current.runtimeType && current is! TicketProcessDetailLoading) {
                  return true;
                }
                if (_processType == null && current is TicketProcessDetailCheckToTrinhLoaded) {
                  return true;
                }
                return false;
              },
              listenWhen: (previous, current) {
                if (current is TicketProcessDetailLoaded) {
                  return true;
                }
                return current is TicketProcessDetailCheckToTrinhLoaded ||
                    (current is TicketProcessDetailByIdLoaded && _processType != 0) ||
                    current is SignedFileUrlLoaded ||
                    current is TicketProcessDetailError ||
                    current is UserTaskInfoLoaded;
              },
              listener: (context, state) {
                if (state is TicketProcessDetailCheckToTrinhLoaded) {
                  _handleProcessTypeLoaded(state);
                } else if (_processType != 0 && state is TicketProcessDetailByIdLoaded) {
                  _handleTicketDetailLoaded(state);
                } else if (state is SignedFileUrlLoaded) {
                  _handleSignedFileUrl(state);
                } else if (state is UserTaskInfoLoaded) {
                  _handleUserTaskInfoLoaded(state);
                } else if (state is TicketProcessDetailLoaded) {
                  _currentTicketProcessDetailModel = state.ticketProcessDetail;
                  developer.log('✅ TicketProcessDetailModel updated');
                } else if (state is TicketProcessDetailByIdLoaded) {
                  _thongTinChungModel = state.thongTinChungModel;
                  developer.log('✅ ThongTinChungModel updated');
                }
                // Note: Error handling can be added here if needed
              },
              builder: (context, state) {
                if (_processType == null && state is TicketProcessDetailLoading) {
                  return GradientBackground(
                    child: Column(
                      children: [
                        Header(title: _getTicketTitle()),
                        Expanded(
                          child: Center(child: AppConstraint.buildLoading(context)),
                        ),
                      ],
                    ),
                  );
                }

                return GradientBackground(
                  child: Column(
                    children: [
                      Header(title: _getTicketTitle()),
                      Expanded(child: _buildScreenContent(state)),
                    ],
                  ),
                );
              },
            ),
            bottomNavigationBar: buildHandleTicketBottomBar(
              context,
              _thongTinChungModel ?? ThongTinChungModel(),
              _processType ?? 0,
              ticketStatus: _safeGetProperty('status') ?? '',
              ticket: widget.ticket,
              isHandleTicket: true,
              assignee: _assignee,
              onWorkflowNodeSelected: _onWorkflowNodeSelected,
              nodeType: selectedNodeType ?? '',
            ),
          );
  }
}
