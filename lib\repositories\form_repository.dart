import 'dart:convert';
import 'dart:io';
import 'dart:developer' as developer;
import 'package:eapprove/models/form/all_submission_type_response.dart';
import 'package:eapprove/models/form/check_to_trinh_response_model.dart';
import 'package:eapprove/models/form/confirm_submit_response_model.dart';
import 'package:eapprove/models/form/tao_to_trinh_request_body.dart';
import 'package:eapprove/models/form/tao_to_trinh_response_model.dart';
import 'package:eapprove/models/form/currency_request_model.dart';
import 'package:eapprove/models/form/currency_response_model.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';
import 'package:eapprove/models/form/form_request_body.dart';
import 'package:eapprove/models/form/form_xml_response_model.dart';
import 'package:eapprove/models/form/individual_info_request_model.dart';
import 'package:eapprove/models/form/individual_info_response_model.dart';
import 'package:eapprove/models/form/md_service_request_model.dart';
import 'package:eapprove/models/form/md_service_response.dart';
import 'package:eapprove/models/form/title_by_user_model.dart';
import 'package:eapprove/models/form/user_info_model.dart';
import 'package:eapprove/models/form/default_signature_response_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:flutter/cupertino.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:eapprove/repositories/account_repository.dart';

import 'package:eapprove/models/form/bpmProcInst_create_request_model.dart';
import 'package:eapprove/models/form/bpmProcInst_create_response_model.dart';
import 'package:eapprove/models/form/call_service_request_model.dart';
import 'package:eapprove/models/form/list_base_url_response.dart';
import 'package:eapprove/models/form/service_by_id_with_submission.dart';
import 'package:eapprove/models/form/submitter_info_response_model.dart';
import 'package:eapprove/models/form/upload_file_response_model.dart';
import 'package:eapprove/models/form/account_multi_chart_response_model.dart';
import 'package:eapprove/models/form/priority_management_request_model.dart';
import 'package:eapprove/models/form/priority_management_response_model.dart';

class FormRepository {
  final ApiService _apiService;

  FormRepository({required ApiService apiService}) : _apiService = apiService;

  Future<BpmnFormXMLResponse> getFormXmlData(
      {required String procDefId}) async {
    try {
      final response =
          await _apiService.get('business-process/bpmProcInst/$procDefId/xml');
      if (response.statusCode == 200) {
        final dynamic jsonData = json.decode(response.body);
        final BpmnFormXMLResponse formXMLResponse =
            BpmnFormXMLResponse.fromJson(jsonData);
        return formXMLResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data - getFormXmlData: $e');
    }
  }

  Future<FormResponse> getFormTemplate(
      {required FormRequestBody requestBody}) async {
    try {
      final response = await _apiService.post(
          'business-process/template-manage/getTemplateHistory',
          requestBody.toJson());

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return FormResponse.fromJson(jsonData);
      } else {
        throw Exception(
            'Failed to load form template, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching form data: $e');
    }
  }

  String formatFormResponse(FormResponse response) {
    final JsonEncoder encoder = JsonEncoder.withIndent('  ');

    try {
      final Map<String, dynamic> responseMap = response.toJson();

      if (responseMap.containsKey('data') &&
          responseMap['data'] is Map &&
          responseMap['data'].containsKey('template')) {
        var template = responseMap['data']['template'];
        if (template is String) {
          try {
            responseMap['data']['template'] = json.decode(template);
          } catch (e) {
            // If parsing fails, leave as is
          }
        }
      }
      return encoder.convert(responseMap);
    } catch (e) {
      return response.toString();
    }
  }

  Future<IndividualInfoResponse> getIndividualInfo(
      {required IndividualInfoRequestModel requestBody}) async {
    try {
      final response = await _apiService.post(
          'customer/chart-node/getLoadTemplate', requestBody.toJson());
      developer.log('getIndividualInfo: ${response.body.length}', name: 'getIndividualInfo');
      if (response.statusCode == 200) {
        final dynamic jsonData = json.decode(response.body);
        // Handle empty array response
        if (jsonData is List && jsonData.isEmpty) {
          return IndividualInfoResponse(
            code: 200,
            message: 'Success',
            data: [],
          );
        }
        return IndividualInfoResponse.fromJson(jsonData);
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data getIndividualInfo: $e');
    }
  }

  Future<dynamic> getMdService(
      {required MdServiceRequestBody requestBody}) async {
    try {
      final response = await _apiService.post(
          'md-service/masterData/getLoadTemplate', requestBody.toJson());
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data getMdService: $e');
    }
  }

  Future<CurrencyResponseModel> getCurrency(
      {required CurrencyRequestModel requestBody}) async {
    try {
      final response = await _apiService.post(
          'business-process/currency/get-currencys', requestBody.toJson());
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final CurrencyResponseModel currencyResponse =
            CurrencyResponseModel.fromJson(jsonData);
        return currencyResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data getCurrency: $e');
    }
  }

  Future<SubmitterInfoResponseModel> getBpmProcdefGetAll(
      {required int processId}) async {
    try {
      if (processId <= 0) {
        throw Exception(
            'Invalid processId: $processId. ProcessId must be a positive integer.');
      }

      final response =
          await _apiService.get('business-process/bpmProcdefGetAll/$processId');

      if (response.statusCode == 200) {
        final dynamic jsonData = json.decode(response.body);

        if (jsonData == null) {
          throw Exception('Empty response received for processId: $processId');
        }

        if (jsonData is! Map<String, dynamic>) {
          throw Exception(
              'Unexpected response format: ${jsonData.runtimeType}');
        }

        final code = jsonData['code'];
        if (code == null) {
          throw Exception('Missing code in response');
        }

        final int codeValue;
        if (code is int) {
          codeValue = code;
        } else if (code is String) {
          codeValue = int.tryParse(code) ?? 0;
        } else {
          throw Exception('Invalid code type: ${code.runtimeType}');
        }

        final Map<String, dynamic> safeJsonData =
            Map<String, dynamic>.from(jsonData);
        safeJsonData['code'] = codeValue;

        final SubmitterInfoResponseModel submitterInfoResponse =
            SubmitterInfoResponseModel.fromJson(safeJsonData);
        return submitterInfoResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data getBpmProcdefGetAll: $e');
    }
  }

  Future<ConfirmSubmitResponseModel> getApprovalVinaphaco(
      {required String procDefId}) async {
    try {
      final response = await _apiService
          .get('business-process/bpmProcdef/approval/$procDefId');

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> jsonData = json.decode(response.body);
          final ConfirmSubmitResponseModel approvalResponse =
              ConfirmSubmitResponseModel.fromJson(jsonData);
          return approvalResponse;
        } catch (parseError) {
          throw Exception('Error parsing response data: $parseError');
        }
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data - getApprovalVinaphaco: $e');
    }
  }

  Future<BpmProcInstCreateResponseModel> createBpmProcInst(
      {required String procDefId,
      required int ticketId,
      required BpmProcInstCreateRequestModel requestBody}) async {
    try {
      final response = await _apiService.post(
          'business-process/bpmProcInst/create/$procDefId/0',
          requestBody.toJson());

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final BpmProcInstCreateResponseModel createFormResponse =
            BpmProcInstCreateResponseModel.fromJson(jsonData);
        return createFormResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data - createBpmProcInst: $e');
    }
  }

  Future<TaoToTrinhResponseModel> taoToTrinh(
      {required TaoToTrinhRequestBody requestBody}) async {
    try {
      developer.log('taoToTrinh: ${requestBody.toJson()}', name: 'FormRepository');
      final response = await _apiService.post(
          'business-process/print-sign-zone/type-template',
          requestBody.toJson());
          
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final TaoToTrinhResponseModel createFormResponse =
            TaoToTrinhResponseModel.fromJson(jsonData);
        return createFormResponse;
      } else {
        throw Exception(
            'Lỗi khi tạo form, mã trạng thái: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Lỗi khi tạo form: $e');
    }
  }

  Future<UploadSingleFileResponseModel> uploadFile(
      Map<String, dynamic> fileData,
      {Map<String, dynamic>? extraData}) async {
    try {
      final response = await _apiService.uploadSingleFile(
          'business-process/file/upload?type=ticket', fileData,
          extraData: extraData);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final UploadSingleFileResponseModel uploadFileResponse =
            UploadSingleFileResponseModel.fromJson(jsonData);
        return uploadFileResponse;
      } else {
        throw Exception(
            'Failed to upload file, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error uploading file: $e');
    }
  }

  Future<UploadMultiFileResponseModel> uploadMultiFile(
      List<Map<String, dynamic>> files,
      {Map<String, dynamic>? extraData}) async {
    try {
      final response = await _apiService.uploadFiles(
          'business-process/file/upload-multi?type=ticket', files,
          extraData: extraData);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final UploadMultiFileResponseModel uploadFileResponse =
            UploadMultiFileResponseModel.fromJson(jsonData);
        return uploadFileResponse;
      } else {
        throw Exception(
            'Failed to upload files, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error uploading files: $e');
    }
  }

  Future<dynamic> getCallService(
      {required CallServiceRequestModel requestBody, String? baseURL}) async {
    try {
      final response = await _apiService.post(
        'business-process/bpm-service/call-service',
        requestBody.toJson(),
        customBaseUrl: baseURL,
      );

      if (response.statusCode == 200) {
        if (response.body.isEmpty) {
          throw Exception('Empty response received from server ');
        }

        try {
          final dynamic jsonData = json.decode(response.body);
          return jsonData;
        } catch (parseError) {
          throw Exception('Invalid JSON response: $parseError');
        }
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}, response: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data: $e');
    }
  }

  Future<List<ListBaseUrlResponseModel>> getListBaseUrl() async {
    try {
      final response = await _apiService
          .get('business-process/api-management/getListBaseUrl');

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        if (jsonData.isNotEmpty) {
          final List<ListBaseUrlResponseModel> listBaseUrlResponses = jsonData
              .map((item) => ListBaseUrlResponseModel.fromJson(item))
              .toList();

          return listBaseUrlResponses;
        } else {
          throw Exception('No base URL data found');
        }
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data - getListBaseUrl: $e');
    }
  }

  Future<dynamic> getDataFromAPILink({
    required final Map<String, dynamic> requestBody,
    required String endpoint,
    required String apiURL,
    String? requestKey,
  }) async {
    try {
      final Box authBox = Hive.box('authentication');
      String? username = authBox.get('username');

      if (username == null) {
        try {
          final accountRepository = AccountRepository(apiService: _apiService);
          final userInfo = await accountRepository.getUserInfoByToken();
          if (userInfo.meta.code == 200) {
            username = userInfo.data?.username;
            await authBox.put('username', username);
          }
        } catch (e) {
          // Handle error silently
        }
      }

      final response = await _apiService.post(
        endpoint,
        requestBody,
        customBaseUrl: apiURL,
        headers: {
          "username": username ?? '',
          "Content-Type": "application/json; charset=utf-8",
          "Accept": "application/json; charset=utf-8"
        },
      );
      if (response.statusCode == 200) {
        final String decodedBody = utf8.decode(response.bodyBytes);
        final Map<String, dynamic> jsonData = json.decode(decodedBody);
        return jsonData;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data: $e');
    }
  }

  Future<AllSubmissionTypeResponse> getAllSubmissionType() async {
    try {
      final response = await _apiService
          .get('business-process/submissionType/getAllSubmissionType');
      developer.log('getAllSubmissionType: ${response.body}', name: 'FormRepository');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final AllSubmissionTypeResponse allSubmissionTypeResponse =
            AllSubmissionTypeResponse.fromJson(jsonData);
        return allSubmissionTypeResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data: $e');
    }
  }

  Future<ServiceByIdWithPermissionResponse> getServiceByIdWithPermission(
      {required int id}) async {
    try {
      final response = await _apiService.get(
          'business-process/service-pack/getServiceByIdWithPermission?id=$id');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final ServiceByIdWithPermissionResponse serviceResponse =
            ServiceByIdWithPermissionResponse.fromJson(jsonData);
        return serviceResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data: $e');
    }
  }

  Future<TitleByUserResponse> getFinalTitleByListUser(
      {required List<String> usernames}) async {
    try {
      final response = await _apiService.post(
          'customer/userTitle/getFinalTitleByListUser', usernames);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final TitleByUserResponse userInfoResponse =
            TitleByUserResponse.fromJson(jsonData);
        return userInfoResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data: $e');
    }
  }

  Future<String> getDefaultSignature({required String username}) async {
    try {
      final response = await _apiService
          .get('customer/v1/signature/getDefaultSignature/$username');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final String signatureResponse = jsonData['data'];
        return signatureResponse;
      } else {
        throw Exception(
            'Failed to load default signature, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching default signature: $e');
    }
  }

  Future<UserInfoByUserNameResponse> getUserInfoByUserName(
      {required String username}) async {
    try {
      final response = await _apiService
          .get('customer/userInfo/getByUsername?username=$username');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final UserInfoByUserNameResponse userInfoResponse =
            UserInfoByUserNameResponse.fromJson(jsonData);
        return userInfoResponse;
      } else {
        throw Exception(
            'Failed to load user info, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching user info by username: $e');
    }
  }

  Future<CheckToTrinhResponseModel> checkToTrinh(
      {required String procDefId,
      required int serviceId,
      required String taskDefKey}) async {
    try {
      final response = await _apiService.get(
          'business-process/bpmProcdef/check-type/$serviceId/$procDefId/$taskDefKey');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final CheckToTrinhResponseModel checkToTrinhResponse =
            CheckToTrinhResponseModel.fromJson(jsonData);
        return checkToTrinhResponse;
      } else {
        throw Exception(
            'Failed to load BPMN data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching BPMN data: $e');
    }
  }

  Future<AccountMultiChartResponse> getAccountMultiChart({
    required String username,
    required String chartIds,
  }) async {
    try {
      final response = await _apiService.get(
        'customer/chart/getAccountMultiChart?username=$username&chartIds=$chartIds',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final AccountMultiChartResponse accountMultiChartResponse =
            AccountMultiChartResponse.fromJson(jsonData);
        return accountMultiChartResponse;
      } else {
        throw Exception(
            'Failed to load account multi chart data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching account multi chart data: $e');
    }
  }

  Future<PriorityManagementResponseModel> getPriorityManagement({
    required PriorityManagementRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/priority-management/search',
        requestBody.toJson(),
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return PriorityManagementResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load priority management data, status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching priority management data: $e');
    }
  }
}
