import 'dart:convert';

import 'package:eapprove/models/feature_respone_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:logger/logger.dart';

class FeatureRepository {
  final Logger _logger = LoggerConfig.logger;
  final ApiService _apiService;

  FeatureRepository({required ApiService apiService}) : _apiService = apiService;

  Future<Feature> getFeatureByTenant() async {
    try {
      final response = await _apiService.get(
        'subscription/feature/getFeatureByTenant',
      );

      _logger.d('Response status Feature API: ${response.statusCode}');
      String responseBody = utf8.decode(response.bodyBytes);
      _logger.d('Response Body: $responseBody');

      if (response.statusCode != 200) {
        _logger.e('API lỗi - Mã trạng thái: ${response.statusCode}');
        if (response.statusCode == 401) {
          throw Exception('Lỗi 401: Phiên đăng nhập hết hạn, vui lòng đăng nhập lại.');
        } else if (response.statusCode == 500) {
          throw Exception('Lỗi 500: Lỗi server, vui lòng thử lại sau.');
        } else {
          throw Exception('API trả về mã lỗi: ${response.statusCode}');
        }
      }

      final Map<String, dynamic> data = jsonDecode(responseBody);
      return Feature.fromJson(data);
    } catch (e, stackTrace) {
      _logger.e('⛔ Lỗi khi fetch feature data: $e', error: e, stackTrace: stackTrace);
      throw Exception('Lỗi khi gọi API: $e');
    }
  }
}
