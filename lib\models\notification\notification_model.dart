import 'dart:developer';

class NotificationModel {
  final String? code;
  final String? message;
  final Data? data;

  NotificationModel({this.code, this.message, this.data});

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    try {
      log('Parsing NotificationModel from JSON: $json');
      return NotificationModel(
        code: json['code'],
        message: json['message'],
        data: json['data'] != null ? Data.fromJson(json['data']) : null,
      );
    } catch (e) {
      log('Error parsing NotificationModel: $e');
      return NotificationModel();
    }
  }
}

class Data {
  final String? object;
  final List<NotificationData>? data;

  Data({this.object, this.data});

  factory Data.fromJson(Map<String, dynamic> json) {
    try {
      log('Parsing Data from JSON: $json');
      return Data(
        object: json['object'],
        data: json['data'] is Map
            ? (json['data']['EAPP'] as List?)?.map((item) => NotificationData.from<PERSON>son(item)).toList()
            : null,
      );
    } catch (e) {
      log('Error parsing Data: $e');
      return Data();
    }
  }
}

class NotificationData {
  final String? id;
  final int? receiveTime;
  final String? receiver;
  bool? seen;
  final String? type;
  final Payload? payload;
  final ExtraInfo? extraInfo;
  final String? system;

  NotificationData({
    this.id,
    this.receiveTime,
    this.receiver,
    this.seen,
    this.type,
    this.payload,
    this.extraInfo,
    this.system,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    try {
      log('Parsing NotificationData from JSON: \$json');
      return NotificationData(
        id: json['id'],
        receiveTime: json['receiveTime'],
        receiver: json['receiver'],
        seen: json['seen'],
        type: json['type'],
        payload: json['payload'] != null ? Payload.fromJson(json['payload']) : null,
        extraInfo: json['extraInfo'] != null ? ExtraInfo.fromJson(json['extraInfo']) : null,
        system: json['system'],
      );
    } catch (e) {
      log('Error parsing NotificationData: $e');
      return NotificationData();
    }
  }
}

class Payload {
  final String? title;
  final String? body;

  Payload({this.title, this.body});

  factory Payload.fromJson(Map<String, dynamic> json) {
    try {
      log('Parsing Payload from JSON: $json');
      return Payload(
        title: json['title'],
        body: json['body'],
      );
    } catch (e) {
      log('Error parsing Payload: $e');
      return Payload();
    }
  }
}

class ExtraInfo {
  final bool? isWeb;
  final bool? isPersistence;
  final String? urlTicket;

  ExtraInfo({this.isWeb, this.isPersistence, this.urlTicket});

  factory ExtraInfo.fromJson(Map<String, dynamic> json) {
    try {
      log('Parsing ExtraInfo from JSON: $json');
      return ExtraInfo(
        isWeb: json['isWeb'],
        isPersistence: json['isPersistence'],
         urlTicket: json['urlTicket'],
      );
    } catch (e) {
      log('Error parsing ExtraInfo: $e');
      return ExtraInfo();
    }
  }
}
