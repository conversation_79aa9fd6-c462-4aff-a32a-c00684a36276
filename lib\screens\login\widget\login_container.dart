import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoginContainer extends StatelessWidget {
  final Widget? child;
  const LoginContainer({
    super.key,
    this.child,
  });

  @override
  Widget build(BuildContext context) => Container(
        margin: EdgeInsets.only(top: 120.h),
        width: 343.w,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(255, 255, 255, 0.5),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: child,
      );
}
