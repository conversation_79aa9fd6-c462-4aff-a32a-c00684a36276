import 'package:equatable/equatable.dart';

abstract class SignPrintEvent extends Equatable {
  const SignPrintEvent();

  @override
  List<Object?> get props => [];
}

class LoadSignPrintDataEvent extends SignPrintEvent {
  final int bpmTpSignZone;
  final String procInstId;

  const LoadSignPrintDataEvent({
    required this.bpmTpSignZone,
    required this.procInstId,
  });

  @override
  List<Object?> get props => [bpmTpSignZone, procInstId];
} 