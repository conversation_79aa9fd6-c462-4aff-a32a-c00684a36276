import 'dart:async';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/models/authentication/login_model.dart';
import 'package:eapprove/models/meta.dart';
import 'package:eapprove/repositories/authentication_repository.dart';
import 'package:eapprove/services/secure_storage_service.dart';
import 'package:eapprove/services/token.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:logger/logger.dart';
import 'package:eapprove/models/authentication/token_model.dart' as auth;
import 'package:hive_flutter/hive_flutter.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  final AuthenticationRepository _authenticationRepository;
  final TokenManager _tokenManager;
  final Logger _logger = LoggerConfig.logger;
  final SecureStorageService _secureStorage = SecureStorageService();

  static const String _authPrefix = 'auth_';
  static const String _usernameKey = '${_authPrefix}username';
  static const String _rememberMeKey = '${_authPrefix}remember_me';
  static const String _sessionStateKey = '${_authPrefix}session_state';
  static const String _userEmailKey = '${_authPrefix}user_email';
  static const String _is2FAKey = '${_authPrefix}is_2fa';

  final _authenticationStatusController =
      StreamController<AuthenticationStatus>.broadcast();
  Stream<AuthenticationStatus> get authenticationStatus =>
      _authenticationStatusController.stream;
  AuthenticationStatus _currentStatus = AuthenticationStatus.unknown;

  AuthenticationBloc({
    required AuthenticationRepository authenticationRepository,
    required TokenManager tokenManager,
  })  : _authenticationRepository = authenticationRepository,
        _tokenManager = tokenManager,
        super(AuthenticationInitial()) {
    on<LoginSubmitted>(_onLoginSubmitted);
    on<LoginReset>(_onLoginReset);
    on<LoadSavedLogin>(_onLoadSavedLogin);
    on<LogoutRequested>(_onLogoutRequested);
    // on<ResetPassSubmitted>(_onResetPassSubmitted);
    // on<FirstTimeLoginSubmitted>(_onFirstTimeLoginSubmitted);
    // on<VerifyOtpSubmitted>(_onVerifyOtpSubmitted);
    // on<ResendOtpRequested>(_onResendOtpRequested);
    on<GetTokenRequested>(_onGetTokenRequested);
    on<RefreshTokenRequested>(_onRefreshTokenRequested);
    on<CheckAuthenticationStatus>(_onCheckAuthenticationStatus);
    on<SaveLoginInfo>(_onSaveLoginInfo);
    on<SaveSwitchAccountToken>(_onSaveSwitchAccountToken);

    _tokenManager.tokenStatus.listen((status) {
      if (status == TokenStatus.invalid) {
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
      } else if (status == TokenStatus.valid) {
        _updateAuthenticationStatus(AuthenticationStatus.authenticated);
      }
    });
  }

  void _updateAuthenticationStatus(AuthenticationStatus status) {
    if (_currentStatus != status) {
      _currentStatus = status;
      _authenticationStatusController.add(status);
    }
  }

  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthenticationLoading());

    try {
      final loginModel = await _authenticationRepository.login(
        event.username,
        event.password,
      );

      if (loginModel.meta.code == 200) {
        await _secureStorage.writeString(_usernameKey, event.username);
        await _secureStorage.writeBool(_rememberMeKey, event.rememberMe);

        if (!Hive.isBoxOpen("authentication")) {
          await Hive.openBox("authentication");
        }
        final authBox = Hive.box("authentication");
        await authBox.put("username", event.username);

        if (await _secureStorage.containsKey('${_authPrefix}password')) {
          await _secureStorage.delete('${_authPrefix}password');
        }

        if (loginModel.data?.sessionState != null &&
            loginModel.data!.sessionState!.isNotEmpty) {
          await _secureStorage.writeString(
              _sessionStateKey, loginModel.data!.sessionState!);
        }

        if (loginModel.data?.email != null &&
            loginModel.data!.email.isNotEmpty) {
          await _secureStorage.writeString(
              _userEmailKey, loginModel.data!.email);
        }

        if (loginModel.data != null) {
          await _saveIs2FAStatus(loginModel.data?.is2FA ?? false);
        }

        // if (loginModel.data?.hash != null) {
        //   emit(FirstTimeLoginRequired(
        //     username: event.username,
        //     sessionState: loginModel.data?.sessionState ?? '',
        //     hash: loginModel.data?.hash ?? '',
        //     is2FA: loginModel.data?.is2FA ?? false,
        //     email: loginModel.data?.email,
        //   ));
        // } else if (loginModel.data?.is2FA == true && loginModel.data?.sessionState != null) {
        //   emit(OtpVerificationRequired(
        //     username: event.username,
        //     sessionState: loginModel.data?.sessionState ?? '',
        //     hash: null,
        //     email: loginModel.data?.email,
        //   ));
        // } else
        if (loginModel.data?.code != null) {
          add(GetTokenRequested(
            code: loginModel.data!.code!,
            is2FA: false,
          ));
        } else {
          emit(LoginSuccess(loginModel));
          _updateAuthenticationStatus(AuthenticationStatus.authenticated);
        }
      } else {
        _handleLoginError(loginModel, emit);
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Login bloc error',
        error: e,
        stackTrace: stackTrace,
      );
      final errorMessage = 'Lỗi đăng nhập: ${e.toString()}';
      SnackbarCore.error(errorMessage);
      emit(AuthenticationFailure(errorMessage));
      _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
    }
  }

  Future<void> _saveIs2FAStatus(bool is2FA) async {
    try {
      await _secureStorage.writeBool(_is2FAKey, is2FA);
      _logger.i('2FA status saved to secure storage: $is2FA');
    } catch (e, stackTrace) {
      _logger.e(
        'Error saving 2FA status: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  Future<bool> isUserRequire2FA() async {
    try {
      return await _secureStorage.readBool(_is2FAKey, defaultValue: false);
    } catch (e, stackTrace) {
      _logger.e(
        'Error checking 2FA requirement: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  Future<void> saveSessionState(String sessionState) async {
    try {
      await _secureStorage.writeString(_sessionStateKey, sessionState);
      _logger.i('Session state saved to secure storage: $sessionState');
    } catch (e, stackTrace) {
      _logger.e(
        'Error saving session state: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  Future<String?> getSessionState() async {
    try {
      return await _secureStorage.readString(_sessionStateKey);
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting session state: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<void> _onGetTokenRequested(
    GetTokenRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthenticationLoading());

    try {
      _logger
          .d('Getting token with code: ${event.code}, is2FA: ${event.is2FA}');

      final tokenResponse = await _authenticationRepository.getToken(
        event.code,
        is2FA: event.is2FA,
      );

      _logger.d('Token response code: ${tokenResponse.meta.code}');

      if (tokenResponse.meta.code == 200 && tokenResponse.data != null) {
        _logger.d('Token retrieved successfully, saving token data');
        await _tokenManager.saveTokenData(tokenResponse.data!);

        await Future.delayed(const Duration(milliseconds: 500));

        emit(TokenSuccess(tokenResponse));
        _updateAuthenticationStatus(AuthenticationStatus.authenticated);
      } else {
        _logger.e('Failed to get token: ${tokenResponse.meta.message}');
        emit(AuthenticationFailure(tokenResponse.meta.message));
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Get token error',
        error: e,
        stackTrace: stackTrace,
      );
      final errorMessage = 'Lỗi lấy token: ${e.toString()}';
      emit(AuthenticationFailure(errorMessage));
      _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
    }
  }

  Future<void> _onRefreshTokenRequested(
    RefreshTokenRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      final savedTokenData = await _tokenManager.getTokenData();

      if (savedTokenData == null) {
        emit(AuthenticationInitial());
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
        return;
      }

      final tokenResponse = await _authenticationRepository.refreshToken(
        savedTokenData['refreshToken'] as String,
      );

      if (tokenResponse.meta.code == 200 && tokenResponse.data != null) {
        await _tokenManager.saveTokenData(tokenResponse.data!);
        emit(TokenSuccess(tokenResponse));
        _updateAuthenticationStatus(AuthenticationStatus.authenticated);
      } else {
        await _tokenManager.clearTokenData();
        emit(AuthenticationInitial());
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Refresh token error',
        error: e,
        stackTrace: stackTrace,
      );
      await _tokenManager.clearTokenData();
      emit(AuthenticationInitial());
      _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
    }
  }

  Future<void> _onCheckAuthenticationStatus(
    CheckAuthenticationStatus event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthenticationLoading());

    try {
      final savedTokenData = await _tokenManager.getTokenData();

      if (savedTokenData == null) {
        emit(AuthenticationInitial());
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
        return;
      }

      final now = DateTime.now();
      final tokenExpiry = DateTime.fromMillisecondsSinceEpoch(
        savedTokenData['expiryTime'] as int,
      );
      final refreshTokenExpiry = DateTime.fromMillisecondsSinceEpoch(
        savedTokenData['refreshExpiryTime'] as int,
      );

      if (now.isAfter(refreshTokenExpiry)) {
        await _tokenManager.clearTokenData();
        emit(AuthenticationInitial());
        _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
      } else if (now.isAfter(tokenExpiry)) {
        add(const RefreshTokenRequested());
      } else {
        final tokenData = auth.TokenData(
          accessToken: savedTokenData['accessToken'] as String,
          expiresIn: savedTokenData['expiresIn'] as int,
          refreshExpiresIn: savedTokenData['refreshExpiresIn'] as int,
          refreshToken: savedTokenData['refreshToken'] as String,
          tokenType: savedTokenData['tokenType'] as String,
          notBeforePolicy: savedTokenData['notBeforePolicy'] as int,
          sessionState: savedTokenData['sessionState'] as String,
          scope: savedTokenData['scope'] as String,
        );

        final tokenResponse = auth.TokenResponse(
          meta: Meta(code: 200, message: "Token valid"),
          data: tokenData,
        );
        emit(TokenSuccess(tokenResponse));
        _updateAuthenticationStatus(AuthenticationStatus.authenticated);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Check authentication status error',
        error: e,
        stackTrace: stackTrace,
      );
      emit(AuthenticationInitial());
      _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
    }
  }

  Future<void> _onSaveSwitchAccountToken(
    SaveSwitchAccountToken event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthenticationLoading());

    try {
      // Convert to the correct TokenData type
      final authTokenData = auth.TokenData(
          accessToken: event.tokenData.accessToken,
          expiresIn: event.tokenData.expiresIn,
          refreshExpiresIn: event.tokenData.refreshExpiresIn,
          refreshToken: event.tokenData.refreshToken,
          tokenType: event.tokenData.tokenType,
          notBeforePolicy: event.tokenData.notBeforePolicy,
          sessionState: event.tokenData.sessionState,
          scope: event.tokenData.scope);

      await _tokenManager.saveTokenData(authTokenData);

      final tokenResponse = auth.TokenResponse(
        meta: Meta(code: 200, message: "Access token from account switch"),
        data: authTokenData,
      );

      emit(TokenSuccess(tokenResponse));
      _updateAuthenticationStatus(AuthenticationStatus.authenticated);
      _logger.i('Token saved successfully from account switch');
    } catch (e, stackTrace) {
      _logger.e(
        'Save switch account token error',
        error: e,
        stackTrace: stackTrace,
      );
      emit(AuthenticationFailure('Error saving token data: ${e.toString()}'));
    }
  }

  Future<String?> getCurrentToken() async {
    return await _tokenManager.getFormattedToken();
  }

  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      await _tokenManager.clearTokenData();

      await _secureStorage.delete(_usernameKey);
      await _secureStorage.delete('${_authPrefix}password');
      await _secureStorage.delete(_userEmailKey);
      await _secureStorage.writeBool(_rememberMeKey, false);

      // Clear username from Hive box
      if (Hive.isBoxOpen("authentication")) {
        final authBox = Hive.box("authentication");
        await authBox.delete("username");
      }

      await _secureStorage.deleteAll();

      emit(AuthenticationInitial());
      _updateAuthenticationStatus(AuthenticationStatus.unauthenticated);
    } catch (e, stackTrace) {
      _logger.e(
        'Logout error',
        error: e,
        stackTrace: stackTrace,
      );
      emit(AuthenticationFailure('Logout error: ${e.toString()}'));
    }
  }

  Future<void> _onLoadSavedLogin(
    LoadSavedLogin event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      final savedInfo = await getSavedLoginInfo();
      emit(SavedLoginLoaded(
        username: savedInfo['username'] as String,
        password: savedInfo['password'] as String,
        rememberMe: savedInfo['rememberMe'] as bool,
      ));
    } catch (e, stackTrace) {
      _logger.e(
        'Error loading saved login info: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      emit(const SavedLoginLoaded(
        username: '',
        password: '',
        rememberMe: false,
      ));
    }
  }

  void _onLoginReset(
    LoginReset event,
    Emitter<AuthenticationState> emit,
  ) {
    emit(AuthenticationInitial());
    _updateAuthenticationStatus(AuthenticationStatus.unknown);
  }

  Future<void> _saveLoginInfo(String username, bool rememberMe,
      {String? email}) async {
    try {
      await _secureStorage.writeString(_usernameKey, username);
      await _secureStorage.writeBool(_rememberMeKey, rememberMe);

      if (await _secureStorage.containsKey('${_authPrefix}password')) {
        await _secureStorage.delete('${_authPrefix}password');
      }

      if (email != null && email.isNotEmpty) {
        await _secureStorage.writeString(_userEmailKey, email);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error saving login info: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  Future<void> _onSaveLoginInfo(
    SaveLoginInfo event,
    Emitter<AuthenticationState> emit,
  ) async {
    await _saveLoginInfo(event.username, event.rememberMe);
  }

  Future<Map<String, dynamic>> getSavedLoginInfo() async {
    try {
      final username = await _secureStorage.readString(_usernameKey) ?? '';
      final password =
          await _secureStorage.readString('${_authPrefix}password') ?? '';
      final rememberMe =
          await _secureStorage.readBool(_rememberMeKey, defaultValue: false);
      final email = await _secureStorage.readString(_userEmailKey) ?? '';

      return {
        'username': username,
        'password': password,
        'rememberMe': rememberMe,
        'email': email,
      };
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting saved login info: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return {
        'username': '',
        'password': '',
        'rememberMe': false,
        'email': '',
      };
    }
  }

  // Future<void> _onResetPassSubmitted(
  //   ResetPassSubmitted event,
  //   Emitter<AuthenticationState> emit,
  // ) async {
  //   emit(AuthenticationLoading());

  //   try {
  //     final resetPassResponse = await _authenticationRepository.resetPassword(
  //       event.username,
  //     );

  //     if (resetPassResponse.meta.code == 200) {
  //       emit(AuthenticationInitial());
  //       SnackbarCore.success(resetPassResponse.meta.message);
  //     } else {
  //       _logger.e(resetPassResponse.meta.message);
  //       emit(AuthenticationFailure(resetPassResponse.meta.message));
  //     }
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'Reset password bloc error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     final errorMessage = 'Lỗi đặt lại mật khẩu: ${e.toString()}';
  //     SnackbarCore.error(errorMessage);
  //     emit(AuthenticationFailure(errorMessage));
  //   }
  // }

  // Future<void> _onFirstTimeLoginSubmitted(
  //   FirstTimeLoginSubmitted event,
  //   Emitter<AuthenticationState> emit,
  // ) async {
  //   emit(AuthenticationLoading());

  //   try {
  //     final response = await _authenticationRepository.changePassFirstLogin(
  //       event.username,
  //       event.password,
  //       event.rePassword,
  //       event.hash,
  //     );

  //     if (response.meta.code == 200) {
  //       SnackbarCore.success('Đổi mật khẩu thành công, vui lòng đăng nhập lại');
  //       emit(FirstTimeLoginSuccess(response));

  //       if (response.data?.code != null) {
  //         add(GetTokenRequested(
  //           code: response.data!.code!,
  //           is2FA: response.data!.is2FA ?? false,
  //         ));
  //       }
  //     } else {
  //       emit(AuthenticationFailure(response.meta.message));
  //     }
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'First time login error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     final errorMessage = 'Lỗi đổi mật khẩu: ${e.toString()}';
  //     emit(AuthenticationFailure(errorMessage));
  //   }
  // }

  // Future<void> _onVerifyOtpSubmitted(
  //   VerifyOtpSubmitted event,
  //   Emitter<AuthenticationState> emit,
  // ) async {
  //   emit(AuthenticationLoading());

  //   try {
  //     String sessionStateToUse = event.sessionState;
  //     _logger.d(
  //         'Verifying OTP: ${event.otp} with sessionState: $sessionStateToUse');

  //     final response = await _authenticationRepository.verifyOtp(
  //       event.otp,
  //       sessionStateToUse,
  //     );

  //     _logger.d(
  //         'OTP verification response: ${response.meta.code} - ${response.meta.message}');

  //     if (response.meta.code == 200) {
  //       emit(OtpVerificationSuccess(response));

  //       if (response.data?.is2FA == true) {
  //         if (response.data?.sessionState != null) {
  //           _logger.d(
  //               '2FA flow: Using sessionState for token: ${response.data!.sessionState}');
  //           add(GetTokenRequested(
  //             code: response.data!.sessionState!,
  //             is2FA: true,
  //           ));
  //         } else {
  //           _logger.e('2FA flow: No sessionState found in response');
  //           emit(AuthenticationFailure(
  //               'Không tìm thấy session state sau khi xác thực OTP'));
  //         }
  //       } else {
  //         if (response.data?.code != null) {
  //           _logger.d(
  //               'Non-2FA flow: Using code for token: ${response.data!.code}');
  //           add(GetTokenRequested(
  //             code: response.data!.code!,
  //             is2FA: false,
  //           ));
  //         } else {
  //           _logger.e('Non-2FA flow: No code found in response');
  //           emit(AuthenticationFailure(
  //               'Không tìm thấy code sau khi xác thực OTP'));
  //         }
  //       }
  //     } else {
  //       _logger.e('OTP verification failed: ${response.meta.message}');
  //       emit(AuthenticationFailure(response.meta.message));
  //     }
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'OTP verification bloc error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     final errorMessage = 'Lỗi xác thực OTP: ${e.toString()}';
  //     emit(AuthenticationFailure(errorMessage));
  //   }
  // }

  // Future<void> _onResendOtpRequested(
  //   ResendOtpRequested event,
  //   Emitter<AuthenticationState> emit,
  // ) async {
  //   emit(AuthenticationLoading());

  //   try {
  //     final response = await _authenticationRepository.resendOtp(
  //       event.sessionState,
  //       hash: event.hash,
  //     );

  //     if (response.meta.code == 200) {
  //       emit(const OtpResendSuccess());
  //       SnackbarCore.success('Đã gửi lại mã OTP');
  //     } else {
  //       emit(AuthenticationFailure(response.meta.message));
  //     }
  //   } catch (e, stackTrace) {
  //     _logger.e(
  //       'Resend OTP bloc error',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     final errorMessage = 'Lỗi gửi lại OTP: ${e.toString()}';
  //     emit(AuthenticationFailure(errorMessage));
  //   }
  // }

  void _handleLoginError(
      LoginResponse loginResponse, Emitter<AuthenticationState> emit) {
    String errorMessage = "";
    final fieldErrors = <String, String?>{};

    switch (loginResponse.meta.code) {
      case 20007001:
        errorMessage = 'Client_Id hoặc Redirect_Uri không tồn tại';
        break;
      case 20007002:
        errorMessage = "";
        fieldErrors['username'] = 'Tài khoản không tồn tại';
        break;
      case 20007006:
        errorMessage = "";
        fieldErrors['username'] = null;
        fieldErrors['password'] = 'Tài khoản hoặc mật khẩu không đúng';
        break;
      case 20007018:
        fieldErrors['username'] = 'Tên người dùng không tồn tại';
        break;
      default:
        errorMessage = loginResponse.meta.message;
        break;
    }

    emit(AuthenticationFailure(errorMessage, fieldErrors));
  }

  AuthenticationStatus getCurrentAuthStatus() {
    return _currentStatus;
  }

  @override
  Future<void> close() async {
    await _authenticationStatusController.close();
    return super.close();
  }
}
