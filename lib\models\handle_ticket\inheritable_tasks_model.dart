class InheritableTasksResponse {
  final int code;
  final List<InheritableTaskData> data;
  final String message;

  InheritableTasksResponse({
    required this.code,
    required this.data,
    required this.message,
  });

  factory InheritableTasksResponse.fromJson(Map<String, dynamic> json) {
    return InheritableTasksResponse(
      code: json['code'] ?? 0,
      data: (json['data'] as List<dynamic>?)
          ?.map((item) => InheritableTaskData.fromJson(item as Map<String, dynamic>))
          .toList() ??
          [],
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class InheritableTaskData {
  final String? id;
  final String? taskId;
  final String? taskExecutionId;
  final String? taskProcInstId;
  final String? taskProcDefId;
  final String? taskCaseInstId;
  final String? taskDefKey;
  final String? taskName;
  final String? signStatus;
  final String? taskPriority;
  final String? taskAssignee;
  final String? taskCreatedTime;
  final String? taskStartedTime;
  final String? slaFinish;
  final String? slaResponse;
  final String? taskFinishedTime;
  final String? slaResponseTime;
  final String? slaFinishTime;
  final String? responseDuration;
  final String? finishDuration;
  final String? taskDoneTime;
  final String? taskType;
  final String? taskCreatedUser;
  final String? taskStatus;
  final bool? taskIsFirst;
  final String? assignType;
  final String? actionUser;
  final String? templateVersionId;
  final String? parentTemplateVersionId;

  InheritableTaskData({
    this.id,
    this.taskId,
    this.taskExecutionId,
    this.taskProcInstId,
    this.taskProcDefId,
    this.taskCaseInstId,
    this.taskDefKey,
    this.taskName,
    this.signStatus,
    this.taskPriority,
    this.taskAssignee,
    this.taskCreatedTime,
    this.taskStartedTime,
    this.slaFinish,
    this.slaResponse,
    this.taskFinishedTime,
    this.slaResponseTime,
    this.slaFinishTime,
    this.responseDuration,
    this.finishDuration,
    this.taskDoneTime,
    this.taskType,
    this.taskCreatedUser,
    this.taskStatus,
    this.taskIsFirst,
    this.assignType,
    this.actionUser,
    this.templateVersionId,
    this.parentTemplateVersionId,
  });

  factory InheritableTaskData.fromJson(Map<String, dynamic> json) {
    return InheritableTaskData(
      id: json['id']?.toString(),
      taskId: json['taskId']?.toString(),
      taskExecutionId: json['taskExecutionId']?.toString(),
      taskProcInstId: json['taskProcInstId']?.toString(),
      taskProcDefId: json['taskProcDefId']?.toString(),
      taskCaseInstId: json['taskCaseInstId']?.toString(),
      taskDefKey: json['taskDefKey']?.toString(),
      taskName: json['taskName']?.toString(),
      signStatus: json['signStatus']?.toString(),
      taskPriority: json['taskPriority']?.toString(),
      taskAssignee: json['taskAssignee']?.toString(),
      taskCreatedTime: json['taskCreatedTime']?.toString(),
      taskStartedTime: json['taskStartedTime']?.toString(),
      slaFinish: json['slaFinish']?.toString(),
      slaResponse: json['slaResponse']?.toString(),
      taskFinishedTime: json['taskFinishedTime']?.toString(),
      slaResponseTime: json['slaResponseTime']?.toString(),
      slaFinishTime: json['slaFinishTime']?.toString(),
      responseDuration: json['responseDuration']?.toString(),
      finishDuration: json['finishDuration']?.toString(),
      taskDoneTime: json['taskDoneTime']?.toString(),
      taskType: json['taskType']?.toString(),
      taskCreatedUser: json['taskCreatedUser']?.toString(),
      taskStatus: json['taskStatus']?.toString(),
      taskIsFirst: json['taskIsFirst'] as bool?,
      assignType: json['assignType']?.toString(),
      actionUser: json['actionUser']?.toString(),
      templateVersionId: json['templateVersionId']?.toString(),
      parentTemplateVersionId: json['parentTemplateVersionId']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'taskExecutionId': taskExecutionId,
      'taskProcInstId': taskProcInstId,
      'taskProcDefId': taskProcDefId,
      'taskCaseInstId': taskCaseInstId,
      'taskDefKey': taskDefKey,
      'taskName': taskName,
      'signStatus': signStatus,
      'taskPriority': taskPriority,
      'taskAssignee': taskAssignee,
      'taskCreatedTime': taskCreatedTime,
      'taskStartedTime': taskStartedTime,
      'slaFinish': slaFinish,
      'slaResponse': slaResponse,
      'taskFinishedTime': taskFinishedTime,
      'slaResponseTime': slaResponseTime,
      'slaFinishTime': slaFinishTime,
      'responseDuration': responseDuration,
      'finishDuration': finishDuration,
      'taskDoneTime': taskDoneTime,
      'taskType': taskType,
      'taskCreatedUser': taskCreatedUser,
      'taskStatus': taskStatus,
      'taskIsFirst': taskIsFirst,
      'assignType': assignType,
      'actionUser': actionUser,
      'templateVersionId': templateVersionId,
      'parentTemplateVersionId': parentTemplateVersionId,
    };
  }
} 