class AssistantOpinionResponseModel {
  final int code;
  final Data data;
  final String message;

  AssistantOpinionResponseModel({
    required this.code,
    required this.data,
    required this.message,
  });

  factory AssistantOpinionResponseModel.fromJson(Map<dynamic, dynamic> json) {
    return AssistantOpinionResponseModel(
      code: json['code'] as int,
      data: Data.fromJson(json['data'] as Map<dynamic, dynamic>),
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'data': data.toJson(),
      'message': message,
    };
  }
}

class Data {
  final int page;
  final int limit;
  final String? search;
  final String sortBy;
  final String? sortType;
  final int size;
  final List<Content> content;
  final int totalElements;
  final int number;
  final int numberOfElements;
  final int totalPages;
  final bool first;
  final bool last;

  Data({
    required this.page,
    required this.limit,
    required this.search,
    required this.sortBy,
    required this.sortType,
    required this.size,
    required this.content,
    required this.totalElements,
    required this.number,
    required this.numberOfElements,
    required this.totalPages,
    required this.first,
    required this.last,
  });

  factory Data.fromJson(Map<dynamic, dynamic> json) {
    return Data(
      page: json['page'] as int,
      limit: json['limit'] as int,
      search: json['search'] as String?,
      sortBy: json['sortBy'] as String,
      sortType: json['sortType'] as String?,
      size: json['size'] as int,
      content: (json['content'] as List<dynamic>)
          .map((e) => Content.fromJson(e as Map<dynamic, dynamic>))
          .toList(),
      totalElements: json['totalElements'] as int,
      number: json['number'] as int,
      numberOfElements: json['numberOfElements'] as int,
      totalPages: json['totalPages'] as int,
      first: json['first'] as bool,
      last: json['last'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'size': size,
      'content': content.map((e) => e.toJson()).toList(),
      'totalElements': totalElements,
      'number': number,
      'numberOfElements': numberOfElements,
      'totalPages': totalPages,
      'first': first,
      'last': last,
    };
  }
}

class Content {
  final int id;
  final int ticketId;
  final String assistantEmail;
  final String opinion;
  final String fileUrl;
  final int status;
  final String? fileName;
  final int createAt;
  final int updateAt;
  final String formatedCreateAt;
  final String formatedUpdateAt;

  Content({
    required this.id,
    required this.ticketId,
    required this.assistantEmail,
    required this.opinion,
    required this.fileUrl,
    required this.status,
    required this.fileName,
    required this.createAt,
    required this.updateAt,
    required this.formatedCreateAt,
    required this.formatedUpdateAt,
  });

  factory Content.fromJson(Map<dynamic, dynamic> json) {
    return Content(
      id: json['id'] as int,
      ticketId: json['ticketId'] as int,
      assistantEmail: json['assistantEmail'] as String,
      opinion: json['opinion'] as String,
      fileUrl: json['fileUrl'] as String,
      status: json['status'] as int,
      fileName: json['fileName'] as String?,
      createAt: json['createAt'] as int,
      updateAt: json['updateAt'] as int,
      formatedCreateAt: json['formatedCreateAt'] as String,
      formatedUpdateAt: json['formatedUpdateAt'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'assistantEmail': assistantEmail,
      'opinion': opinion,
      'fileUrl': fileUrl,
      'status': status,
      'fileName': fileName,
      'createAt': createAt,
      'updateAt': updateAt,
      'formatedCreateAt': formatedCreateAt,
      'formatedUpdateAt': formatedUpdateAt,
    };
  }
}