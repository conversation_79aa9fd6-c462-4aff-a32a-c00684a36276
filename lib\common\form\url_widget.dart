import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:eapprove/common/form/form_label.dart';

class URLWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;
  final bool? isShowLabel;

  const URLWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
    this.isShowLabel = true,
  }) : super(key: key);

  @override
  State<URLWidget> createState() => _URLWidgetState();
}

class _URLWidgetState extends State<URLWidget> {
  late TextEditingController _controller;
  bool _isValidUrl = true;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.data.value != null ? widget.data.value.toString() : '',
    );
    _validateUrl(_controller.text);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(URLWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data.value != widget.data.value) {
      _controller.text = widget.data.value != null ? widget.data.value.toString() : '';
      _validateUrl(_controller.text);
    }
  }

  void _validateUrl(String text) {
    if (text.isEmpty) {
      setState(() {
        _isValidUrl = true;
      });
      return;
    }

    Uri? uri = Uri.tryParse(text);
    bool isValid = uri != null &&
        (uri.scheme == 'http' || uri.scheme == 'https') &&
        uri.host.isNotEmpty;

    setState(() {
      _isValidUrl = isValid;
    });
  }

  Future<void> _launchUrl(String urlString) async {
    if (urlString.isEmpty) return;

    // Add https:// prefix if no scheme is provided
    if (!urlString.startsWith('http://') && !urlString.startsWith('https://')) {
      urlString = 'https://$urlString';
    }

    final Uri url = Uri.parse(urlString);

    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        // Show error message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not launch $urlString')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't render if display is false
    if (widget.data.display == false) {
      return const SizedBox.shrink();
    }

    // Get label text
    String labelText = widget.data.label ?? 'URL';

    // Use displayName if available, otherwise use label
    if (widget.data.displayName != null && widget.data.displayName!.isNotEmpty) {
      labelText = widget.data.displayName!;
    }

    // Check if field is required
    bool isRequired = widget.data.validations != null &&
        widget.data.validations!['required'] == true;

    // Check if horizontal layout is required
    bool isHorizontal = false;
    if (widget.data.isHorizontal != null) {
      isHorizontal = widget.data.isHorizontal == 2.0;
    }

    // Create the label widget
    Widget labelWidget = Row(
      children: [
        Text(
          labelText,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: widget.data.fontWeight == 'bold' ? FontWeight.bold : FontWeight.normal,
            fontStyle: widget.data.fontWeight == 'italic' ? FontStyle.italic : FontStyle.normal,
            decoration: widget.data.fontWeight == 'underline' ? TextDecoration.underline : TextDecoration.none,
          ),
        ),
        if (isRequired)
          Text(
            ' *',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.red,
            ),
          ),
        if (widget.data.tooltip != null && widget.data.tooltip!.isNotEmpty) ...[
          SizedBox(width: 4.w),
          Tooltip(
            message: widget.data.tooltip!,
            child: Icon(
              Icons.info_outline,
              size: 16.sp,
              color: Colors.grey,
            ),
          ),
        ],
      ],
    );

    // Create the URL field or display widget
    Widget urlWidget;

    if (widget.data.readonly == true) {
      // Read-only mode - display as clickable link if valid
      if (_controller.text.isNotEmpty) {
        urlWidget = RichText(
          text: TextSpan(
            text: _controller.text,
            style: TextStyle(
              fontSize: 14.sp,
              color: _isValidUrl ? Colors.blue : Colors.black54,
              decoration: _isValidUrl ? TextDecoration.underline : TextDecoration.none,
            ),
            recognizer: _isValidUrl ? (TapGestureRecognizer()
              ..onTap = () => _launchUrl(_controller.text)) : null,
          ),
        );
      } else {
        urlWidget = Text(
          '-',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black54,
          ),
        );
      }
    } else {
      // Editable mode - show text field
      urlWidget = TextField(
        controller: _controller,
        onChanged: (value) {
          _validateUrl(value);
          widget.data.value = value;
          widget.onChange?.call(widget.data.name ?? '', value);
        },
        decoration: InputDecoration(
          hintText: widget.data.placeholder ?? 'Enter URL',
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(
              color: _isValidUrl ? Colors.grey.withOpacity(0.5) : Colors.red,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(
              color: _isValidUrl ? getColorSkin().primaryBlue : Colors.red,
            ),
          ),
          errorText: !_isValidUrl ? 'Please enter a valid URL' : null,
          suffixIcon: _controller.text.isNotEmpty ? IconButton(
            icon: Icon(
              _isValidUrl ? Icons.open_in_new : Icons.error_outline,
              color: _isValidUrl ? Colors.blue : Colors.red,
            ),
            onPressed: _isValidUrl ? () => _launchUrl(_controller.text) : null,
          ) : null,
        ),
        keyboardType: TextInputType.url,
        readOnly: widget.data.readonly == true,
      );
    }

    // Handle error display
    Widget errorWidget = const SizedBox.shrink();
    if (widget.data.error != null && widget.data.error!.isNotEmpty) {
      errorWidget = Padding(
        padding: EdgeInsets.only(top: 4.h),
        child: Text(
          widget.data.error!,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.red,
          ),
        ),
      );
    }

    // Return the widget based on horizontal/vertical layout
    if (isHorizontal) {
      // Horizontal layout (label on left, URL field on right)
      return Padding(
        padding: EdgeInsets.only(bottom: 16.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 120.w,
              child: FormLabel(
                displayName: widget.data.displayName,
                label: widget.data.label,
                suggestText: widget.data.suggestText,
                isRequired: widget.data.validations?['required'] == true,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  urlWidget,
                  errorWidget,
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      // Vertical layout (label above, URL field below)
      return Padding(
        padding: EdgeInsets.only(bottom: 16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if(widget.isShowLabel == true)
              FormLabel(
                displayName: widget.data.displayName,
                label: widget.data.label,
                suggestText: widget.data.suggestText,
                isRequired: widget.data.validations?['required'] == true,
              ),
            SizedBox(height: 8.h),
            urlWidget,
            errorWidget,
          ],
        ),
      );
    }
  }
}