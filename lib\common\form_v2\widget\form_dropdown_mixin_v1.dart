// import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
// import 'package:eapprove/blocs/dropdown/dropdown_event.dart';
// import 'package:eapprove/blocs/dropdown/dropdown_state.dart';
// import 'package:eapprove/blocs/form/form_bloc.dart';
// import 'package:eapprove/common/form_v2/form_state_manager.dart';
// import 'package:eapprove/enum/enum.dart';
// import 'package:eapprove/helper/api_expression.dart';
// import 'package:eapprove/models/form/call_service_request_model.dart';
// import 'package:eapprove/models/form/form_item_info.dart';
// import 'package:eapprove/models/form/individual_info_request_model.dart';
// import 'package:eapprove/models/form/md_service_request_model.dart';
// import 'package:eapprove/models/form/md_service_response.dart';
// import 'package:eapprove/common/form/custom_dropdown_multi_form.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'package:flutter_sdk/widgets/custom_dropdown.dart';
// import 'dart:developer' as developer;

// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:collection/collection.dart';

// // Constants
// const _kDefaultDropdownHeight = 40.0;
// const _kDefaultBorderRadius = 10.0;
// const _kDefaultPadding = 12.0;
// const _kDefaultSpacing = 8.0;

// // Mixins
// mixin DropdownDataMixin on State<DropdownWidget> {
//   bool _isLoading = false;
//   List<SelectItem> _cachedItems = [];
//   String? _lastRequestKey;
//   String? _lastDependentValue;

//   String _getRequestKey() {
//     if (widget.data.typeOption == 'masterData' ||
//         widget.data.optionType == 'masterData') {
//       final masterDataId =
//           widget.data.optionConfig?['masterDataId']?.toString() ?? '';
//       final conditionFilters = _buildConditionFilters();
//       return 'masterData_${masterDataId}_${conditionFilters.map((f) => f.toJson()).toList()}';
//     } else if (widget.data.typeOption == 'orgchart' ||
//         widget.data.optionType == 'orgchart') {
//       final orgchartType =
//           widget.data.optionConfig?['orgchartType']?.toString() ?? '';
//       final orgchartExpressions =
//           widget.data.optionConfig?['orgchartExpression'] as List<dynamic>? ??
//               [];
//       return 'orgchart_${orgchartType}_${orgchartExpressions.map((e) => e.toString()).toList()}';
//     }
//     return 'static_${widget.data.options?.length ?? 0}';
//   }

//   String? _getDependentValue() {
//     final dependentField =
//         widget.data.optionConfig?['dependentField']?.toString();
//     if (dependentField != null) {
//       final dropdownState = context.read<DropdownBloc>().state;
//       if (dropdownState.dropdownResponses.containsKey(dependentField)) {
//         final response = dropdownState.dropdownResponses[dependentField];
//         if (response?.data.isNotEmpty == true) {
//           return response!.data.first['value']?.toString();
//         }
//       }
//     }
//     return null;
//   }

//   List<ConditionFilter> _buildConditionFilters() {
//     final mdExpressions =
//         widget.data.optionConfig?['mdExpression'] as List<dynamic>?;

//     return mdExpressions?.map((expression) {
//           final begin = expression['type']?.toString() ?? '';
//           final operator = expression['expression']?.toString() ?? '=';
//           final condition = expression['condition']?.toString() ?? 'and';

//           return ConditionFilter(
//             condition: condition,
//             begin: begin,
//             operator: operator,
//             end: "1000",
//           );
//         }).toList() ??
//         [];
//   }
// }

// mixin DropdownDisplayMixin on State<DropdownWidget> {
//   String _formatDisplayValue(MdService item) {
//     if (item.id == null) return '';
//     final displayFields =
//         widget.data.optionConfig?['masterDataDisplay'] as List<dynamic>? ?? [];
//     if (displayFields.isEmpty) {
//       return item.id ?? '';
//     }

//     final displayValues = displayFields
//         .map((field) => item.toJson()[field]?.toString() ?? '')
//         .where((value) => value.isNotEmpty)
//         .toList();

//     return displayValues.isNotEmpty ? displayValues.join('_') : item.id ?? '';
//   }

//   List<String> _getDefaultDisplayFields(String orgchartType) {
//     switch (orgchartType) {
//       case 'department':
//         return ['departmentName'];
//       case 'position':
//         return ['positionName'];
//       case 'user':
//       default:
//         return ['username'];
//     }
//   }

//   String _getDefaultValueField(String orgchartType) {
//     switch (orgchartType) {
//       case 'department':
//         return 'username';
//       case 'position':
//         return 'positionId';
//       case 'user':
//       default:
//         return 'username';
//     }
//   }

//   String _getDefaultLabel(dynamic info, String orgchartType) {
//     switch (orgchartType) {
//       case 'department':
//         return '${info.username ?? ''}_${info.name ?? ''}_${info.position ?? ''}';
//       case 'position':
//         return info.position ?? '';
//       case 'user':
//       default:
//         return info.username ?? '';
//     }
//   }
// }

// class DropdownWidget extends StatefulWidget {
//   final FormItemInfo data;
//   final FormStateManager stateManager;
//   final Function(String, dynamic)? onChange;

//   const DropdownWidget({
//     Key? key,
//     required this.data,
//     required this.onChange,
//     required this.stateManager,
//   }) : super(key: key);

//   @override
//   State<DropdownWidget> createState() => _DropdownWidgetState();
// }

// class _DropdownWidgetState extends State<DropdownWidget>
//     with DropdownDataMixin, DropdownDisplayMixin {
//   @override
//   void initState() {
//     super.initState();
//     // Reset only dropdown-related API responses
//     _fetchDropdownData();
//     // _fetchMasterData();
//     // _fetchOrgchartData();
//     // _buildSelectItems();
//     // _findDefaultValue();
//   }

//   @override
//   void didUpdateWidget(DropdownWidget oldWidget) {
//     super.didUpdateWidget(oldWidget);

//     // final dependentValue = _getDependentValue();
//     // if (dependentValue != _lastDependentValue) {
//     //   _lastDependentValue = dependentValue;
//     //   _fetchDropdownData();
//     // }

//     // if (oldWidget.data != widget.data) {
//     //   final newRequestKey = _getRequestKey();
//     //   if (newRequestKey != _lastRequestKey) {
//     //     _lastRequestKey = newRequestKey;
//     //     _fetchDropdownData();
//     //   }
//     // }
//   }

//   void _fetchDropdownData() {
//     // Xác định loại API cần gọi dựa trên type của dropdown
//     if (widget.data.optionType == 'masterData') {
//       _fetchMasterData();
//     }
//     if (widget.data.optionType == 'orgchart') {
//       _fetchOrgchartData();
//     }
//     if (widget.data.optionType == 'api_link') {
//       _fetchApiLinkData();
//     }
//     // if (widget.data.optionType == FormDropdownOptionType.normal) {
//     //   _fetchNormalData();
//     // }
//   }

//   void _fetchMasterData() {
//     final masterDataId =
//         widget.data.optionConfig?['masterDataId']?.toString() ?? '';
//     if (masterDataId.isEmpty) return;

//     final conditionFilters = _buildConditionFilters();
//     final requestModel = IndividualInfoRequestModel(
//       orgchart: [],
//       infoType: 'masterData',
//       condition: conditionFilters
//           .map((filter) => Condition(
//                 expression: filter.condition ?? 'and',
//                 filterType: filter.begin ?? '',
//                 filterField: 'id',
//                 operator: filter.operator ?? '=',
//                 value: filter.end ?? '',
//               ))
//           .toList(),
//     );

//     context.read<DropdownBloc>().add(GetDropdownDataRequested(
//           requestModel: requestModel,
//           requestKey: widget.data.name ?? '',
//         ));
//   }

//   void _fetchOrgchartData() {
//     final orgchartType =
//         widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';
//     final orgchartExpressions =
//         widget.data.optionConfig?['orgchartExpression'] as List<dynamic>? ?? [];

//     if (orgchartType.isEmpty) return;

//     developer.log(
//         'Fetch orgchart data: ${widget.data.id} ${widget.data.name} ${widget.data.value}',
//         name: 'dropdown_widget');

//     final requestModel = ApiExpression.convertOrgExpressionToInputDataFilter(
//         orgchartExpressions, orgchartType, [], widget.stateManager);
//     developer.log(
//         'Request model: ${widget.data.name} ${requestModel.toJsonString()}',
//         name: 'dropdown_widget');

//     Future.delayed(const Duration(milliseconds: 100), () {
//       context.read<DropdownBloc>().add(GetDropdownDataRequested(
//             requestModel: requestModel,
//             requestKey: widget.data.name ?? '',
//           ));
//     });
//   }

//   void _fetchApiLinkData() async {
//     // Chuẩn bị tham số request cho API API link
//     final listBaseUrl = context.read<FormBloc>().state.listBaseUrlResponse;

//     final apiURLBase = widget.data.optionConfig?['apiURLBase']?.toString();

//     // Đảm bảo xử lý đồng bộ trước khi tìm kiếm
//     await Future.delayed(Duration.zero);

//     final baseURL = listBaseUrl
//         ?.firstWhereOrNull(
//           (e) => e.id.toString() == apiURLBase.toString(),
//         )
//         ?.url;

//     if (baseURL == null || baseURL.isEmpty) {
//       developer.log(
//           'dropdown_widget - No valid baseURL found for apiURLBase: $apiURLBase',
//           name: 'dropdown_widget');
//       return;
//     }

//     final requestBody = ApiExpression.getApiLinkInput(
//         widget.data.optionConfig?['apiExpression'] as List<dynamic>? ?? [],
//         widget.stateManager);
//     const checkURLBase = [
//       'call-service-admin',
//       'call-service-ihrp',
//       'call-service-cons',
//       'call-service-sap'
//     ];
//     bool found = checkURLBase.any((char) => baseURL?.contains(char) ?? false);

//     developer.log('found: ${widget.data.name} ${baseURL} $found',
//         name: 'dropdown_widget');

//     if (found) {
//       context.read<DropdownBloc>().add(GetDropdownDataFromAPILink(
//             requestModel: requestBody,
//             requestKey: widget.data.name ?? '',
//             endpoint: widget.data.optionConfig?['apiURL']?.toString() ?? '',
//             apiURL: baseURL,
//           ));
//       return;
//     } else {
//       final input = {
//         'url': Uri.parse('${baseURL}${widget.data.optionConfig?['apiURL']}'),
//         'method': widget.data.optionConfig?['apiMethod']?.toString() ?? 'POST',
//         'params': requestBody,
//         'headers': widget.data.optionConfig?['addHeaderTemplate']
//                 as Map<String, dynamic> ??
//             {},
//         'isSaveLog': widget.data.optionConfig?['isSaveLog'] ?? false,
//       };

//       // context.read<DropdownBloc>().add(GetDropdownDataFromAPILinkService(
//       //       requestModel: CallServiceRequestModel.fromJson(requestBody),
//       //       requestKey: widget.data.name ?? '',
//       //       apiURL: baseURL,
//       //     ));
//     }
//   }

//   void _fetchNormalData() {
//     // Chuẩn bị tham số request cho API normal
//     // final normalDataUrl = widget.data.optionConfig?['normalDataUrl']?.toString() ?? '';
//     // if (normalDataUrl.isEmpty) return;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BlocListener<DropdownBloc, DropdownState>(
//       listener: (context, state) {
//         if (!state.isLoading && state.errorMessage == null) {
//           final response = state.dropdownResponses[widget.data.name ?? ''];
//           if (response != null) {
//             // _handleDropdownResponse(response);
//             // _buildDropdownItems(response);
//           }
//         }else{
//           developer.log('state: ${state.errorMessage}', name: 'dropdown_widget');
//         }

//       },
//       child: BlocBuilder<DropdownBloc, DropdownState>(
//         builder: (context, state) {
//           final response = state.dropdownResponses[widget.data.name ?? ''];

//           if (response == null) {
//             return CustomDropdownMenu(
//               label: '',
//               options: [],
//               onSelected: (selectedItems) {},
//               placeholder: 'Select options',
//               dropdownHeight: _kDefaultDropdownHeight.h,
//             );
//           } else {
//             return renderDropdownMenu(response);
//           }
//         },
//       ),
//     );
//   }

//   // void _handleDropdownResponse(BaseResponseModel response) {
//   //   if (response.data.isEmpty) return;

//   //   final items = _buildDropdownItems(response);
//   //   // if (items.isNotEmpty && widget.data.value == null) {
//   //   //   widget.data.value = items.first.value;
//   //   //   widget.onChange?.call(widget.data.name ?? '', widget.data.value);
//   //   // }
//   // }

//   List<SelectItem> _buildDropdownItems(BaseResponseModel response) {
//     if (widget.data.optionType == 'masterData') {
//       final items =
//           response.data.where((item) => item['id'] != null).map((item) {
//         final displayValue = _formatDisplayValue(item);
//         return SelectItem(
//           label: displayValue,
//           value: item['id'],
//         );
//       }).toList();

//       return items;
//     }

//     if (widget.data.optionType == 'orgchart') {
//       final orgchartType =
//           widget.data.optionConfig?['orgchartType']?.toString() ?? 'user';

//       final displayFields =
//           widget.data.optionConfig?['displayField'] as List<dynamic>? ??
//               _getDefaultDisplayFields(orgchartType);

//       final valueField = widget.data.optionConfig?['valueField']?.toString() ??
//           _getDefaultValueField(orgchartType);

//       final items = response.data.map((info) {
//         final displayValues = displayFields
//             .map((field) => info.toJson()[field]?.toString() ?? '')
//             .where((value) => value.isNotEmpty)
//             .toList();

//         String label;
//         String value;

//         if (orgchartType == 'department') {
//           value = info.toJson()['username'] ?? '';
//           label = displayValues.isNotEmpty
//               ? displayValues.join('_')
//               : '${info.toJson()['username'] ?? ''}_${info.toJson()['name'] ?? ''}_${info.toJson()['position'] ?? ''}';
//         } else {
//           label = displayValues.isNotEmpty
//               ? displayValues.join('_')
//               : _getDefaultLabel(info.toJson(), orgchartType);
//           value = info.toJson()[valueField]?.toString() ?? '';
//         }

//         return SelectItem(label: label, value: value);
//       }).toList();

//       return items;
//     }

//     if (widget.data.optionType == 'api_link') {
//       final items = response.data.map((item) {
//         final displayFields =
//             widget.data.optionConfig?['apiDisplay'] as List<dynamic>? ?? [];
//         if (displayFields.isEmpty) {
//           return SelectItem(label: item.id ?? '', value: item.id ?? '');
//         }

//         developer.log('item: $item', name: 'dropdown_widget');

//         final displayValues = displayFields
//             .map((field) => item[field]?.toString() ?? '')
//             .where((value) => value.isNotEmpty)
//             .toList();

//         return SelectItem(
//           label: displayValues.isNotEmpty
//               ? displayValues.join('_')
//               : item.id ?? '',
//           value: item[widget.data.optionConfig?['apiValue']]?.toString() ?? '',
//         );
//       }).toList();

//       return items;
//     }

//     // Handle static options
//     if (widget.data.optionType == 'normal' && widget.data.option != null) {
//       return widget.data.option!.map((option) {
//         return SelectItem(
//           label: option['label']?.toString() ?? '',
//           value: option['value']?.toString() ?? '',
//         );
//       }).toList();
//     }

//     return [];
//   }

//   Widget renderDropdownMenu(BaseResponseModel response) {
//     final items = _buildDropdownItems(response ?? BaseResponseModel(data: []));
//     final Box authBox = Hive.box('authentication');
//     final String? username = authBox.get('username');

//     late SelectItem defaultValue;
//     if (widget.data.value != null && widget.data.value.isNotEmpty) {
      
//       defaultValue = items.firstWhere(
//         (item) => item.value == widget.data.value,
//         orElse: () => SelectItem(label: '', value: ''),
//       );
//     } else if (widget.data.isUserCurrentUserLogin == true) {
//       defaultValue = items.firstWhere(
//         (item) => item.value == username,
//         orElse: () => SelectItem(label: '', value: ''),
//       );
      
//     } else if (widget.data.defaultSelect == true) {
//       defaultValue =
//           items.isNotEmpty ? items.first : SelectItem(label: '', value: '');
//     } else {
//       defaultValue = SelectItem(label: '', value: '');
//     }

//     return widget.data.validations?['isMulti'] == true
//         ? CustomDropdownMultiForm(
//             label: widget.data.displayName ?? '',
//             placeholder: widget.data.placeholder ?? 'Select options',
//             options: items,
//             selectedItems: items
//                 .where((item) =>
//                     widget.data.value != null &&
//                     (widget.data.value as List).contains(item.value))
//                 .toList(),
//             onSelected: (selectedItems) {
//               widget.data.value =
//                   selectedItems.map((item) => item.value).toList();
//               widget.onChange?.call(widget.data.name ?? '', widget.data.value);
//             },
//             isFilled: true,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(_kDefaultBorderRadius.r),
//               border: Border.all(color: getColorSkin().lightGray, width: 1),
//               color: widget.data.readonly == true
//                   ? getColorSkin().whiteSmoke
//                   : getColorSkin().white,
//             ),
//             isDisabled: widget.data.readonly == true,
//             dropdownHeight: 40.h,
//             isRequired: widget.data.validations?['required'] == true,
//           )
//         : CustomDropdownMenu(
//             showLabel: false,
//             key: widget.data.name != null ? Key(widget.data.name!) : null,
//             label: widget.data.displayName ?? '',
//             placeholder: widget.data.placeholder ?? 'Select an option',
//             options: items,
//             onSelected: (selectedItem) {
//               developer.log(
//                   'Selected item: ${widget.data.id} ${widget.data.name} ${selectedItem?.value}',
//                   name: 'dropdown_widget');
//               if (selectedItem != null) {
//                 widget.data.value = selectedItem.value;
//                 widget.onChange
//                     ?.call(widget.data.name ?? '', selectedItem.value);
//               } else {
//                 widget.data.value = null;
//                 widget.onChange?.call(widget.data.name ?? '', null);
//               }
//             },
//             isFilled: true,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(_kDefaultBorderRadius.r),
//               border: Border.all(color: getColorSkin().lightGray, width: 1),
//               color: widget.data.readonly == true
//                   ? getColorSkin().whiteSmoke
//                   : getColorSkin().white,
//             ),
//             defaultValue: defaultValue,
//             showDeleteIcon:
//                 widget.data.value != null && widget.data.readonly == false,
//             isDisabled: widget.data.readonly == true,
//             dropdownHeight: 40.h,
//             isRequired: widget.data.validations?['required'] == true,
//           );
//   }
// }
