// import 'package:eapprove/models/form/form_content_response_model.dart' as form_models;
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'package:flutter_sdk/widgets/custom_dropdown.dart';
//
// class FormFieldRenderer extends StatelessWidget {
//   final form_models.FormField field;
//   final form_models.FormTemplate template;
//
//   const FormFieldRenderer({
//     super.key,
//     required this.field,
//     required this.template,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     // Render the field based on its type
//     switch (field.type) {
//       case 'text':
//         return _buildTextField(field as form_models.TextField);
//       case 'textarea':
//         return _buildTextAreaField(field as form_models.TextAreaField);
//       case 'select':
//         return _buildSelectField(field as form_models.SelectField);
//       case 'date':
//         return _buildDateField(field as form_models.DateField);
//       case 'file':
//         return _buildFileField(field as form_models.FileField);
//       case 'splitter':
//         return _buildSplitterField(field as form_models.SplitterField);
//       case 'matrix':
//         return _buildMatrixField(field as form_models.MatrixField);
//       case 'radio':
//         return _buildRadioField(field as form_models.RadioField);
//       case 'checkbox':
//         return _buildCheckboxField(field as form_models.CheckboxField);
//       case 'label':
//         return _buildLabelField(field as form_models.LabelField);
//       case 'table':
//         return _buildTableField(field as form_models.TableField);
//       case 'link':
//         return _buildLinkField(field as form_models.LinkField);
//       case 'currency':
//         return _buildCurrencyField(field as form_models.CurrencyField);
//       case 'formula':
//         return _buildFormulaField(field as form_models.FormulaField);
//       default:
//         return Text(
//           'Unsupported field type: ${field.type}',
//           style: TextStyle(color: Colors.red, fontSize: 14.sp),
//         );
//     }
//   }
//
//   Widget _buildTextField(form_models.TextField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         TextField(
//           controller: TextEditingController(text: field.value),
//           readOnly: field.readonly ?? false,
//           decoration: InputDecoration(
//             hintText: field.placeholder,
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(8.r),
//               borderSide: BorderSide(color: getColorSkin().lightGray),
//             ),
//             filled: true,
//             fillColor: getColorSkin().white,
//           ),
//           onChanged: (value) {
//             // Handle value change
//           },
//         ),
//       ],
//     );
//   }
//
//   Widget _buildTextAreaField(form_models.TextAreaField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         TextField(
//           controller: TextEditingController(text: field.value),
//           readOnly: field.readonly ?? false,
//           maxLines: 5,
//           minLines: 3,
//           decoration: InputDecoration(
//             hintText: field.placeholder,
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(8.r),
//               borderSide: BorderSide(color: getColorSkin().lightGray),
//             ),
//             filled: true,
//             fillColor: getColorSkin().white,
//           ),
//           onChanged: (value) {
//             // Handle value change
//           },
//         ),
//       ],
//     );
//   }
//
//   Widget _buildSelectField(form_models.SelectField field) {
//     final options = field.option.map((opt) =>
//       SelectItem(label: opt.label, value: opt.value)
//     ).toList();
//
//     // Find default value if available
//     SelectItem? defaultValue;
//     if (field.value != null && field.value!.isNotEmpty) {
//       for (final opt in field.option) {
//         if (opt.value == field.value) {
//           defaultValue = SelectItem(label: opt.label, value: opt.value);
//           break;
//         }
//       }
//     }
//
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         CustomDropdownMenu(
//           dropdownHeight: 40.h,
//           options: options,
//           onSelected: (value) {
//             // Handle selection
//           },
//           label: field.label,
//           placeholder: field.placeholder ?? 'Select an option',
//           defaultValue: defaultValue,
//           decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(10.r),
//               border:Border.all(color: getColorSkin().lightGray, width: 1)
//           ),
//           showDeleteIcon: false,
//         ),
//       ],
//     );
//   }
//
//   Widget _buildDateField(form_models.DateField field) {
//     // Basic date field implementation
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         InkWell(
//           onTap: () {
//             // Show date picker
//           },
//           child: Container(
//             padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
//             decoration: BoxDecoration(
//               border: Border.all(color: getColorSkin().lightGray),
//               borderRadius: BorderRadius.circular(8.r),
//               color: getColorSkin().white,
//             ),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: Text(
//                     field.value ?? field.placeholder ?? 'Select date',
//                     style: TextStyle(
//                       fontSize: 14.sp,
//                       color: field.value != null
//                         ? getColorSkin().black
//                         : getColorSkin().secondaryText,
//                     ),
//                   ),
//                 ),
//                 Icon(Icons.calendar_today, color: getColorSkin().secondaryText),
//               ],
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildFileField(form_models.FileField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         Container(
//           padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
//           decoration: BoxDecoration(
//             color: getColorSkin().white,
//             borderRadius: BorderRadius.circular(8.r),
//             border: Border.all(color: getColorSkin().lightGray),
//           ),
//           child: Row(
//             children: [
//               Icon(Icons.attach_file, color: getColorSkin().primaryBlue),
//               SizedBox(width: 8.w),
//               Expanded(
//                 child: Text(
//                   field.placeholder ?? 'Upload file',
//                   style: TextStyle(
//                     fontSize: 14.sp,
//                     color: getColorSkin().secondaryText,
//                   ),
//                 ),
//               ),
//               ElevatedButton(
//                 onPressed: () {
//                   // Handle file upload
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: getColorSkin().primaryBlue,
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(4.r),
//                   ),
//                   minimumSize: Size(80.w, 32.h),
//                 ),
//                 child: Text(
//                   'Browse',
//                   style: TextStyle(
//                     fontSize: 12.sp,
//                     color: Colors.white,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildSplitterField(form_models.SplitterField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         if (field.splitterDesc != null && field.splitterDesc!.isNotEmpty)
//           Padding(
//             padding: EdgeInsets.only(bottom: 8.h),
//             child: Text(
//               field.splitterDesc!,
//               style: TextStyle(
//                 fontSize: 16.sp,
//                 fontWeight: FontWeight.w600,
//                 color: getColorSkin().black,
//               ),
//             ),
//           ),
//         Divider(
//           color: getColorSkin().lightGray,
//           thickness: 1,
//         ),
//       ],
//     );
//   }
//   Widget _buildMatrixField(form_models.MatrixField field) {
//     // Helper function to safely get cell value
//     String getCellValue(dynamic rowData, String key) {
//       if (rowData is Map<String, dynamic>) {
//         return rowData[key]?.toString() ?? '';
//       }
//       return '';
//     }
//
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         SingleChildScrollView(
//           scrollDirection: Axis.horizontal,
//           child: Table(
//             border: TableBorder.all(color: getColorSkin().grey2),
//             columnWidths: {
//               for (int i = 0; i < (field.columns.length); i++)
//                 i: FixedColumnWidth(120.w),
//             },
//             defaultVerticalAlignment: TableCellVerticalAlignment.middle,
//             children: [
//               // Header row
//               TableRow(
//                 children: field.columns.map((column) {
//                   final headerText = column is Map<String, dynamic>
//                       ? column['label']?.toString() ?? column['name']?.toString() ?? ''
//                       : column?.toString() ?? '';
//                   return TableCell(
//                     child: Padding(
//                       padding: const EdgeInsets.all(8.0),
//                       child: Text(
//                         headerText,
//                         style: TextStyle(
//                           fontSize: 14.sp,
//                           fontWeight: FontWeight.w500,
//                           color: getColorSkin().black,
//                         ),
//                       ),
//                     ),
//                   );
//                 }).toList(),
//               ),
//               // Data rows
//               if (field.option != null)
//                 ...field.option!.map((dynamic rowData) {
//                   return TableRow(
//                     children: field.columns.map((column) {
//                       final key = column is Map<String, dynamic>
//                           ? column['name']?.toString() ?? ''
//                           : column?.toString() ?? '';
//
//                       return TableCell(
//                         child: Padding(
//                           padding: const EdgeInsets.all(8.0),
//                           child: Text(
//                             getCellValue(rowData, key),
//                             style: TextStyle(
//                               fontSize: 14.sp,
//                               color: getColorSkin().ink1,
//                             ),
//                           ),
//                         ),
//                       );
//                     }).toList(),
//                   );
//                 }).toList(),
//             ],
//           ),
//         ),
//         if (field.specialField != null && field.specialField!.isNotEmpty)
//           Padding(
//             padding: EdgeInsets.only(top: 8.h),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: field.specialField!.map((specialField) {
//                 return Padding(
//                   padding: EdgeInsets.only(top: 4.h),
//                   child: Text(
//                     specialField?.toString() ?? '',
//                     style: TextStyle(
//                       fontSize: 14.sp,
//                       color: getColorSkin().secondaryText,
//                     ),
//                   ),
//                 );
//               }).toList(),
//             ),
//           ),
//       ],
//     );
//   }
//
//   Widget _buildRadioField(form_models.RadioField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         ...field.option.map((opt) => RadioListTile<String>(
//           title: Text(
//             opt.label,
//             style: TextStyle(
//               fontSize: 14.sp,
//               color: getColorSkin().black,
//             ),
//           ),
//           value: opt.value,
//           groupValue: field.value,
//           onChanged: field.readonly == true ? null : (String? value) {
//             // Handle value change
//           },
//           activeColor: getColorSkin().primaryBlue,
//           contentPadding: EdgeInsets.zero,
//         )).toList(),
//       ],
//     );
//   }
//
//   Widget _buildCheckboxField(form_models.CheckboxField field) {
//     // Parse the current value into a list of selected values
//     List<String> selectedValues = [];
//     if (field.value != null && field.value!.isNotEmpty) {
//       try {
//         if (field.value is String) {
//           selectedValues = field.value!.split(',');
//         }
//       } catch (e) {
//         debugPrint('Error parsing checkbox values: $e');
//       }
//     }
//
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         ...field.option.map((opt) => CheckboxListTile(
//           title: Text(
//             opt.label,
//             style: TextStyle(
//               fontSize: 14.sp,
//               color: getColorSkin().black,
//             ),
//           ),
//           value: selectedValues.contains(opt.value),
//           onChanged: field.readonly == true ? null : (bool? checked) {
//             // Handle value change
//           },
//           activeColor: getColorSkin().primaryBlue,
//           contentPadding: EdgeInsets.zero,
//           controlAffinity: ListTileControlAffinity.leading,
//         )).toList(),
//       ],
//     );
//   }
//
//   Widget _buildLabelField(form_models.LabelField field) {
//     Color? textColor;
//     if (field.textColor != null) {
//       try {
//         textColor = Color(int.parse(field.textColor!.replaceAll('#', ''), radix: 16) + 0xFF000000);
//       } catch (e) {
//         debugPrint('Error parsing text color: $e');
//       }
//     }
//
//     Color? backgroundColor;
//     if (field.backgroundColor != null) {
//       try {
//         backgroundColor = Color(int.parse(field.backgroundColor!.replaceAll('#', ''), radix: 16) + 0xFF000000);
//       } catch (e) {
//         debugPrint('Error parsing background color: $e');
//       }
//     }
//
//     TextAlign textAlignment = TextAlign.left;
//     switch (field.textAlign?.toLowerCase()) {
//       case 'center':
//         textAlignment = TextAlign.center;
//         break;
//       case 'right':
//         textAlignment = TextAlign.right;
//         break;
//       case 'justify':
//         textAlignment = TextAlign.justify;
//         break;
//       default:
//         textAlignment = TextAlign.left;
//     }
//
//     return Container(
//       padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(8.r),
//       ),
//       child: Text(
//         field.label,
//         style: TextStyle(
//           fontSize: field.fontSize?.sp ?? 14.sp,
//           fontWeight: field.fontWeight != null ? FontWeight.w600 : FontWeight.normal,
//           color: textColor ?? getColorSkin().black,
//         ),
//         textAlign: textAlignment,
//       ),
//     );
//   }
//
//   Widget _buildTableField(form_models.TableField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         SingleChildScrollView(
//           scrollDirection: Axis.horizontal,
//           child: Table(
//             border: TableBorder.all(color: getColorSkin().grey2),
//             columnWidths: {
//               for (int i = 0; i < field.columns.length; i++)
//                 i: FixedColumnWidth(120.w),
//             },
//             defaultVerticalAlignment: TableCellVerticalAlignment.middle,
//             children: [
//               // Header row
//               TableRow(
//                 children: field.columns.map((column) {
//                   final headerText = column is Map<String, dynamic>
//                       ? column['label']?.toString() ?? column['name']?.toString() ?? ''
//                       : column?.toString() ?? '';
//                   return TableCell(
//                     child: Container(
//                       padding: EdgeInsets.all(8.w),
//                       color: getColorSkin().lightGray,
//                       child: Text(
//                         headerText,
//                         style: TextStyle(
//                           fontSize: 14.sp,
//                           fontWeight: FontWeight.w500,
//                           color: getColorSkin().black,
//                         ),
//                       ),
//                     ),
//                   );
//                 }).toList(),
//               ),
//               // Data rows
//               if (field.option != null)
//                 ...field.option!.map((opt) {
//                   return TableRow(
//                     children: field.columns.map((column) {
//                       final key = column is Map<String, dynamic>
//                           ? column['name']?.toString() ?? ''
//                           : column?.toString() ?? '';
//                       final value = opt.value;
//
//                       return TableCell(
//                         child: Container(
//                           padding: EdgeInsets.all(8.w),
//                           child: Text(
//                             value,
//                             style: TextStyle(
//                               fontSize: 14.sp,
//                               color: getColorSkin().ink1,
//                             ),
//                           ),
//                         ),
//                       );
//                     }).toList(),
//                   );
//                 }).toList(),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildLinkField(form_models.LinkField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         InkWell(
//           onTap: field.readonly == true ? null : () {
//             // Handle link tap
//           },
//           child: Container(
//             padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
//             decoration: BoxDecoration(
//               border: Border.all(color: getColorSkin().lightGray),
//               borderRadius: BorderRadius.circular(8.r),
//               color: getColorSkin().white,
//             ),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: Text(
//                     field.value ?? field.placeholder ?? '',
//                     style: TextStyle(
//                       fontSize: 14.sp,
//                       color: getColorSkin().primaryBlue,
//                       decoration: TextDecoration.underline,
//                     ),
//                   ),
//                 ),
//                 Icon(
//                   Icons.open_in_new,
//                   size: 16.sp,
//                   color: getColorSkin().primaryBlue,
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildCurrencyField(form_models.CurrencyField field) {
//     final TextEditingController controller = TextEditingController(text: field.value);
//     final String symbol = field.currencySymbol ?? '\$';
//     final int decimals = field.decimalPlaces ?? 2;
//
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         TextField(
//           controller: controller,
//           readOnly: field.readonly ?? false,
//           keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
//           decoration: InputDecoration(
//             hintText: field.placeholder,
//             prefixText: '$symbol ',
//             prefixStyle: TextStyle(
//               color: getColorSkin().black,
//               fontSize: 14.sp,
//             ),
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(8.r),
//               borderSide: BorderSide(color: getColorSkin().lightGray),
//             ),
//             filled: true,
//             fillColor: getColorSkin().white,
//           ),
//           inputFormatters: [
//             FilteringTextInputFormatter.allow(RegExp(r'[0-9\.-]')),
//             TextInputFormatter.withFunction((oldValue, newValue) {
//               // Allow only one decimal point
//               if (newValue.text.isEmpty) return newValue;
//               if (newValue.text == '.') return const TextEditingValue(text: '0.');
//
//               // Handle negative numbers if allowed
//               if (field.allowNegative == true && newValue.text == '-') {
//                 return newValue;
//               }
//
//               try {
//                 final double? value = double.tryParse(newValue.text);
//                 if (value != null) {
//                   final String formatted = value.toStringAsFixed(decimals);
//                   if (formatted == newValue.text) return newValue;
//
//                   // If we have more decimals than allowed, truncate
//                   final String truncated = value.toStringAsFixed(decimals);
//                   return TextEditingValue(
//                     text: truncated,
//                     selection: TextSelection.collapsed(offset: truncated.length),
//                   );
//                 }
//               } catch (e) {
//                 debugPrint('Error formatting currency: $e');
//               }
//               return oldValue;
//             }),
//           ],
//           onChanged: (value) {
//             // Handle value change
//           },
//         ),
//       ],
//     );
//   }
//
//   Widget _buildFormulaField(form_models.FormulaField field) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           field.label,
//           style: TextStyle(
//             fontSize: 14.sp,
//             fontWeight: FontWeight.w500,
//             color: getColorSkin().black,
//           ),
//         ),
//         const SizedBox(height: 8),
//         Container(
//           padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
//           decoration: BoxDecoration(
//             border: Border.all(color: getColorSkin().lightGray),
//             borderRadius: BorderRadius.circular(8.r),
//             color: field.readonly == true ? getColorSkin().lightGray.withOpacity(0.1) : getColorSkin().white,
//           ),
//           child: Row(
//             children: [
//               Expanded(
//                 child: Text(
//                   field.value ?? field.placeholder ?? '',
//                   style: TextStyle(
//                     fontSize: 14.sp,
//                     color: getColorSkin().black,
//                   ),
//                 ),
//               ),
//               if (field.isAutoCalculate == true)
//                 Icon(
//                   Icons.calculate_outlined,
//                   size: 16.sp,
//                   color: getColorSkin().secondaryText,
//                 ),
//             ],
//           ),
//         ),
//         if (field.formula != null && field.formula!.isNotEmpty)
//           Padding(
//             padding: EdgeInsets.only(top: 4.h),
//             child: Text(
//               'Formula: ${field.formula}',
//               style: TextStyle(
//                 fontSize: 12.sp,
//                 color: getColorSkin().secondaryText,
//                 fontStyle: FontStyle.italic,
//               ),
//             ),
//           ),
//       ],
//     );
//   }
// }