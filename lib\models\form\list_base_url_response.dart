class ListBaseUrlResponseModel {
  int? id;
  String? name;
  String? url;
  String? method;
  String? header;
  String? body;
  String? response;
  String? returnResponse;
  String? tokenAttribute;
  String? errorAttribute;
  String? successCondition;
  bool? continueOnError;
  String? type;
  int? status;
  int? authenApiId;
  int? baseUrlId;
  String? description;
  String? createdUser;
  List<int>? createdTime;
  String? updatedUser;
  List<int>? updatedTime;
  String? shareWith;
  int? isDeleted;
  String? companyCode;
  String? companyName;

  ListBaseUrlResponseModel({
    this.id,
    this.name,
    this.url,
    this.method,
    this.header,
    this.body,
    this.response,
    this.returnResponse,
    this.tokenAttribute,
    this.errorAttribute,
    this.successCondition,
    this.continueOnError,
    this.type,
    this.status,
    this.authenApiId,
    this.baseUrlId,
    this.description,
    this.createdUser,
    this.createdTime,
    this.updatedUser,
    this.updatedTime,
    this.shareWith,
    this.isDeleted,
    this.companyCode,
    this.companyName,
  });

  ListBaseUrlResponseModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert to string
    String? safeString(dynamic value) {
      if (value == null) return null;
      if (value is String) return value;
      return value.toString();
    }

    // Helper function to safely convert to int
    int? safeInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) return int.tryParse(value);
      return null;
    }

    // Helper function to safely convert to bool
    bool? safeBool(dynamic value) {
      if (value == null) return null;
      if (value is bool) return value;
      if (value is String) return value.toLowerCase() == 'true';
      if (value is int) return value != 0;
      return null;
    }

    // Helper function to safely convert to List<int>
    List<int>? safeIntList(dynamic value) {
      if (value == null) return null;
      if (value is List) {
        return value.map((e) => safeInt(e) ?? 0).toList();
      }
      return null;
    }

    id = safeInt(json['id']);
    name = safeString(json['name']);
    url = safeString(json['url']);
    method = safeString(json['method']);
    header = safeString(json['header']);
    body = safeString(json['body']);
    response = safeString(json['response']);
    returnResponse = safeString(json['returnResponse']);
    tokenAttribute = safeString(json['tokenAttribute']);
    errorAttribute = safeString(json['errorAttribute']);
    successCondition = safeString(json['successCondition']);
    continueOnError = safeBool(json['continueOnError']);
    type = safeString(json['type']);
    status = safeInt(json['status']);
    authenApiId = safeInt(json['authenApiId']);
    baseUrlId = safeInt(json['baseUrlId']);
    description = safeString(json['description']);
    createdUser = safeString(json['createdUser']);
    createdTime = safeIntList(json['createdTime']);
    updatedUser = safeString(json['updatedUser']);
    updatedTime = safeIntList(json['updatedTime']);
    shareWith = safeString(json['shareWith']);
    isDeleted = safeInt(json['isDeleted']);
    companyCode = safeString(json['companyCode']);
    companyName = safeString(json['companyName']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['url'] = url;
    data['method'] = method;
    data['header'] = header;
    data['body'] = body;
    data['response'] = response;
    data['returnResponse'] = returnResponse;
    data['tokenAttribute'] = tokenAttribute;
    data['errorAttribute'] = errorAttribute;
    data['successCondition'] = successCondition;
    data['continueOnError'] = continueOnError;
    data['type'] = type;
    data['status'] = status;
    data['authenApiId'] = authenApiId;
    data['baseUrlId'] = baseUrlId;
    data['description'] = description;
    data['createdUser'] = createdUser;
    data['createdTime'] = createdTime;
    data['updatedUser'] = updatedUser;
    data['updatedTime'] = updatedTime;
    data['shareWith'] = shareWith;
    data['isDeleted'] = isDeleted;
    data['companyCode'] = companyCode;
    data['companyName'] = companyName;
    return data;
  }

  // Helper method to convert createdTime List<int> to DateTime
  DateTime? getCreatedDateTime() {
    if (createdTime == null || createdTime!.length < 6) return null;

    return DateTime(
      createdTime![0],  // year
      createdTime![1],  // month
      createdTime![2],  // day
      createdTime![3],  // hour
      createdTime![4],  // minute
      createdTime![5],  // second
    );
  }

  // Helper method to convert updatedTime List<int> to DateTime
  DateTime? getUpdatedDateTime() {
    if (updatedTime == null || updatedTime!.length < 6) return null;

    return DateTime(
      updatedTime![0],  // year
      updatedTime![1],  // month
      updatedTime![2],  // day
      updatedTime![3],  // hour
      updatedTime![4],  // minute
      updatedTime![5],  // second
    );
  }
}

// Helper class to parse a list of API configs from JSON
class ListBaseUrlResponseModelList {
  static List<ListBaseUrlResponseModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => ListBaseUrlResponseModel.fromJson(json)).toList();
  }
}