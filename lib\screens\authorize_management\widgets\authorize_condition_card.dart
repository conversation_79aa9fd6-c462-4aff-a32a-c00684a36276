import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/authorize_management/authorize_condition_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_sdk/widgets/custom_info_row.dart';

class CardConditionAuthorize extends StatefulWidget {
  final AuthorizeConditionResponse condition;

  const CardConditionAuthorize({
    super.key,
    required this.condition,
  });

  @override
  State<CardConditionAuthorize> createState() => _CardConditionAuthorizeState();
}

class _CardConditionAuthorizeState extends State<CardConditionAuthorize> {
  @override
  Widget build(BuildContext context) {
    return CustomExpandedList<String>(
      expandedSvgIconPath: StringImage.ic_arrow_up,
      collapsedSvgIconPath: StringImage.ic_arrow_right,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      childPadding: EdgeInsets.symmetric(horizontal: 12.w),
      title: "2. <PERSON>i<PERSON><PERSON> kiện ủy quyền",
      isBuildLeading: false,
      isExpanded: true,
      titleStyle:
          getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
      child: _buildFormContent(),
    );
  }

  Widget _buildFormContent() {
    if (widget.condition.data.isEmpty) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Text(
          'Không có điều kiện ủy quyền',
          style:
              getTypoSkin().label4Regular.copyWith(color: getColorSkin().ink3),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...widget.condition.data.map((condition) {
          return Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 12.h),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: getColorSkin().ink5),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InfoRow(
                  maxLines  : 2,
                  label: 'Điều kiện ủy quyền',
                  value: condition.sltDieuKienUyQuyenText,
                  textAlign: TextAlign.left,
                ),
                SizedBox(height: 8.h),
                InfoRow(
                  maxLines: 2,
                  label: 'Điều kiện so sánh',
                  value: condition.sltDieuKienSoSanhText,
                  textAlign: TextAlign.left,
                ),
                SizedBox(height: 8.h),
                InfoRow(
                  maxLines: 2,
                  label: 'Giá trị ủy quyền',
                  value: condition.txtGiaTriUyQuyen,
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
}
