part of 'check_type_bloc.dart';

class CheckTypeResponseModel {
  final int processType;
  final bool informTo;
  final List<SignFormModel> listSignForm;
  final List<UserTaskModel> listUserTask;

  const CheckTypeResponseModel({
    required this.processType,
    required this.informTo,
    required this.listSignForm,
    required this.listUserTask,
  });

  factory CheckTypeResponseModel.fromJson(Map<String, dynamic> json) {
    return CheckTypeResponseModel(
      processType: json['processType'] ?? 0,
      informTo: json['informTo'] ?? false,
      listSignForm: (json['listSignForm'] as List?)
          ?.map((e) => SignFormModel.fromJson(e))
          .toList() ?? [],
      listUserTask: (json['listUserTask'] as List?)
          ?.map((e) => UserTaskModel.fromJson(e))
          .toList() ?? [],
    );
  }
}

class SignFormModel {
  final String taskDefKey;
  final int printType;
  final String? pdfContent;
  final String templateName;
  final int cfId;
  final String conditionText;
  final String uploadWordsChange;
  final int id;
  final String? content;
  final String status;
  final String uploadWords;

  const SignFormModel({
    required this.taskDefKey,
    required this.printType,
    this.pdfContent,
    required this.templateName,
    required this.cfId,
    required this.conditionText,
    required this.uploadWordsChange,
    required this.id,
    this.content,
    required this.status,
    required this.uploadWords,
  });

  factory SignFormModel.fromJson(Map<String, dynamic> json) {
    return SignFormModel(
      taskDefKey: json['taskDefKey'] ?? '',
      printType: json['printType'] ?? 0,
      pdfContent: json['pdfContent'],
      templateName: json['templateName'] ?? '',
      cfId: json['cf_id'] ?? 0,
      conditionText: json['conditionText'] ?? '',
      uploadWordsChange: json['uploadWordsChange'] ?? '',
      id: json['id'] ?? 0,
      content: json['content'],
      status: json['status'] ?? '',
      uploadWords: json['uploadWords'] ?? '',
    );
  }
}

class UserTaskModel {
  final String taskDefKey;
  final String zoomeField;
  final String taskName;

  const UserTaskModel({
    required this.taskDefKey,
    required this.zoomeField,
    required this.taskName,
  });

  factory UserTaskModel.fromJson(Map<String, dynamic> json) {
    return UserTaskModel(
      taskDefKey: json['taskDefKey'] ?? '',
      zoomeField: json['zoomeField'] ?? '',
      taskName: json['taskName'] ?? '',
    );
  }
}

abstract class CheckTypeState extends Equatable {
  const CheckTypeState();

  @override
  List<Object> get props => [];
}

class CheckTypeInitial extends CheckTypeState {}

class CheckTypeLoading extends CheckTypeState {}

class CheckTypeSuccess extends CheckTypeState {
  final CheckTypeResponseModel response;

  const CheckTypeSuccess(this.response);

  @override
  List<Object> get props => [response];
}

class CheckTypeFailure extends CheckTypeState {
  final String error;

  const CheckTypeFailure(this.error);

  @override
  List<Object> get props => [error];
} 