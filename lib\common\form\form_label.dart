import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FormLabel extends StatelessWidget {
  final String? displayName;
  final String? label;
  final String? suggestText;
  final bool isRequired;
  final VoidCallback? onSuggestTap;

  const FormLabel({
    super.key,
    this.displayName,
    this.label,
    this.suggestText,
    this.isRequired = false,
    this.onSuggestTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(),
      child: Row(
        children: [
          Expanded(
            child: RichText(
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              softWrap: true,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: displayName != null && displayName!.isNotEmpty
                        ? displayName
                        : label,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                  ),
                  if (isRequired)
                    const TextSpan(
                      text: ' *',
                      style: TextStyle(color: Colors.red),
                    ),
                  if (suggestText != null && suggestText!.isNotEmpty)
                    WidgetSpan(
                      child: Padding(
                        padding: EdgeInsets.only(left: 4.w),
                        child: GestureDetector(
                          onTap: onSuggestTap ?? () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text(suggestText!)),
                            );
                          },
                          child: const Icon(Icons.help_outline, size: 20),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 