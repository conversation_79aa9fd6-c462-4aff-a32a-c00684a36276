import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';

class TextAreaWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;

  const TextAreaWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
  }) : super(key: key);

  @override
  State<TextAreaWidget> createState() => _TextAreaWidgetState();
}

class _TextAreaWidgetState extends State<TextAreaWidget> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller.text = widget.data.value?.toString() ?? '';
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(TextAreaWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.data.value?.toString() != _controller.text) {
      _controller.text = widget.data.value?.toString() ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final labelWidget = Text(
      widget.data.label ?? '',
      style: TextStyle(
        fontWeight: widget.data.fontWeight == 'bold'
            ? FontWeight.bold
            : FontWeight.normal,
        fontStyle: widget.data.fontWeight == 'italic'
            ? FontStyle.italic
            : FontStyle.normal,
        decoration: widget.data.fontWeight == 'underline'
            ? TextDecoration.underline
            : TextDecoration.none,
      ),
    );
    return _buildTextFormField();
    // // Check if isHorizontal is a double and convert to bool, or use directly if it's already bool
    // bool isHorizontalBool = false;
    // if (widget.data.isHorizontal != null) {
    //   isHorizontalBool = widget.data.isHorizontal is double
    //       ? widget.data.isHorizontal == 2.0
    //       : widget.data.isHorizontal == true;
    // }

    // return Padding(
    //   padding: const EdgeInsets.only(bottom: 16.0),
    //   child: _buildTextFormField(),
    // );
  }

  Widget _buildTextFormField() {
    return TextFormField(
      controller: _controller,
      maxLines: 5,
      decoration: InputDecoration(
        hintText: widget.data.placeholder,
        labelText: widget.data.label,
        errorText: widget.data.error,
        filled: true,
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 12.h,
        ),
        suffixIcon: !widget.data.readonly!
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _controller.clear();
                  // widget.onChange(widget.data.name ?? '', '');
                  Future.microtask(() {
                    widget.onChange?.call(widget.data.name ?? '', '');
                  });
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
        ),
      ),
      onChanged: (value) {
        Future.microtask(() {
          widget.onChange?.call(widget.data.name ?? '', value);
        });
      },
      enabled: !widget.data.readonly!,
      textInputAction: TextInputAction.newline,
      autofocus: false,
    );
  }
}
