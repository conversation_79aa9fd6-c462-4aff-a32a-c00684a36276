import 'package:eapprove/screens/pyc/widgets/status_card_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class StatusCard extends StatelessWidget {
  final RequestStatusCardModel card;
  final Function()? onTap;

  const StatusCard({
    super.key,
    required this.card,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              card.title,
              style: getTypoSkin().regular12.copyWith(
                    color: getColorSkin().ink2,
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                FIcon(
                  icon: card.icon,
                  width: 36.w,
                  height: 36.h,
                ),
                SizedBox(width: 8.w),
                Text(
                  card.count.toString(),
                  style: getTypoSkin().medium20.copyWith(
                        color: getColorSkin().ink1,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
