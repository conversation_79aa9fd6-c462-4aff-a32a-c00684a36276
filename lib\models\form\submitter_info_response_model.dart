import 'dart:convert';

class SubmitterInfoResponseModel {
  final int? code;
  final String? message; 
  final List<WorkflowDefinition> data;

  const SubmitterInfoResponseModel({
    this.code,
    this.message,
    required this.data,
  });

  factory SubmitterInfoResponseModel.fromJson(Map<String, dynamic> json) {
    return SubmitterInfoResponseModel(
      code: json['code'] != null ? int.tryParse(json['code'].toString()) : null,
      message: json['message']?.toString(),
      data: (json['data'] as List<dynamic>? ?? [])
          .map((item) => WorkflowDefinition.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  factory SubmitterInfoResponseModel.fromJsonString(String jsonString) {
    return SubmitterInfoResponseModel.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }

  bool get isSuccess => code == 1;
}

class WorkflowDefinition {
  final String? id;
  final String? procDefId;
  final String? name;
  final List<ServiceNamePopUp> serviceNamePopUp;
  final String? serviceCount;
  final String? serviceName;
  final String? description;
  final String? key;
  final String? deploymentId;
  final String? resourceName;
  final String? bytes;
  final String? fileXml;
  final String? prioritized;
  final String? userCreated;
  final String? status;
  final List<Owner> owners;
  final String? createdDate;
  final String? createdUser;
  final String? updatedDate;
  final String? userUpdate;
  final String? autoClose;
  final String? autoCancel;
  final dynamic blockLocation;
  final bool? stepByStepResultForCreate;
  final bool? inFormTo;
  final bool? location;
  final bool? update;
  final bool? requestUpdate;
  final bool? createNewAndDouble;
  final bool? autoInherits;
  final List<dynamic> bpmProcdefInherits;
  final List<dynamic> bpmProcdefNotifications;
  final List<dynamic> bpmProcdefApiDtos;
  final int? priorityId;
  final bool? cancel;
  final bool? isAssistant;
  final bool? isEditAssistant;
  final bool? isAutoCancel;
  final bool? hideInfo;
  final bool? showInfo;
  final bool? additionalRequest;
  final String? cancelTasks;
  final dynamic listCancelTasks;
  final List<String> listShowInfoTasks;
  final dynamic listHideInfoTasks;
  final List<dynamic> shareWith;
  final dynamic listHideRelatedTicketValue;
  final bool? hideRelatedTicket;
  final dynamic listChangeImplementerValue;
  final dynamic listAuthorityOnTicketValue;
  final dynamic authorityOnTicket;
  final dynamic listAuthorityOnTicketStep;
  final bool? offNotification;
  final List<String> applyFor;
  final bool? recall;
  final bool? showInputTask;
  final dynamic lstShowInputTaskDefKeys;
  final dynamic listHideRuTasks;
  final bool? hideInherit;
  final List<String> listHideInheritTasks;
  final dynamic hideComment;
  final dynamic listHideCommentTasks;
  final dynamic hideDownload;
  final dynamic listHideDownloadTasks;
  final dynamic hideShareTicket;
  final List<dynamic> bpmProcdefViewFileApis;
  final dynamic specialFlow;
  final dynamic specialParentId;
  final dynamic specialCompanyCode;
  final dynamic listSpecialFormKey;
  final bool? autoCompleteTask;
  final bool? disableApprovedTicket;
  final bool? warningApprovedTicket;
  final dynamic listWarningApprovedTicketTasks;
  final dynamic listDisabledApprovedTicketTasks;

  const WorkflowDefinition({
    this.id,
    this.procDefId,
    this.name,
    required this.serviceNamePopUp,
    this.serviceCount,
    this.serviceName,
    this.description,
    this.key,
    this.deploymentId,
    this.resourceName,
    this.bytes,
    this.fileXml,
    this.prioritized,
    this.userCreated,
    this.status,
    required this.owners,
    this.createdDate,
    this.createdUser,
    this.updatedDate,
    this.userUpdate,
    this.autoClose,
    this.autoCancel,
    this.blockLocation,
    this.stepByStepResultForCreate,
    this.inFormTo,
    this.location,
    this.update,
    this.requestUpdate,
    this.createNewAndDouble,
    this.autoInherits,
    required this.bpmProcdefInherits,
    required this.bpmProcdefNotifications,
    required this.bpmProcdefApiDtos,
    this.priorityId,
    this.cancel,
    this.isAssistant,
    this.isEditAssistant,
    this.isAutoCancel,
    this.hideInfo,
    this.showInfo,
    this.additionalRequest,
    this.cancelTasks,
    this.listCancelTasks,
    required this.listShowInfoTasks,
    this.listHideInfoTasks,
    required this.shareWith,
    this.listHideRelatedTicketValue,
    this.hideRelatedTicket,
    this.listChangeImplementerValue,
    this.listAuthorityOnTicketValue,
    this.authorityOnTicket,
    this.listAuthorityOnTicketStep,
    this.offNotification,
    required this.applyFor,
    this.recall,
    this.showInputTask,
    this.lstShowInputTaskDefKeys,
    this.listHideRuTasks,
    this.hideInherit,
    required this.listHideInheritTasks,
    this.hideComment,
    this.listHideCommentTasks,
    this.hideDownload,
    this.listHideDownloadTasks,
    this.hideShareTicket,
    required this.bpmProcdefViewFileApis,
    this.specialFlow,
    this.specialParentId,
    this.specialCompanyCode,
    this.listSpecialFormKey,
    this.autoCompleteTask,
    this.disableApprovedTicket,
    this.warningApprovedTicket,
    this.listWarningApprovedTicketTasks,
    this.listDisabledApprovedTicketTasks,
  });

  factory WorkflowDefinition.fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert to boolean
    bool safeBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is String) {
        return value.toLowerCase() == 'true';
      }
      if (value is int) {
        return value != 0;
      }
      return false;
    }

    // Helper function to safely convert to string
    String safeString(dynamic value) {
      if (value == null) return '';
      if (value is String) return value;
      return value.toString();
    }

    return WorkflowDefinition(
      id: safeString(json['id']),
      procDefId: safeString(json['procDefId']),
      name: safeString(json['name']),
      serviceNamePopUp: (json['serviceNamePopUp'] as List<dynamic>?)
          ?.map((item) => ServiceNamePopUp.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      serviceCount: safeString(json['serviceCount']),
      serviceName: json['serviceName'] != null ? safeString(json['serviceName']) : null,
      description: json['description'] != null ? safeString(json['description']) : null,
      key: safeString(json['key']),
      deploymentId: safeString(json['deploymentId']),
      resourceName: safeString(json['resourceName']),
      bytes: safeString(json['bytes']),
      fileXml: safeString(json['fileXml']),
      prioritized: safeString(json['prioritized']),
      userCreated: safeString(json['userCreated']),
      status: safeString(json['status']),
      owners: (json['owners'] as List<dynamic>?)
          ?.map((item) => Owner.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      createdDate: safeString(json['createdDate']),
      createdUser: json['createdUser'] != null ? safeString(json['createdUser']) : null,
      updatedDate: safeString(json['updatedDate']),
      userUpdate: safeString(json['userUpdate']),
      autoClose: safeString(json['autoClose']),
      autoCancel: json['autoCancel'] != null ? safeString(json['autoCancel']) : null,
      blockLocation: json['blockLocation'],
      stepByStepResultForCreate: safeBool(json['stepByStepResultForCreate']),
      inFormTo: safeBool(json['inFormTo']),
      location: safeBool(json['location']),
      update: safeBool(json['update']),
      requestUpdate: safeBool(json['requestUpdate']),
      createNewAndDouble: safeBool(json['createNewAndDouble']),
      autoInherits: safeBool(json['autoInherits']),
      bpmProcdefInherits: (json['bpmProcdefInherits'] as List<dynamic>?) ?? [],
      bpmProcdefNotifications: (json['bpmProcdefNotifications'] as List<dynamic>?) ?? [],
      bpmProcdefApiDtos: (json['bpmProcdefApiDtos'] as List<dynamic>?) ?? [],
      priorityId: json['priorityId'] is int ? json['priorityId'] as int : 0,
      cancel: safeBool(json['cancel']),
      isAssistant: safeBool(json['isAssistant']),
      isEditAssistant: safeBool(json['isEditAssistant']),
      isAutoCancel: safeBool(json['isAutoCancel']),
      hideInfo: safeBool(json['hideInfo']),
      showInfo: safeBool(json['showInfo']),
      additionalRequest: safeBool(json['additionalRequest']),
      cancelTasks: safeString(json['cancelTasks']),
      listCancelTasks: json['listCancelTasks'],
      listShowInfoTasks: (json['listShowInfoTasks'] as List<dynamic>?)
          ?.map((item) => safeString(item))
          .toList() ?? [],
      listHideInfoTasks: json['listHideInfoTasks'],
      shareWith: (json['shareWith'] as List<dynamic>?) ?? [],
      listHideRelatedTicketValue: json['listHideRelatedTicketValue'],
      hideRelatedTicket: safeBool(json['hideRelatedTicket']),
      listChangeImplementerValue: json['listChangeImplementerValue'],
      listAuthorityOnTicketValue: json['listAuthorityOnTicketValue'],
      authorityOnTicket: json['authorityOnTicket'],
      listAuthorityOnTicketStep: json['listAuthorityOnTicketStep'],
      offNotification: safeBool(json['offNotification']),
      applyFor: (json['applyFor'] as List<dynamic>?)
          ?.map((item) => safeString(item))
          .toList() ?? [],
      recall: safeBool(json['recall']),
      showInputTask: safeBool(json['showInputTask']),
      lstShowInputTaskDefKeys: json['lstShowInputTaskDefKeys'],
      listHideRuTasks: json['listHideRuTasks'],
      hideInherit: safeBool(json['hideInherit']),
      listHideInheritTasks: (json['listHideInheritTasks'] as List<dynamic>?)
          ?.map((item) => safeString(item))
          .toList() ?? [],
      hideComment: json['hideComment'],
      listHideCommentTasks: json['listHideCommentTasks'],
      hideDownload: json['hideDownload'],
      listHideDownloadTasks: json['listHideDownloadTasks'],
      hideShareTicket: json['hideShareTicket'],
      bpmProcdefViewFileApis: (json['bpmProcdefViewFileApis'] as List<dynamic>?) ?? [],
      specialFlow: json['specialFlow'],
      specialParentId: json['specialParentId'],
      specialCompanyCode: json['specialCompanyCode'],
      listSpecialFormKey: json['listSpecialFormKey'],
      autoCompleteTask: safeBool(json['autoCompleteTask']),
      disableApprovedTicket: safeBool(json['disableApprovedTicket']),
      warningApprovedTicket: safeBool(json['warningApprovedTicket']),
      listWarningApprovedTicketTasks: json['listWarningApprovedTicketTasks'],
      listDisabledApprovedTicketTasks: json['listDisabledApprovedTicketTasks'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'procDefId': procDefId,
       'name': name,
      'serviceNamePopUp': serviceNamePopUp.map((item) => item.toJson()).toList(),
      'serviceCount': serviceCount,
      'serviceName': serviceName,
      'description': description,
      'key': key,
      'deploymentId': deploymentId,
      'resourceName': resourceName,
      'bytes': bytes,
      'fileXml': fileXml,
      'prioritized': prioritized,
      'userCreated': userCreated,
      'status': status,
      'owners': owners.map((item) => item.toJson()).toList(),
      'createdDate': createdDate,
      'createdUser': createdUser,
      'updatedDate': updatedDate,
      'userUpdate': userUpdate,
      'autoClose': autoClose,
      'autoCancel': autoCancel,
      'blockLocation': blockLocation,
      'stepByStepResultForCreate': stepByStepResultForCreate,
      'inFormTo': inFormTo,
      'location': location,
      'update': update,
      'requestUpdate': requestUpdate,
      'createNewAndDouble': createNewAndDouble,
      'autoInherits': autoInherits,
      'bpmProcdefInherits': bpmProcdefInherits,
      'bpmProcdefNotifications': bpmProcdefNotifications,
      'bpmProcdefApiDtos': bpmProcdefApiDtos,
      'priorityId': priorityId,
      'cancel': cancel,
      'isAssistant': isAssistant,
      'isEditAssistant': isEditAssistant,
      'isAutoCancel': isAutoCancel,
      'hideInfo': hideInfo,
      'showInfo': showInfo,
      'additionalRequest': additionalRequest,
      'cancelTasks': cancelTasks,
      'listCancelTasks': listCancelTasks,
      'listShowInfoTasks': listShowInfoTasks,
      'listHideInfoTasks': listHideInfoTasks,
      'shareWith': shareWith,
      'listHideRelatedTicketValue': listHideRelatedTicketValue,
      'hideRelatedTicket': hideRelatedTicket,
      'listChangeImplementerValue': listChangeImplementerValue,
      'listAuthorityOnTicketValue': listAuthorityOnTicketValue,
      'authorityOnTicket': authorityOnTicket,
      'listAuthorityOnTicketStep': listAuthorityOnTicketStep,
      'offNotification': offNotification,
      'applyFor': applyFor,
      'recall': recall,
      'showInputTask': showInputTask,
      'lstShowInputTaskDefKeys': lstShowInputTaskDefKeys,
      'listHideRuTasks': listHideRuTasks,
      'hideInherit': hideInherit,
      'listHideInheritTasks': listHideInheritTasks,
      'hideComment': hideComment,
      'listHideCommentTasks': listHideCommentTasks,
      'hideDownload': hideDownload,
      'listHideDownloadTasks': listHideDownloadTasks,
      'hideShareTicket': hideShareTicket,
      'bpmProcdefViewFileApis': bpmProcdefViewFileApis,
      'specialFlow': specialFlow,
      'specialParentId': specialParentId,
      'specialCompanyCode': specialCompanyCode,
      'listSpecialFormKey': listSpecialFormKey,
      'autoCompleteTask': autoCompleteTask,
      'disableApprovedTicket': disableApprovedTicket,
      'warningApprovedTicket': warningApprovedTicket,
      'listWarningApprovedTicketTasks': listWarningApprovedTicketTasks,
      'listDisabledApprovedTicketTasks': listDisabledApprovedTicketTasks,
    };
  }
}

class ServiceNamePopUp {
  final String? id;
  final String? serviceName;
  final int? submissionType;

  const ServiceNamePopUp({
    this.id,
    this.serviceName,
    this.submissionType,
  });

  factory ServiceNamePopUp.fromJson(Map<String, dynamic> json) {
    // Safely handle submissionType conversion
    final dynamic submissionTypeValue = json['submissionType'];
    int submissionType;
    
    if (submissionTypeValue == null) {
      submissionType = 0; // Default value if null
    } else if (submissionTypeValue is int) {
      submissionType = submissionTypeValue;
    } else if (submissionTypeValue is String) {
      submissionType = int.tryParse(submissionTypeValue) ?? 0;
    } else {
      submissionType = 0; // Default value for other types
    }

    return ServiceNamePopUp(
      id: json['id'] as String,
      serviceName: json['serviceName'] as String,
      submissionType: submissionType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'serviceName': serviceName,
      'submissionType': submissionType,
    };
  }
}

class Owner {
  final int? id;
  final String? name;

  const Owner({
    this.id,
    this.name,
  });

  factory Owner.fromJson(Map<String, dynamic> json) {
    return Owner(
      id: json['id'] != null ? int.tryParse(json['id'].toString()) : null,
      name: json['name']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}