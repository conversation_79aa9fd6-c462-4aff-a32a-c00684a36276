import 'package:equatable/equatable.dart';

abstract class AttachmentListEvent extends Equatable {
  const AttachmentListEvent();

  @override
  List<Object?> get props => [];
}

class GetAttachmentList extends AttachmentListEvent {
  final int ticketId;
  final String? ticketStartUserId;

  const GetAttachmentList({
    required this.ticketId, 
    this.ticketStartUserId,
  });

  @override
  List<Object?> get props => [ticketId];
}

class NextPage extends AttachmentListEvent {
  final String procInstId;
  final int ticketId;

  const NextPage({
    required this.procInstId,
    required this.ticketId,
  }); 

  @override
  List<Object?> get props => [procInstId, ticketId];
} 

class ResetPage extends AttachmentListEvent {
  final String procInstId;
  final int ticketId;
  
  const ResetPage({
    required this.procInstId,
    required this.ticketId,
  });

  @override
  List<Object?> get props => [procInstId, ticketId];
}

class InitialAttachmentList extends AttachmentListEvent {
  @override
  List<Object?> get props => [];
}
