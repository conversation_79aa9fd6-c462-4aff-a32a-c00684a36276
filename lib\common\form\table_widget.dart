// import 'dart:convert';

// import 'package:eapprove/common/form/checkbox_widget.dart';
// import 'package:eapprove/common/form/date_picker_widget.dart';
// import 'package:eapprove/common/form/radio_button_widget.dart';
// import 'package:eapprove/common/form/table_cell_widget.dart';
// import 'package:eapprove/common/form/text_area_widget.dart';
// import 'package:eapprove/common/form/text_input_widget.dart';
// import 'package:eapprove/common/form_v2/form_state_manager.dart';
// import 'package:eapprove/enum/enum.dart';
// import 'package:eapprove/models/form/form_item_info.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_sdk/utils/theme_utils.dart';
// import 'dart:developer' as developer;
// class TableWidget extends StatefulWidget {
//   final FormItemInfo data;
//   final Function(String, dynamic)? onChange;
//   final FormStateManager stateManager;

//   const TableWidget({
//     Key? key,
//     required this.data,
//     this.onChange,
//     required this.stateManager,
//   }) : super(key: key);

//   @override
//   State<TableWidget> createState() => _TableWidgetState();
// }

// class _TableWidgetState extends State<TableWidget> {
//   List<Map<String, dynamic>> _tableData = [];
//   List<TableColumn> _columns = [];
//   final TextEditingController _searchController = TextEditingController();
//   String _searchText = '';

//   @override
//   void initState() {
//     super.initState();
//     _initializeTableData();
//     _searchController.addListener(() {
//       setState(() {
//         _searchText = _searchController.text;
//       });
//     });
//     debugPrint("TableWidget initialized with data: ${widget.data.name}");
//   }

//   @override
//   void didUpdateWidget(TableWidget oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.data != widget.data) {
//       _initializeTableData();
//     }
//   }

//   @override
//   void dispose() {
//     _searchController.dispose();
//     super.dispose();
//   }

//   void _initializeTableData() {
//     debugPrint("Initializing table data for: ${widget.data.name}");

//     // Parse columns configuration
//     _parseColumns();

//     // Initialize table data
//     if (widget.data.value != null) {
//       try {
//         if (widget.data.value is String) {
//           String valueStr = widget.data.value.toString().trim();
//           if (valueStr.isEmpty) {
//             _tableData = [];
//           } else {
//             _tableData = List<Map<String, dynamic>>.from(json.decode(valueStr));
//           }
//         } else if (widget.data.value is List) {
//           _tableData = List<Map<String, dynamic>>.from(widget.data.value);
//         } else {
//           _tableData = [];
//         }
//       } catch (e) {
//         debugPrint("Error parsing table data: $e");
//         _tableData = [];
//       }
//     } else {
//       _tableData = [];
//     }

//     // If table is empty, create a default row
//     if (_tableData.isEmpty) {
//       _tableData = [_createEmptyRow()];
//     }

//     debugPrint("Table data initialized with ${_tableData.length} rows and ${_columns.length} columns");
//   }

//   void _parseColumns() {
//     _columns = [];

//     // First, get the columns from the table configuration
//     if (widget.data.columns != null && widget.data.columns!.isNotEmpty) {
//       // Sort columns by colSortOrder
//       final sortedColumns = List<FormItemInfo>.from(widget.data.columns!);
//       sortedColumns.sort((a, b) => (a.colSortOrder ?? 0).compareTo(b.colSortOrder ?? 0));

//       // Debug each column
//       // for (var col in sortedColumns) {
//       //   debugPrint("Column: name=${col.name}, type=${col.type}, label=${col.label} ${col.hideColumn}");
//       // }

//       // Process each column
//       for (var col in sortedColumns) {
//         // Skip column placeholders (that don't have real form elements)
//         if (col.name == null || col.name!.isEmpty) continue;

//         // Determine if the column is hidden
//         // bool hideColumn = false;
//         // if (col.hideColumn == true || col.display == false) {
//         //   hideColumn = true;
//         // }

//         // developer.log('Column: id=${col.id}, colName=${col.col}, label=${col.label}, name=${col.name}, hideColumn=${col.hideColumn}', name: 'TableWidget');

//         _columns.add(TableColumn(
//           id: col.id ?? '',
//           colName: col.col ?? '',
//           label: col.label ?? '',
//           name: col.name ?? '',
//           formItem: col,
//           hideTitle: col.hideTableTitle == true,
//           hideColumn: col.hideColumn ?? false,
//         ));
//       }
//     }

   

//     // Check if we need to use optionConfig.tableColumns
//     if (_columns.isEmpty && widget.data.optionConfig != null) {
//       final tableColumns = widget.data.optionConfig!['tableColumns'];
//       if (tableColumns is List) {
//         for (var colConfig in tableColumns) {
//           // Skip invalid configurations
//           if (colConfig is! Map) continue;

//           String colId = colConfig['id']?.toString() ?? '';
//           String colName = colConfig['col']?.toString() ?? '';
//           String label = colConfig['label']?.toString() ?? '';
//           bool hideTitle = colConfig['hideTableTitle'] == true;
//           bool hideColumn = colConfig['hideColumn'] == true;

//           // Find the corresponding form item in columns
//           FormItemInfo? formItem;
//           if (widget.data.columns != null) {
//             formItem = widget.data.columns!.firstWhere(
//                   (item) => item.col == colName || item.id == colId,
//               orElse: () => FormItemInfo(
//                 label: label,
//                 type: FormItemType.input,
//               ),
//             );
//           }

//           _columns.add(TableColumn(
//             id: colId,
//             colName: colName,
//             label: label,
//             name: colConfig['name']?.toString() ?? '',
//             formItem: formItem,
//             hideTitle: hideTitle,
//             hideColumn: hideColumn,
//           ));
//         }
//       }
//     }

//     // If still no columns, create default ones
//     if (_columns.isEmpty) {
//       _columns = [
//         TableColumn(
//           id: 'default',
//           colName: 'default',
//           label: 'Default',
//           name: 'default',
//           formItem: null,
//           hideTitle: false,
//           hideColumn: false,
//         ),
//       ];
//     }
// //  developer.log('_columns_columns: ${_columns.map((e) => {
// //       'id': e.id,
// //       'colName': e.colName,
// //       'label': e.label,
// //       'name': e.name,
// //       'hideColumn': e.hideColumn,
// //     }).toList()}', name: 'TableWidget');
// //     debugPrint("Parsed ${_columns.length} columns");
//   }

//   Map<String, dynamic> _createEmptyRow() {
//     Map<String, dynamic> emptyRow = {};

//     // Initialize with empty values for each column
//     for (var column in _columns) {
//       emptyRow[column.name] = '';
//     }

//     return emptyRow;
//   }

//   void _handleAddRow() {
//     setState(() {
//       _tableData.add(_createEmptyRow());
//     });

//     _updateParentValue();
//   }

//   void _handleDeleteRow(int index) {
//     if (index < 0 || index >= _tableData.length) return;

//     setState(() {
//       _tableData.removeAt(index);
//     });

//     // If no rows left, add an empty one
//     if (_tableData.isEmpty) {
//       setState(() {
//         _tableData.add(_createEmptyRow());
//       });
//     }

//     _updateParentValue();
//   }

//   void _updateCellValue(int rowIndex, String columnName, dynamic value) {
//     debugPrint("Updating cell: row=$rowIndex, column=$columnName, value=$value");

//     if (rowIndex < 0 || rowIndex >= _tableData.length) return;

//     setState(() {
//       _tableData[rowIndex][columnName] = value;
//     });

//     _updateParentValue();
//   }

//   void _updateParentValue() {
//     widget.data.value = _tableData;
//     widget.onChange?.call(widget.data.name ?? '', _tableData);
//   }

//   @override
//   Widget build(BuildContext context) {
    

//     return Container(
//       margin: EdgeInsets.only(bottom: 16.h),
//       alignment: Alignment.centerLeft,
//       width: double.infinity,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           // Table header
//           // _buildTableHeader(),

//           // Table content
//           Container(
//             alignment: Alignment.centerLeft,
//             child: SingleChildScrollView(
//               scrollDirection: Axis.horizontal,
//               child: _buildTableContent(),
//             ),
//           ),

//           // Add row button
//           if (widget.data.isHideAddColumnTable != true && widget.data.readonly != true)
//             _buildTableActions(),
//         ],
//       ),
//     );
//   }

//   Widget _buildTableHeader() {
//     return Row(
//       children: [
//         Expanded(
//           child: Text(
//             widget.data.label ?? 'Table',
//             style: getTypoSkin().bodyRegular14.copyWith(
//               color: getColorSkin().ink1,
//               fontWeight: FontWeight.w500,
//             ),
//           ),
//         ),
//         //
//         // // Search field if enabled
//         // if (widget.data.isHideSearchTable != true)
//         //   SizedBox(
//         //     width: 200.w,
//         //     child: TextField(
//         //       controller: _searchController,
//         //       decoration: InputDecoration(
//         //         hintText: 'Search...',
//         //         prefixIcon: Icon(Icons.search, size: 16.sp),
//         //         contentPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
//         //         border: OutlineInputBorder(
//         //           borderRadius: BorderRadius.circular(4.r),
//         //         ),
//         //       ),
//         //       style: TextStyle(fontSize: 12.sp),
//         //     ),
//         //   ),
//       ],
//     );
//   }

//   Widget _buildTableContent() {
//     // Filter visible columns
//     final visibleColumns = _columns.where((col) => !col.hideColumn && !col.hideTitle).toList();

//     // If no columns to display, show empty state
//     if (visibleColumns.isEmpty) {
//       return _buildEmptyState("No columns configured");
//     }

//     // If table is empty, show empty state
//     if (_tableData.isEmpty) {
//       return _buildEmptyState("No data");
//     }

//     // Filter data based on search
//     final filteredData = _searchText.isEmpty
//         ? _tableData
//         : _tableData.where((row) {
//       return row.values.any((value) {
//         return value != null &&
//             value.toString().toLowerCase().contains(_searchText.toLowerCase());
//       });
//     }).toList();

//     // If no data after filtering, show empty search result
//     if (filteredData.isEmpty) {
//       return _buildEmptyState("No matching results");
//     }

//     // developer.log('TableWidgetTableWidget: ${visibleColumns.map((e) => e.name).toList()}', name: 'TableWidget');
//     // Build the table using Table widget for consistent spacing
//     return Table(
//       border: TableBorder.all(color: getColorSkin().grey2),
//       columnWidths: _getColumnWidths(visibleColumns),
//       defaultVerticalAlignment: TableCellVerticalAlignment.middle,
//       children: [
//         // Header row
//         if (!visibleColumns.every((col) => col.hideTitle))
//           TableRow(
//             decoration: BoxDecoration(
//               color: Colors.grey.shade100,
//             ),
//             children: [
//               // STT column (row number) if not hidden
//               if (widget.data.isHideStt != true)
//                 TableCell(
//                   child: Padding(
//                     padding: EdgeInsets.all(8.w),
//                     child: Text(
//                       'STT',
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: 14.sp,
//                         color: getColorSkin().ink1,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                 ),

//               // Column headers
//               ...visibleColumns.map((column) {
//                 // Skip hidden title columns
//                 if (column.hideTitle) return const SizedBox.shrink();

//                 return TableCell(
//                   child: Padding(
//                     padding: EdgeInsets.all(8.w),
//                     child: Text(
//                       column.label,
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: 14.sp,
//                         color: getColorSkin().ink1,
//                       ),
//                     ),
//                   ),
//                 );
//               }).toList(),

//               // Action column (delete button)
//               if (widget.data.isHideDeleteColumnTable != true)
//                 TableCell(
//                   child: Center(
//                     child: Text(
//                       '',
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: 14.sp,
//                       ),
//                     ),
//                   ),
//                 ),
//             ],
//           ),

//         // Data rows
//         ...filteredData.asMap().entries.map((entry) {
//           final rowIndex = entry.key;
//           final rowData = entry.value;

//           return TableRow(
//             decoration: BoxDecoration(
//               color: rowIndex.isEven ? Colors.white : Colors.grey.shade50,
//             ),
//             children: [
//               // STT column (row number) if not hidden
//               if (widget.data.isHideStt != true)
//                 TableCell(
//                   child: Padding(
//                     padding: EdgeInsets.all(8.w),
//                     child: Text(
//                       '${rowIndex + 1}',
//                       style: TextStyle(
//                         fontSize: 14.sp,
//                         color: getColorSkin().ink1,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                 ),

//               // Data cells
//               ...visibleColumns.map((column) {
//                 return TableCell(
//                   verticalAlignment: TableCellVerticalAlignment.middle,
//                   child: Padding(
//                     padding: EdgeInsets.all(4.w),
//                     child: _buildCellWidget(rowIndex, column, rowData),
//                   ),
//                 );
//               }).toList(),

//               // Delete row button
//               if (widget.data.isHideDeleteColumnTable != true)
//                 TableCell(
//                   child: Center(
//                     child: IconButton(
//                       icon: Icon(
//                         Icons.delete_outline,
//                         color: Colors.red,
//                         size: 18.sp,
//                       ),
//                       onPressed: widget.data.readonly == true
//                           ? null
//                           : () => _handleDeleteRow(rowIndex),
//                       constraints: BoxConstraints(
//                         minWidth: 30.w,
//                         minHeight: 30.h,
//                       ),
//                       padding: EdgeInsets.zero,
//                     ),
//                   ),
//                 ),
//             ],
//           );
//         }).toList(),
//       ],
//     );
//   }

//   Map<int, TableColumnWidth> _getColumnWidths(List<TableColumn> visibleColumns) {
//     Map<int, TableColumnWidth> columnWidths = {};

//     // Add STT column width if not hidden
//     if (widget.data.isHideStt != true) {
//       columnWidths[0] = FixedColumnWidth(50.w);
//     }

//     // Add column widths for each visible column
//     int columnIndex = widget.data.isHideStt != true ? 1 : 0;
//     for (var column in visibleColumns) {
//       // Get width from column config or use default
//       double width = 200.w;

//       // Try to get width from formItem
//       if (column.formItem != null && column.formItem!.width != null) {
//         try {
//           width = double.parse(column.formItem!.width!) * 1.w;
//         } catch (e) {
//           // Use default if parsing fails
//         }
//       }

//       columnWidths[columnIndex] = FixedColumnWidth(width);
//       columnIndex++;
//     }

//     // Add action column width if delete button is shown
//     if (widget.data.isHideDeleteColumnTable != true) {
//       columnWidths[columnIndex] = FixedColumnWidth(50.w);
//     }

//     return columnWidths;
//   }

//   Widget _buildCellWidget(int rowIndex, TableColumn column, Map<String, dynamic> rowData) {
//     // Add debug information about the cell being rendered
//     debugPrint('Building cell: row=$rowIndex, column=${column.name}, formItemType=${column.formItem?.type}');
//     debugPrint('Cell value: ${rowData[column.name]}');

//     // If column has a form item, use it to create the cell widget
//     if (column.formItem != null) {
//       // Create a copy of the form item with the current value
//       final cellFormItem = FormItemInfo.fromJson(column.formItem!.toJson());
//       cellFormItem.value = rowData[column.name];
//       cellFormItem.readonly = widget.data.readonly;

//       debugPrint('Cell widget type: ${cellFormItem.type}, readonly: ${cellFormItem.readonly}');

//       // Some components need special handling or don't work well in table cells
//       switch (cellFormItem.type) {
//         case FormItemType.input:
//           debugPrint('Rendering INPUT cell with inputType: ${cellFormItem.inputType}');
//           return InputTextWidget(
//             data: cellFormItem,
//             onChange: (_, value) => _updateCellValue(rowIndex, column.name, value),
//             stateManager: widget.stateManager,
//             );

//         case FormItemType.dropdown:
//           debugPrint('Rendering DROPDOWN cell with ${cellFormItem} options');
//           // Use compact dropdown for better table cell fit
//           return SizedBox(
//             height: 38.h,
//             child: DropdownButtonFormField<String>(
//               value: cellFormItem.value?.toString(),
//               items: _getDropdownItems(cellFormItem),
//               onChanged: cellFormItem.readonly == true
//                   ? null
//                   : (value) => _updateCellValue(rowIndex, column.name, value),
//               decoration: InputDecoration(
//                 contentPadding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
//                 isDense: true,
//                 border: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(4.r),
//                 ),
//                 filled: true,
//                 fillColor: cellFormItem.readonly == true
//                     ? getColorSkin().whiteSmoke
//                     : getColorSkin().white,
//               ),
//               isExpanded: true,
//               style: TextStyle(fontSize: 12.sp),
//             ),
//           );

//         case FormItemType.select:
//           debugPrint('Rendering SELECT cell with options count: ${cellFormItem.options?.length ?? 0}');
//           // Use specialized table cell select widget
//           return TableCellSelectWidget(
//             data: cellFormItem,
//             onChange: (_, value) => _updateCellValue(rowIndex, column.name, value),
//           );

//         case FormItemType.checkbox:
//           debugPrint('Rendering CHECKBOX cell with value: ${cellFormItem.value}');
//           return CheckboxWidget(
//             stateManager: widget.stateManager,
//             data: cellFormItem,
//             onChange: (_, value) => _updateCellValue(rowIndex, column.name, value),
//           );

//         case FormItemType.radioButton:
//           debugPrint('Rendering RADIO BUTTON cell');
//           return RadioButtonWidget(
//             stateManager: widget.stateManager,
//             data: cellFormItem,
//             onChange: (_, value) => _updateCellValue(rowIndex, column.name, value),
//           );

//         case FormItemType.datePicker:
//           debugPrint('Rendering DATE PICKER cell with value: ${cellFormItem.value}');
//           return DatePickerWidget(
//             stateManager: widget.stateManager,
//             data: cellFormItem,
//             onChange: (_, value) => _updateCellValue(rowIndex, column.name, value),
//           );

//         case FormItemType.textArea:
//           debugPrint('Rendering TEXT AREA cell');
//           return TextAreaWidget(
//             stateManager: widget.stateManager,
//             data: cellFormItem,
//             onChange: (_, value) => _updateCellValue(rowIndex, column.name, value),
//           );

//         default:
//           debugPrint('Rendering DEFAULT TEXT cell (unknown type: ${cellFormItem.type})');
//           // Default text display
//           return Padding(
//             padding: EdgeInsets.symmetric(vertical: 8.h),
//             child: Text(
//               rowData[column.name]?.toString() ?? '',
//               style: TextStyle(
//                 fontSize: 14.sp,
//                 color: getColorSkin().ink1,
//               ),
//             ),
//           );
//       }
//     }

//     debugPrint('Rendering plain text cell (no formItem)');
//     // Default text display if no form item
//     return Padding(
//       padding: EdgeInsets.symmetric(vertical: 8.h),
//       child: Text(
//         rowData[column.name]?.toString() ?? '',
//         style: TextStyle(
//           fontSize: 14.sp,
//           color: getColorSkin().ink1,
//         ),
//       ),
//     );
//   }

//   List<DropdownMenuItem<String>> _getDropdownItems(FormItemInfo formItem) {
//     List<Map<String, dynamic>> options = [];

//     // Combine options from both sources
//     if (formItem.options != null && formItem.options!.isNotEmpty) {
//       options.addAll(formItem.options!);
//     }

//     if (formItem.option != null && formItem.option!.isNotEmpty) {
//       options.addAll(formItem.option!);
//     }

//     return options.map<DropdownMenuItem<String>>((option) {
//       return DropdownMenuItem<String>(
//         value: option['value']?.toString() ?? '',
//         child: Text(
//           option['label']?.toString() ?? '',
//           style: TextStyle(fontSize: 12.sp),
//           overflow: TextOverflow.ellipsis,
//         ),
//       );
//     }).toList();
//   }

//   Widget _buildEmptyState(String message) {
//     return Container(
//       height: 120.h,
//       width: 300.w,
//       alignment: Alignment.center,
//       decoration: BoxDecoration(
//         border: Border.all(color: getColorSkin().grey2),
//       ),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(
//             Icons.table_chart_outlined,
//             size: 32.sp,
//             color: Colors.grey,
//           ),
//           SizedBox(height: 8.h),
//           Text(
//             message,
//             style: TextStyle(
//               color: Colors.grey,
//               fontSize: 14.sp,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildTableActions() {
//     return Padding(
//       padding: EdgeInsets.all(16.w),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.end,
//         children: [
//           ElevatedButton.icon(
//             onPressed: _handleAddRow,
//             icon: Icon(Icons.add, size: 16.sp),
//             label: Text(
//               'Thêm dòng',
//               style: TextStyle(fontSize: 12.sp),
//             ),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: getColorSkin().primaryBlue,
//               foregroundColor: Colors.white,
//               padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class TableColumn {
//   final String id;
//   final String colName;
//   final String label;
//   final String name;
//   final FormItemInfo? formItem;
//   final bool hideTitle;
//   final bool hideColumn;

//   TableColumn({
//     required this.id,
//     required this.colName,
//     required this.label,
//     required this.name,
//     this.formItem,
//     this.hideTitle = false,
//     this.hideColumn = false,
//   });
// }