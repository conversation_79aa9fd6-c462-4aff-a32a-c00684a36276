class TicketScreenModel {
  final String title;
  final List<String> tabTitles;

  TicketScreenModel({required this.title, required this.tabTitles});
}

final List<TicketScreenModel> ticketScreens = [
  TicketScreenModel(
    title: "Phiếu yêu cầu của tôi",
    tabTitles: [
      "<PERSON><PERSON> phê duyệt",
      "<PERSON>à<PERSON> thành",
      "<PERSON><PERSON><PERSON> về/<PERSON>hu hồi",
      "<PERSON><PERSON><PERSON>",
      "<PERSON><PERSON><PERSON><PERSON>",
      "<PERSON><PERSON><PERSON><PERSON> chia sẻ/theo dõi",
      "Đ<PERSON> chia sẻ",
      "<PERSON><PERSON> lọc"
    ],
  ),
  TicketScreenModel(
    title: "<PERSON>ếu yêu cầu thực hiện",
    tabTitles: ["<PERSON><PERSON> lý", "<PERSON><PERSON><PERSON> thành", "<PERSON><PERSON><PERSON> về/<PERSON>hu hồi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> lọc"],
  ),
  TicketScreenModel(
    title: "Phiếu yêu cầu phê duyệt",
    tabTitles: [
      "<PERSON><PERSON> chờ phê duyệt",
      "<PERSON><PERSON> phê duyệt",
      "<PERSON><PERSON><PERSON> về/<PERSON>hu hồi",
      "<PERSON><PERSON><PERSON>",
      "<PERSON><PERSON> lọc"
    ],
  ),
  TicketScreenModel(
    title: "<PERSON>ếu yêu c<PERSON>u tr<PERSON> lý",
    tab<PERSON>itles: ["X<PERSON> lý", "Hoàn thành", "H<PERSON>y", "<PERSON> dõi", "<PERSON>ộ lọc"],
  ),
];
