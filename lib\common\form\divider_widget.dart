//
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:eapprove/models/form/form_item_info.dart';
//
// class DividerWidget extends StatelessWidget {
//   final FormItemInfo data;
//   final Function(String, dynamic) onChange;
//
//   const DividerWidget({
//     Key? key,
//     required this.data,
//     required this.onChange,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     // Don't render if display is false
//     if (data.display == false) {
//       return const SizedBox.shrink();
//     }
//
//     // Get divider color - default to grey if not specified
//     Color dividerColor = Colors.grey.withOpacity(0.3);
//     if (data.color != null && data.color.isNotEmpty) {
//       try {
//         // Try to parse color from string if provided
//         dividerColor = Color(int.parse(data.color.replaceAll('#', '0xFF')));
//       } catch (e) {
//         // If parsing fails, keep default color
//         debugPrint('Failed to parse divider color: $e');
//       }
//     }
//
//     // Get divider thickness - default to 1.0 if not specified
//     double thickness = 1.0;
//     if (data.thickness != null) {
//       thickness = data.thickness is double
//           ? data.thickness
//           : double.tryParse(data.thickness.toString()) ?? 1.0;
//     }
//
//     // Get divider margin
//     EdgeInsets margin = EdgeInsets.symmetric(vertical: 16.h);
//     if (data.margin != null) {
//       // If margin is specified in the data, use it
//       try {
//         double verticalMargin = data.margin is double
//             ? data.margin
//             : double.tryParse(data.margin.toString()) ?? 16.0;
//         margin = EdgeInsets.symmetric(vertical: verticalMargin.h);
//       } catch (e) {
//         debugPrint('Failed to parse divider margin: $e');
//       }
//     }
//
//     // Check if this is a divider with label
//     bool hasLabel = data.label != null && data.label.isNotEmpty;
//
//     if (hasLabel) {
//       // Return divider with label
//       return Padding(
//         padding: margin,
//         child: Row(
//           children: [
//             Expanded(
//               child: Divider(
//                 color: dividerColor,
//                 thickness: thickness,
//               ),
//             ),
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 16.w),
//               child: Text(
//                 data.label,
//                 style: TextStyle(
//                   color: Colors.black54,
//                   fontSize: 14.sp,
//                   fontWeight: data.fontWeight == 'bold' ? FontWeight.bold : FontWeight.normal,
//                   fontStyle: data.fontWeight == 'italic' ? FontStyle.italic : FontStyle.normal,
//                   decoration: data.fontWeight == 'underline' ? TextDecoration.underline : TextDecoration.none,
//                 ),
//               ),
//             ),
//             Expanded(
//               child: Divider(
//                 color: dividerColor,
//                 thickness: thickness,
//               ),
//             ),
//           ],
//         ),
//       );
//     } else {
//       // Return simple divider
//       return Padding(
//         padding: margin,
//         child: Divider(
//           color: dividerColor,
//           thickness: thickness,
//         ),
//       );
//     }
//   }
// }