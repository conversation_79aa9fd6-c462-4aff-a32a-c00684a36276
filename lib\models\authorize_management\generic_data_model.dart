class GenericDataAuthorize<T> {
  final int? page;
  final int? limit;
  final int? size;
  final int? totalElements;
  final int? number;
  final int? numberOfElements;
  final int? totalPages;
  final bool? first;
  final bool? last;
  final List<T>? content;

  GenericDataAuthorize({
    this.page,
    this.limit,
    this.size,
    this.totalElements,
    this.number,
    this.numberOfElements,
    this.totalPages,
    this.first,
    this.last,
    this.content,
  });

  factory GenericDataAuthorize.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return GenericDataAuthorize<T>(
      page: json['page'] ?? 0,
      limit: json['limit'] ?? 0,
      size: json['size'] ?? 0,
      totalElements: json['totalElements'] ?? 0,
      number: json['number'] ?? 0,
      numberOfElements: json['numberOfElements'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      first: json['first'] ?? false,
      last: json['last'] ?? false,
      content: (json['content'] as List<dynamic>?)?.map((item) {
            return fromJsonT(item as Map<String, dynamic>);
          }).toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'page': page,
      'limit': limit,
      'size': size,
      'totalElements': totalElements,
      'number': number,
      'numberOfElements': numberOfElements,
      'totalPages': totalPages,
      'first': first,
      'last': last,
      'content': content?.map(toJsonT).toList(),
    };
  }

  GenericDataAuthorize<T> copyWith({
    int? page,
    int? limit,
    int? size,
    int? totalElements,
    int? number,
    int? numberOfElements,
    int? totalPages,
    bool? first,
    bool? last,
    List<T>? content,
  }) {
    return GenericDataAuthorize<T>(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      size: size ?? this.size,
      totalElements: totalElements ?? this.totalElements,
      number: number ?? this.number,
      numberOfElements: numberOfElements ?? this.numberOfElements,
      totalPages: totalPages ?? this.totalPages,
      first: first ?? this.first,
      last: last ?? this.last,
      content: content ?? this.content,
    );
  }
}
