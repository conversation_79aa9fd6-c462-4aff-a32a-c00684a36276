import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/models/form/form_item_info.dart';

FormItemInfo createTestTable() {
  return FormItemInfo(
    id: 'test_table',
    name: 'test_table',
    type: FormItemType.table,
    label: 'Test Table',
    columns: [
      // STT column
      FormItemInfo(
        id: 'stt',
        name: 'stt',
        type: FormItemType.input,
        label: 'STT',
        readonly: true,
        colSortOrder: 0,
      ),
      // Name column
      FormItemInfo(
        id: 'name',
        name: 'name',
        type: FormItemType.input,
        label: 'Họ và tên',
        colSortOrder: 1,
        validations: {
          'required': true,
        },
      ),
      // Status column
      FormItemInfo(
        id: 'status',
        name: 'status',
        type: FormItemType.dropdown,
        label: 'Trạng thái',
        colSortOrder: 2,
        options: [
          {'value': 'approved', 'label': 'Đã duyệt'},
          {'value': 'pending', 'label': '<PERSON><PERSON> duyệt'},
          {'value': 'rejected', 'label': 'Từ chối'},
        ],
      ),
      // Date column
      FormItemInfo(
        id: 'date',
        name: 'date',
        type: FormItemType.datePicker,
        label: 'Ngày',
        colSortOrder: 3,
      ),
    ],
    // Sample data
    value: [
      {
        'stt': '1',
        'name': 'Nguyễn Văn A',
        'status': 'approved',
        'date': '2024-04-23',
      },
      {
        'stt': '2',
        'name': 'Trần Thị B',
        'status': 'pending',
        'date': '2024-04-22',
      },
      {
        'stt': '3',
        'name': 'Lê Văn C',
        'status': 'rejected',
        'date': '2024-04-21',
      },
    ],
  );
} 