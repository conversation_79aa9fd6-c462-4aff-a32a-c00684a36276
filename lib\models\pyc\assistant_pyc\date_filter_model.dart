class DateFilterModel {
  final String? type;
  final String? fromDate;
  final String? toDate;

  DateFilterModel({this.type, this.fromDate, this.toDate});

  factory DateFilterModel.fromJson(Map<String, dynamic> json) => DateFilterModel(
        type: json['type'],
        fromDate: json['fromDate'],
        toDate: json['toDate'],
      );

  Map<String, dynamic> toJson() => {
        'type': type,
        'fromDate': fromDate,
        'toDate': toDate,
      };
}
