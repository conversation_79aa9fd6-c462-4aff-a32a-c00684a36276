import 'package:eapprove/models/ticket_input_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'dart:convert';
import 'dart:developer' as developer;

class TicketInputRepository {
  final ApiService _apiService;

  TicketInputRepository({required ApiService apiService}) : _apiService = apiService;

  Future<TicketInputModel> getTicketInputData({
    required String taskId,
    required String type,
    required String token,
    required String chart,
  }) async {
    try {
      final endpoint = 'business-process/actHiVarInst/getByTask?taskId=$taskId&type=$type';

      developer.log('endpoint: $endpoint');
      final response = await _apiService.get(
        endpoint,
      );
      developer.log('response: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return TicketInputModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load ticket input data: ${response.statusCode}');
      }
    } catch (e) {
      developer.log('❌ Error in getTicketInputData: $e');
      rethrow;
    }
  }
} 