import 'package:equatable/equatable.dart';

abstract class TicketHistoryEvent extends Equatable {
  const TicketHistoryEvent();

  @override
  List<Object?> get props => [];
}

class GetTicketHistory extends TicketHistoryEvent {
  final String procInstId;
  final int ticketId;
  final bool nextPage;
  final String? taskDefKey;

  const GetTicketHistory({
    required this.procInstId,
    required this.ticketId, 
    this.nextPage = false,
    this.taskDefKey,
  });

  @override
  List<Object?> get props => [procInstId, ticketId];
}

class NextPage extends TicketHistoryEvent {
  final String procInstId;
  final int ticketId;

  const NextPage({
    required this.procInstId,
    required this.ticketId,
  }); 

  @override
  List<Object?> get props => [procInstId, ticketId];
} 

class ResetPage extends TicketHistoryEvent {
  final String procInstId;
  final int ticketId;
  
  const ResetPage({
    required this.procInstId,
    required this.ticketId,
  });

  @override
  List<Object?> get props => [procInstId, ticketId];
}

class InitialTicketHistory extends TicketHistoryEvent {
  @override
  List<Object?> get props => [];
}
