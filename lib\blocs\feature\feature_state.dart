import 'package:equatable/equatable.dart';
import 'package:eapprove/models/feature_respone_model.dart';

abstract class FeatureState extends Equatable {
  const FeatureState();

  @override
  List<Object?> get props => [];
}

class FeatureInitial extends FeatureState {}

class FeatureLoading extends FeatureState {}

class FeatureLoaded extends FeatureState {
  final Feature featureData;
  final Map<int, bool> tabVisibility;

  const FeatureLoaded({
    required this.featureData,
    required this.tabVisibility,
  });

  FeatureLoaded copyWith({
    Feature? featureData,
    Map<int, bool>? tabVisibility,
  }) {
    return FeatureLoaded(
      featureData: featureData ?? this.featureData,
      tabVisibility: tabVisibility ?? this.tabVisibility,
    );
  }

  @override
  List<Object?> get props => [featureData, tabVisibility];
}

class FeatureError extends FeatureState {
  final String message;

  const FeatureError(this.message);

  @override
  List<Object> get props => [message];
}
