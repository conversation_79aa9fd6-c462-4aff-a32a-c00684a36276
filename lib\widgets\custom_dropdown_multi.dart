import 'dart:async';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDropdownMulti extends StatefulWidget {
  final String label;
  final String? placeholder;
  final TextStyle? placeholderStyle;
  final TextStyle? bottomSheetTitleStyle;
  final List<SelectItem> options;
  final ValueChanged<List<SelectItem>> onSelected;
  final bool? isDisabled;
  final bool? isFilled;
  final FormFieldValidator<String?>? validator;
  final bool showLabel;
  final Decoration? decoration;
  final double? dropdownHeight;
  final TextStyle? titleStyle;
  final bool? showDeleteIcon;
  final List<SelectItem>? defaultValues;
  final Color? disabledColor;
  final TextStyle? titleTextStyle;
  final Widget? customBottomSheetContent;
  final bool useCheckboxStyle;
  final VoidCallback? onRemoveValue;
  final bool enableSearch;
  final String searchHint;
  final InputDecoration? searchDecoration;
  final Function(String)? onSearchChanged;
  final String? imgSearchPath;
  final bool showBottomSheetLabel;
  final bool showSelectAllOption;
  const CustomDropdownMulti({
    super.key,
    required this.label,
    this.placeholder,
    this.placeholderStyle,
    this.bottomSheetTitleStyle,
    required this.options,
    required this.onSelected,
    this.isDisabled = false,
    this.isFilled = false,
    this.validator,
    this.showLabel = true,
    this.decoration,
    this.dropdownHeight,
    this.titleStyle,
    this.showDeleteIcon = true,
    this.defaultValues,
    this.disabledColor,
    this.titleTextStyle,
    this.customBottomSheetContent,
    this.useCheckboxStyle = false,
    this.onRemoveValue,
    this.enableSearch = false,
    this.searchHint = 'Tìm kiếm',
    this.searchDecoration,
    this.onSearchChanged,
    this.imgSearchPath,
    this.showBottomSheetLabel = true,
    this.showSelectAllOption = true,
  });

  @override
  State<CustomDropdownMulti> createState() => _CustomDropdownMultiState();
}

class _CustomDropdownMultiState extends State<CustomDropdownMulti> {
  List<SelectItem> selectedValues = [];
  Map<String, bool> expandedNodes = {};
  late TextEditingController searchController;
  late FocusNode searchFocusNode;
  List<SelectItem> filteredOptions = [];
  Timer? _debounceTimer;
  double? _calculatedListHeight;

  @override
  void initState() {
    super.initState();
    selectedValues = widget.defaultValues ?? [];
    searchController = TextEditingController();
    searchFocusNode = FocusNode();
    filteredOptions = List.from(widget.options);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    searchController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  bool get hasSelectedValues => selectedValues.isNotEmpty;

  void _onRemoveSelect() {
    setState(() {
      selectedValues = [];
      searchController.clear();
      filteredOptions = List.from(widget.options);
    });

    if (widget.onRemoveValue != null) {
      widget.onRemoveValue!();
    } else {
      widget.onSelected([]);
    }
    widget.onSearchChanged?.call('');
  }

  void _onItemSelected(SelectItem item) {
    if (widget.isDisabled == true) return;
    setState(() {
      if (item.value == '-1' || item.value == '[-3,-2,-1,0,1,-4]') {
        selectedValues = [item];
      } else if (selectedValues.any((element) => element.value == '-1' || element.value == '[-3,-2,-1,0,1,-4]')) {
        selectedValues = [item];
      } else {
        if (selectedValues.any((element) => element.value == item.value)) {
          selectedValues.removeWhere((element) => element.value == item.value);
        } else {
          selectedValues.add(item);
        }
      }
    });
  }

  void _filterOptions(String query) {
    _debounceTimer?.cancel();

    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      setState(() {
        debugPrint('Filtering with query: $query');
        if (query.isEmpty) {
          filteredOptions = List.from(widget.options);
        } else {
          filteredOptions = widget.options.where((item) {
            bool matches = item.label.toLowerCase().contains(query.toLowerCase());
            if (!matches && item.children.isNotEmpty) {
              return item.children.any((child) => child.label.toLowerCase().contains(query.toLowerCase()));
            }
            return matches;
          }).toList();
        }
        widget.onSearchChanged?.call(query);
      });
    });
  }

  double _calculateHeaderHeight(BuildContext context) {
    double headerHeight = 105.0;

    if (widget.enableSearch) {
      headerHeight += 72.0;
    }

    return headerHeight;
  }

  void _showBottomSheet() {
    if (widget.isDisabled == true) return;

    searchController.clear();
    filteredOptions = List.from(widget.options);

    final maxSheetHeight = MediaQuery.of(context).size.height * 0.8;

    showModalBottomSheet(
      backgroundColor: getColorSkin().white,
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      constraints: BoxConstraints(
        maxHeight: maxSheetHeight,
      ),
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return StatefulBuilder(
            builder: (context, setStateLocal) {
              return SafeArea(
                child: SizedBox(
                  height: maxSheetHeight,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header section
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton(
                              onPressed: () {
                                searchFocusNode.unfocus();
                                setStateLocal(() {
                                  selectedValues.clear();
                                  filteredOptions = List.from(widget.options);
                                  searchController.clear();
                                });
                              },
                              child: Text('Xóa tất cả',
                                  style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().primaryBlue)),
                            ),
                            if (widget.showBottomSheetLabel)
                              Expanded(
                                child: Text(
                                  widget.placeholder ?? widget.label,
                                  style: widget.bottomSheetTitleStyle ??
                                      getTypoSkin().title6Regular.copyWith(
                                            color: getColorSkin().ink1,
                                          ),
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: true,
                                  maxLines: 1,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            IconButton(
                                onPressed: () {
                                  searchFocusNode.unfocus();
                                  widget.onSelected(selectedValues);
                                  Navigator.pop(context);
                                },
                                icon: SvgPicture.asset(
                                  StringImage.ic_close,
                                  width: 15.w,
                                  height: 15.h,
                                  colorFilter: ColorFilter.mode(getColorSkin().ink2, BlendMode.srcIn),
                                )),
                          ],
                        ),
                      ),
                      Divider(height: 1, color: getColorSkin().lightGray),
                      // Search section
                      if (widget.enableSearch)
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                          child: TextField(
                            controller: searchController,
                            focusNode: searchFocusNode,
                            onChanged: _filterOptions,
                            onTapOutside: (_) {
                              searchFocusNode.unfocus();
                            },
                            cursorColor: getColorSkin().black,
                            decoration: widget.searchDecoration ??
                                InputDecoration(
                                  hintText: widget.searchHint,
                                  hintStyle: TextStyle(
                                    fontSize: 14.sp,
                                    color: getColorSkin().secondaryText,
                                  ),
                                  prefixIcon: Padding(
                                    padding: EdgeInsets.only(left: 16.w, right: 8.w),
                                    child: Builder(
                                      builder: (context) {
                                        try {
                                          return SvgPicture.asset(
                                            widget.imgSearchPath ?? 'assets/icons/ic_search.svg',
                                            colorFilter: ColorFilter.mode(
                                              getColorSkin().secondaryText,
                                              BlendMode.srcIn,
                                            ),
                                            width: 14.r,
                                            height: 14.r,
                                          );
                                        } catch (e) {
                                          print('Error loading search icon: $e');
                                          return Icon(
                                            Icons.search,
                                            color: getColorSkin().secondaryText,
                                            size: 14.r,
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16.w,
                                    vertical: 12.h,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.all(Radius.circular(20.r)),
                                    borderSide: BorderSide(
                                      color: getColorSkin().secondaryColor2BorderColor,
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.all(Radius.circular(20.r)),
                                    borderSide: BorderSide(
                                      color: getColorSkin().secondaryColor2BorderColor,
                                      width: 1,
                                    ),
                                  ),
                                ),
                          ),
                        ),

                      Expanded(
                        child: filteredOptions.isEmpty
                            ? Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16.h),
                                  child: Text(
                                    'Không có kết quả nào',
                                    style: getTypoSkin().bodyRegular14,
                                  ),
                                ),
                              )
                            : Builder(
                                builder: (context) {
                                  // Calculate visibleOptions once per build
                                  final visibleOptions = filteredOptions.where((item) => item.value != '-1').toList();
                                  // Ensure itemCount is consistent with visibleOptions length
                                  final itemCount = visibleOptions.length + 1; // +1 for "Select All"

                                  return ListView.builder(
                                    itemCount: itemCount,
                                    itemBuilder: (context, index) {
                                      // Index 0: "Chọn tất cả"
                                      if (index == 0 && widget.showSelectAllOption) {
                                        final allSelected = selectedValues.any((item) => item.value == '-1');
                                        return ListTile(
                                          title: Text('Chọn tất cả'),
                                          onTap: () => setStateLocal(() {
                                            selectedValues = [SelectItem(label: 'Chọn tất cả', value: '-1')];
                                          }),
                                          trailing: widget.useCheckboxStyle
                                              ? Checkbox(
                                                  value: allSelected,
                                                  onChanged: (_) => setStateLocal(() {
                                                    if (allSelected) {
                                                      selectedValues.clear();
                                                    } else {
                                                      selectedValues = [SelectItem(label: 'Chọn tất cả', value: '-1')];
                                                    }
                                                  }),
                                                  activeColor: getColorSkin().primaryBlue,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(4.r),
                                                  ),
                                                )
                                              : allSelected
                                                  ? Icon(Icons.check, color: getColorSkin().primaryBlue)
                                                  : null,
                                        );
                                      }

                                      final actualIndex = widget.showSelectAllOption ? index - 1 : index;
                                      if (actualIndex >= visibleOptions.length) {
                                        return SizedBox.shrink();
                                      }

                                      final item = visibleOptions[actualIndex];
                                      return _hasNestedStructure()
                                          ? _buildTreeNode(item, setStateLocal, 0)
                                          : _buildFlatItem(item, setStateLocal);
                                    },
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        });
      },
    );
  }

  bool _hasNestedStructure() {
    return filteredOptions.any((item) => item.children.isNotEmpty);
  }

  Widget _buildTreeNode(
    SelectItem item,
    StateSetter setStateLocal,
    int depth,
  ) {
    bool hasChildren = item.children.isNotEmpty;
    bool isExpanded = expandedNodes[item.value] ?? false;
    bool isSelected = selectedValues.any((selected) => selected.value == item.value);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: isSelected ? getColorSkin().blue2 : getColorSkin().white,
            border: Border(
              bottom: BorderSide(
                color: getColorSkin().ink5,
                width: 0.5,
              ),
            ),
          ),
          child: ListTile(
            title: Text(
              item.label,
              style: TextStyle(
                fontSize: 16.sp,
                color: getColorSkin().ink1,
              ),
            ),
            leading: hasChildren
                ? IconButton(
                    icon: Icon(
                      isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                      color: getColorSkin().ink3,
                    ),
                    onPressed: () {
                      setStateLocal(() {
                        expandedNodes[item.value] = !isExpanded;
                        // Recalculate height when expanding/collapsing nodes
                        _recalculateListHeight();
                      });
                    },
                  )
                : SizedBox(width: 40),
            contentPadding: EdgeInsets.only(left: 16.w + (depth * 16.w), right: 16.w),
            onTap: () {
              setStateLocal(() {
                _onItemSelected(item);
              });
            },
            trailing: widget.useCheckboxStyle
                ? Checkbox(
                    value: isSelected,
                    onChanged: (_) {
                      setStateLocal(() {
                        _onItemSelected(item);
                      });
                    },
                    activeColor: getColorSkin().primaryBlue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  )
                : isSelected
                    ? Icon(Icons.check, color: getColorSkin().primaryBlue)
                    : null,
          ),
        ),
        if (isExpanded && hasChildren)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: item.children
                .where((child) => child.label.toLowerCase().contains(searchController.text.toLowerCase()))
                .map((child) => _buildTreeNode(child, setStateLocal, depth + 1))
                .toList(),
          ),
      ],
    );
  }

  void _recalculateListHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final maxSheetHeight = MediaQuery.of(context).size.height * 0.8;
      final headerHeight = _calculateHeaderHeight(context);
      final availableHeight = maxSheetHeight - headerHeight;

      int visibleItemCount = _countVisibleItems();
      const itemHeight = 56.0;

      final totalItemsHeight = visibleItemCount * itemHeight;
      _calculatedListHeight = totalItemsHeight < availableHeight ? totalItemsHeight : availableHeight;
    });
  }

  int _countVisibleItems() {
    int count = filteredOptions.where((item) => item.value != '-1').length + 1; // +1 for "Select All"

    for (var item in filteredOptions) {
      if (expandedNodes[item.value] == true) {
        count += item.children
            .where((child) => child.label.toLowerCase().contains(searchController.text.toLowerCase()))
            .length;

        count += _countExpandedChildren(item.children);
      }
    }

    return count;
  }

  int _countExpandedChildren(List<SelectItem> children) {
    int count = 0;

    for (var child in children) {
      if (expandedNodes[child.value] == true && child.children.isNotEmpty) {
        count += child.children
            .where((grandchild) => grandchild.label.toLowerCase().contains(searchController.text.toLowerCase()))
            .length;

        count += _countExpandedChildren(child.children);
      }
    }

    return count;
  }

  Widget _buildFlatItem(SelectItem item, StateSetter setStateLocal) {
    final isSelected = selectedValues.any((element) => element.value == item.value);

    return Container(
      decoration: BoxDecoration(
        color: isSelected ? getColorSkin().blue2 : getColorSkin().white,
        border: Border(
          bottom: BorderSide(
            color: getColorSkin().ink5,
            width: 0.5,
          ),
        ),
      ),
      child: widget.useCheckboxStyle
          ? CheckboxListTile(
              title: Text(
                item.label,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: getColorSkin().ink1,
                ),
              ),
              value: isSelected,
              onChanged: (bool? value) {
                _onItemSelected(item);
                setStateLocal(() {});
              },
              activeColor: getColorSkin().primaryBlue,
              checkboxShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4.r),
              ),
              side: BorderSide(
                color: getColorSkin().ink4,
                width: 1.5,
              ),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 8.h,
              ),
              dense: true,
            )
          : ListTile(
              title: Text(
                item.label,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: getColorSkin().ink1,
                ),
              ),
              onTap: () {
                _onItemSelected(item);
                setStateLocal(() {});
              },
              trailing: isSelected ? Icon(Icons.check, color: getColorSkin().primaryBlue) : null,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 8.h,
              ),
            ),
    );
  }

  String get displayText {
    if (hasSelectedValues) {
      if (selectedValues.length == 1) {
        return selectedValues.first.label;
      }
      return '${selectedValues.first.label} (+${selectedValues.length - 1})';
    }
    return widget.placeholder ?? widget.label;
  }

  Color get displayTextColor => hasSelectedValues ? Colors.black : Colors.grey;

  IconData get trailingIcon {
    if (hasSelectedValues && widget.showDeleteIcon == true) {
      return Icons.highlight_remove_outlined;
    } else {
      return Icons.keyboard_arrow_down;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label.isNotEmpty && widget.showLabel)
          Padding(
            padding: EdgeInsets.only(bottom: 4.h),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: widget.label,
                    style: widget.titleStyle ??
                        getTypoSkin().label4Regular.copyWith(
                              color: getColorSkin().ink1,
                            ),
                  ),
                  if (widget.validator != null && widget.showLabel)
                    const TextSpan(
                      text: " *",
                      style: TextStyle(color: Colors.black),
                    ),
                ],
              ),
            ),
          ),
        GestureDetector(
          onTap: (widget.isDisabled == true) ? null : _showBottomSheet,
          child: Container(
            height: widget.dropdownHeight ?? 56.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: widget.decoration ??
                BoxDecoration(
                  border: Border.all(color: getColorSkin().ink5),
                  borderRadius: BorderRadius.circular(8.r),
                  color: widget.isDisabled == true
                      ? getColorSkin().whiteSmoke
                      : widget.isFilled == true
                          ? getColorSkin().whiteSmoke
                          : getColorSkin().white,
                ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayText,
                        style: widget.placeholderStyle ?? TextStyle(fontSize: 16, color: displayTextColor),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: widget.isDisabled == true
                      ? null
                      : () =>
                          hasSelectedValues && widget.showDeleteIcon == true ? _onRemoveSelect() : _showBottomSheet(),
                  child: Icon(trailingIcon, color: getColorSkin().secondaryText),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
