import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';

class PasswordValidationResult {
  final bool isValid;
  final String? errorMessage;

  PasswordValidationResult({
    required this.isValid,
    this.errorMessage,
  });

  static PasswordValidationResult success() => PasswordValidationResult(isValid: true);

  static PasswordValidationResult error(String message) => PasswordValidationResult(
        isValid: false,
        errorMessage: message,
      );
}

PasswordValidationResult validatePasswordWithDetails(String password, {String? username, String? email}) {
  log("Valid password: $password");
  if (email != null) {
    log("Comparing with email: $email");
  }

  if (password.length < 8 || password.length > 32) {
    return PasswordValidationResult.error('Mật khẩu phải có ít nhất 8 ký tự và tối đa 32 ký tự');
  }

  if (!RegExp(r'[a-z]').hasMatch(password)) {
    return PasswordValidationResult.error('Mật khẩu phải chứa ít nhất một ký tự thường');
  }

  if (!RegExp(r'[A-Z]').hasMatch(password)) {
    return PasswordValidationResult.error('Mật khẩu phải chứa ít nhất một ký tự in hoa');
  }

  if (!RegExp(r'[0-9]').hasMatch(password)) {
    return PasswordValidationResult.error('Mật khẩu phải chứa ít nhất một chữ số');
  }

  if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
    return PasswordValidationResult.error('Mật khẩu phải chứa ít nhất một ký tự đặc biệt');
  }

  if (email != null) {
    log("Password contains email? ${password.toLowerCase().contains(email.toLowerCase())}");

    final emailParts = email.split('@');
    if (emailParts.length > 1) {
      final emailUsername = emailParts[0];
      if (emailUsername.length > 3 && password.toLowerCase().contains(emailUsername.toLowerCase())) {
        return PasswordValidationResult.error('Mật khẩu không được chứa tên người dùng trong email của bạn');
      }
    }
  }

  if (username != null && username.isNotEmpty && username.length > 3) {
    if (password.toLowerCase().contains(username.toLowerCase())) {
      return PasswordValidationResult.error('Mật khẩu không được chứa tên đăng nhập của bạn');
    }
  }

  return PasswordValidationResult.success();
}

class PasswordValidationField extends StatefulWidget {
  final TextEditingController controller;
  final String? username;
  final String? email;
  final String labelText;
  final Function(bool) onValidChanged;
  final bool obscureText;
  final VoidCallback toggleVisibility;

  const PasswordValidationField({
    super.key,
    required this.controller,
    this.username,
    this.email,
    required this.labelText,
    required this.onValidChanged,
    required this.obscureText,
    required this.toggleVisibility,
  });

  @override
  State<PasswordValidationField> createState() => _PasswordValidationFieldState();
}

class _PasswordValidationFieldState extends State<PasswordValidationField> {
  bool _hasMinLength = false;
  bool _hasSpecialChar = false;
  bool _hasNumber = false;
  bool _hasUpperCase = false;
  bool _hasLowerCase = false;
  bool _notContainsUsername = true;
  bool _notContainsEmail = true;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_validatePassword);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_validatePassword);
    super.dispose();
  }

  void _validatePassword() {
    final password = widget.controller.text;

    setState(() {
      _hasMinLength = password.length >= 8 && password.length <= 32;

      _hasSpecialChar = RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);

      _hasNumber = RegExp(r'[0-9]').hasMatch(password);

      _hasUpperCase = RegExp(r'[A-Z]').hasMatch(password);

      _hasLowerCase = RegExp(r'[a-z]').hasMatch(password);

      _notContainsUsername = true;
      if (widget.username != null && widget.username!.isNotEmpty && widget.username!.length > 3) {
        _notContainsUsername = !password.toLowerCase().contains(widget.username!.toLowerCase());
      }

      _notContainsEmail = true;
      if (widget.email != null && widget.email!.isNotEmpty) {
        final emailParts = widget.email!.split('@');
        if (emailParts.length > 1) {
          final emailUsername = emailParts[0];
          if (emailUsername.length > 3) {
            _notContainsEmail = !password.toLowerCase().contains(emailUsername.toLowerCase());
          }
        }
      }
    });

    final isValid = _hasMinLength &&
        _hasSpecialChar &&
        _hasNumber &&
        _hasUpperCase &&
        _hasLowerCase &&
        _notContainsUsername &&
        _notContainsEmail;

    widget.onValidChanged(isValid);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextField(
          controller: widget.controller,
          backgroundColor: getColorSkin().white,
          obscureText: widget.obscureText,
          showClearIcon: true,
          labelText: widget.labelText,
          maxLength: 50,
          suffixIcon: FFilledButton.icon(
            onPressed: widget.toggleVisibility,
            child: Icon(
              widget.obscureText ? Icons.visibility_off : Icons.visibility,
              color: getColorSkin().subtitle,
            ),
          ),
        ),
        SizedBox(height: 12.h),
        _buildValidationItem(_hasMinLength, 'Độ dài từ 8-32 ký tự'),
        SizedBox(height: 4.h),
        _buildValidationItem(_hasSpecialChar, 'Ít nhất 1 ký tự đặc biệt'),
        SizedBox(height: 4.h),
        _buildValidationItem(_hasNumber, 'Ít nhất 1 chữ số'),
        SizedBox(height: 4.h),
        _buildValidationItem(_hasUpperCase, 'Ít nhất 1 chữ in HOA'),
        SizedBox(height: 4.h),
        _buildValidationItem(_hasLowerCase, 'Ít nhất 1 chữ in thường'),
        SizedBox(height: 4.h),
        _buildValidationItem(_notContainsUsername, 'Không được chứa tên người dùng'),
        SizedBox(height: 4.h),
        _buildValidationItem(_notContainsEmail, 'Không được là địa chỉ email'),
      ],
    );
  }

  Widget _buildValidationItem(bool isValid, String text) {
    Color iconColor;
    String iconType;

    if (widget.controller.text.isEmpty) {
      iconColor = getColorSkin().subtitle;
      iconType = FIconData.icValid;
    } else if (isValid) {
      iconColor = getColorSkin().successPrimary;
      iconType = FIconData.icValid;
    } else {
      iconColor = getColorSkin().errorPrimary;
      iconType = FIconData.icValid;
    }

    return Row(
      children: [
        Center(
          child: FIcon(
            icon: iconType,
            color: iconColor,
            width: 16.w,
            height: 16.h,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          text,
          style: getTypoSkin().body2Regular.copyWith(
                color: getColorSkin().primaryText,
              ),
        ),
      ],
    );
  }
}
