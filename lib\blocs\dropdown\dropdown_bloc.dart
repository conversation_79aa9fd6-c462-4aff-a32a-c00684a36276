import 'package:eapprove/blocs/dropdown/dropdown_event.dart';
import 'package:eapprove/blocs/dropdown/dropdown_state.dart';
import 'package:eapprove/models/form/individual_info_response_model.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:eapprove/utils/function.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

class DropdownBloc extends Bloc<DropdownEvent, DropdownState> {
  final FormRepository _formRepository;
  final Map<String, IndividualInfoResponse> _dropdownResponses = {};

  DropdownBloc({
    required FormRepository formRepository,
  })  : _formRepository = formRepository,
        super(const DropdownState()) {
    on<GetDropdownDataRequested>(_onGetDropdownDataRequested);
    on<ResetDropdownData>(_onResetDropdownData);
    on<GetDropdownDataFromAPILink>(_onGetDropdownDataFromAPILink);
    on<GetDropdownDataFromAPILinkService>(_onGetDropdownDataFromAPILinkService);
    on<GetDropdownDataFromMasterData>(_onGetDropdownDataFromMasterData);
  }

  Future<void> _onGetDropdownDataRequested(
    GetDropdownDataRequested event,
    Emitter<DropdownState> emit,
  ) async {
    try {
      // If not forcing refresh and we already have the data, return cached data
      if (!event.forceRefresh &&
          state.dropdownResponses.containsKey(event.requestKey)) {
        return;
      }

      emit(state.copyWith(isLoading: {
        ...state.isLoading,
        event.requestKey: true,
      }));

      developer.log('event.requestModel: ${event.requestKey} ${event.requestModel is Map ? event.requestModel : event.requestModel.toJson()}', name: 'GetDropdownDataRequested');
      
      final response = await _formRepository.getIndividualInfo(
        requestBody: event.requestModel,
      );
      final data = response.data;
     
      final groupedData = data != null && data.isNotEmpty ? removeDuplicateElements(
          data, event.requestModel.condition?.firstOrNull?.filterField ?? '') : [];
      developer.log('GetDropdownDataRequestedresponse: ${event.requestKey} ${groupedData}', name: 'GetDropdownDataRequested');

      final baseResponse = BaseResponseModel(
        isSuccess: true,
        data: groupedData,
      );

      emit(state.copyWith(
        isLoading: {
          ...state.isLoading,
          event.requestKey: false,
        },
        dropdownResponses: {
          ...state.dropdownResponses,
          event.requestKey: baseResponse,
        },
      ));
    } catch (e) {
      developer.log('Error getting dropdown data: ${event.requestKey}  $e', name: 'GetDropdownDataRequested');
      final currentResponses =
          Map<String, BaseResponseModel>.from(state.dropdownResponses);
      currentResponses[event.requestKey] = BaseResponseModel(
        isSuccess: false,
        errorMessage: e.toString(),
        data: [],
      );
      emit(state.copyWith(
        isLoading: {
          ...state.isLoading,
          event.requestKey: false,
        },
        dropdownResponses: currentResponses,
      ));
    }
  }

  Future<void> _onGetDropdownDataFromAPILink(
    GetDropdownDataFromAPILink event,
    Emitter<DropdownState> emit,
  ) async {
    try {
      if (!event.forceRefresh &&
          state.dropdownResponses.containsKey(event.requestKey)) {
        return;
      }

      emit(state.copyWith(isLoading: {
        ...state.isLoading,
        event.requestKey: true,
      }));

      developer.log(
          '_onGetDropdownDataFromAPILinkeventrequestModel: ${event.requestKey} ${event.requestModel} ${event.endpoint} ${event.apiURL}',
          name: 'dropdown_bloc');

      final raw = await _formRepository.getDataFromAPILink(
          requestBody: event.requestModel,
          endpoint: event.endpoint,
          apiURL: event.apiURL,
          requestKey: event.requestKey);

      if (raw == null) {
        throw Exception('API response is null');
      }

      final data = raw is Map ? (raw['data'] ?? []) : (raw is List ? raw : []);

      final baseResponse = BaseResponseModel(
        isSuccess: true,
        data: data,
      );
      // developer.log('baseResponse: ${baseResponse.data}',
      //     name: 'dropdown_bloc');
      emit(state.copyWith(
        isLoading: {
          ...state.isLoading,
          event.requestKey: false,
        },
        dropdownResponses: {
          ...state.dropdownResponses,
          event.requestKey: baseResponse,
        },
      ));
    } catch (e) {
      developer.log(
          'Error in _onGetDropdownDataFromAPILink ${event.apiURL} ${event.endpoint} ${event.requestModel}: $e',
          name: 'dropdown_bloc');

      final currentResponses =
          Map<String, BaseResponseModel>.from(state.dropdownResponses);
      currentResponses[event.requestKey] = BaseResponseModel(
        isSuccess: false,
        errorMessage: e.toString(),
        data: [],
      );
      emit(state.copyWith(
        isLoading: {
          ...state.isLoading,
          event.requestKey: false,
        },
        dropdownResponses: currentResponses,
      ));
    }
  }

  Future<void> _onGetDropdownDataFromAPILinkService(
    GetDropdownDataFromAPILinkService event,
    Emitter<DropdownState> emit,
  ) async {
    try {
      // debugPrint('event.requestModel: ${event.requestModel.toJson()}');

      final response = await _formRepository.getCallService(
        requestBody: event.requestModel,
        baseURL: event.apiURL,
      );
      // developer.log('responseresponseresponse: $response',
      //     name: 'dropdown_bloc');

      final baseResponse = BaseResponseModel(
        isSuccess: true,
        data: response is Map ? response['data'] : response,
      );

      emit(state.copyWith(
        isLoading: {
          ...state.isLoading,
          event.requestKey: false,
        },
        dropdownResponses: {
          ...state.dropdownResponses,
          event.requestKey: baseResponse,
        },
      ));
    } catch (e) {
      developer.log(
          'Error in _onGetDropdownDataFromAPILinkService ${event.apiURL} ${event.requestKey} ${event.requestModel}: $e',
          name: 'dropdown_bloc');
    }
  }

  Future<void> _onGetDropdownDataFromMasterData(
    GetDropdownDataFromMasterData event,
    Emitter<DropdownState> emit,
  ) async {
    try {
      if (!event.forceRefresh &&
          state.dropdownResponses.containsKey(event.requestKey)) {
        return;
      }
      // developer.log(
      //     'GetDropdownDataFromMasterData.requestModel: ${event.requestModel.toJson()}',
      //     name: 'dropdown_bloc');
      final response = await _formRepository.getMdService(
        requestBody: event.requestModel,
      );

      final baseResponse = BaseResponseModel(
        isSuccess: true,
        data: response is Map ? response['data'] : response,
      );

      emit(state.copyWith(
        isLoading: {
          ...state.isLoading,
          event.requestKey: false,
        },
        dropdownResponses: {
          ...state.dropdownResponses,
          event.requestKey: baseResponse,
        },
      ));
    } catch (e) {
      developer.log(
          'Error in _onGetDropdownDataFromMasterData ${event.requestKey} ${event.requestModel}: $e',
          name: 'dropdown_bloc');
    }
  }

  void _onResetDropdownData(
    ResetDropdownData event,
    Emitter<DropdownState> emit,
  ) {
    _dropdownResponses.clear();
    emit(const DropdownState());
  }
}
