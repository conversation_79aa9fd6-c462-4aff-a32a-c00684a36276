class ApproveResponseModel {
  final int code;
  final String message;
  final ApproveData data;

  ApproveResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ApproveResponseModel.fromJson(Map<String, dynamic> json) {
    return ApproveResponseModel(
      code: json['code'],
      message: json['message'],
      data: ApproveData.fromJson(json['data']),
    );
  }
}

class ApproveData {
  final bool isComplete;

  ApproveData({
    required this.isComplete,
  });

  factory ApproveData.fromJson(Map<String, dynamic> json) {
    return ApproveData(
      isComplete: json['isComplete'],
    );
  }
}
