import 'dart:developer';

class UnreadModel {
  final String? code;
  final String? message;
  final UnreadData? data;

  UnreadModel({this.code, this.message, this.data});

  factory UnreadModel.fromJson(Map<String, dynamic> json) {
    try {
      return UnreadModel(
        code: json['code'] as String?,
        message: json['message'] as String?,
        data: json['data'] != null ? UnreadData.fromJson(json['data'] as Map<String, dynamic>) : null,
      );
    } catch (e) {
      log('Error parsing UnreadModel: $e');
      return UnreadModel(code: null, message: null, data: UnreadData(object: null, count: {}));
    }
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'message': message,
        'data': data?.toJson(),
      };
}

class UnreadData {
  final String? object;
  final Map<String, int>? count;

  UnreadData({this.object, this.count});

  factory UnreadData.fromJson(Map<String, dynamic> json) {
    try {
      final countMap = <String, int>{};
      final rawCount = json['count'];
      if (rawCount is Map<String, dynamic>) {
        rawCount.forEach((key, value) {
          countMap[key] = (value as num).toInt();
        });
      }

      return UnreadData(
        object: json['object'] as String?,
        count: countMap,
      );
    } catch (e) {
      log('Error parsing UnreadData: $e');
      return UnreadData(object: null, count: {});
    }
  }

  Map<String, dynamic> toJson() => {
        'object': object,
        'count': count,
      };
}
