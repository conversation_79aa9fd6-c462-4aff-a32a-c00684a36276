import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_state.dart';
import 'package:eapprove/screens/user/user_information_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ProfileHeader extends StatelessWidget {
  final VoidCallback? onTrailingIconPressed;
  final bool showChangeAccIcon;

  const ProfileHeader({
    super.key,
    this.onTrailingIconPressed,
    this.showChangeAccIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 4.h, top: 10.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: const Color.fromRGBO(255, 255, 255, 1),
      ),
      child: BlocBuilder<UserListBloc, UserListState>(
        builder: (context, state) {
          if (state is UserListLoaded) {
            final userADM = state.userADM;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  child: _buildUserInfoSection(context, userADM),
                ),
                const CustomDivider(
                  indent: 0,
                  endIndent: 0,
                  thickness: 1,
                  color: Color(0xffF3F3F3),
                ),
                _buildUserDetailsSection(userADM),
              ],
            );
          }
          return const Center(child: Text('Chưa có dữ liệu'));
        },
      ),
    );
  }

  Widget _buildUserInfoSection(BuildContext context, dynamic userADM) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAvatar(),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNameSection(context, userADM),
              SizedBox(height: 4.h),
              _buildJobTitleSection(userADM),
              SizedBox(height: 4.h),
              _buildPositionSection(userADM),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.h),
      child: SizedBox(
        width: 48.w,
        height: 48.h,
        child: SvgPicture.asset(StringImage.avatar_default),
      ),
    );
  }

  Widget _buildNameSection(BuildContext context, dynamic userADM) {
    return InkWell(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const UserInformationScreen()),
      ),
      child: Text(
        userADM?.fullName ?? 'chưa có dữ liệu',
        style: getTypoSkin().title2Medium.copyWith(
              color: getColorSkin().title,
            ),
      ),
    );
  }

  Widget _buildJobTitleSection(dynamic userADM) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          _getJobTitles(userADM),
          style: getTypoSkin().subtitle3Regular.copyWith(
                color: getColorSkin().secondaryText,
              ),
        ),
        if (showChangeAccIcon) _buildChangeAccountButton(),
      ],
    );
  }

  Widget _buildPositionSection(dynamic userADM) {
    return InkWell(
      onTap: () {},
      child: Text(
        userADM?.jobTitles?.map((job) => job.positionName).join(", ") ??
            "Chưa có dữ liệu",
        style: getTypoSkin().body2Regular.copyWith(
              color: getColorSkin().primaryText,
            ),
      ),
    );
  }

  Widget _buildChangeAccountButton() {
    return GestureDetector(
      onTap: onTrailingIconPressed,
      child: Container(
        height: 20.h,
        width: 20.w,
        decoration: BoxDecoration(
          color: Colors.orange,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: SvgPicture.asset(StringImage.ic_renew),
        ),
      ),
    );
  }

  Widget _buildUserDetailsSection(dynamic userADM) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _buildDetailTile('Công ty', _getCompanyNames(userADM)),
        ),
        const CustomDivider(indent: 0, endIndent: 0, thickness: 1, color: Color(0xffF3F3F3)),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _buildDetailTile('Email', userADM?.email ?? "chưa có dữ liệu"),
        ),
        const CustomDivider(indent: 0, endIndent: 0, thickness: 1, color: Color(0xffF3F3F3)),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _buildDetailTile('Điện thoại', userADM?.phone ?? "chưa có dữ liệu"),
        ),
      ],
    );
  }

  Widget _buildDetailTile(String label, String value, {bool isRightAligned = true}) {
    final bool isLongValue = value.length > 30;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Text(
        label,
        style: getTypoSkin().title6Regular.copyWith(
              color: getColorSkin().title,
            ),
      ),
      titleAlignment: isLongValue
          ? ListTileTitleAlignment.top
          : ListTileTitleAlignment.center,
      title: isRightAligned
          ? Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Flexible(
                  child: Text(
                    value,
                    style: getTypoSkin().title5Medium.copyWith(
                          color: getColorSkin().primaryText,
                        ),
                    textAlign: TextAlign.right,
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ],
            )
          : Text(
              value,
              style: getTypoSkin().title5Medium.copyWith(
                    color: getColorSkin().primaryText,
                  ),
            ),
    );
  }

  String _getJobTitles(dynamic userADM) {
    if (userADM?.jobTitles == null) return "Chưa có dữ liệu";

    final validJobs = userADM.jobTitles!
        .where((job) => job.jobTilteName?.trim().isNotEmpty == true)
        .map((job) => job.jobTilteName!);

    return validJobs.isEmpty ? "Chưa có dữ liệu" : validJobs.join(", ");
  }

  String _getCompanyNames(dynamic userADM) {
    if (userADM?.jobTitles?.isNotEmpty != true) return "Chưa có dữ liệu";

    return userADM.jobTitles!.map((job) => job.parentName).join(", ");
  }
}