import 'package:eapprove/blocs/authorize_management/authorize_management_event.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_state.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/repositories/authorize_management_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AuthorizeManagementBloc
    extends Bloc<AuthorizeManagementEvent, AuthorizeManagementState> {
  final AuthorizeManagementRepository authorizeManagementRepository;

  AuthorizeManagementBloc(this.authorizeManagementRepository)
      : super(const AuthorizeManagementState(
            status: AuthorizeManagementStatus.initial)) {
    on<FetchAuthorizeList>(_onFetchAuthorizeList);
    on<FetchAuthorizeListFilter>(_onFetchAuthorizeListFilter);
    on<ClearAuthorizeListFilter>((event, emit) {
      emit(state.copyWith(
        authorizeManagementListFilter: [], // Xóa danh sách
        status: AuthorizeManagementStatus.initial, // Đặt trạng thái về ban đầu
      ));
    });
  }

  Future<void> _onFetchAuthorizeList(
      FetchAuthorizeList event, Emitter<AuthorizeManagementState> emit) async {
    final isLoadMore = event.isLoadMore ?? false;

    try {
      emit(state.copyWith(
        status: isLoadMore
            ? AuthorizeManagementStatus.loaded
            : AuthorizeManagementStatus.loading,
        isPaginating: isLoadMore,
      ));
      final response = await authorizeManagementRepository
          .getFilterAuthorizeManagement(requestBody: event.requestModel!);
      final oldList = isLoadMore
          ? (state.authorizeManagementResponseModel?.data.content ?? [])
          : <AuthorizeItemData>[];
      final newList = response.data.content ?? <AuthorizeItemData>[];
      final List<AuthorizeItemData> combined = [...oldList, ...newList];
      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );
      final hasMore = response.data.totalPages! > event.requestModel!.page!;
      bool containsAllElements(List<int> a, List<int> b) {
        return b.every((element) => a.contains(element));
      }

      if (containsAllElements(
          [0, -1, -2, -3, -4], event.requestModel!.status as List<int>)) {
        emit(state.copyWith(
          status: AuthorizeManagementStatus.loaded,
          authorizeManagementList: combined,
          authorizeManagementListAll: combined,
          authorizeManagementResponseModel: updatedResponse,
          isPaginating: false,
          isLoadingMore: hasMore,
        ));
      }
      if (containsAllElements([0], event.requestModel!.status as List<int>)) {
        emit(state.copyWith(
          status: AuthorizeManagementStatus.loaded,
          authorizeManagementList: combined,
          authorizeManagementListOngoing: combined,
          authorizeManagementResponseModel: updatedResponse,
          isPaginating: false,
          isLoadingMore: hasMore,
        ));
      }
      if (containsAllElements(
          [-1, -2, -3, -4], event.requestModel!.status as List<int>)) {
        emit(state.copyWith(
          status: AuthorizeManagementStatus.loaded,
          authorizeManagementList: combined,
          authorizeManagementListExpired: combined,
          authorizeManagementResponseModel: updatedResponse,
          isPaginating: false,
          isLoadingMore: hasMore,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: AuthorizeManagementStatus.error,
        errorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }

  Future<void> _onFetchAuthorizeListFilter(FetchAuthorizeListFilter event,
      Emitter<AuthorizeManagementState> emit) async {
    final isLoadMore = event.isLoadMore ?? false;
    try {
      emit(state.copyWith(
        status: isLoadMore
            ? AuthorizeManagementStatus.loaded
            : AuthorizeManagementStatus.loading,
        isPaginating: isLoadMore,
      ));

      final response = await authorizeManagementRepository
          .getFilterAuthorizeManagement(requestBody: event.requestModel!);

      final oldList = isLoadMore
          ? (state.authorizeManagementResponseModel?.data.content ?? [])
          : <AuthorizeItemData>[];
      final newList = response.data.content ?? <AuthorizeItemData>[];
      final List<AuthorizeItemData> combined = [...oldList, ...newList];
      final updatedResponse = response.copyWith(
        data: response.data.copyWith(content: combined),
      );
      final hasMore = response.data.totalPages! > event.requestModel!.page!;

      emit(state.copyWith(
        status: AuthorizeManagementStatus.loaded,
        authorizeManagementListFilter: combined,
        authorizeManagementResponseModel: updatedResponse,
        isPaginating: false,
        isLoadingMore: hasMore,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthorizeManagementStatus.error,
        errorMessage: e.toString(),
        isPaginating: false,
      ));
    }
  }
}
