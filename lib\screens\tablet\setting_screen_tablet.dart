import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/screens/authorize_management/authorize_information_screen.dart';
import 'package:eapprove/screens/authorize_management/authorize_management_screen.dart';
import 'package:eapprove/screens/setting_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class SettingScreenTablet extends StatefulWidget {
  const SettingScreenTablet({super.key});

  @override
  State<SettingScreenTablet> createState() => _SettingScreenTabletState();
}

class _SettingScreenTabletState extends State<SettingScreenTablet> {
  bool isNotify = false;
  bool isLogin = false;

  final GlobalKey<NavigatorState> _rightColumnNavigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<UserListBloc>().add(FetchUserList());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 20.0.h),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 1,
              child: EAppSettingScreen(),
            ),

            // Divider
            VerticalDivider(
              width: 1,
              thickness: 1,
              color: getColorSkin().dividers2,
              indent: 0,
              endIndent: 0,
            ),

            Expanded(
              flex: 2,
              child: Navigator(
                key: _rightColumnNavigatorKey,
                onGenerateRoute: (settings) {
                  // Route gốc - hiển thị AuthorizeManagementScreen
                  if (settings.name == '/' || settings.name == null) {
                    return MaterialPageRoute(
                      builder: (context) => AuthorizeManagementScreen(),
                    );
                  }

                  if (settings.name == '/detail') {
                    final AuthorizeItemData request = settings.arguments as AuthorizeItemData;
                    return MaterialPageRoute(
                      builder: (context) => AuthorizationDetail(
                        title: "${request.requestCode} - ${request.assignName}",
                        request: request,
                      ),
                    );
                  }

                  return null;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
