import 'package:eapprove/models/form/check_to_trinh_response_model.dart';
import 'package:eapprove/models/handle_ticket/assistant_opinion_request_model.dart';
import 'package:eapprove/models/handle_ticket/assistant_opinion_response_model.dart';
import 'package:eapprove/models/handle_ticket/inheritable_tasks_model.dart';
import 'package:eapprove/models/form/check_to_trinh_response_model.dart';
import 'package:eapprove/models/handle_ticket/share_ticket_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/user_drop_down_model.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:eapprove/models/pyc/ticket_process_detail_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_phieu_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/models/handle_ticket/approve_response_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class TicketProcessDetailState extends Equatable {
  const TicketProcessDetailState();

  @override
  List<Object?> get props => [];
}

class TicketProcessDetailInitial extends TicketProcessDetailState {}

class TicketProcessDetailLoading extends TicketProcessDetailState {}

class TicketProcessDetailLoaded extends TicketProcessDetailState {
  final TicketProcessDetailModel ticketProcessDetail;

  const TicketProcessDetailLoaded(this.ticketProcessDetail);

  @override
  List<Object?> get props => [ticketProcessDetail];
}

class TicketProcessDetailByIdLoaded extends TicketProcessDetailState {
  final ThongTinChungModel thongTinChungModel;

  const TicketProcessDetailByIdLoaded(this.thongTinChungModel);

  @override
  List<Object?> get props => [thongTinChungModel];
}

class ThongTinPhieuLoaded extends TicketProcessDetailState {
  final ThongTinPhieuResponseModel thongTinPhieu;

  const ThongTinPhieuLoaded(this.thongTinPhieu);

  @override
  List<Object?> get props => [thongTinPhieu];
}

class ThongTinDauVaoLoaded extends TicketProcessDetailState {
  final ThongTinDauVaoResponseModel thongTinDauVao;

const ThongTinDauVaoLoaded(this.thongTinDauVao);

  @override
  List<Object?> get props => [thongTinDauVao];
}

class TicketProcessDetailError extends TicketProcessDetailState {
  final String message;
  final int? errorCode;

  const TicketProcessDetailError(this.message, {this.errorCode});

  @override
  List<Object?> get props => [message];
}

class TicketProcessDetailCheckToTrinhLoaded extends TicketProcessDetailState {
  final CheckToTrinhResponseModel checkToTrinhResponseModel;

  const TicketProcessDetailCheckToTrinhLoaded(this.checkToTrinhResponseModel);

  @override
  List<Object?> get props => [checkToTrinhResponseModel];
}

class AssistantOpinionLoaded extends TicketProcessDetailState {
  final AssistantOpinionResponseModel assistantOpi;

  const AssistantOpinionLoaded(this.assistantOpi);

  @override
  List<Object?> get props => [assistantOpi];
}

class SignedFileUrlLoaded extends TicketProcessDetailState {
  final String fileUrl;
  final String originalFileName;
  final ThongTinChungModel thongTinChungModel;

  const SignedFileUrlLoaded(this.fileUrl, this.originalFileName, this.thongTinChungModel);
}

class UserTaskInfoLoaded extends TicketProcessDetailState {
  final UserTaskInfoResponse userTaskInfo;

  const UserTaskInfoLoaded(this.userTaskInfo);

  AssigneeInfo? getCreator() {
    if (userTaskInfo.data == null) return null;

    for (final task in userTaskInfo.data!) {
      if (task.taskType == 'startEvent' && (task.lstAssigneeInfo?.isNotEmpty ?? false)) {
        return task.lstAssigneeInfo!.first;
      }
    }
    return null;
  }

  AssigneeInfo? getAssigneeByTaskDefKey(String taskDefKey) {
    final task = userTaskInfo.data?.firstWhere(
      (t) => t.taskDefKey == taskDefKey,
      orElse: () => TaskInfo(),
    );
    return task?.lstAssigneeInfo?.isNotEmpty == true ? task!.lstAssigneeInfo!.first : null;
  }

  AssigneeInfo? getCurrentAssignee() {
    if (userTaskInfo.data == null) return null;

    for (final task in userTaskInfo.data!) {
      for (final assignee in task.lstAssigneeInfo ?? []) {
        if (assignee.taskStatus == 'COMPLETED') {
          return assignee;
        }
      }
    }
    return null;
  }

  String? getPrevTaskDefKey(String step) {
    debugPrint("userTaskInfo.data ${userTaskInfo.data}");
    if (userTaskInfo.data == null || userTaskInfo.data!.isEmpty) return null;
    debugPrint("step:: $step");
    // Find the index of the current step
    final currentIndex = userTaskInfo.data!.indexWhere((task) => task.taskDefKey == step);
    debugPrint("currentIndex $currentIndex");
    if (currentIndex <= 0) return null; // No previous step if current is first or not found

    // Get the previous task
    final prevTask = userTaskInfo.data![currentIndex - 1];
    if (prevTask.lstAssigneeInfo == null || prevTask.lstAssigneeInfo!.isEmpty) return null;

    return prevTask.taskDefKey;
  }
}

//chia sẻ
class ShareTicketsLoaded extends TicketProcessDetailState {
  final ShareResponseModel shareResponse;
  final int currentPage;
  final String currentType;

  const ShareTicketsLoaded({
    required this.shareResponse,
    required this.currentPage,
    required this.currentType,
  });
}

class ShareTicketActionSuccess extends TicketProcessDetailState {
  final String message;

  const ShareTicketActionSuccess({
    required this.message,
  });
}

class UserDropdownLoaded extends TicketProcessDetailState {
  final UserDropdownResponse userResponse;

  const UserDropdownLoaded({
    required this.userResponse,
  });
}

class ApproveTicketSuccess extends TicketProcessDetailState {
  final ApproveResponseModel response;
  final String message;

  const ApproveTicketSuccess({
    required this.response,
    required this.message,
  });
}

class ApproveTicketError extends TicketProcessDetailState {
  final String message;

  const ApproveTicketError({required this.message});
}

class SignPrintZoneSuccess extends TicketProcessDetailState {
  final Map<String, dynamic> response;
  final String message;

  const SignPrintZoneSuccess({
    required this.response,
    required this.message,
  });
}

class SignPrintZoneError extends TicketProcessDetailState {
  final String message;

  const SignPrintZoneError({required this.message});
}

class InheritableTasksListLoaded extends TicketProcessDetailState {
  final InheritableTasksResponse inheritableTasksResponse;

  const InheritableTasksListLoaded(this.inheritableTasksResponse);

  @override
  List<Object?> get props => [inheritableTasksResponse];
}

class SaveDraftSuccess extends TicketProcessDetailState {
  final String message;

  const SaveDraftSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class SaveDraftError extends TicketProcessDetailState {
  final String message;

  const SaveDraftError({required this.message});

  @override
  List<Object?> get props => [message];
}
