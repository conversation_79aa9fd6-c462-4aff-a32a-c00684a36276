import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

class PycTicketCard extends StatelessWidget {
  final String title;
  final String serviceName;
  final int? createdTime;
  final String? status;

  const PycTicketCard({
    super.key,
    required this.title,
    required this.serviceName,
    this.createdTime,
    this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Colors.white70,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w700,
              color: const Color(0xff323232),
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            serviceName,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: const Color(0xff777B7E),
            ),
          ),
          SizedBox(height: 8.h),
          LayoutBuilder(
            builder: (context, constraints) {
              return Row(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        StringImage.ic_calendar,
                        width: 16.w,
                        height: 16.h,
                      ),
                      SizedBox(width: 6.w),
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: constraints.maxWidth * 0.4,
                        ),
                        child: Text(
                          _formatDate(createdTime),
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xff8C8C8C),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(width: 6.w),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: constraints.maxWidth * 0.8,
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: _getStatusBackgroundColor(context),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getStatusText(context),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: _getStatusTextColor(context),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatDate(int? timestamp) {
    if (timestamp == null) return "Không có ngày";
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat("dd-MM-yyyy").format(date);
  }

  String _getStatusText(BuildContext context) {
    if (status == null) return "Không xác định";
    final blocState = context.read<PycBloc>().state;
    return blocState.getStatusLabel(status!) ?? "Không xác định";
  }

  Color _getStatusTextColor(BuildContext context) {
    final blocState = context.read<PycBloc>().state;
    return blocState.getStatusColor(status);
  }

  Color _getStatusBackgroundColor(BuildContext context) {
    final blocState = context.read<PycBloc>().state;
    return blocState.getStatusBackgroundColor(status);
  }
}
