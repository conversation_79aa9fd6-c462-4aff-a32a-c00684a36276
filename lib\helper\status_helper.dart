import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class StatusHelper {
  static const Map<String, String> _statusFixMap = {
    "COMPLETED": "COMPLETE",
    "CLOSED": "CLOSE",
  };

  static String? getStatusLabel(String? ticketStatus, Map<String, String> statusConfigMap) {
    if (ticketStatus == null) return null;
    final fixedStatus = _statusFixMap[ticketStatus] ?? ticketStatus;
    return statusConfigMap[fixedStatus] ?? fixedStatus;
  }

  static Color getStatusColor(String? ticketStatus) {
    final fixedStatus = _statusFixMap[ticketStatus] ?? ticketStatus;
    switch (fixedStatus) {
      case "PROCESSING":
      case "OPENED":
        return getColorSkin().primaryBlue;
      case "RECALLING":
      case "ADDITIONAL_REQUEST":
      case "AGREE_TO_RECALL":
      case "DELETED_BY_RU":
      case "RECALLED":
        return getColorSkin().orange;
      case "COMPLETE":
      case "CLOSE":
        return getColorSkin().successPrimary;
      case "CANCEL":
        return getColorSkin().red;
      case "DRAFT":
      case "ACTIVE":
      case "FOLLOWED":
        return getColorSkin().primaryText;
      default:
        return getColorSkin().primaryText;
    }
  }

  static Color getStatusBackgroundColor(String? ticketStatus) {
    final fixedStatus = _statusFixMap[ticketStatus] ?? ticketStatus;
    switch (fixedStatus) {
      case "PROCESSING":
      case "OPENED":
        return getColorSkin().blue2;
      case "RECALLING":
      case "ADDITIONAL_REQUEST":
      case "AGREE_TO_RECALL":
      case "DELETED_BY_RU":
      case "RECALLED":
        return getColorSkin().orange2;
      case "COMPLETE":
      case "CLOSE":
        return getColorSkin().green2;
      case "CANCEL":
        return getColorSkin().red2;
      case "DRAFT":
      case "ACTIVE":
      case "FOLLOWED":
        return getColorSkin().grey2;
      default:
        return getColorSkin().grey2;
    }
  }
} 