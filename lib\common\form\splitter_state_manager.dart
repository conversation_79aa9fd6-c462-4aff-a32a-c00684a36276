// import 'package:eapprove/models/form/form_item_info.dart';
// import 'package:eapprove/utils/expression_evaluator.dart';
// import 'package:eapprove/helper/event_expression.dart';
// import 'package:eapprove/common/form/form_state_manager.dart';
// import 'dart:developer' as developer;
// import 'package:flutter/foundation.dart';

// class SplitterStateManager {
//   final FormItemInfo _data;
//   final List<FormItemInfo>? _childFormItems;
//   final FormStateManager _formStateManager;
//   final VoidCallback? onStateChanged;
//   bool _isExpanded;
//   List<FormItemInfo> _children = [];
//   String? _description;
//   Map<String, dynamic> _formValues = {};
//   Map<String, bool> _visibilityStates = {};
//   Map<String, bool> _readonlyStates = {};
//   Map<String, String?> _errorStates = {};
//   Map<String, String> _modifiedLabels = {};

//   SplitterStateManager({
//     required FormItemInfo data,
//     List<FormItemInfo>? childFormItems,
//     required FormStateManager formStateManager,
//     this.onStateChanged,
//   })  : _data = data,
//         _childFormItems = childFormItems,
//         _formStateManager = formStateManager,
//         _isExpanded = data.splitterOpen == 2,
//         _description = data.splitterDesc {
//     _initializeChildren();
//   }

//   void _initializeChildren() {
//     // Don't render if display is false
//     if (_data.display == false) {
//       _children = [];
//       return;
//     }

//     // Find child items that belong to this splitter
//     if (_childFormItems != null) {
//       for (var item in _childFormItems!) {
//         final splitterId = item.splitter is String ? item.splitter as String : null;
//         if (splitterId != null && splitterId == _data.id) {
//           _children.add(item);
//           // Initialize form values and visibility states for each child
//           if (item.name != null) {
//             _formValues[item.name!] = item.value;
//             _visibilityStates[item.name!] = item.display ?? true;
//             _readonlyStates[item.name!] = item.readonly ?? false;
//             _errorStates[item.name!] = item.error;
//           }
//         }
//       }
//     }
//   }

//   bool get isVisible => _data.display != false && _children.isNotEmpty;
//   bool get isExpanded => _isExpanded;
//   List<FormItemInfo> get children => _children;
//   String? get description => _description;

//   void toggleExpanded() {
//     _isExpanded = !_isExpanded;
//   }

//   void setExpanded(bool value) {
//     _isExpanded = value;
//   }

//   void _notifyStateChanged() {
//     onStateChanged?.call();
//   }

//   void updateFieldValue(String fieldName, dynamic value) {
//     developer.log('Updating field in splitter: $fieldName with value: $value', name: 'SplitterStateManager');
    
//     // Update local form values
//     _formValues[fieldName] = value;
    
//     // Update form state manager
//     _formStateManager.updateFieldValue(fieldName, value);
    
//     // Evaluate expressions for all fields in the form
//     _evaluateFormExpressions();
    
//     // Notify state changed
//     _notifyStateChanged();
//   }

//   void _evaluateFormExpressions() {
//     developer.log('Evaluating form expressions', name: 'SplitterStateManager');
    
//     // Get all form values including those from other splitters
//     final allFormValues = _formStateManager.getFormValues();
    
//     // Update local form values with the latest from form state manager
//     _formValues = Map.from(allFormValues);
    
//     // Evaluate expressions for each child
//     for (var child in _children) {
//       if (child.name == null) continue;
      
//       if (child.eventExpression != null && child.eventExpression!.isNotEmpty) {
//         final actions = evaluateConditions(child.eventExpression!, allFormValues);
//         developer.log('Actions: $actions', name: 'SplitterStateManager');
        
//         bool stateChanged = false;
        
//         // Update states based on event expressions
//         if (actions.containsKey('hien_thi')) {
//           _visibilityStates[child.name!] = actions['hien_thi'];
//           stateChanged = true;
//         }
//         if (actions.containsKey('chi_duoc_doc')) {
//           _readonlyStates[child.name!] = actions['chi_duoc_doc'];
//           stateChanged = true;
//         }
//         if (actions.containsKey('valid')) {
//           _errorStates[child.name!] = actions['valid'];
//           stateChanged = true;
//         }
//         if (actions.containsKey('bat_buoc')) {
//           if (actions['bat_buoc'] == true && 
//               (allFormValues[child.name!] == null || allFormValues[child.name!].toString().isEmpty)) {
//             _errorStates[child.name!] = 'Trường này là bắt buộc';
//             stateChanged = true;
//           }
//         }
        
//         // Handle field value updates
//         if (actions.containsKey(child.name!)) {
//           _formValues[child.name!] = actions[child.name!];
//           _formStateManager.updateFieldValue(child.name!, actions[child.name!]);
//           stateChanged = true;
//         }
        
//         // Handle label changes
//         if (actions.containsKey('ten_truong')) {
//           _modifiedLabels[child.name!] = actions['ten_truong'];
//           stateChanged = true;
//         }

//         developer.log('Modified labels: $_modifiedLabels', name: 'SplitterStateManager');
//         developer.log('Form values: $_formValues', name: 'SplitterStateManager');
        
//         if (stateChanged) {
//           _notifyStateChanged();
//         }
//       }
//     }
//   }

//   bool isChildVisible(FormItemInfo child) {
//     if (child.name == null) return false;
    
//     // First check the default display value
//     if (child.display == false) return false;
    
//     // Then check event expression conditions
//     if (child.eventExpression != null && child.eventExpression!.isNotEmpty) {
//       final actions = evaluateConditions(child.eventExpression!, _formValues);
//       return actions['hien_thi'] ?? true;
//     }
    
//     // Finally check the visibility state
//     return _visibilityStates[child.name!] ?? true;
//   }

//   bool isChildReadonly(FormItemInfo child) {
//     if (child.name == null) return false;
//     return _readonlyStates[child.name!] ?? false;
//   }

//   String? getChildError(FormItemInfo child) {
//     if (child.name == null) return null;
//     return _errorStates[child.name!];
//   }

//   String getChildLabel(FormItemInfo child) {
//     if (child.name == null) return child.label ?? '';
//     return _modifiedLabels[child.name!] ?? child.label ?? '';
//   }
// }