import 'package:eapprove/utils/utils.dart';

class ServiceData {
  final int? id;
  final int? parentId;
  final String? serviceName;
  final String? color;
  final String? icon;
  final int? serviceType;
  final int? processId;
  final String? processName;
  final int? countChild;
  final DateTime? createdDate;
  final DateTime? modifiedDate;
  final String? companyCode;
  final String? companyName;
  final List<ServiceData>? child;
  final String? status;
  final String? procDefId;
  final bool? notShowingMoblie;
  final String? url;
  final int? positionPackage;  
  final bool? specialFlow;

  const ServiceData({
    this.id,
    this.parentId,
    this.serviceName,
    this.color,
    this.icon,
    this.serviceType,
    this.processId,
    this.processName,
    this.countChild,
    this.createdDate,
    this.modifiedDate,
    this.companyCode,
    this.companyName,
    this.child,
    this.status,
    this.procDefId,
    this.notShowingMoblie,
    this.url,
    this.positionPackage,  
    this.specialFlow,
  });

  factory ServiceData.fromJson(Map<String, dynamic> json) {
    List<ServiceData>? childList;
    if (json['child'] != null) {
      try {
        childList = (json['child'] as List<dynamic>)
            .map((item) => ServiceData.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (e) {
        childList = [];
      }
    } else {
      childList = [];
    }
    return ServiceData(
      id: json['id'],
      parentId: json['parentId'],
      serviceName: json['serviceName'] ?? '',
      color: json['color'] ?? '',
      icon: json['icon'] ?? '',
      serviceType: json['serviceType'] ?? 0,
      processId: json['processId'],
      processName: json['processName'] ?? '',
      countChild: json['countChild'] ?? 0,
      createdDate: json['createdDate'] != null ? Utils.dateTimeConverter(json['createdDate']) : null,
      modifiedDate: json['modifiedDate'] != null ? Utils.dateTimeConverter(json['modifiedDate']) : null,
      companyCode: json['companyCode'] ?? '',
      companyName: json['companyName'] ?? '',
      child: childList,
      status: json['status'] ?? '',
      procDefId: json['procDefId'],
      notShowingMoblie: json['notShowingMoblie'] ?? false,
      url: json['url'] as String?,
      positionPackage: json['positionPackage'],    
      specialFlow: json['specialFlow'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'serviceName': serviceName,
      'color': color,
      'icon': icon,
      'serviceType': serviceType,
      'processId': processId,
      'processName': processName,
      'countChild': countChild,
      'createdDate': createdDate?.toIso8601String(),
      'modifiedDate': modifiedDate?.toIso8601String(),
      'companyCode': companyCode,
      'companyName': companyName,
      'child': child?.map((c) => c.toJson()).toList(),
      'status': status,
      'procDefId': procDefId,
      'notShowingMoblie': notShowingMoblie,
      'url': url,
      'positionPackage': positionPackage,    
      'specialFlow': specialFlow,
    };
  }
}