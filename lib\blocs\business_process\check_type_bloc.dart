import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/business_process_repository.dart';

part 'check_type_event.dart';
part 'check_type_state.dart';

class CheckTypeBloc extends Bloc<CheckTypeEvent, CheckTypeState> {
  final BusinessProcessRepository _repository;

  CheckTypeBloc({required BusinessProcessRepository repository})
      : _repository = repository,
        super(CheckTypeInitial()) {
    on<CheckTypeRequested>(_onCheckTypeRequested);
    on<ClearCheckTypeState>(_onClearCheckTypeState);
  }

  void _onClearCheckTypeState(ClearCheckTypeState event, Emitter<CheckTypeState> emit) {
    emit(CheckTypeInitial());
  }

  Future<void> _onCheckTypeRequested(
    CheckTypeRequested event,
    Emitter<CheckTypeState> emit,
  ) async {
    try {
      emit(CheckTypeLoading());
      
      final response = await _repository.checkProcessType(
        event.serviceId,
        event.procDefId,
      );

      if (response['code'] == 1 && response['message'] == 'Success') {
        final data = CheckTypeResponseModel.fromJson(response['data']);
        emit(CheckTypeSuccess(data));
      } else {
        emit(CheckTypeFailure(response['message'] ?? 'Unknown error'));
      }
    } catch (e) {
      emit(CheckTypeFailure(e.toString()));
    }
  }
} 