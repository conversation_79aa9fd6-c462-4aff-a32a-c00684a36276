import 'dart:convert';

class UserProfileResponseModel {
  final MetaData meta;
  final UserProfileData? data;

  const UserProfileResponseModel({
    required this.meta,
    required this.data,
  });

  factory UserProfileResponseModel.fromJson(Map<String, dynamic> json) {
    return UserProfileResponseModel(
      meta: MetaData.fromJson(json['meta'] as Map<String, dynamic>),
      data: UserProfileData.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  factory UserProfileResponseModel.fromJsonString(String jsonString) {
    return UserProfileResponseModel.fromJson(
      json.decode(jsonString) as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'meta': meta.toJson(),
      'data': data?.toJson(),
    };
  }

  bool get isSuccess => meta.code == 200;
}

class MetaData {
  final int code;
  final String message;

  const MetaData({
    required this.code,
    required this.message,
  });

  factory MetaData.fromJson(Map<String, dynamic> json) {
    return MetaData(
      code: json['code'] as int,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
    };
  }
}

class UserProfileData {
  final String id;
  final String username;
  final bool enabled;
  final String? firstName;
  final String? lastName;
  final String? email;
  final Map<String, List<String>> attributes;

  const UserProfileData({
    required this.id,
    required this.username,
    required this.enabled,
    this.firstName,
    this.lastName,
    this.email,
    required this.attributes,
  });

  factory UserProfileData.fromJson(Map<String, dynamic> json) {
    final attributesMap = <String, List<String>>{};
    final rawAttributes = json['attributes'] as Map<String, dynamic>;

    rawAttributes.forEach((key, value) {
      if (value is List) {
        attributesMap[key] = (value as List).map((e) => e.toString()).toList();
      }
    });

    return UserProfileData(
      id: json['id'] as String,
      username: json['username'] as String,
      enabled: json['enabled'] as bool,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      email: json['email'] as String?,
      attributes: attributesMap,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'enabled': enabled,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'attributes': attributes,
    };
  }

  // Helper methods to easily access common attributes
  String? get alias => getFirstAttributeValue('ALIAS');
  String? get phone => getFirstAttributeValue('PHONE');
  String? get orgCode => getFirstAttributeValue('ORG_CODE');
  String? get positionId => getFirstAttributeValue('POSITION_ID');
  bool get isAdmin => getFirstAttributeValue('IS_ADMIN') == '1';
  bool get is2FA => getFirstAttributeValue('IS2FA') == 'true';

  List<String>? getLinkedAccounts() {
    final linkedAccountsStr = getFirstAttributeValue('LINKED_ACCOUNT_LIST');
    if (linkedAccountsStr == null) return null;
    return linkedAccountsStr.split(',');
  }

  String? getFirstAttributeValue(String key) {
    final values = attributes[key];
    if (values == null || values.isEmpty) return null;
    return values.first;
  }
}