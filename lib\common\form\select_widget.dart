import 'package:flutter/material.dart';
import 'package:eapprove/models/form/form_item_info.dart';

class SelectWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic) onChange;

  const SelectWidget({
    Key? key,
    required this.data,
    required this.onChange,
  }) : super(key: key);

  @override
  State<SelectWidget> createState() => _SelectWidgetState();
}

class _SelectWidgetState extends State<SelectWidget> {
  @override
  Widget build(BuildContext context) {
    final options = widget.data.options ?? [];
    final isMultiSelect = widget.data.validations?['isMulti'] == true;

    final labelWidget = Text(
      widget.data.label ?? '',
      style: TextStyle(
        fontWeight: widget.data.fontWeight == 'bold'
            ? FontWeight.bold
            : FontWeight.normal,
        fontStyle: widget.data.fontWeight == 'italic'
            ? FontStyle.italic
            : FontStyle.normal,
        decoration: widget.data.fontWeight == 'underline'
            ? TextDecoration.underline
            : TextDecoration.none,
      ),
    );

    // Check if isHorizontal is a double and convert to bool, or use directly if it's already bool
    bool isHorizontalBool = false;
    if (widget.data.isHorizontal != null) {
      isHorizontalBool = widget.data.isHorizontal is double
          ? widget.data.isHorizontal == 2.0
          : widget.data.isHorizontal == true;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: isHorizontalBool
          ? Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 120, child: labelWidget),
          Expanded(
            child: _buildDropdown(options, isMultiSelect),
          ),
        ],
      )
          : Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          labelWidget,
          const SizedBox(height: 8),
          _buildDropdown(options, isMultiSelect),
        ],
      ),
    );
  }

  Widget _buildDropdown(List<Map<String, dynamic>> options, bool isMultiSelect) {
    if (isMultiSelect) {
      return _buildMultiSelectDropdown(options);
    } else {
      return _buildSingleSelectDropdown(options);
    }
  }

  Widget _buildSingleSelectDropdown(List<Map<String, dynamic>> options) {
    return DropdownButtonFormField<String>(
      value: widget.data.value?.toString(),
      decoration: InputDecoration(
        hintText: widget.data.placeholder,
        helperText: widget.data.tooltip,
        errorText: widget.data.error?.isNotEmpty == true ? widget.data.error : null,
        border: const OutlineInputBorder(),
      ),
      isExpanded: true,
      items: options.map((option) {
        return DropdownMenuItem<String>(
          value: option['value']?.toString() ?? '',
          child: Text(option['label']?.toString() ?? ''),
        );
      }).toList(),
      onChanged: widget.data.readonly == true
          ? null
          : (value) {
        setState(() {
          widget.data.value = value;
        });
        widget.onChange(widget.data.name ?? '', value);
      },
    );
  }

  Widget _buildMultiSelectDropdown(List<Map<String, dynamic>> options) {
    // Initialize selected values
    List<String> selectedValues = widget.data.value != null
        ? List<String>.from(widget.data.value)
        : [];

    return InputDecorator(
      decoration: InputDecoration(
        hintText: widget.data.placeholder,
        helperText: widget.data.tooltip,
        errorText: widget.data.error?.isNotEmpty == true ? widget.data.error : null,
        border: const OutlineInputBorder(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display selected values
          if (selectedValues.isNotEmpty)
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: selectedValues.map((value) {
                String label = options
                    .firstWhere(
                      (option) => (option['value']?.toString() ?? '') == value,
                  orElse: () => {'label': value},
                )['label']?.toString() ?? value;

                return Chip(
                  label: Text(label),
                  onDeleted: widget.data.readonly == true
                      ? null
                      : () {
                    setState(() {
                      selectedValues.remove(value);
                      widget.data.value = selectedValues;
                    });
                    widget.onChange(widget.data.name ?? '', selectedValues);
                  },
                );
              }).toList(),
            ),

          // Dropdown button
          Align(
            alignment: Alignment.centerRight,
            child: ElevatedButton(
              onPressed: widget.data.readonly == true
                  ? null
                  : () {
                _showMultiSelectDialog(options, selectedValues);
              },
              child: const Text('Select Options'),
            ),
          ),
        ],
      ),
    );
  }

  void _showMultiSelectDialog(List<Map<String, dynamic>> options, List<String> selectedValues) {
    // Create a temporary list to store the selected values during dialog interaction
    List<String> tempSelectedValues = List<String>.from(selectedValues);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(widget.data.label ?? 'Select Options'),
              content: SingleChildScrollView(
                child: ListBody(
                  children: options.map((option) {
                    final String value = option['value']?.toString() ?? '';
                    final String label = option['label']?.toString() ?? '';
                    final bool isSelected = tempSelectedValues.contains(value);

                    return CheckboxListTile(
                      value: isSelected,
                      title: Text(label),
                      onChanged: (bool? checked) {
                        setDialogState(() {
                          if (checked == true) {
                            if (!tempSelectedValues.contains(value)) {
                              tempSelectedValues.add(value);
                            }
                          } else {
                            tempSelectedValues.remove(value);
                          }
                        });
                      },
                    );
                  }).toList(),
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('Cancel'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('OK'),
                  onPressed: () {
                    setState(() {
                      widget.data.value = tempSelectedValues;
                    });
                    widget.onChange(widget.data.name ?? '', tempSelectedValues);
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}