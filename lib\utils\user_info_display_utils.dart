import 'package:eapprove/models/authorize_management/list_user_info_response.dart';

class UserInfoDisplayUtils {
  static String getUserDisplayName(UserInfo userInfo) {
    return '${userInfo.lastname} ${userInfo.firstname}';
  }

  static String getUserSubtitle(UserInfo userInfo) {
    return userInfo.titleName;
  }

  static String getUserFullDisplay(UserInfo userInfo) {
    return '${getUserDisplayName(userInfo)} - ${getUserSubtitle(userInfo)}';
  }

  static String getUserDisplayWithUsername(UserInfo userInfo) {
    return '${userInfo.username} - ${getUserFullDisplay(userInfo)}';
  }
} 