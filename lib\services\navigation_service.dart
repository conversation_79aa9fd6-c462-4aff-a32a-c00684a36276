import 'package:flutter/material.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  VoidCallback? _onHomeScreenBack;
  VoidCallback? _onLogout;

  void setCallbacks({
    VoidCallback? onHomeScreenBack,
    VoidCallback? onLogout,
  }) {
    _onHomeScreenBack = onHomeScreenBack;
    _onLogout = onLogout;
  }

  VoidCallback? get onHomeScreenBack => _onHomeScreenBack;
  VoidCallback? get onLogout => _onLogout;

  void clearCallbacks() {
    _onHomeScreenBack = null;
    _onLogout = null;
  }
} 