import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_bloc.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_event.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_state.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_event.dart';
import 'package:eapprove/models/authorize_management/authorize_management_request_model.dart';
import 'package:eapprove/screens/authorize_management/authorize_search_screen.dart';
import 'package:eapprove/screens/authorize_management/widgets/authorize_card.dart';
import 'package:eapprove/screens/user/user_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_tab_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';

class AuthorizeManagementScreen extends StatefulWidget {
  static const routeName = "/authorize_management";

  static Route route() {
    return MaterialPageRoute<void>(
        builder: (_) => const AuthorizeManagementScreen());
  }

  const AuthorizeManagementScreen({super.key});

  @override
  State<AuthorizeManagementScreen> createState() =>
      _AuthorizeManagementScreenState();
}

class _AuthorizeManagementScreenState extends State<AuthorizeManagementScreen> {
  int page = 1;
  List<int> status = [0, -1, -2, -3];
  int _selectedTabIndex = 0;
  final isTablet = DeviceUtils.isTablet;
  Box box = Hive.box('authentication');
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _selectedTabIndex);

    getAuthorizeManagementList(page, null);
    _loadUserInfo();
  }

  Future<void> getAuthorizeManagementList(pageValue, status) async {
    final username = box.get('username', defaultValue: '');
    context.read<AuthorizeManagementBloc>().add(FetchAuthorizeList(
        requestModel: AuthorizeManagementRequestModel(
            search: "",
            sortBy: "createdDate",
            sortType: "DESC",
            status: status ?? [0, -1, -2, -3],
            limit: 10,
            page: pageValue,
            userLogin: username)));
  }

  Future<void> getNextPageAuthorizeManagementList() async {
    page++;
    final username = box.get('username', defaultValue: '');
    context.read<AuthorizeManagementBloc>().add(FetchAuthorizeList(
        requestModel: AuthorizeManagementRequestModel(
          search: "",
          sortBy: "createdDate",
          sortType: "DESC",
          status: status,
          limit: 10,
          page: page,
          userLogin: username,
        ),
        isLoadMore: true));
  }

  void _loadUserInfo() {
    context.read<UserInfoBloc>().add(GetUserInfo(
          concurrently: [0],
          managerLevel: ['primary', 'secondary', 'staff'],
          search: '',
          sortBy: 'id',
          sortType: 'ASC',
          page: 1,
          limit: 99999999,
          size: 99999999,
        ));
  }

  Future<void> _onRefresh() async {
    page = 1;
    final username = box.get('username', defaultValue: '');
    context.read<AuthorizeManagementBloc>().add(FetchAuthorizeList(
        requestModel: AuthorizeManagementRequestModel(
            search: "",
            sortBy: "createdDate",
            sortType: "DESC",
            status: status,
            limit: 10,
            page: 1,
            userLogin: username)));
  }

  @override
  Widget build(BuildContext context) {
    final List<String> tabTitles = ['Tất cả', 'Đang hiệu lực', 'Hết hiệu lực'];

    return GradientBackground(
      showBottomImage: false,
      showUpperImage: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: CustomAppBar(
          backgroundColor: Colors.transparent,
          leading: isTablet
              ? null
              : IconButton(
                  icon: SvgPicture.asset(StringImage.ic_arrow_left),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
          title: 'Quản lý ủy quyền',
          titleTextStyle: isTablet
              ? getTypoSkin().title3Medium.copyWith(color: getColorSkin().ink1)
              : getTypoSkin().medium20.copyWith(color: getColorSkin().ink1),
          titleSpacing: isTablet ? 0 : -10.w,
          actionsPadding: EdgeInsets.zero,
          centerTitle: isTablet ? true : false,
          actions: [
            Transform.translate(
              offset: isTablet ? Offset(0, 0) : Offset(17, 0),
              child: IconButton(
                iconSize: 24.w.h,
                splashColor: getColorSkin().transparent,
                highlightColor: getColorSkin().transparent,
                icon: SvgPicture.asset(StringImage.ic_search),
                onPressed: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const UyQuyenSearchScreen()));
                },
              ),
            ),
            if (!isTablet)
              IconButton(
                iconSize: 24.w.h,
                highlightColor: getColorSkin().transparent,
                icon: SvgPicture.asset(StringImage.ic_user),
                onPressed: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const UserScreen()));
                },
              ),
          ],
        ),
        body: Column(
          children: [
            // CustomTabBar without tab contents
            CustomTabBar(
              key: ValueKey(
                  _selectedTabIndex), // Thay đổi Key khi _selectedTabIndex thay đổi
              visibleTabContents: false,
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
              tabTitles: tabTitles,
              initialIndex: _selectedTabIndex, // Cập nhật initialIndex
              selectedBackgroundColor:
                  getColorSkin().secondaryColor1TagBackground,
              unselectedBackgroundColor: getColorSkin().grey3Background,
              selectedTextColor: getColorSkin().secondaryColor1,
              unselectedTextColor: getColorSkin().subtitle,
              tabContents: List.generate(tabTitles.length, (i) {
                return SizedBox.shrink();
              }),
              onTabSelected: (index) {
                setState(() {
                  _selectedTabIndex = index; // Cập nhật tab được chọn
                });
                _pageController
                    .jumpToPage(index); // hoặc animateToPage(index, ...)

                if (index == 0) {
                  status = [0, -1, -2, -3];
                  page = 1;
                } else if (index == 1) {
                  status = [0];
                  page = 1;
                } else if (index == 2) {
                  status = [-1, -2, -3];
                  page = 1;
                }
                getAuthorizeManagementList(page, status);
              },
            ),
            // listview
            Expanded(
              child: Container(
                color: getColorSkin().transparent,
                child: BlocBuilder<AuthorizeManagementBloc,
                    AuthorizeManagementState>(
                  builder: (context, state) {
                    final List<Widget> tabContentsWidget = [];
                    for (var i = 0; i < 3; i++) {
                      tabContentsWidget.add(
                        state.status == AuthorizeManagementStatus.loading
                            ? ListView.builder(
                                itemCount: 6,
                                itemBuilder: (_, __) =>
                                    AppConstraint.buildShimmer(
                                  child: Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: 4.h, horizontal: 12.w),
                                    padding: EdgeInsets.all(8.w),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          height: 40.h,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                        ),
                                        SizedBox(height: 4.h),
                                        Container(
                                          width: 150.w,
                                          height: 14.h,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                        ),
                                        SizedBox(height: 8.h),
                                        Row(
                                          children: [
                                            Container(
                                              width: 16.w,
                                              height: 16.h,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            ),
                                            SizedBox(width: 6.w),
                                            Container(
                                              width: 80.w,
                                              height: 12.h,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            ),
                                            const Spacer(),
                                            Container(
                                              width: 80.w,
                                              height: 24.h,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : state.status == AuthorizeManagementStatus.loaded
                                ? Container(
                                    padding: EdgeInsets.only(
                                        left: 8.w, right: 8.w, bottom: 36.h),
                                    child: AuthorizeCard(
                                      data: i == 0
                                          ? state.authorizeManagementListAll
                                          : i == 1
                                              ? state
                                                  .authorizeManagementListOngoing
                                              : state
                                                  .authorizeManagementListExpired,
                                      onRefresh: _onRefresh,
                                      updateAuthorizeManagementList:
                                          getNextPageAuthorizeManagementList,
                                    ),
                                  )
                                : state.status ==
                                        AuthorizeManagementStatus.error
                                    ? Center(
                                        child: Text(state.errorMessage ??
                                            'Đã xảy ra lỗi'))
                                    : const Center(
                                        child: Text('Không có dữ liệu')),
                      );
                    }

                    // Sử dụng PageView để cuộn ngang
                    return PageView.builder(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() {
                          _selectedTabIndex = index; // Cập nhật tab được chọn
                        });
                        if (index == 0) {
                          status = [0, -1, -2, -3];
                          page = 1;
                        } else if (index == 1) {
                          status = [0];
                          page = 1;
                        } else if (index == 2) {
                          status = [-1, -2, -3];
                          page = 1;
                        }
                        getAuthorizeManagementList(page, status);
                      },
                      itemCount: tabContentsWidget.length,
                      itemBuilder: (context, index) {
                        return tabContentsWidget[index];
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
