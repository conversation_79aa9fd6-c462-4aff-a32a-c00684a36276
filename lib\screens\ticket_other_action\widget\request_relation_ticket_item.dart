import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/ticket_other_action/request_relation_ticket_history_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class RequestRelationTicketItem extends StatelessWidget {
  final RequestRelationTicketHistoryModel item;
  const RequestRelationTicketItem({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Color(0xFFE6F7FF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${item.code} - ${item.name}',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Color(0xFF262626),
            ),
          ),
          Container(
            width: double.infinity,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  flex: 6, // 60% width
                  child: Text(
                    item.user,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Color(0xFF595959),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Flexible(
                  flex: 4, // 40% width
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: item.status.getStatusTag(),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Text(
                item.fromDate,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Color(0xFF8C8C8C),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: SvgPicture.asset(
                  StringImage.ic_arrow_right,
                  width: 12.w,
                  height: 12.h,
                  colorFilter: ColorFilter.mode(Color(0xFF8C8C8C),
                      BlendMode.srcIn),
                ),
              ),
              Text(
                item.toDate ?? '',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Color(0xFF8C8C8C),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            item.description,
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xFF262626),
              fontWeight: FontWeight.bold,

            ),
          ),
        ],
      ),
    );
  }
}
