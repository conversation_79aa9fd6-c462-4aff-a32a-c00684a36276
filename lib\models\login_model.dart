class LoginModel {
  final String code;
  final String message;
  final String status;
  final String timestamp;
  final dynamic data; // data có thể null hoặc chứa dữ liệu kh<PERSON>c

  <PERSON>ode<PERSON>({
    required this.code,
    required this.message,
    required this.status,
    required this.timestamp,
    this.data,
  });

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    return LoginModel(
      code: json['code'] ?? '',
      message: json['message'] ?? '',
      status: json['status'] ?? '',
      timestamp: json['timestamp'] ?? '',
      data: json['data'],
    );
  }
}
