import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/main.dart';
import 'package:eapprove/models/account/chart_node_by_code_model.dart';
import 'package:eapprove/models/account/chart_node_response_model.dart';
import 'package:eapprove/models/form/form_request_body.dart';
import 'package:eapprove/models/form/form_xml_response_model.dart';
import 'package:eapprove/models/form/individual_info_request_model.dart';
import 'package:eapprove/models/form/md_service_response.dart';
import 'package:eapprove/models/form/submitter_info_response_model.dart';
import 'package:eapprove/models/form/bpmProcInst_create_response_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/repositories/account_repository.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:developer' as developer;

import 'package:eapprove/models/form/individual_info_response_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

class FormBloc extends Bloc<FormEvent, FormInfoState> {
  final FormRepository _formRepository;
  final AccountRepository _accountRepository;
  final Map<String, IndividualInfoResponse> _individualInfoCache = {};
  final Map<String, MdServiceResponseModel> _mdServiceCache = {};
  final Map<String, bool> _pendingRequests = {};

  FormBloc({
    required FormRepository formRepository,
    required AccountRepository accountRepository,
  })  : _formRepository = formRepository,
        _accountRepository = accountRepository,
        super(const FormInfoState()) {
    // Register all event handlers
    on<GetUserInfoByUsername>(_onGetUserInfoByUsername);
    on<GetFinalTitleByListUser>(_onGetFinalTitleByListUser);
    on<LoadFormRequested>(_onLoadFormRequested);
    on<GetChartDataRequested>(_onGetChartDataRequested);
    on<GetNodeByUserAndChartIdRequested>(_onGetNodeByUserAndChartIdRequested);
    on<GetMdServiceRequested>(_onGetMdServiceRequested);
    on<FormReset>(_onFormReset);
    on<ClearUploadState>(_onClearUploadState);
    on<GetBpmProcdefGetAllRequested>(_onGetBpmProcdefGetAllRequested);
    on<LoadAllFormDataRequested>(_onLoadAllFormDataRequested);
    on<TaoToTrinhRequested>(_onTaoToTrinhRequested);
    on<GetApprovalVinaphacoRequested>(_onGetApprovalVinaphacoRequested);
    on<UploadFileRequested>(_onUploadFileRequested);
    on<UploadMultiFilesRequested>(_onUploadMultiFilesRequested);
    on<CreateBpmProcInstRequested>(_onCreateBpmProcInstRequested);
    on<CallService>(_onCallService);
    on<GetListBaseUrl>(_onGetListBaseUrl);
    on<GetServiceByIdWithPermission>(_onGetServiceByIdWithPermissionRequested);
    on<GetDefaultSignatureRequested>(_onGetDefaultSignatureRequested);
    on<GetAccountMultiChartRequested>(_onGetAccountMultiChartRequested);
    on<GetPriorityManagementRequested>(_onGetPriorityManagementRequested);
    // on<FormSubmitting>(_onFormSubmitting);
  }

  void _onClearUploadState(
      ClearUploadState event, Emitter<FormInfoState> emit) {
    emit(state.copyWith(
      uploadFileResponse: null,
      name: null,
      fileType: null,
      isUploadingFile: false,
    ));
  }

  Future<void> _onGetUserInfoByUsername(
    GetUserInfoByUsername event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));
      developer.log('getUserInfoByUserName: ${event.username}',
          name: 'FormBloc');
      final response = await _formRepository.getUserInfoByUserName(
        username: event.username,
      );
      developer.log('getUserInfoByUserName response: ${response.toJson()}',
          name: 'FormBloc');
      emit(state.copyWith(
        isLoading: false,
        userInfoByUsernameResponse: response,
      ));
    } catch (e) {
      debugPrint('Error getting user info: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetUserInfoByUsername: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetUserInfoByUsername: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onLoadAllFormDataRequested(
    LoadAllFormDataRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isFormLoading: true, errorMessage: null, isNetworkError: false));

      // Load form XML
      final xmlResponse = await _formRepository.getFormXmlData(
        procDefId: event.procDefId,
      );

      final bpmnDefinition = xmlResponse.parseXml();
      // developer.log('bpmnDefinition: ${bpmnDefinition.process.elements.map((e) => {
      //   'type': e.runtimeType.toString(),
      //   'id': e.id,
      //   'name': e.name,
      //   if (e is BpmnUserTask) 'assignee': e.assignee?.contains('jsonArrayToString') == true
      //     ? e.assignee?.split('(').last.split(')').first
      //     : e.assignee,
      //   if (e is BpmnStartEvent) 'formKey': e.formKey,
      // }).toList()}', name: 'FormBloc');

      String? formKey;
      for (final element in bpmnDefinition.process.elements) {
        if (element is BpmnStartEvent) {
          formKey = element.formKey;
          break;
        }
      }

      if (formKey == null) {
        throw Exception('Không tim thấy formKey ');
      }

      // Get the form template
      final formResponse = await _formRepository.getFormTemplate(
        requestBody: FormRequestBody(
          urlName: formKey,
          procInstId: '',
          taskDefinitionKey: '',
        ),
      );

      // Load chart data
      final chartData = await _accountRepository.getChartData();

      // Load submitter

      final submitterInfoResponse =
          (event.processId != null && event.processId! > 0)
              ? await _formRepository.getBpmProcdefGetAll(
                  processId: event.processId!)
              : null;

      final allSubmissionType = await _formRepository.getAllSubmissionType();
      developer.log('All submission types: ${allSubmissionType.toJson()}',
          name: 'FormBloc');

      // Get chart node data
      final Box authBox = Hive.box('authentication');
      final String? username = authBox.get('username');

      final chartNodeData =
          chartData != null && chartData.data.isNotEmpty && username != null
              ? await _accountRepository.getNodeByUserAndChartId(
                  chartId: chartData.data.first.id,
                  username: username,
                  concurrently: -1,
                )
              : null;

      emit(state.copyWith(
        isFormLoading: false,
        xmlResponse: xmlResponse,
        bpmnDefinition: bpmnDefinition,
        formResponse: formResponse,
        chartData: chartData,
        chartNodeData: chartNodeData,
        submitterInfoResponse: submitterInfoResponse,
        allSubmissionType: allSubmissionType,
      ));
    } catch (e) {
      debugPrint('Error loading form data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isFormLoading: false,
          errorMessage: '_onLoadAllFormDataRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isFormLoading: false,
          errorMessage: '_onLoadAllFormDataRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onLoadFormRequested(
    LoadFormRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      debugPrint('▶️ Start _onLoadFormRequested');
      emit(state.copyWith(
          isLoading: true,
          errorMessage: null,
          isNetworkError: false,
          isFormLoading: true));

      // Step 1: Get and parse BPMN XML
      final xmlResponse =
          await _formRepository.getFormXmlData(procDefId: event.procDefId);
      final bpmnDefinition = xmlResponse.parseXml();

      // Step 2: Extract formKey and taskDefinitionKey from start event
      String? formKey;
      String? taskDefinitionKey;
      // Tìm UserTask có tên chứa "Người ký" hoặc có assignee
      for (final element in bpmnDefinition.process.elements) {
        if (element is BpmnUserTask) {
          if (event.isApprove == false) {
            formKey = element.formKey;
            taskDefinitionKey = element.id;
            developer.log(
                '[🧩 taskDefinitionKey] ID: ${element.id}, FormKey: $formKey',
                name: 'FormBloc');
            break;
          } else {
            formKey = element.formKey;
            taskDefinitionKey = element.id;
            developer.log(
                '[🧩 taskDefinitionKey] ID: ${element.id}, FormKey: $formKey',
                name: 'FormBloc');
            break;
          }
        }
      }

      developer.log('LoadFormRequestedformKey: $formKey',
          name: 'LoadFormRequestedformKey');
      developer.log('LoadFormRequestedtaskDefinitionKey: $taskDefinitionKey',
          name: 'LoadFormRequestedformKey');

      if (formKey == null)
        throw Exception('❌ No formKey found in BPMN StartEvent');

      // Step 3: Determine procInstId if not creating ticket
      String procInstId = '';
      if (!event.isCreateTicket && event.ticket != null) {
        final ticket = event.ticket;
        try {
          if (ticket is MyPycContent || ticket is AssisContent) {
            procInstId = (ticket as dynamic).ticketId?.toString() ?? '';
          } else if (ticket is ProcContent || ticket is ApproveContent) {
            procInstId = ticket.procInstId;
            developer.log('procInstId1223445: $procInstId', name: 'FormBloc');
          }
        } catch (e) {
          developer.log('⚠️ Failed to extract procInstId from ticketId: $e');
        }
      }

      developer.log(
          '📥 Calling getFormTemplate with urlName=$formKey, procInstId=$procInstId, taskDefinitionKey=$taskDefinitionKey');
      final resquestBody = FormRequestBody(
        urlName: formKey,
        procInstId: event.isCreateTicket ? '' : procInstId,
        taskDefinitionKey: event.taskDefKey ?? taskDefinitionKey ?? '',
      );

      developer.log('resquestBody: ${resquestBody.toJson()}',
          name: 'LoadFormRequestedformKey');

      // Step 4: Load form template
      final formResponse = await _formRepository.getFormTemplate(
        requestBody: resquestBody,
      );
      developer.log("formResponse:: $formResponse");

      // Step 5: Parallel requests
      final chartData = await _accountRepository.getChartData();
      final individualInfoResponse = await _formRepository.getIndividualInfo(
        requestBody: IndividualInfoRequestModel.defaultFilter(),
      );

      // Step 6: Get chart node info (if available)
      ChartNodeResponseModel? chartNodeData;
      ChartNodeByCodeModel? chartNodeCustomData;

      if (chartData.data.isNotEmpty) {
        final Box authBox = Hive.box('authentication');
        final String? username = authBox.get('username');

        if (username != null) {
          chartNodeData = await _accountRepository.getNodeByUserAndChartId(
            chartId: chartData.data.first.id,
            username: username,
            concurrently: -1,
          );
        }
      }
      developer.log(
          "chartNodeDatachartNodeData ${chartNodeData?.code} ${chartNodeData?.data.first.chartNodeCode}");
      if (chartNodeData != null && chartNodeData.data.isNotEmpty) {
        chartNodeCustomData = await _accountRepository.getChartNodeCustomByCode(
          code: chartNodeData.data.first.chartNodeCode,
        );
      }

      // Step 7: Get submitter info if available
      SubmitterInfoResponseModel? submitterInfoResponse;
      if (event.processId != null) {
        submitterInfoResponse = await _formRepository.getBpmProcdefGetAll(
          processId: event.processId!,
        );
      }

      // Step 8: Emit final loaded state
      emit(state.copyWith(
        isLoading: false,
        isFormLoading: false,
        xmlResponse: xmlResponse,
        bpmnDefinition: bpmnDefinition,
        formResponse: formResponse,
        chartData: chartData,
        chartNodeData: chartNodeData,
        chartNodeCustomData: chartNodeCustomData,
        userInfoResponse: individualInfoResponse,
        submitterInfoResponse: submitterInfoResponse,
      ));
    } catch (e) {
      final msg = e.toString();
      debugPrint('❌ Error loading form: $msg');
      developer.log('[ERROR] _onLoadFormRequested: $msg');

      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          isFormLoading: false,
          errorMessage: 'No internet: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        developer
            .log("_onLoadFormRequested_onLoadFormRequested: ${e.toString()}");
        emit(state.copyWith(
          isLoading: false,
          isFormLoading: false,
          errorMessage: 'Unexpected error: $msg',
          isNetworkError: false,
        ));
      }
    }
  }

  // Future<void> _onGetIndividualInfoRequested(
  //   GetIndividualInfoRequested event,
  //   Emitter<FormInfoState> emit,
  // ) async {
  //   try {
  //     debugPrint('GetIndividualInfoRequested event received');
  //     debugPrint(
  //         'Request body GetIndividualInfoRequested: ${event.requestModel.toJson()}');
  //     debugPrint('Dependent value: ${event.dependentValue}');

  //     // Create a cache key from the request model and any dependent values
  //     final cacheKey =
  //         '${event.requestModel.toJsonString()}_${event.dependentValue ?? ''}';

  //     // Check if request is already pending
  //     if (_pendingRequests[cacheKey] == true) {
  //       debugPrint('Request already pending for key: $cacheKey');
  //       return;
  //     }

  //     // Check cache first
  //     if (_individualInfoCache.containsKey(cacheKey)) {
  //       debugPrint('Using cached individual info data');
  //       final response = _individualInfoCache[cacheKey];

  //       // Determine which response to update based on the request type and context
  //       if (event.requestModel.infoType == 'user') {
  //         if (event.dependentValue == null) {
  //           // This is a regular user dropdown request
  //           emit(state.copyWith(
  //             userInfoResponse: response,
  //             isLoading: false,
  //             errorMessage: null,
  //             isNetworkError: false,
  //           ));
  //         } else {
  //           // This is a department dropdown request that depends on a user
  //           emit(state.copyWith(
  //             departmentInfoResponse: response,
  //             isLoading: false,
  //             errorMessage: null,
  //             isNetworkError: false,
  //           ));
  //         }
  //       } else {
  //         // This is an inform for user request
  //         emit(state.copyWith(
  //           informForUserInfoResponse: response,
  //           isLoading: false,
  //           errorMessage: null,
  //           isNetworkError: false,
  //         ));
  //       }
  //       return;
  //     }

  //     // Mark request as pending
  //     _pendingRequests[cacheKey] = true;

  //     final individualInfoResponse = await _formRepository.getIndividualInfo(
  //       requestBody: event.requestModel,
  //     );

  //     debugPrint(
  //         'Individual info response: ${individualInfoResponse.toJson()}');
  //     debugPrint(
  //         'Individual info data length: ${individualInfoResponse.data.length}');

  //     // Cache the response
  //     _individualInfoCache[cacheKey] = individualInfoResponse;

  //     // Create a new state with the updated response based on the request type
  //     final newState = state.copyWith(
  //       userInfoResponse: event.requestModel.infoType == 'user' &&
  //               event.dependentValue == null
  //           ? individualInfoResponse
  //           : state.userInfoResponse,
  //       departmentInfoResponse: event.requestModel.infoType == 'user' &&
  //               event.dependentValue != null
  //           ? individualInfoResponse
  //           : state.departmentInfoResponse,
  //       informForUserInfoResponse: event.requestModel.infoType != 'user'
  //           ? individualInfoResponse
  //           : state.informForUserInfoResponse,
  //       isLoading: false,
  //       errorMessage: null,
  //       isNetworkError: false,
  //     );

  //     debugPrint('Emitting new state with individual info data');
  //     emit(newState);
  //   } catch (e) {
  //     debugPrint('Error getting individual info: $e');
  //     if (e is NoInternetException) {
  //       emit(state.copyWith(
  //         errorMessage: '_onGetIndividualInfoRequested: ${e.message}',
  //         isNetworkError: true,
  //         isLoading: false,
  //       ));
  //     } else {
  //       emit(state.copyWith(
  //         errorMessage: '_onGetIndividualInfoRequested: ${e.toString()}',
  //         isNetworkError: false,
  //         isLoading: false,
  //       ));
  //     } finally {
  //       // Clear pending request flag
  //       _pendingRequests.remove(
  //           '${event.requestModel.toJsonString()}_${event.dependentValue ?? ''}');
  //     }
  //   }
  // }

  Future<void> _onGetChartDataRequested(
    GetChartDataRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      final chartData = await _accountRepository.getChartData();
      debugPrint('Chart data: $chartData');

      emit(state.copyWith(
        chartData: chartData,
      ));
    } catch (e) {
      debugPrint('Error getting chart data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          errorMessage: '_onGetChartDataRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          errorMessage: '_onGetChartDataRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetNodeByUserAndChartIdRequested(
    GetNodeByUserAndChartIdRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      final Box authBox = Hive.box('authentication');
      final String? username = authBox.get('username');

      if (username == null) {
        throw Exception('Username not found in authentication box');
      }

      if (state.chartData == null || state.chartData!.data.isEmpty) {
        debugPrint('Waiting for chart data to be available');
        return;
      }

      final int chartId = state.chartData!.data.first.id;
      const int concurrently = -1;

      final chartNodeData = await _accountRepository.getNodeByUserAndChartId(
        chartId: chartId,
        username: 'employee',
        concurrently: concurrently,
      );

      emit(state.copyWith(
        chartNodeData: chartNodeData,
      ));
    } catch (e) {
      debugPrint('Error getting chart node data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          errorMessage: '_onGetNodeByUserAndChartIdRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          errorMessage: '_onGetNodeByUserAndChartIdRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetMdServiceRequested(
    GetMdServiceRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      debugPrint('GetMdServiceRequested event received');
      debugPrint(
          'Request body masterDataId: ${event.requestBody.masterDataId}');
      debugPrint(
          'Request body conditionFilter: ${event.requestBody.conditionFilter}');

      // Create a cache key from the request body
      final cacheKey =
          '${event.requestBody.masterDataId}_${event.requestBody.conditionFilter.map((f) => f.toJson()).toList()}';

      // Check if request is already pending
      if (_pendingRequests[cacheKey] == true) {
        debugPrint('Request already pending for key: $cacheKey');
        return;
      }

      // Check cache first
      if (_mdServiceCache.containsKey(cacheKey)) {
        debugPrint('Using cached MD service data');
        emit(state.copyWith(
          mdServiceResponse: _mdServiceCache[cacheKey],
          isLoading: false,
          errorMessage: null,
          isNetworkError: false,
        ));
        return;
      }

      // Mark request as pending
      _pendingRequests[cacheKey] = true;

      final mdServiceResponse = await _formRepository.getMdService(
        requestBody: event.requestBody,
      );

      debugPrint(
          'MdService response received: ${mdServiceResponse.data.length} items');
      debugPrint('Response data: ${mdServiceResponse.toJson()}');

      // Cache the response
      _mdServiceCache[cacheKey] = mdServiceResponse;

      emit(state.copyWith(
        mdServiceResponse: mdServiceResponse,
        isLoading: false,
        errorMessage: null,
        isNetworkError: false,
      ));
    } catch (e) {
      debugPrint('Error getting MD service data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          errorMessage: '_onGetMdServiceRequested: ${e.message}',
          isNetworkError: true,
          isLoading: false,
        ));
      } else {
        emit(state.copyWith(
          errorMessage: '_onGetMdServiceRequested: ${e.toString()}',
          isNetworkError: false,
          isLoading: false,
        ));
      }
    } finally {
      // Clear pending request flag
      _pendingRequests.remove(
          '${event.requestBody.masterDataId}_${event.requestBody.conditionFilter.map((f) => f.toJson()).toList()}');
    }
  }

  void _onFormReset(FormReset event, Emitter<FormInfoState> emit) {
    // Clear all caches
    _individualInfoCache.clear();
    _mdServiceCache.clear();
    _pendingRequests.clear();

    // Reset to initial state with all fields null
    emit(const FormInfoState(
      isLoading: false,
      isSubmitting: false,
      isUploadingFile: false,
      isPrintingFile: false,
      errorMessage: null,
      isNetworkError: false,
      xmlResponse: null,
      bpmnDefinition: null,
      formResponse: null,
      userInfoResponse: null,
      departmentInfoResponse: null,
      informForUserInfoResponse: null,
      chartData: null,
      chartNodeData: null,
      mdServiceResponse: null,
      submitterInfoResponse: null,
      taoToTrinhResponseModel: null,
      confirmSubmitResponseModel: null,
      uploadFileResponse: null,
      uploadMultipleFileResponse: null,
      bpmProcInstCreateResponse: null,
      callServiceResponse: null,
      listBaseUrlResponse: null,
      serviceByIdWithPermissionResponse: null,
      submissionTypeName: null,
      userInfoByUsernameResponse: null,
      titleByUserResponse: null,
      defaultSignatureResponse: null,
      allSubmissionType: null,
      submissionType: null,
      name: null,
      fileType: null,
    ));
  }

  Future<void> _onGetBpmProcdefGetAllRequested(
    GetBpmProcdefGetAllRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      final submitterInfoResponse = await _formRepository.getBpmProcdefGetAll(
        processId: event.processId,
      );

      emit(state.copyWith(
        submitterInfoResponse: submitterInfoResponse,
      ));
    } catch (e) {
      debugPrint('Error getting BPM process definition: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          errorMessage: '_onGetBpmProcdefGetAllRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          errorMessage: '_onGetBpmProcdefGetAllRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onTaoToTrinhRequested(
    TaoToTrinhRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isPrintingFile: true, errorMessage: null, isNetworkError: false));

      final response =
          await _formRepository.taoToTrinh(requestBody: event.requestBody);
      debugPrint('TaoToTrinh response: ${response.toJson()}');

      emit(state.copyWith(
        isPrintingFile: false,
        taoToTrinhResponseModel: response,
      ));
    } catch (e) {
      debugPrint('Error creating form: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isPrintingFile: false,
          errorMessage: '_onTaoToTrinhRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isPrintingFile: false,
          errorMessage: '_onTaoToTrinhRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetApprovalVinaphacoRequested(
    GetApprovalVinaphacoRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.getApprovalVinaphaco(
          procDefId: event.procDefId);
      debugPrint('ApprovalVinaphaco response: ${response.toJson()}');

      emit(state.copyWith(
        isLoading: false,
        confirmSubmitResponseModel: response,
      ));
    } catch (e) {
      debugPrint('Error getting approval data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage:
              '_onGetApprovalVinaphacoRequested NoInternetException : ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetApprovalVinaphacoRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onUploadFileRequested(
    UploadFileRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isUploadingFile: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.uploadFile(event.fileData,
          extraData: event.extraData);
      debugPrint('Upload file response: ${response.toJson()}');

      emit(state.copyWith(
        isUploadingFile: false,
        uploadFileResponse: response,
        name: event.name,
        fileType: event.fileType,
      ));
    } catch (e) {
      debugPrint('Error uploading file: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isUploadingFile: false,
          errorMessage: '_onUploadFileRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isUploadingFile: false,
          errorMessage: '_onUploadFileRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onUploadMultiFilesRequested(
    UploadMultiFilesRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isUploadingFile: true, errorMessage: null, isNetworkError: false));
      final List<Map<String, dynamic>> files = event.files;
      final response = await _formRepository.uploadMultiFile(files,
          extraData: event.extraData);
      debugPrint('Upload multi files response: ${response.toJson()}');

      emit(state.copyWith(
        isUploadingFile: false,
        uploadMultipleFileResponse: response,
        name: event.name,
        fileType: event.fileType,
      ));
    } catch (e) {
      debugPrint('Error uploading multiple files: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isUploadingFile: false,
          errorMessage: e.message,
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isUploadingFile: false,
          errorMessage: e.toString(),
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onCreateBpmProcInstRequested(
    CreateBpmProcInstRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isSubmitting: true,
          isLoading: true,
          errorMessage: null,
          isNetworkError: false));
      // debugPrint('Create BPM Process Instance event received');
      developer
          .log('CreateBpmProcInstRequestedBody: ${event.requestBody.toJson()}');
      late dynamic response;
      late bool hasPrintSignZone = false;
      response = await _formRepository.createBpmProcInst(
        procDefId: event.procDefId,
        ticketId: event.ticketId,
        requestBody: event.requestBody,
      );
      // debugPrint('Create BPM Process Instance Response: ${response.toJson()}');
      if (event.printSignZoneRequest != null && response.code == 1) {
        final taoToTrinhRes = await _formRepository.taoToTrinh(
          requestBody: event.printSignZoneRequest!,
        );
        // Map taoToTrinhRes về BpmProcInstCreateResponseModel nếu cần
        response = BpmProcInstCreateResponseModel(
          code: taoToTrinhRes.code,
          message: taoToTrinhRes.message,
          // ... các trường khác mapping tương ứng
        );
      }
      emit(state.copyWith(
        isSubmitting: false,
        isLoading: false,
        bpmProcInstCreateResponse: response,
        hasPrintSignZone: hasPrintSignZone,
      ));
    } catch (e) {
      debugPrint('Error creating BPM process instance: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
            isSubmitting: false,
            isLoading: false,
            errorMessage: '_onCreateBpmProcInstRequested: ${e.message}',
            isNetworkError: true,
            bpmProcInstCreateResponse: null));
      } else {
        emit(state.copyWith(
            isSubmitting: false,
            isLoading: false,
            errorMessage: '_onCreateBpmProcInstRequested: ${e.toString()}',
            isNetworkError: false,
            bpmProcInstCreateResponse: null));
      }
    }
  }

  Future<void> _onCallService(
    CallService event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.getCallService(
          requestBody: event.requestModel, baseURL: event.baseURL);
      debugPrint('Call service response: ${response.toJson()}');

      emit(state.copyWith(
        isLoading: false,
        callServiceResponse: response,
      ));
    } catch (e) {
      debugPrint('Error calling service: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onCallService: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onCallService: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetListBaseUrl(
    GetListBaseUrl event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.getListBaseUrl();
      // debugPrint('List Base URL response: ${response.toJson()}');

      emit(state.copyWith(
        isLoading: false,
        listBaseUrlResponse: response,
      ));
    } catch (e) {
      debugPrint('Error getting list base URL: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetListBaseUrl: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetListBaseUrl: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetServiceByIdWithPermissionRequested(
    GetServiceByIdWithPermission event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.getServiceByIdWithPermission(
        id: event.serviceId,
      );

      // First check if service data exists before proceeding
      if (response.data == null) {
        emit(state.copyWith(
          isLoading: false,
          serviceByIdWithPermissionResponse: response,
        ));
        return;
      }

      // Now fetch submission types
      try {
        final allSubmissionType = await _formRepository.getAllSubmissionType();
        debugPrint('All submission types: ${allSubmissionType.toJson()}');

        // Check for valid submission type data
        if (allSubmissionType.data != null &&
            allSubmissionType.data!.isNotEmpty) {
          final submissionTypeId = response.data!.submissionType;

          // Only try to find a match if submissionTypeId is not null
          if (submissionTypeId != null) {
            final matchingType = allSubmissionType.data!.firstWhere(
              (type) => type.id == submissionTypeId,
              orElse: () => allSubmissionType.data!.first,
            );

            debugPrint('Found submission type: ${matchingType.typeName}');

            emit(state.copyWith(
                isLoading: false,
                serviceByIdWithPermissionResponse: response,
                submissionTypeName: matchingType.typeName,
                submissionType: matchingType));
            return;
          }
        }

        emit(state.copyWith(
          isLoading: false,
          serviceByIdWithPermissionResponse: response,
        ));
      } catch (submissionTypeError) {
        debugPrint('Error fetching submission types: $submissionTypeError');
        emit(state.copyWith(
          isLoading: false,
          serviceByIdWithPermissionResponse: response,
        ));
      }
    } catch (e) {
      debugPrint('Error getting service by ID with permission: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage:
              '_onGetServiceByIdWithPermissionRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage:
              '_onGetServiceByIdWithPermissionRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetFinalTitleByListUser(
    GetFinalTitleByListUser event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      developer.log('event.usernames: ${event.usernames}', name: 'FormBloc');

      final response = await _formRepository.getFinalTitleByListUser(
        usernames: event.usernames,
      );

      emit(state.copyWith(
        isLoading: false,
        titleByUserResponse: response,
      ));
    } catch (e) {
      debugPrint('Error getting final title by list user: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetFinalTitleByListUser: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetFinalTitleByListUser: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetDefaultSignatureRequested(
    GetDefaultSignatureRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.getDefaultSignature(
        username: event.username,
      );

      emit(state.copyWith(
        isLoading: false,
        defaultSignatureResponse: response,
      ));
    } catch (e) {
      debugPrint('Error getting default signature: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetDefaultSignatureRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetDefaultSignatureRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetAccountMultiChartRequested(
    GetAccountMultiChartRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));
      final response = await _formRepository.getAccountMultiChart(
        username: event.username,
        chartIds: event.chartIds,
      );

      emit(state.copyWith(
        isLoading: false,
        accountMultiChartResponse: response,
      ));
    } catch (e) {
      debugPrint('Error getting account multi chart data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetAccountMultiChartRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetAccountMultiChartRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  Future<void> _onGetPriorityManagementRequested(
    GetPriorityManagementRequested event,
    Emitter<FormInfoState> emit,
  ) async {
    try {
      emit(state.copyWith(
          isLoading: true, errorMessage: null, isNetworkError: false));

      final response = await _formRepository.getPriorityManagement(
        requestBody: event.requestBody,
      );

      emit(state.copyWith(
        isLoading: false,
        priorityManagementResponse: response,
      ));
    } catch (e) {
      debugPrint('Error getting priority management data: $e');
      if (e is NoInternetException) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetPriorityManagementRequested: ${e.message}',
          isNetworkError: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: '_onGetPriorityManagementRequested: ${e.toString()}',
          isNetworkError: false,
        ));
      }
    }
  }

  // void _onFormSubmitting(FormSubmitting event, Emitter<FormInfoState> emit) {
  //   emit(state.copyWith(isSubmitting: true));
  // }
}
