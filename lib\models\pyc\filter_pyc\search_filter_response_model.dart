import 'package:eapprove/models/pyc/filter_pyc/search_filter_data_model.dart';

class SearchFilterResponseModel {
  final String code;
  final String message;
  final List<ListUserInfoDataModel> data;

  const SearchFilterResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory SearchFilterResponseModel.fromJson(Map<String, dynamic> json) {
    final dynamic dataJson = json['data'];
    List<ListUserInfoDataModel> dataList;

    if (dataJson is List) {
      dataList =
          dataJson.map((item) => ListUserInfoDataModel.fromJson(item)).toList();
    } else if (dataJson is Map<String, dynamic>) {
      dataList = [ListUserInfoDataModel.fromJson(dataJson)];
    } else {
      dataList = [];
    }

    return SearchFilterResponseModel(
      code: json['code'] ?? '',
      message: json['message'] ?? '',
      data: dataList,
    );
  }
}

