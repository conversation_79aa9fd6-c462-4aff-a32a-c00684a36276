part of 'print_file_template_bloc.dart';

abstract class PrintFileTemplateEvent extends Equatable {
  const PrintFileTemplateEvent();

  @override
  List<Object> get props => [];
}

class PrintFileTemplateRequested extends PrintFileTemplateEvent {

  const PrintFileTemplateRequested({
    required this.requestBody,
  });

  final Map<String, dynamic> requestBody;

  @override
  List<Object> get props => [
        requestBody,
      ];
}

class ClearPrintFileState extends PrintFileTemplateEvent {
  const ClearPrintFileState();
} 