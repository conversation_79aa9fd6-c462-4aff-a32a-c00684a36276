import 'package:flutter/material.dart';

class PDFContentWrapper extends StatelessWidget {
  final Widget child;
  final bool showOverlay;
  final VoidCallback? onOverlayTap;
  
  const PDFContentWrapper({
    super.key,
    required this.child,
    this.showOverlay = false,
    this.onOverlayTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (showOverlay)
          Positioned.fill(
            child: GestureDetector(
              onTap: onOverlayTap,
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
          ),
      ],
    );
  }
}