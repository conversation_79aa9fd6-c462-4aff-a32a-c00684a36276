import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'dart:developer' as developer;

// Events
abstract class BottomNavEvent extends Equatable {
  const BottomNavEvent();

  @override
  List<Object> get props => [];
}

class SetBottomNavVisibility extends BottomNavEvent {
  final bool isVisible;

  const SetBottomNavVisibility(this.isVisible);

  @override
  List<Object> get props => [isVisible];
}

class NavigateToTabletTab extends BottomNavEvent {
  final int tabIndex;

  const NavigateToTabletTab(this.tabIndex);

  @override
  List<Object> get props => [tabIndex];
}


class SwitchTab extends BottomNavEvent {
  final int index;

  const SwitchTab(this.index);

  @override
  List<Object> get props => [index];
}

// State
class BottomNavState extends Equatable {
  final bool isVisible;
  final int? navigateToTabIndex;
  final int currentIndex;

  const BottomNavState({
    this.isVisible = true,
    this.navigateToTabIndex,
    this.currentIndex = 0,
  });

  BottomNavState copyWith({
    bool? isVisible,
    int? navigateToTabIndex,
    int? currentIndex,
  }) {
    return BottomNavState(
      isVisible: isVisible ?? this.isVisible,
      navigateToTabIndex: navigateToTabIndex,
      currentIndex: currentIndex ?? this.currentIndex ,
    );
  }

  @override
  List<Object?> get props => [isVisible, navigateToTabIndex, currentIndex];
}

// BLoC
class BottomNavBloc extends Bloc<BottomNavEvent, BottomNavState> {
  BottomNavBloc() : super(const BottomNavState()) {
    on<SetBottomNavVisibility>((event, emit) {
      developer.log('BottomNavBloc: Setting visibility to ${event.isVisible}', name: 'BottomNavBloc');
      emit(state.copyWith(isVisible: event.isVisible));
    });

    on<NavigateToTabletTab>((event, emit) {
      emit(state.copyWith(navigateToTabIndex: event.tabIndex));
      emit(state.copyWith(navigateToTabIndex: null));
    });

    on<SwitchTab>((event, emit) {
      developer.log('BottomNavBloc: Switching to tab ${event.index}', name: 'BottomNavBloc');
      emit(state.copyWith(currentIndex: event.index));
    });
  }
} 