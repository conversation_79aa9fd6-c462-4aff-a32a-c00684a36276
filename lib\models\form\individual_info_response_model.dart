import 'dart:convert';

class IndividualInfoResponse {
  final int code;
  final String message;
  final List<IndividualInfo> data;
  final String? requestKey;

  IndividualInfoResponse({
    required this.code,
    required this.message,
    required this.data,
    this.requestKey,
  });

  factory IndividualInfoResponse.fromJson(Map<String, dynamic> json) {
    return IndividualInfoResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((item) => IndividualInfo.fromJson(item as Map<String, dynamic>))
          .toList(),
      requestKey: json['requestKey'] as String?,
    );
  }

  factory IndividualInfoResponse.fromJsonString(String jsonString) {
    return IndividualInfoResponse.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((info) => info.toJson()).toList(),
      'requestKey': requestKey,
    };
  }

  bool get isSuccess => code == 1;
}

class IndividualInfo {
  final String? viceManager;
  final String? titleCodeTc;
  final String? code;
  final int chartNodeId;
  final String? positionCode;
  final String? managerLevel;
  final String? title;
  final String? titleCode;
  final String? chartCode;
  final int chartId;
  final String? shortNameChart;
  final String? directManager;
  final String? shortNameChartNode;
  final int userTitleId;
  final String? staffCode;
  final String? costCenter;
  final String? positionTc;
  final int userId;
  final String? titleTc;
  final String? chartName;
  final String? name;
  final String? costCenterName;
  final String? fullname;
  final String? position;
  final String? positionCodeTc;
  final String? username;

  IndividualInfo({
    this.viceManager,
    this.titleCodeTc,
    this.code,
    required this.chartNodeId,
    this.positionCode,
    this.managerLevel,
    this.title,
    this.titleCode,
    this.chartCode,
    required this.chartId,
    this.shortNameChart,
    this.directManager,
    this.shortNameChartNode,
    required this.userTitleId,
    this.staffCode,
    this.costCenter,
    this.positionTc,
    required this.userId,
    this.titleTc,
    this.chartName,
    this.name,
    this.costCenterName,
    this.fullname,
    this.position,
    this.positionCodeTc,
    this.username,
  });

  factory IndividualInfo.fromJson(Map<String, dynamic> json) {
    return IndividualInfo(
      viceManager: json['viceManager'] as String?,
      titleCodeTc: json['titleCodeTc'] as String?,
      code: json['code'] as String?,
      chartNodeId: json['chartNodeId'] as int,
      positionCode: json['positionCode'] as String?,
      managerLevel: json['managerLevel'] as String?,
      title: json['title'] as String?,
      titleCode: json['titleCode'] as String?,
      chartCode: json['chartCode'] as String?,
      chartId: json['chartId'] as int,
      shortNameChart: json['shortNameChart'] as String?,
      directManager: json['directManager'] as String?,
      shortNameChartNode: json['shortNameChartNode'] as String?,
      userTitleId: json['userTitleId'] as int,
      staffCode: json['staffCode'] as String?,
      costCenter: json['costCenter'] as String?,
      positionTc: json['positionTc'] as String?,
      userId: json['userId'] as int,
      titleTc: json['titleTc'] as String?,
      chartName: json['chartName'] as String?,
      name: json['name'] as String?,
      costCenterName: json['costCenterName'] as String?,
      fullname: json['fullname'] as String?,
      position: json['position'] as String?,
      positionCodeTc: json['positionCodeTc'] as String?,
      username: json['username'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'viceManager': viceManager,
      'titleCodeTc': titleCodeTc,
      'code': code,
      'chartNodeId': chartNodeId,
      'positionCode': positionCode,
      'managerLevel': managerLevel,
      'title': title,
      'titleCode': titleCode,
      'chartCode': chartCode,
      'chartId': chartId,
      'shortNameChart': shortNameChart,
      'directManager': directManager,
      'shortNameChartNode': shortNameChartNode,
      'userTitleId': userTitleId,
      'staffCode': staffCode,
      'costCenter': costCenter,
      'positionTc': positionTc,
      'userId': userId,
      'titleTc': titleTc,
      'chartName': chartName,
      'name': name,
      'costCenterName': costCenterName,
      'fullname': fullname,
      'position': position,
      'positionCodeTc': positionCodeTc,
      'username': username,
    };
  }
}