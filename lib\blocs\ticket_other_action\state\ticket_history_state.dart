import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:equatable/equatable.dart';

class TicketHistoryState extends Equatable {

  final ServiceStatus status;
  final String? errorMessage;
  final TicketOtherActionResponse? response;

  const TicketHistoryState({
    this.status = ServiceStatus.initial,
    this.errorMessage,
    this.response,
  });

  TicketHistoryState copyWith({
    ServiceStatus? status,
    String? errorMessage,
    TicketOtherActionResponse? response,
  }) {
    return TicketHistoryState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      response: response ?? this.response,
    );
  }

  @override
  List<Object?> get props => [status, errorMessage, response];
}
