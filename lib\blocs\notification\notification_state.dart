import 'package:eapprove/models/notification/notification_model.dart';
import 'package:equatable/equatable.dart';

abstract class NotiState extends Equatable {
  const NotiState(); // Constructor for optimization

  @override
  List<Object> get props => [];
}

class NotiInitial extends NotiState {
  const NotiInitial();

  @override
  String toString() => 'NotiInitial';
}

class NotiLoading extends NotiState {
  const NotiLoading();

  @override
  String toString() => 'NotiLoading';
}

class NotiLoaded extends NotiState {
  final List<NotificationData> notifications;
  final bool hasReachedMax;

  const NotiLoaded(this.notifications, {this.hasReachedMax = false});

  @override
  List<Object> get props => [notifications, hasReachedMax];

  @override
  String toString() => 'NotiLoaded(notifications: ${notifications.length}, hasReachedMax: $hasReachedMax)';
}

class UnreadLoaded extends NotiState {
  final int unreadAmount;

  const UnreadLoaded(this.unreadAmount);

  @override
  List<Object> get props => [unreadAmount];

  @override
  String toString() => 'UnreadLoaded(unreadAmount: $unreadAmount)';
}

class NotiError extends NotiState {
  final String message;

  const NotiError(this.message);

  @override
  List<Object> get props => [message];

  @override
  String toString() => 'NotiError(message: $message)';
}
