import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/common/collapse_view.dart';
import 'package:eapprove/models/handle_ticket/user_task_info_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_svg/svg.dart';

extension FirstOrNullExtension<E> on List<E>? {
  E? get firstOrNull => (this == null || this!.isEmpty) ? null : this!.first;
}

class ApproverListScreen extends StatefulWidget {
  final dynamic ticketId;

  const ApproverListScreen({super.key, required this.ticketId});

  @override
  State<ApproverListScreen> createState() => _ApproverListScreenState();
}

class _ApproverListScreenState extends State<ApproverListScreen> {
  @override
  void initState() {
    super.initState();
    context.read<TicketProcessDetailBloc>().add(
          LoadUserTaskInfo(ticketId: widget.ticketId.toString()),
        );
    context.read<PycBloc>().add(
          FetchStatusTask(
            StatusTicketRequest(
              code: "STATUS_TASK",
              type: "system",
              page: 1,
              limit: 9999,
              status: ["active"],
              chartId: "",
              search: "",
              sortBy: "id",
              sortType: "DESC",
            ),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    final child = _buildApproverListContent(context);

    if (isTablet) {
      // Hiển thị popup ở giữa màn hình, giống mobile nhưng có bo góc, width nhỏ hơn
      return Center(
        child: Material(
          color: Colors.transparent,
          child: Center(
            child: GestureDetector(
              onTap: () {},
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                elevation: 8,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Container(
                    width: 407.w,
                    height: 732.h,
                    child: child,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      // Mobile: giữ nguyên giao diện cũ
      return child;
    }
  }

  Widget _buildApproverListContent(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return GradientBackground(
      showBottomImage: false,
      showUpperImage: false,
      child: Scaffold(
        backgroundColor: getColorSkin().transparent,
        appBar: CustomAppBar(
          centerTitle: true,
          automaticallyImplyLeading: isTablet ? false : true,
          leading: isTablet ? null : IconButton(
            onPressed: () => Navigator.pop(context),
            icon: SvgPicture.asset(
              StringImage.ic_arrow_left,
              colorFilter: ColorFilter.mode(
                getColorSkin().ink1,
                BlendMode.srcIn,
              ),
            ),
          ),
          actions: [
            isTablet ?
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: SvgPicture.asset(
                    StringImage.ic_close,
                    colorFilter: ColorFilter.mode(
                      getColorSkin().ink1,
                      BlendMode.srcIn,
                    ),
                  ),
                )
            : const SizedBox(),
          ],
          title: 'Danh sách người phê duyệt',
          titleTextStyle:
              getTypoSkin().medium16.copyWith(color: getColorSkin().ink1, textBaseline: TextBaseline.ideographic),
        ),
        body: BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
          
          builder: (context, state) {
            if (state is TicketProcessDetailLoading) {
              return AppConstraint.buildShimmer(
                child: SizedBox(
                  height: 500.h,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: 5,
                    itemBuilder: (context, index) {
                      return Container(
                        width: double.infinity,
                        height: 100.h,
                        margin: EdgeInsets.symmetric(vertical: 8.h),
                        decoration: BoxDecoration(
                          color: getColorSkin().white,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      );
                    },
                  ),
                ),
              );
            }

            if (state is TicketProcessDetailError) {
              return Center(child: Text('Lỗi: ${state.message}'));
            }

            if (state is UserTaskInfoLoaded) {
              final taskList = state.userTaskInfo.data ?? [];

              final submitter = taskList
                  .firstWhere(
                    (task) => task.taskType == 'startEvent',
                    orElse: () => TaskInfo(
                      taskDefKey: '',
                      taskName: '',
                      taskType: '',
                      lstAssigneeInfo: [],
                      formKey: '',
                    ),
                  )
                  .lstAssigneeInfo
                  ?.firstOrNull;

              final approvers = taskList
                  .where((task) => task.taskType != 'startEvent')
                  .expand<AssigneeInfo>((task) => task.lstAssigneeInfo ?? [])
                  .toList();

              return ListView(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                children: [
                  _buildSubmitterSection(submitter),
                  SizedBox(height: 12.h),
                  _buildApproverSection(approvers),
                ],
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }

  Widget _buildSubmitterSection(AssigneeInfo? submitter) {
    return CollapseView(
      backgroundColor: getColorSkin().secondaryColor1TagBackground,
      title: 'Người đề trình',
      isExpanded: true,
      children: [
        if (submitter != null) _buildAssigneeCard(submitter, isSubmitter: true) else Text('Không có người đề trình'),
      ],
    );
  }

  Widget _buildApproverSection(List<AssigneeInfo> approvers) {
    return CollapseView(
      backgroundColor: getColorSkin().secondaryColor1TagBackground,
      title: 'Người ký (Duyệt tuần tự)',
      isExpanded: true,
      children: approvers
          .map((e) => Padding(
                padding: EdgeInsets.only(bottom: 12.h),
                child: _buildAssigneeCard(e, isSubmitter: false),
              ))
          .toList(),
    );
  }

  Widget _buildAssigneeCard(AssigneeInfo a, {required bool isSubmitter}) {
    final pycState = context.read<PycBloc>().state;
    String statusName = 'Không xác định';

    if (isSubmitter) {
      statusName = 'Đệ trình phiếu';
    } else {
      final statusModel = pycState.statusTaskResponseModel?.data.content
          ?.firstWhere((e) => e.configValue == a.taskStatus, orElse: () => StatusContentModel());
      statusName = statusModel?.configName ?? a.taskStatus ?? '';
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${a.fullName} - ${a.title}', style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1)),
          SizedBox(height: 6.h),
          Text('Tình trạng: $statusName', style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1)),
          SizedBox(height: 4.h),
          Text(
            'Ngày trình/ngày duyệt: ${a.completedTime?.isNotEmpty == true ? a.completedTime : ''}',
            style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink1),
          ),
        ],
      ),
    );
  }
}
