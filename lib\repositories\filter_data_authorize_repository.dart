import 'dart:convert';
import 'dart:developer' as developer;

import 'package:eapprove/models/authorize_management/filter_data_authorize_response.dart';
import 'package:eapprove/services/api_services.dart';

class FilterDataAuthorizeRepository {
  final ApiService _apiService;

  FilterDataAuthorizeRepository({required ApiService apiService}) : _apiService = apiService;

  Future<FilterDataAuthorizeResponse> getFilterData({
    required String search,
    required String sortBy,
    required String sortType,
    required String limit,
    required String page,
    required int totalPages,
    required int totalElements,
    required List<String> listAssignUser,
    required List<String> listAssignedUser,
    required List<int> status,
    required List<String> listCreatedUser,
    required List<String> listUpdatedUser,
    required List<Map<String, String>> listDateFilter,
    required String userLogin,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/assign/getFilterData',
        {
          'search': search,
          'sortBy': sortBy,
          'sortType': sortType,
          'limit': limit,
          'page': page,
          'totalPages': totalPages,
          'totalElements': totalElements,
          'listAssignUser': listAssignUser,
          'listAssignedUser': listAssignedUser,
          'status': status,
          'listCreatedUser': listCreatedUser,
          'listUpdatedUser': listUpdatedUser,
          'listDateFilter': listDateFilter,
          'userLogin': userLogin,
        },
      );

      developer.log('FilterDataAuthorize Request:', name: 'API');
      developer.log('URL: business-process/assign/getFilterData', name: 'API');
      developer.log('Body: ${jsonEncode({
        'search': search,
        'sortBy': sortBy,
        'sortType': sortType,
        'limit': limit,
        'page': page,
        'totalPages': totalPages,
        'totalElements': totalElements,
        'listAssignUser': listAssignUser,
        'listAssignedUser': listAssignedUser,
        'status': status,
        'listCreatedUser': listCreatedUser,
        'listUpdatedUser': listUpdatedUser,
        'listDateFilter': listDateFilter,
        'userLogin': userLogin,
      })}', name: 'API');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        developer.log('FilterDataAuthorize Response:', name: 'API');
        developer.log('Status Code: ${response.statusCode}', name: 'API');
        developer.log('Response Body: ${response.body}', name: 'API');
        
        return FilterDataAuthorizeResponse.fromJson(responseData);
      } else {
        developer.log('FilterDataAuthorize Error:', name: 'API');
        developer.log('Status Code: ${response.statusCode}', name: 'API');
        developer.log('Response Body: ${response.body}', name: 'API');
        
        throw Exception('Failed to load filter data: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      developer.log('FilterDataAuthorize Exception:', name: 'API');
      developer.log('Error: $e', name: 'API');
      developer.log('StackTrace: $stackTrace', name: 'API');
      
      throw Exception('Failed to get filter data: $e');
    }
  }
} 