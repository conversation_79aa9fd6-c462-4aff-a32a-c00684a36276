import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';

import 'package:eapprove/enum/enum.dart';

class FormModel {
  final String key;
  final List<FormItemInfo> formItems;
  bool _isSubmitting = false;
  String? _generalError;

  FormModel({
    required this.key,
    required this.formItems,
  });

  /// Create an empty FormModel
  factory FormModel.empty() {
    return FormModel(
      key: 'default',
      formItems: [],
    );
  }

  /// Submit form data if validation passes
  /// @param callback Function to call with form values on successful validation
  /// @param onError Optional function to call with error message if validation fails
  Future<void> onSubmit(
      Function(Map<String, dynamic>) callback, {
        Function(String)? onError,
        bool validateAll = true,
      }) async {
    if (_isSubmitting) return; // Prevent double submission

    _isSubmitting = true;
    _generalError = null;

    try {
      // Clear previous error messages
      _clearErrors();

      final bool isValid = validate(validateAll: validateAll);

      if (isValid) {
        final values = getValues();
        await callback(values);
      } else {
        final errorMessage = _getFirstErrorMessage() ?? 'Form validation failed';
        if (onError != null) {
          onError(errorMessage);
        }
      }
    } catch (e) {
      _generalError = 'An unexpected error occurred: $e';
      if (onError != null) {
        onError(_generalError!);
      }
    } finally {
      _isSubmitting = false;
    }
  }

  /// Validate all form items
  /// @param validateAll If true, validates all fields; if false, stops at first error
  bool validate({bool validateAll = true}) {
    bool isValid = true;

    for (var item in formItems) {
      // Skip validation for hidden items or items marked as readonly/forceView
      if (item.display == false || item.readonly == true || item.forceView == true) {
        continue;
      }
      debugPrint('Validating item bool validate: ${item.name}, value: ${item.value}');

      // Validate individual item
      bool itemValid = _validateItem(item);

      if (!itemValid && !validateAll) {
        return false; // Early exit if not validating all items
      }

      isValid = isValid && itemValid;
    }

    return isValid;
  }

  /// Validate a specific form item based on its type and validation rules
  bool _validateItem(FormItemInfo item) {
    debugPrint('Validating item: ${item.name}, value: ${item.value}');
    if (item.name == null || item.validations == null) {
      return true;
    }

    // Clear previous error
    item.error = null;

    final validations = item.validations!;

    // Required field validation
    if (validations['required'] == true) {
      if (item.value == null ||
          (item.value is String && (item.value as String).trim().isEmpty) ||
          (item.value is List && (item.value as List).isEmpty)) {
        item.error = validations['requiredMessage'] ?? 'This field is required';
        return false;
      }
    }

    // If not required and empty, skip other validations
    if (item.value == null ||
        (item.value is String && (item.value as String).isEmpty) ||
        (item.value is List && (item.value as List).isEmpty)) {
      return true;
    }

    // Length validation for string values
    if (item.value is String) {
      final String stringValue = item.value as String;

      if (item.maxLength != null && stringValue.length > item.maxLength!) {
        item.error = 'Maximum length exceeded (${item.maxLength} characters)';
        return false;
      }

      if (item.minLength != null && stringValue.length < item.minLength!) {
        item.error = 'Minimum length not met (${item.minLength} characters)';
        return false;
      }
    }

    if ((item.inputType == 'number' || item.inputType == 'currency') &&
        item.value != null && item.value is! num) {
      try {
        final num parsedValue = num.parse(item.value.toString());
        if (item.minValue != null && parsedValue < item.minValue!) {
          item.error = 'Value must be at least ${item.minValue}';
          return false;
        }

        if (item.maxValue != null && parsedValue > item.maxValue!) {
          item.error = 'Value must be at most ${item.maxValue}';
          return false;
        }

        if (item.isDecimal == false && parsedValue != parsedValue.toInt()) {
          item.error = 'Only whole numbers are allowed';
          return false;
        }
      } catch (e) {
        item.error = 'Please enter a valid number';
        return false;
      }
    }

    if (validations['pattern'] != null && item.value is String) {
      final pattern = RegExp(validations['pattern']);
      if (!pattern.hasMatch(item.value)) {
        item.error = validations['patternMessage'] ?? 'Invalid format';
        return false;
      }
    }

    if (item.type == FormItemType.table && item.columns != null) {
      // Validate each column in the table if needed
      // This would be more complex and depends on your table structure
    }

    return true;
  }


  /// Get all form values as a map
  Map<String, dynamic> getValues() {
    final result = <String, dynamic>{};

    for (var item in formItems) {
      // Skip items without names or those that are not displayed
      if (item.name == null || item.display == false) {
        continue;
      }

      // For special fields, use dataValue if available
      if (item.dataValue != null) {
        result[item.name!] = item.dataValue;
      } else {
        result[item.name!] = item.value;
        debugPrint('FormModel getValues: ${item.name}: ${item.value}');
      }

      // Add any additional metadata needed for specific field types
      if (item.type == FormItemType.table && item.columns != null) {
        // Handle table values specifically if needed
      }
    }

    return result;
  }

  /// Get first error message from all form items
  String? _getFirstErrorMessage() {
    for (var item in formItems) {
      if (item.error != null && item.error!.isNotEmpty) {
        return '${item.label ?? item.name}: ${item.error}';
      }
    }
    return _generalError;
  }

  /// Clear all error messages
  void _clearErrors() {
    for (var item in formItems) {
      item.error = null;
    }
    _generalError = null;
  }

  /// Update a specific field value
  void updateFieldValue(String fieldName, dynamic newValue) {
    for (var item in formItems) {
      if (item.name == fieldName) {
        item.value = newValue;
        break;
      }
    }
  }

  /// Reset the form to initial values
  void reset() {
    for (var item in formItems) {
      item.value = null;
      item.error = null;
    }
    _generalError = null;
  }
}