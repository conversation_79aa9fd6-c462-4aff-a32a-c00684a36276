import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class CustomTabBarTest extends StatefulWidget {
  final List<String> tabTitles;
  final List<Widget> tabContents;
  final int initialIndex;
  final Function(int)? onTabSelected;
  final Function(int)? onTabChanged;
  final TabController? tabController;
  final Color? selectedBackgroundColor;
  final Color? unselectedBackgroundColor;
  final Color? selectedTextColor;
  final Color? unselectedTextColor;
  final TextStyle? textStyle;
  final EdgeInsets? tabPadding;
  final double? borderRadius;
  final Duration animationDuration;
  final EdgeInsets? tabBarPadding;
  final EdgeInsets? labelPadding;
  final EdgeInsets? contentPadding;
  final bool lazyLoad;
  final Curve animationCurve;
  final List<int>? dropdownTabIndex;
  final Widget dropdown;
  final Function(TabController)? onTabControllerCreated;

  const CustomTabBarTest({
    super.key,
    required this.tabTitles,
    required this.tabContents,
    this.initialIndex = 0,
    this.onTabSelected,
    this.onTabChanged,
    this.tabController,
    this.selectedBackgroundColor,
    this.unselectedBackgroundColor,
    this.selectedTextColor,
    this.unselectedTextColor,
    this.textStyle,
    this.tabPadding,
    this.borderRadius,
    this.animationDuration = const Duration(milliseconds: 0),
    this.tabBarPadding,
    this.labelPadding,
    this.lazyLoad = false,
    this.animationCurve = Curves.easeInOut,
    this.contentPadding,
    this.dropdownTabIndex,
    this.dropdown = const SizedBox.shrink(),
    this.onTabControllerCreated,
  }) : assert(tabTitles.length == tabContents.length, 'Number of tab titles must match number of tab contents');

  @override
  State<CustomTabBarTest> createState() => _CustomTabBarTestState();
}

class _CustomTabBarTestState extends State<CustomTabBarTest> with SingleTickerProviderStateMixin {
  late ValueNotifier<int> selectedTab;
  late TabController _tabController;
  late List<bool> _initializedTabs;

  @override
  void initState() {
    super.initState();
    selectedTab = ValueNotifier<int>(widget.initialIndex);

    _initializedTabs =
        List.generate(widget.tabTitles.length, (index) => !widget.lazyLoad || index == widget.initialIndex);

    if (widget.tabController == null) {
      _tabController = TabController(
        length: widget.tabTitles.length,
        vsync: this,
        initialIndex: widget.initialIndex,
      );
    } else {
      _tabController = widget.tabController!;
    }

    _tabController.addListener(_handleTabChange);

    if (widget.onTabControllerCreated != null) {
      widget.onTabControllerCreated!(_tabController);
    }
  }

  @override
  void didUpdateWidget(CustomTabBarTest oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialIndex != widget.initialIndex) {
      _tabController.animateTo(widget.initialIndex);
      selectedTab.value = widget.initialIndex;
    }
  }

  @override
  void dispose() {
    if (widget.tabController == null) {
      _tabController.dispose();
    }
    _tabController.removeListener(_handleTabChange);
    selectedTab.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging || _tabController.index != selectedTab.value) {
      final newIndex = _tabController.index;

      if (widget.lazyLoad && !_initializedTabs[newIndex]) {
        setState(() {
          _initializedTabs[newIndex] = true;
        });
      }

      selectedTab.value = newIndex;

      if (widget.onTabSelected != null) {
        widget.onTabSelected!(newIndex);
      }

      if (widget.onTabChanged != null) {
        widget.onTabChanged!(newIndex);
      }
    }
  }

  Widget _buildTabBar() {
    return ValueListenableBuilder<int>(
      valueListenable: selectedTab,
      builder: (context, index, child) {
        return TabBar(
          controller: _tabController,
          isScrollable: true,
          padding: widget.tabBarPadding ?? EdgeInsets.only(left: 12.w),
          labelPadding: widget.labelPadding ?? EdgeInsets.symmetric(horizontal: 4.h),
          labelColor: widget.selectedTextColor ?? getColorSkin().secondaryColor1,
          unselectedLabelColor: widget.unselectedTextColor ?? getColorSkin().subtitle,
          tabAlignment: TabAlignment.start,
          dividerColor: getColorSkin().transparent,
          indicator: BoxDecoration(color: getColorSkin().transparent),
          overlayColor: WidgetStateProperty.all(getColorSkin().transparent),
          onTap: (index) {
            selectedTab.value = index;
            if (widget.onTabSelected != null) {
              widget.onTabSelected!(index);
            }
            if (widget.onTabChanged != null) {
              widget.onTabChanged!(index);
            }
          },
          tabs: List.generate(widget.tabTitles.length, (i) {
            bool isSelected = i == index;
            return Tab(
              child: Semantics(
                label: widget.tabTitles[i],
                selected: isSelected,
                child: AnimatedContainer(
                  duration: widget.animationDuration,
                  curve: widget.animationCurve,
                  padding: widget.tabPadding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? widget.selectedBackgroundColor ?? getColorSkin().secondaryColor1TagBackground
                        : widget.unselectedBackgroundColor ?? getColorSkin().grey3Background,
                    borderRadius: BorderRadius.circular(
                      widget.borderRadius ?? 12.r,
                    ),
                  ),
                  child: Text(
                    widget.tabTitles[i],
                    style: widget.textStyle ?? getTypoSkin().buttonText2Regular,
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildTabBar(),
        ValueListenableBuilder<int>(
          valueListenable: selectedTab,
          builder: (context, index, child) {
            final showDropdown = widget.dropdownTabIndex != null && widget.dropdownTabIndex!.contains(index);
            return showDropdown ? widget.dropdown : SizedBox.shrink();
          },
        ),
        Expanded(
          child: ValueListenableBuilder<int>(
            valueListenable: selectedTab,
            builder: (context, index, child) {
              return TabBarView(
                physics: const NeverScrollableScrollPhysics(),
                controller: _tabController,
                children: widget.tabContents,
              );
            },
          ),
        ),
      ],
    );
  }
}
