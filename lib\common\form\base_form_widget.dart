// import 'package:eapprove/models/form/form_item_info.dart';
// import 'package:flutter/material.dart';
// import 'package:eapprove/helper/event_expression.dart';
// import 'package:eapprove/enum/enum.dart';
// import 'package:eapprove/utils/expression_evaluator.dart';

// class FormWidgetState {
//   bool isVisible = true;
//   bool isReadonly = false;
//   bool isDisabled = false;
//   String? label;
//   String? tooltip;
//   String? error;
//   bool hideDeleteButton = false;
//   FormItemType? type;
//   List<Map<String, dynamic>>? options;

//   FormWidgetState({
//     required this.isVisible,
//     required this.isReadonly,
//     required this.isDisabled,
//     this.label,
//     this.tooltip,
//     this.error,
//     this.hideDeleteButton = false,
//     this.type,
//     this.options,
//   });

//   factory FormWidgetState.fromFormItemInfo(FormItemInfo info) {
//     return FormWidgetState(
//       isVisible: info.display ?? true,
//       isReadonly: info.readonly ?? false,
//       isDisabled: info.disabled ?? false,
//       label: info.label,
//       tooltip: info.tooltip,
//       error: info.error,
//       hideDeleteButton: info.isHideDeleteColumnTable ?? false,
//       type: info.type,
//       options: info.options,
//     );
//   }
// }

// abstract class BaseFormWidget extends StatefulWidget {
//   final FormItemInfo data;
//   final Function(String, dynamic) onChange;
//   final Map<String, dynamic> formValues;

//   const BaseFormWidget({
//     Key? key,
//     required this.data,
//     required this.onChange,
//     required this.formValues,
//   }) : super(key: key);

//   @override
//   State<BaseFormWidget> createState() => _BaseFormWidgetState();
// }

// class _BaseFormWidgetState extends State<BaseFormWidget> {
//   late FormWidgetState _state;

//   @override
//   void initState() {
//     super.initState();
//     _state = FormWidgetState.fromFormItemInfo(widget.data);
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Evaluate event expressions if they exist
//     if (widget.data.eventExpression != null && widget.data.eventExpression!.isNotEmpty) {
//       final actions = evaluateConditions(widget.data.eventExpression!, widget.formValues);
      
//       // Apply the actions from event expressions
//       for (final entry in actions.entries) {
//         switch (entry.key) {
//           case 'hien_thi':
//             setState(() {
//               _state.isVisible = entry.value;
//             });
//             break;
            
//           case 'chi_duoc_doc':
//             setState(() {
//               _state.isReadonly = entry.value;
//               _state.isDisabled = entry.value;
//             });
//             break;
            
//           case 'ten_truong':
//             if (entry.value is String) {
//               setState(() {
//                 _state.label = entry.value;
//               });
//             }
//             break;
            
//           case 'huong_dan':
//             if (entry.value is String) {
//               setState(() {
//                 _state.tooltip = entry.value;
//               });
//             }
//             break;
            
//           case 'valid':
//             if (entry.value is String) {
//               setState(() {
//                 _state.error = entry.value;
//               });
//             }
//             break;
            
//           case 'switchField':
//             if (entry.value is Map) {
//               setState(() {
//                 final config = entry.value as Map;
//                 if (config['type'] != null) {
//                   _state.type = config['type'];
//                 }
//                 if (config['options'] != null) {
//                   _state.options = config['options'];
//                 }
//               });
//             }
//             break;
            
//           case 'hide_delete_btn':
//             setState(() {
//               _state.hideDeleteButton = entry.value;
//             });
//             break;
//         }
//       }
//     }

//     // Don't render if not visible
//     if (!_state.isVisible) {
//       return const SizedBox.shrink();
//     }

//     // Build the widget with common properties
//     return buildWidget(context);
//   }

//   Widget buildWidget(BuildContext context) {
//     throw UnimplementedError('buildWidget must be implemented by subclasses');
//   }

//   // Helper method to build label with tooltip
//   Widget buildLabel() {
//     if (_state.label == null) return const SizedBox.shrink();

//     return Row(
//       children: [
//         Text(
//           _state.label!,
//           style: TextStyle(
//             fontWeight: widget.data.validations?['required'] == true ? FontWeight.bold : null,
//           ),
//         ),
//         if (widget.data.validations?['required'] == true)
//           const Padding(
//             padding: EdgeInsets.only(left: 4),
//             child: Text(
//               '*',
//               style: TextStyle(color: Colors.red),
//             ),
//           ),
//         if (_state.tooltip != null)
//           Padding(
//             padding: const EdgeInsets.only(left: 4),
//             child: Tooltip(
//               message: _state.tooltip,
//               child: const Icon(Icons.help_outline, size: 16),
//             ),
//           ),
//       ],
//     );
//   }

//   // Helper method to show error message
//   Widget buildError() {
//     if (_state.error == null) return const SizedBox.shrink();

//     return Padding(
//       padding: const EdgeInsets.only(top: 4),
//       child: Text(
//         _state.error!,
//         style: const TextStyle(color: Colors.red, fontSize: 12),
//       ),
//     );
//   }
// }