import 'package:bloc/bloc.dart';
import 'package:eapprove/repositories/ticket_dialog_action_repository.dart';
import 'ticket_dialog_action_event.dart';
import 'ticket_dialog_action_state.dart';

class TicketDialogActionBloc extends Bloc<TicketDialogActionEvent, TicketDialogActionState> {
  final TicketDialogActionRepository repository;

  TicketDialogActionBloc({required this.repository}) : super(TicketDialogActionInitial()) {
    on<CancelDraftEvent>(_onDraftCancel);
    on<CancelSubmitEvent>(_onSubmitCancel);
    on<UploadFileEvent>(_onUploadFiles);
    on<ReturnDraftEvent>(_onDraftReturn);
    on<ReturnSubmitEvent>(_onSubmitReturn);
    on<TicketDialogActionInitialEvent>(_onInit);
    on<AuthorizeListGetListEvent>(_onGetListeAuthorized);
    on<AuthorizeEvent>(_onSubmitAuthorized);
  }

  Future<void> _onInit(
    TicketDialogActionInitialEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    emit(TicketDialogActionInitial());
  }

  Future<void> _onUploadFiles(
    UploadFileEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    try {
      final filePaths = await repository.uploadlFile(event.files);
      emit(UploadFileLoaded(filePaths));
      event.onSuccess?.call(filePaths);
    } catch (e) {
      emit(UploadFileError(e.toString()));
      event.onError?.call(e.toString());
    }
  }

  Future<void> _onDraftCancel(
    CancelDraftEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    try {
      final result = await repository.draftCancel(
        ticketProcId: event.ticketProcId,
        reason: event.reason,
        filePath: event.filePath,
      );
      emit(DraftCancelTicketLoaded(message: result));
    } catch (e) {
      emit(DraftCancelTicketError(e.toString()));
    }
  }

  Future<void> _onSubmitCancel(
    CancelSubmitEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    try {
      final message = await repository.submitCancel(
        procInstId: event.ticketProcId,
        taskDefKey: event.taskDefKey,
        ticketId: event.ticketId,
        reason: event.reason,
        filePaths: event.filePaths,
      );

      emit(CancelTicketLoaded(message: message ?? "Success"));
    } catch (e) {
      emit(CancelTicketError(e.toString()));
    }
  }

  Future<void> _onDraftReturn(
    ReturnDraftEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    try {
      final result = await repository.returnDraft(
        otherVariables: event.otherVariables ?? {},
        ticketId: event.ticketProcId,
        reason: event.reason,
        filePath: event.filePath,
      );
      emit(DraftReturnTicketLoaded(message: result));
    } catch (e) {
      emit(DraftReturnTicketError(e.toString()));
    }
  }

  Future<void> _onSubmitReturn(
    ReturnSubmitEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    try {
      final result = await repository.returnSubmit(
        procDefId: event.procDefId,
        procInstId: event.procInstId,
        reason: event.reason,
        taskDefKey: event.taskDefKey,
        taskId: event.taskId,
        ticketId: event.ticketId,
        fileNames: event.fileNames,
        filePaths: event.filePaths,
        fileSizes: event.fileSizes,
      );
      emit(ReturnTicketLoaded(message: result ?? "Success"));
    } catch (e) {
      emit(ReturnTicketError(e.toString()));
    }
  }

  Future<void> _onGetListeAuthorized(
    AuthorizeListGetListEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    emit(AuthorizationListLoading());
    try {
      final result = await repository.getListeAuthorized(event.search ?? "");
      emit(AuthorizationListLoaded(result));
    } catch (e) {
      emit(AuthorizationListError(e.toString()));
    }
  }

  Future<void> _onSubmitAuthorized(
    AuthorizeEvent event,
    Emitter<TicketDialogActionState> emit,
  ) async {
    try {
      final result = await repository.authorized(
        email: event.email,
        ticketId: event.ticketId,
        reason: event.reason,
        id: event.id,
        taskDefKey: event.taskDefKey,
        taskId: event.taskId,
        title: event.title,
      );
      emit(AuthorizedLoaded(message: result ?? "Success"));
    } catch (e) {
      emit(AuthorizedError(message: e.toString()));
    }
  }
}
