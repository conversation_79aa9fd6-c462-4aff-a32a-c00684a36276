import 'dart:developer';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/common/collapse_view.dart';
import 'package:eapprove/models/ticket_other_action/request_relation_ticket_history_model.dart';
import 'package:eapprove/models/ticket_other_action/status_model.dart';
import 'package:eapprove/screens/ticket_other_action/widget/request_relation_ticket_item.dart';
import 'package:eapprove/widgets/custom_bottom_navigationbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_svg/svg.dart';

class RequestRelationTicketHistoryScreen extends StatefulWidget {
  const RequestRelationTicketHistoryScreen({super.key, required this.ticketId, required this.ticketProcId});

  final String ticketId;
  final String ticketProcId;

  @override
  State<RequestRelationTicketHistoryScreen> createState() => _RequestRelationTicketHistoryScreenState();
}

class _RequestRelationTicketHistoryScreenState extends State<RequestRelationTicketHistoryScreen> {
  List<RequestRelationTicketHistoryModel> requestRelationTicketHistory_From = [];
  List<RequestRelationTicketHistoryModel> requestRelationTicketHistory_To = [];

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return GradientBackground(
      showBottomImage: false,
      // showUpperImage: false,
      showUpperImage: false,
      child: Scaffold(
        backgroundColor: getColorSkin().transparent,
        appBar: CustomAppBar(
          automaticallyImplyLeading: isTablet ? false : true,
          centerTitle: true,
          title: 'Phiếu yêu cầu liên quan',
          titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          textColor: getColorSkin().black,
          actionsPadding: EdgeInsets.only(right: 18.w),
          actions: [
            if (isTablet)
              IconButton(onPressed: () => Navigator.pop(context), icon: SvgPicture.asset(StringImage.ic_close))
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(0),
            child: Column(
              children: [
                CollapseView(
                  backgroundColor: isTablet ? getColorSkin().blue2 : getColorSkin().white,
                  title: 'Liên kết thông tin đến',
                  titleSvgIconPath: StringImage.ic_ticket_input,
                  children: [
                    ListView.separated(
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: requestRelationTicketHistory_From.length,
                      separatorBuilder: (context, index) => SizedBox(height: 16.h),
                      itemBuilder: (context, index) {
                        return RequestRelationTicketItem(
                          item: requestRelationTicketHistory_From[index],
                        );
                      },
                    ),
                  ],
                ),
                CollapseView(
                  backgroundColor: isTablet ? getColorSkin().blue2 : getColorSkin().white,
                  title: 'Liên kết thông tin đi',
                  titleSvgIconPath: StringImage.ic_ticket_output,
                  children: [
                    ListView.separated(
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: requestRelationTicketHistory_To.length,
                      separatorBuilder: (context, index) => SizedBox(height: 12.h),
                      itemBuilder: (context, index) {
                        return RequestRelationTicketItem(
                          item: requestRelationTicketHistory_To[index],
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
