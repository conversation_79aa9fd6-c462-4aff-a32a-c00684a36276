import 'package:eapprove/enum/enum.dart';
import 'package:flutter/material.dart';

class FormItemInfo {
  final String? id;
  final String? step;
  final String? row;
  final String? col;
  final double? rowSortOrder;
  final double? colSortOrder;
  final double? fieldSortOrder;
  final String? tab;
  final String? tabItem;
  final String? parentName;
  final String? splitter;
  final Map<String, dynamic>? validations;
  final FormItemType? type;
  final String? label;
  final String? name;
  final String? placeholder;
  final List<Map<String, dynamic>>? eventExpression;
  final dynamic isHorizontal; // To handle both double and bool
  final String? fontWeight;
  final bool? display;
  final String? suggestText;
  final bool? isCloneInSpecFlow;
  final String? displayName;
  final bool? isClearOnClone;
  final bool? isConditionAuth;
  dynamic value;
  bool? readonly;
  final bool? forceView;
  final Map<String, dynamic>? optionConfig;
  final bool? autoGenId;
  final String? autoGenIdValue;
  final bool? maskInput;
  final bool? showLimitInput;
  final bool? useTimeDefault;

  // Additional properties
  final String? tooltip;
  final List<Map<String, dynamic>>? maskInputValue;
  final String? inputType; // text, number, currency
  final double? maxLength;
  final double? minLength;
  final double? maxValue;
  final double? minValue;
  final double? stepNumber;
  final bool? isDecimal;
  final bool? currencyText;
  final String? currencyType;
  final bool? isCheckDifference;
  final bool? isCheckAll;
  final int? isHorizontalItem;
  final int? splitterOpen;
  final String? splitterDesc;
  final List<Map<String, dynamic>>? options;
  dynamic dataValue;

  // Table-specific fields
  List<FormItemInfo>? columns;
  final String? typeOption;
  final String? optionType;
  final List<Map<String, dynamic>>? option;
  final String? displayField;
  final String? valueField;
  final bool? isDragDrop;
  final bool? isCallApiHandWork;
  final String? callApiButtonName;
  final bool? isHideAddColumnTable;
  final bool? isHideDeleteColumnTable;
  final bool? isHideGetTemplateTable;
  final bool? isHideImportExcelTable;
  final bool? isHideStt;
  final bool? isAddRowCountInData;
  final List<String>? specialField;
  final bool? isHideSearchTable;

  // Additional fields needed for complex tables
  final String? width; // For column width
  final bool? hideTableTitle; // To hide column titles
  final bool? hideColumn; // To hide entire columns
  final List<Map<String, dynamic>>? displayFieldObject;
  final bool? isAutoGetLogo;
  final bool? isApprovedBudget;
  final bool? isBusinessPermit;
  final bool? isApprovedBudgetChart;
  final String? approvedBudgetChartType;
  final bool? defaultSelect;
  final bool? autoLoadAll;
  final bool? isMulti; // For multiselect fields
  final bool? isChangeCurrencyType;
  final String? fileType;
  final bool? isUserCurrentUserLogin;
  final String? strCondition;
  final String? strConditionConvert;
  final bool? chkCurrencyType;
  final bool? disabled; // For disabled inputs
  final List<Map<String, dynamic>>? condition; // For formula conditions

  // For validation
  String? error;

  // New fields
  final DropdownConfig? dropdown;

  final bool? isChooseOldDays;
  final bool? isChooseNow;
  final bool? isChooseFutureDays;
  final String? dateFormat;

  final String? acceptFile;
  final int? fileSize;
  final String? displayFile;
  final List<Map<String, dynamic>>? switchFieldConfig;
  final bool? switchField;
  final Map<String, dynamic>? attachFileObjDefault;
  final bool? isHideHistoryUpload;
  final Map<String, dynamic>? attachFileObj;

  // Matrix-specific fields
  final int? rowMatrix;
  final int? colMatrix;

  final String? labelInTable;

  FormItemInfo({
    this.id,
    this.step,
    this.row,
    this.col,
    this.rowSortOrder,
    this.colSortOrder,
    this.fieldSortOrder,
    this.tab,
    this.tabItem,
    this.parentName,
    this.splitter,
    this.validations,
    this.type,
    this.label,
    this.name,
    this.placeholder,
    this.eventExpression,
    this.isHorizontal,
    this.fontWeight,
    this.display,
    this.suggestText,
    this.isCloneInSpecFlow,
    this.displayName,
    this.isClearOnClone,
    this.isConditionAuth,
    this.value,
    this.readonly,
    this.forceView,
    this.optionConfig,
    this.autoGenId,
    this.autoGenIdValue,
    this.maskInput,
    this.showLimitInput,
    this.useTimeDefault,
    this.tooltip,
    this.maskInputValue,
    this.inputType,
    this.maxLength,
    this.minLength,
    this.maxValue,
    this.minValue,
    this.stepNumber,
    this.isDecimal,
    this.currencyText,
    this.currencyType,
    this.isCheckDifference,
    this.isCheckAll,
    this.isHorizontalItem,
    this.splitterOpen,
    this.splitterDesc,
    this.options,
    this.dataValue,
    this.error,
    // Table-specific fields
    this.columns,
    this.typeOption,
    this.optionType,
    this.option,
    this.displayField,
    this.valueField,
    this.isDragDrop,
    this.isCallApiHandWork,
    this.callApiButtonName,
    this.isHideAddColumnTable,
    this.isHideDeleteColumnTable,
    this.isHideGetTemplateTable,
    this.isHideImportExcelTable,
    this.isHideStt,
    this.isAddRowCountInData,
    this.specialField,
    this.isHideSearchTable,
    this.content,
    // Additional fields for complex tables
    this.width,
    this.hideTableTitle,
    this.hideColumn,
    this.displayFieldObject,
    this.isAutoGetLogo,
    this.isApprovedBudget,
    this.isBusinessPermit,
    this.isApprovedBudgetChart,
    this.approvedBudgetChartType,
    this.defaultSelect,
    this.autoLoadAll,
    this.isMulti,
    this.isChangeCurrencyType,
    this.fileType,
    this.isUserCurrentUserLogin,
    this.strCondition,
    this.strConditionConvert,
    this.chkCurrencyType,
    this.disabled,
    this.dropdown,
    this.isChooseOldDays,
    this.isChooseNow,
    this.isChooseFutureDays,
    this.dateFormat,
    this.acceptFile,
    this.fileSize,
    this.displayFile,
    this.switchFieldConfig,
    this.switchField,
    this.attachFileObjDefault,
    this.isHideHistoryUpload,
    this.attachFileObj,
    // Matrix-specific fields
    this.rowMatrix,
    this.colMatrix,
    this.condition,
    this.labelInTable,
  });

  // Content for rows and columns
  final Map<String, dynamic>? content;

  // Safe parsing helper methods
  static String? parseString(dynamic value) {
    if (value == null) return null;
    try {
      return value.toString();
    } catch (e) {
      return null;
    }
  }

  static bool? parseBool(dynamic value) {
    if (value == null) return null;
    try {
      if (value is bool) return value;
      if (value is String) {
        final lowerValue = value.toLowerCase();
        return lowerValue == 'true' || lowerValue == '1';
      }
      if (value is num) {
        return value != 0;
      }
      if (value is List) {
        return value.isNotEmpty;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static double? parseDouble(dynamic value) {
    if (value == null) return null;
    try {
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        final cleanValue = value.replaceAll(',', '');
        return double.tryParse(cleanValue);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static int? parseInt(dynamic value) {
    if (value == null) return null;
    try {
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) {
        final cleanValue = value.replaceAll(',', '');
        return int.tryParse(cleanValue);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Map<String, dynamic>? parseMap(dynamic value) {
    if (value == null) return null;
    try {
      if (value is Map<String, dynamic>) return value;
      if (value is Map) {
        return Map<String, dynamic>.from(value);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static List<T>? parseList<T>(dynamic value, T Function(dynamic) parser) {
    if (value == null) return null;
    try {
      if (value is! List) return null;
      return value.map((item) => parser(item)).toList();
    } catch (e) {
      return null;
    }
  }

  // Factory constructor to create a FormItemInfo from JSON
  factory FormItemInfo.fromJson(Map<String, dynamic> json) {
    // Helper function to parse boolean values that might come as strings
    bool? parseBoolValue(dynamic value) {
      if (value == null) return null;
      try {
        if (value is bool) return value;
        if (value is String) {
          final lowerValue = value.toLowerCase();
          return lowerValue == 'true' || lowerValue == '1';
        }
        if (value is num) {
          return value != 0;
        }
        return null;
      } catch (e) {
        return null;
      }
    }

    // Handle conversion of FormItemType
    FormItemType? formItemType;
    try {
      if (json['type'] != null) {
        if (json['type'] is String) {
          // Convert string type to enum
          switch (json['type']) {
            case 'text':
              formItemType = FormItemType.input;
              break;
            case 'textarea':
              formItemType = FormItemType.textArea;
              break;
            case 'checkbox':
              formItemType = FormItemType.checkbox;
              break;
            case 'radio':
              formItemType = FormItemType.radioButton;
              break;
            case 'select':
              formItemType = FormItemType.dropdown;
              break;
            case 'date':
              formItemType = FormItemType.datePicker;
              break;
            case 'splitter':
              formItemType = FormItemType.splitter;
              break;
            case 'row':
              formItemType = FormItemType.row;
              break;
            case 'column':
              formItemType = FormItemType.column;
              break;
            case 'step':
              formItemType = FormItemType.step;
              break;
            case 'tab':
              formItemType = FormItemType.tab;
              break;
            case 'file':
              formItemType = FormItemType.fileUpload;
              break;
            case 'table':
              formItemType = FormItemType.table;
              break;
            case 'matrix':
              formItemType = FormItemType.matrix;
              break;
            case 'divider':
              formItemType = FormItemType.divider;
              break;
            case 'label':
            case '':
              formItemType = FormItemType.label;
              break;
            case 'url':
              formItemType = FormItemType.url;
              break;
            case 'number':
              formItemType = FormItemType.number;
              break;
            case 'currency':
              formItemType = FormItemType.currency;
              break;
            case 'formula':
              formItemType = FormItemType.formula;
              break;
            default:
              formItemType = FormItemType.input;
          }
        } else if (json['type'] is int) {
          try {
            formItemType = FormItemType.values[json['type']];
          } catch (e) {
            formItemType = FormItemType.input;
          }
        }
      }
    } catch (e) {
      formItemType = FormItemType.input;
    }

    // Safely parse lists of various types
    List<FormItemInfo>? parseFormItemInfoList(dynamic data) {
      return parseList(data, (item) {
        if (item is Map<String, dynamic>) {
          return FormItemInfo.fromJson(item);
        }
        return FormItemInfo.fromJson({});
      });
    }

    List<Map<String, dynamic>>? parseMapList(dynamic data) {
      return parseList(data, (item) {
        if (item is Map) {
          return Map<String, dynamic>.from(item);
        }
        return <String, dynamic>{};
      });
    }

    List<String>? parseStringList(dynamic data) {
      return parseList(data, (item) => item.toString());
    }

    // Handle nested columns for tables
    List<FormItemInfo>? columns = parseFormItemInfoList(json['columns']);

    // Handle eventExpression
    List<Map<String, dynamic>>? eventExpression = parseMapList(json['eventExpression']);

    // Handle options
    List<Map<String, dynamic>>? options = parseMapList(json['options']);

    // Handle option
    List<Map<String, dynamic>>? option = parseMapList(json['option']);

    // Handle maskInputValue
    List<Map<String, dynamic>>? maskInputValue = parseMapList(json['maskInputValue']);

    // Handle specialField
    List<String>? specialField = parseStringList(json['specialField']);

    // Handle displayFieldObject
    List<Map<String, dynamic>>? displayFieldObject = parseMapList(json['displayFieldObject']);

    // Handle isHorizontal - it can be a number or a boolean
    dynamic isHorizontalValue = json['isHorizontal'];
    try {
      if (isHorizontalValue is num) {
        isHorizontalValue = isHorizontalValue.toDouble();
      } else if (isHorizontalValue is String) {
        isHorizontalValue = double.tryParse(isHorizontalValue) ?? 2.0;
      } else if (isHorizontalValue is bool) {
        isHorizontalValue = isHorizontalValue ? 1.0 : 0.0;
      } else {
        isHorizontalValue = 2.0; // Default value
      }
    } catch (e) {
      isHorizontalValue = 2.0;
    }

    // For currency type, set inputType to currency if not specified
    String? inputType = parseString(json['inputType']);
    if (json['type'] == 'currency' && inputType == null) {
      inputType = 'currency';
    }

    return FormItemInfo(
      id: json['id']?.toString(),
      step: json['step']?.toString(),
      row: json['row']?.toString(),
      col: json['col']?.toString(),
      rowSortOrder: json['rowSortOrder'] != null
          ? double.tryParse(json['rowSortOrder'].toString())
          : null,
      colSortOrder: json['colSortOrder'] != null
          ? double.tryParse(json['colSortOrder'].toString())
          : null,
      fieldSortOrder: json['fieldSortOrder'] != null
          ? double.tryParse(json['fieldSortOrder'].toString())
          : null,
      tab: json['tab']?.toString(),
      tabItem: json['tabItem']?.toString(),
      parentName: json['parentName']?.toString(),
      splitter: json['splitter']?.toString(),
      validations: json['validations'] is Map
          ? Map<String, dynamic>.from(json['validations'])
          : null,
      type: formItemType,
      label: json['label']?.toString(),
      name: json['name']?.toString(),
      placeholder: json['placeholder']?.toString(),
      eventExpression: eventExpression,
      isHorizontal: isHorizontalValue,
      fontWeight: json['fontWeight']?.toString(),
      display: parseBoolValue(json['display']),
      suggestText: json['suggestText']?.toString(),
      isCloneInSpecFlow: parseBoolValue(json['isCloneInSpecFlow']),
      displayName: json['displayName']?.toString(),
      isClearOnClone: parseBoolValue(json['isClearOnClone']),
      isConditionAuth: parseBoolValue(json['isConditionAuth']),
      value: json['value'],
      readonly: parseBoolValue(json['readonly']),
      forceView: parseBoolValue(json['forceView']),
      optionConfig: json['optionConfig'] is Map
          ? Map<String, dynamic>.from(
              (json['optionConfig'] as Map).map(
                (key, value) => MapEntry(
                  key.toString(),
                  value is num 
                      ? (value == value.toInt() ? value.toInt() : value.toDouble())
                      : value,
                ),
              ),
            )
          : null,
      autoGenId: parseBoolValue(json['autoGenId']),
      autoGenIdValue: json['autoGenIdValue']?.toString(),
      maskInput: parseBoolValue(json['maskInput']),
      showLimitInput: parseBoolValue(json['showLimitInput']),
      useTimeDefault: parseBoolValue(json['useTimeDefault']),
      tooltip: json['tooltip']?.toString(),
      maskInputValue: maskInputValue,
      inputType: inputType,
      maxLength: json['maxLength'] != null
          ? double.tryParse(json['maxLength'].toString())
          : null,
      minLength: json['minLength'] != null
          ? double.tryParse(json['minLength'].toString())
          : null,
      maxValue: json['maxValue'] != null
          ? double.tryParse(json['maxValue'].toString())
          : null,
      minValue: json['minValue'] != null
          ? double.tryParse(json['minValue'].toString())
          : null,
      stepNumber: json['stepNumber'] != null
          ? double.tryParse(json['stepNumber'].toString())
          : null,
      isDecimal: parseBoolValue(json['isDecimal']),
      currencyText: parseBoolValue(json['currencyText']),
      currencyType: json['currencyType']?.toString(),
      isCheckDifference: parseBoolValue(json['isCheckDifference']),
      isCheckAll: parseBoolValue(json['isCheckAll']),
      isHorizontalItem: json['isHorizontalItem'] != null
          ? int.tryParse(json['isHorizontalItem'].toString())
          : null,
      splitterOpen: json['splitterOpen'] != null
          ? int.tryParse(json['splitterOpen'].toString())
          : null,
      splitterDesc: json['splitterDesc']?.toString(),
      options: options,
      dataValue: json['dataValue'],
      error: '',
      columns: columns,
      typeOption: json['typeOption']?.toString(),
      optionType: json['optionType']?.toString(),
      option: option,
      displayField: json['displayField']?.toString(),
      valueField: json['valueField']?.toString(),
      isDragDrop: json['isDragDrop'] != null
          ? json['isDragDrop'] is bool
              ? json['isDragDrop']
              : json['isDragDrop'] == 'true'
          : null,
      isCallApiHandWork: json['isCallApiHandWork'] != null
          ? json['isCallApiHandWork'] is bool
              ? json['isCallApiHandWork']
              : json['isCallApiHandWork'] == 'true'
          : null,
      callApiButtonName: json['callApiButtonName']?.toString(),
      isHideAddColumnTable: json['isHideAddColumnTable'] != null
          ? json['isHideAddColumnTable'] is bool
              ? json['isHideAddColumnTable']
              : json['isHideAddColumnTable'] == 'true'
          : null,
      isHideDeleteColumnTable: json['isHideDeleteColumnTable'] != null
          ? json['isHideDeleteColumnTable'] is bool
              ? json['isHideDeleteColumnTable']
              : json['isHideDeleteColumnTable'] == 'true'
          : null,
      isHideGetTemplateTable: json['isHideGetTemplateTable'] != null
          ? json['isHideGetTemplateTable'] is bool
              ? json['isHideGetTemplateTable']
              : json['isHideGetTemplateTable'] == 'true'
          : null,
      isHideImportExcelTable: json['isHideImportExcelTable'] != null
          ? json['isHideImportExcelTable'] is bool
              ? json['isHideImportExcelTable']
              : json['isHideImportExcelTable'] == 'true'
          : null,
      isHideStt: json['isHideStt'] != null
          ? json['isHideStt'] is bool
              ? json['isHideStt']
              : json['isHideStt'] == 'true'
          : null,
      isAddRowCountInData: json['isAddRowCountInData'] != null
          ? json['isAddRowCountInData'] is bool
              ? json['isAddRowCountInData']
              : json['isAddRowCountInData'] == 'true'
          : null,
      specialField: specialField,
      isHideSearchTable: json['isHideSearchTable'] != null
          ? json['isHideSearchTable'] is bool
              ? json['isHideSearchTable']
              : json['isHideSearchTable'] == 'true'
          : null,
      content: json['content'] is Map
          ? Map<String, dynamic>.from(json['content'])
          : null,
      // Additional fields for complex tables
      width: json['width']?.toString(),
      hideTableTitle: json['hideTableTitle'] != null
          ? json['hideTableTitle'] is bool
              ? json['hideTableTitle']
              : json['hideTableTitle'] == 'true'
          : null,
      hideColumn: json['hideColumn'] != null
          ? json['hideColumn'] is bool
              ? json['hideColumn']
              : json['hideColumn'] == 'true'
          : null,
      displayFieldObject: displayFieldObject,
      isAutoGetLogo: json['isAutoGetLogo'] != null
          ? json['isAutoGetLogo'] is bool
              ? json['isAutoGetLogo']
              : json['isAutoGetLogo'] == 'true'
          : null,
      isApprovedBudget: json['isApprovedBudget'] != null
          ? json['isApprovedBudget'] is bool
              ? json['isApprovedBudget']
              : json['isApprovedBudget'] == 'true'
          : null,
      isBusinessPermit: json['isBusinessPermit'] != null
          ? json['isBusinessPermit'] is bool
              ? json['isBusinessPermit']
              : json['isBusinessPermit'] == 'true'
          : null,
      isApprovedBudgetChart: json['isApprovedBudgetChart'] != null
          ? json['isApprovedBudgetChart'] is bool
              ? json['isApprovedBudgetChart']
              : json['isApprovedBudgetChart'] == 'true'
          : null,
      approvedBudgetChartType: json['approvedBudgetChartType']?.toString(),
      defaultSelect: json['defaultSelect'] != null
          ? json['defaultSelect'] is bool
              ? json['defaultSelect']
              : json['defaultSelect'] == 'true'
          : null,
      autoLoadAll: json['autoLoadAll'] != null
          ? json['autoLoadAll'] is bool
              ? json['autoLoadAll']
              : json['autoLoadAll'] == 'true'
          : null,
      isMulti: json['isMulti'] != null
          ? json['isMulti'] is bool
              ? json['isMulti']
              : json['isMulti'] == 'true'
          : null,
      isChangeCurrencyType: json['isChangeCurrencyType'] != null
          ? json['isChangeCurrencyType'] is bool
              ? json['isChangeCurrencyType']
              : json['isChangeCurrencyType'] == 'true'
          : null,
      fileType: json['fileType']?.toString(),
      isUserCurrentUserLogin: json['isUserCurrentUserLogin'] != null
          ? json['isUserCurrentUserLogin'] is bool
              ? json['isUserCurrentUserLogin']
              : json['isUserCurrentUserLogin'] == 'true'
          : null,
      strCondition: json['strCondition']?.toString(),
      strConditionConvert: json['strConditionConvert']?.toString(),
      chkCurrencyType: parseBoolValue(json['chkCurrencyType']),
      disabled: parseBoolValue(json['disabled']),
      dropdown: json['dropdown'] != null
          ? DropdownConfig.fromJson(json['dropdown'])
          : null,
      isChooseOldDays: parseBoolValue(json['isChooseOldDays']),
      isChooseNow: parseBoolValue(json['isChooseNow']),
      isChooseFutureDays: parseBoolValue(json['isChooseFutureDays']),
      dateFormat: json['dateFormat']?.toString(),
      acceptFile: json['acceptFile']?.toString(),
      fileSize: json['fileSize'] != null
          ? int.tryParse(json['fileSize'].toString())
          : null,
      displayFile: json['displayFile']?.toString(),
      switchFieldConfig: parseMapList(json['switchFieldConfig']),
      switchField: parseBoolValue(json['switchField']),
      attachFileObjDefault: json['attachFileObjDefault'] != null
          ? Map<String, dynamic>.from(json['attachFileObjDefault'])
          : null,
      isHideHistoryUpload: json['isHideHistoryUpload'] != null
          ? json['isHideHistoryUpload'] is bool
              ? json['isHideHistoryUpload']
              : json['isHideHistoryUpload'] == 'true'
          : null,
      attachFileObj: json['attachFileObj'] != null
          ? Map<String, dynamic>.from(json['attachFileObj'])
          : null,
      rowMatrix: json['rowMatrix'] != null
          ? int.tryParse(json['rowMatrix'].toString())
          : null,
      colMatrix: json['colMatrix'] != null
          ? int.tryParse(json['colMatrix'].toString())
          : null,
      condition: json['condition'] != null 
          ? List<Map<String, dynamic>>.from(json['condition'])
          : null,
      labelInTable: json['labelInTable']?.toString(),
    );
  }

  // Convert FormItemType to string
  static String? formItemTypeToString(FormItemType? type) {
    if (type == null) return null;

    switch (type) {
      case FormItemType.input:
        return 'text';
      case FormItemType.textArea:
        return 'textarea';
      case FormItemType.checkbox:
        return 'checkbox';
      case FormItemType.radioButton:
        return 'radio';
      case FormItemType.dropdown:
        return 'select';
      case FormItemType.datePicker:
        return 'date';
      case FormItemType.splitter:
        return 'splitter';
      case FormItemType.row:
        return 'row';
      case FormItemType.column:
        return 'column';
      case FormItemType.step:
        return 'step';
      case FormItemType.tab:
        return 'tab';
      case FormItemType.fileUpload:
        return 'file';
      case FormItemType.table:
        return 'table';
      case FormItemType.matrix:
        return 'matrix';
      case FormItemType.divider:
        return 'divider';
      case FormItemType.label:
        return 'label';
      case FormItemType.url:
        return 'url';
      default:
        return 'text';
    }
  }

  // Convert FormItemInfo to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};

    // Add all properties to JSON, but filter out nulls
    if (id != null) json['id'] = id;
    if (step != null) json['step'] = step;
    if (row != null) json['row'] = row;
    if (col != null) json['col'] = col;
    if (rowSortOrder != null) json['rowSortOrder'] = rowSortOrder;
    if (colSortOrder != null) json['colSortOrder'] = colSortOrder;
    if (fieldSortOrder != null) json['fieldSortOrder'] = fieldSortOrder;
    if (tab != null) json['tab'] = tab;
    if (tabItem != null) json['tabItem'] = tabItem;
    if (parentName != null) json['parentName'] = parentName;
    if (splitter != null) json['splitter'] = splitter;
    if (validations != null) json['validations'] = validations;
    if (type != null) json['type'] = formItemTypeToString(type);
    if (label != null) json['label'] = label;
    if (name != null) json['name'] = name;
    if (placeholder != null) json['placeholder'] = placeholder;
    if (eventExpression != null) json['eventExpression'] = eventExpression;
    if (isHorizontal != null) json['isHorizontal'] = isHorizontal;
    if (fontWeight != null) json['fontWeight'] = fontWeight;
    if (display != null) json['display'] = display;
    if (suggestText != null) json['suggestText'] = suggestText;
    if (isCloneInSpecFlow != null)
      json['isCloneInSpecFlow'] = isCloneInSpecFlow;
    if (displayName != null) json['displayName'] = displayName;
    if (isClearOnClone != null) json['isClearOnClone'] = isClearOnClone;
    if (isConditionAuth != null) json['isConditionAuth'] = isConditionAuth;
    if (value != null) json['value'] = value;
    if (readonly != null) json['readonly'] = readonly;
    if (forceView != null) json['forceView'] = forceView;
    if (optionConfig != null) json['optionConfig'] = optionConfig;
    if (autoGenId != null) json['autoGenId'] = autoGenId;
    if (autoGenIdValue != null) json['autoGenIdValue'] = autoGenIdValue;
    if (maskInput != null) json['maskInput'] = maskInput;
    if (showLimitInput != null) json['showLimitInput'] = showLimitInput;
    if (useTimeDefault != null) json['useTimeDefault'] = useTimeDefault;
    if (tooltip != null) json['tooltip'] = tooltip;
    if (maskInputValue != null) json['maskInputValue'] = maskInputValue;
    if (inputType != null) json['inputType'] = inputType;
    if (maxLength != null) json['maxLength'] = maxLength;
    if (minLength != null) json['minLength'] = minLength;
    if (maxValue != null) json['maxValue'] = maxValue;
    if (minValue != null) json['minValue'] = minValue;
    if (stepNumber != null) json['stepNumber'] = stepNumber;
    if (isDecimal != null) json['isDecimal'] = isDecimal;
    if (currencyText != null) json['currencyText'] = currencyText;
    if (currencyType != null) json['currencyType'] = currencyType;
    if (isCheckDifference != null)
      json['isCheckDifference'] = isCheckDifference;
    if (isCheckAll != null) json['isCheckAll'] = isCheckAll;
    if (isHorizontalItem != null) json['isHorizontalItem'] = isHorizontalItem;
    if (splitterOpen != null) json['splitterOpen'] = splitterOpen;
    if (splitterDesc != null) json['splitterDesc'] = splitterDesc;
    if (options != null) json['options'] = options;
    if (dataValue != null) json['dataValue'] = dataValue;
    if (error != null && error!.isNotEmpty) json['error'] = error;

    // Handle table-specific fields
    if (columns != null) {
      json['columns'] = columns!.map((column) => column.toJson()).toList();
    }

    if (typeOption != null) json['typeOption'] = typeOption;
    if (optionType != null) json['optionType'] = optionType;
    if (option != null) json['option'] = option;
    if (displayField != null) json['displayField'] = displayField;
    if (valueField != null) json['valueField'] = valueField;
    if (isDragDrop != null) json['isDragDrop'] = isDragDrop;
    if (isCallApiHandWork != null)
      json['isCallApiHandWork'] = isCallApiHandWork;
    if (callApiButtonName != null)
      json['callApiButtonName'] = callApiButtonName;
    if (isHideAddColumnTable != null)
      json['isHideAddColumnTable'] = isHideAddColumnTable;
    if (isHideDeleteColumnTable != null)
      json['isHideDeleteColumnTable'] = isHideDeleteColumnTable;
    if (isHideGetTemplateTable != null)
      json['isHideGetTemplateTable'] = isHideGetTemplateTable;
    if (isHideImportExcelTable != null)
      json['isHideImportExcelTable'] = isHideImportExcelTable;
    if (isHideStt != null) json['isHideStt'] = isHideStt;
    if (isAddRowCountInData != null)
      json['isAddRowCountInData'] = isAddRowCountInData;
    if (specialField != null) json['specialField'] = specialField;
    if (isHideSearchTable != null)
      json['isHideSearchTable'] = isHideSearchTable;
    if (content != null) json['content'] = content;

    // Additional fields for complex tables
    if (width != null) json['width'] = width;
    if (hideTableTitle != null) json['hideTableTitle'] = hideTableTitle;
    if (hideColumn != null) json['hideColumn'] = hideColumn;
    if (displayFieldObject != null)
      json['displayFieldObject'] = displayFieldObject;
    if (isAutoGetLogo != null) json['isAutoGetLogo'] = isAutoGetLogo;
    if (isApprovedBudget != null) json['isApprovedBudget'] = isApprovedBudget;
    if (isBusinessPermit != null) json['isBusinessPermit'] = isBusinessPermit;
    if (isApprovedBudgetChart != null)
      json['isApprovedBudgetChart'] = isApprovedBudgetChart;
    if (approvedBudgetChartType != null)
      json['approvedBudgetChartType'] = approvedBudgetChartType;
    if (defaultSelect != null) json['defaultSelect'] = defaultSelect;
    if (autoLoadAll != null) json['autoLoadAll'] = autoLoadAll;
    if (isMulti != null) json['isMulti'] = isMulti;
    if (isChangeCurrencyType != null)
      json['isChangeCurrencyType'] = isChangeCurrencyType;
    if (fileType != null) json['fileType'] = fileType;
    if (isUserCurrentUserLogin != null)
      json['isUserCurrentUserLogin'] = isUserCurrentUserLogin;
    if (strCondition != null) json['strCondition'] = strCondition;
    if (strConditionConvert != null)
      json['strConditionConvert'] = strConditionConvert;
    if (chkCurrencyType != null) json['chkCurrencyType'] = chkCurrencyType;
    if (disabled != null) json['disabled'] = disabled;

    // Additional fields for dropdown
    if (dropdown != null) json['dropdown'] = dropdown!.toJson();
    if (isChooseOldDays != null) json['isChooseOldDays'] = isChooseOldDays;
    if (isChooseNow != null) json['isChooseNow'] = isChooseNow;
    if (isChooseFutureDays != null)
      json['isChooseFutureDays'] = isChooseFutureDays;
    if (dateFormat != null) json['dateFormat'] = dateFormat;
    if (acceptFile != null) json['acceptFile'] = acceptFile;
    if (fileSize != null) json['fileSize'] = fileSize;
    if (displayFile != null) json['displayFile'] = displayFile;
    if (condition != null) json['condition'] = condition;
    if (labelInTable != null) json['labelInTable'] = labelInTable;
    return json;
  }

  bool validate() {
    if (validations != null &&
        parseBool(validations!['required']) == true &&
        (value == null || value.toString().isEmpty)) {
      error = 'This field is required';
      return false;
    }

    if (validations != null &&
        validations!['maxlength'] != null &&
        value != null &&
        value.toString().length > parseInt(validations!['maxlength'])!) {
      error = 'Maximum length is ${validations!['maxlength']} characters';
      return false;
    }

    if (validations != null &&
        validations!['minlength'] != null &&
        validations!['minlength'].toString().isNotEmpty &&
        value != null &&
        value.toString().length < parseInt(validations!['minlength'])!) {
      error = 'Minimum length is ${validations!['minlength']} characters';
      return false;
    }

    if ((type == FormItemType.input || inputType == 'currency') &&
        (inputType == 'number' || inputType == 'currency') &&
        value != null &&
        value.toString().isNotEmpty) {
      try {
        double numValue = double.parse(value.toString().replaceAll(',', ''));

        if (maxValue != null && numValue > maxValue!) {
          error = 'Maximum value is $maxValue';
          return false;
        }

        if (minValue != null && numValue < minValue!) {
          error = 'Minimum value is $minValue';
          return false;
        }
      } catch (e) {
        error = 'Please enter a valid number';
        return false;
      }
    }

    error = '';
    return true;
  }

  // Helper method to get column position
  int get columnPosition => colSortOrder?.toInt() ?? 0;

  // Helper method to check if this is a table column
  bool get isTableColumn => type == FormItemType.table && columns != null;

  // Helper method to get sorted columns
  List<FormItemInfo> get sortedColumns {
    if (columns == null) return [];
    return List<FormItemInfo>.from(columns!)
      ..sort((a, b) => (a.colSortOrder ?? 0).compareTo(b.colSortOrder ?? 0));
  }

  FormItemInfo copyWith({
    String? id,
    String? step,
    String? row,
    String? col,
    double? rowSortOrder,
    double? colSortOrder,
    double? fieldSortOrder,
    String? tab,
    String? tabItem,
    String? parentName,
    String? splitter,
    Map<String, dynamic>? validations,
    FormItemType? type,
    String? label,
    String? name,
    String? placeholder,
    List<Map<String, dynamic>>? eventExpression,
    dynamic isHorizontal,
    String? fontWeight,
    bool? display,
    String? suggestText,
    bool? isCloneInSpecFlow,
    String? displayName,
    bool? isClearOnClone,
    bool? isConditionAuth,
    dynamic value,
    bool? readonly,
    bool? forceView,
    Map<String, dynamic>? optionConfig,
    bool? autoGenId,
    bool? maskInput,
    bool? showLimitInput,
    bool? useTimeDefault,
    String? tooltip,
    List<Map<String, dynamic>>? maskInputValue,
    String? inputType,
    double? maxLength,
    double? minLength,
    double? maxValue,
    double? minValue,
    double? stepNumber,
    bool? isDecimal,
    bool? currencyText,
    String? currencyType,
    bool? isCheckDifference,
    bool? isCheckAll,
    int? isHorizontalItem,
    int? splitterOpen,
    String? splitterDesc,
    List<Map<String, dynamic>>? options,
    dynamic dataValue,
    List<FormItemInfo>? columns,
    String? typeOption,
    String? optionType,
    List<Map<String, dynamic>>? option,
    String? displayField,
    String? valueField,
    bool? isDragDrop,
    bool? isCallApiHandWork,
    String? callApiButtonName,
    bool? isHideAddColumnTable,
    bool? isHideDeleteColumnTable,
    bool? isHideGetTemplateTable,
    bool? isHideImportExcelTable,
    bool? isHideStt,
    bool? isAddRowCountInData,
    List<String>? specialField,
    bool? isHideSearchTable,
    Map<String, dynamic>? content,
    String? width,
    bool? hideTableTitle,
    bool? hideColumn,
    List<Map<String, dynamic>>? displayFieldObject,
    bool? isAutoGetLogo,
    bool? isApprovedBudget,
    bool? isBusinessPermit,
    bool? isApprovedBudgetChart,
    String? approvedBudgetChartType,
    bool? defaultSelect,
    bool? autoLoadAll,
    bool? isMulti,
    bool? isChangeCurrencyType,
    String? fileType,
    bool? isUserCurrentUserLogin,
    String? strCondition,
    String? strConditionConvert,
    bool? chkCurrencyType,
    bool? disabled,
    DropdownConfig? dropdown,
    bool? isChooseOldDays,
    bool? isChooseNow,
    bool? isChooseFutureDays,
    String? dateFormat,
    String? acceptFile,
    int? fileSize,
    String? displayFile,
    List<Map<String, dynamic>>? switchFieldConfig,
    bool? switchField,
    Map<String, dynamic>? attachFileObjDefault,
    bool? isHideHistoryUpload,
    Map<String, dynamic>? attachFileObj,
    int? rowMatrix,
    int? colMatrix,
    List<Map<String, dynamic>>? condition,
  }) {
    return FormItemInfo(
      id: id ?? this.id,
      step: step ?? this.step,
      row: row ?? this.row,
      col: col ?? this.col,
      rowSortOrder: rowSortOrder ?? this.rowSortOrder,
      colSortOrder: colSortOrder ?? this.colSortOrder,
      fieldSortOrder: fieldSortOrder ?? this.fieldSortOrder,
      tab: tab ?? this.tab,
      tabItem: tabItem ?? this.tabItem,
      parentName: parentName ?? this.parentName,
      splitter: splitter ?? this.splitter,
      validations: validations ?? this.validations,
      type: type ?? this.type,
      label: label ?? this.label,
      name: name ?? this.name,
      placeholder: placeholder ?? this.placeholder,
      eventExpression: eventExpression ?? this.eventExpression,
      isHorizontal: isHorizontal ?? this.isHorizontal,
      fontWeight: fontWeight ?? this.fontWeight,
      display: display ?? this.display,
      suggestText: suggestText ?? this.suggestText,
      isCloneInSpecFlow: isCloneInSpecFlow ?? this.isCloneInSpecFlow,
      displayName: displayName ?? this.displayName,
      isClearOnClone: isClearOnClone ?? this.isClearOnClone,
      isConditionAuth: isConditionAuth ?? this.isConditionAuth,
      value: value ?? this.value,
      readonly: readonly ?? this.readonly,
      forceView: forceView ?? this.forceView,
      optionConfig: optionConfig ?? this.optionConfig,
      autoGenId: autoGenId ?? this.autoGenId,
      maskInput: maskInput ?? this.maskInput,
      showLimitInput: showLimitInput ?? this.showLimitInput,
      useTimeDefault: useTimeDefault ?? this.useTimeDefault,
      tooltip: tooltip ?? this.tooltip,
      maskInputValue: maskInputValue ?? this.maskInputValue,
      inputType: inputType ?? this.inputType,
      maxLength: maxLength ?? this.maxLength,
      minLength: minLength ?? this.minLength,
      maxValue: maxValue ?? this.maxValue,
      minValue: minValue ?? this.minValue,
      stepNumber: stepNumber ?? this.stepNumber,
      isDecimal: isDecimal ?? this.isDecimal,
      currencyText: currencyText ?? this.currencyText,
      currencyType: currencyType ?? this.currencyType,
      isCheckDifference: isCheckDifference ?? this.isCheckDifference,
      isCheckAll: isCheckAll ?? this.isCheckAll,
      isHorizontalItem: isHorizontalItem ?? this.isHorizontalItem,
      splitterOpen: splitterOpen ?? this.splitterOpen,
      splitterDesc: splitterDesc ?? this.splitterDesc,
      options: options ?? this.options,
      dataValue: dataValue ?? this.dataValue,
      columns: columns ?? this.columns,
      typeOption: typeOption ?? this.typeOption,
      optionType: optionType ?? this.optionType,
      option: option ?? this.option,
      displayField: displayField ?? this.displayField,
      valueField: valueField ?? this.valueField,
      isDragDrop: isDragDrop ?? this.isDragDrop,
      isCallApiHandWork: isCallApiHandWork ?? this.isCallApiHandWork,
      callApiButtonName: callApiButtonName ?? this.callApiButtonName,
      isHideAddColumnTable: isHideAddColumnTable ?? this.isHideAddColumnTable,
      isHideDeleteColumnTable:
          isHideDeleteColumnTable ?? this.isHideDeleteColumnTable,
      isHideGetTemplateTable:
          isHideGetTemplateTable ?? this.isHideGetTemplateTable,
      isHideImportExcelTable:
          isHideImportExcelTable ?? this.isHideImportExcelTable,
      isHideStt: isHideStt ?? this.isHideStt,
      isAddRowCountInData: isAddRowCountInData ?? this.isAddRowCountInData,
      specialField: specialField ?? this.specialField,
      isHideSearchTable: isHideSearchTable ?? this.isHideSearchTable,
      content: content ?? this.content,
      width: width ?? this.width,
      hideTableTitle: hideTableTitle ?? this.hideTableTitle,
      hideColumn: hideColumn ?? this.hideColumn,
      displayFieldObject: displayFieldObject ?? this.displayFieldObject,
      isAutoGetLogo: isAutoGetLogo ?? this.isAutoGetLogo,
      isApprovedBudget: isApprovedBudget ?? this.isApprovedBudget,
      isBusinessPermit: isBusinessPermit ?? this.isBusinessPermit,
      isApprovedBudgetChart:
          isApprovedBudgetChart ?? this.isApprovedBudgetChart,
      approvedBudgetChartType:
          approvedBudgetChartType ?? this.approvedBudgetChartType,
      defaultSelect: defaultSelect ?? this.defaultSelect,
      autoLoadAll: autoLoadAll ?? this.autoLoadAll,
      isMulti: isMulti ?? this.isMulti,
      isChangeCurrencyType: isChangeCurrencyType ?? this.isChangeCurrencyType,
      fileType: fileType ?? this.fileType,
      isUserCurrentUserLogin:
          isUserCurrentUserLogin ?? this.isUserCurrentUserLogin,
      strCondition: strCondition ?? this.strCondition,
      strConditionConvert: strConditionConvert ?? this.strConditionConvert,
      chkCurrencyType: chkCurrencyType ?? this.chkCurrencyType,
      disabled: disabled ?? this.disabled,
      dropdown: dropdown ?? this.dropdown,
      isChooseOldDays: isChooseOldDays ?? this.isChooseOldDays,
      isChooseNow: isChooseNow ?? this.isChooseNow,
      isChooseFutureDays: isChooseFutureDays ?? this.isChooseFutureDays,
      dateFormat: dateFormat ?? this.dateFormat,
      acceptFile: acceptFile ?? this.acceptFile,
      fileSize: fileSize ?? this.fileSize,
      displayFile: displayFile ?? this.displayFile,
      switchFieldConfig: switchFieldConfig ?? this.switchFieldConfig,
      switchField: switchField ?? this.switchField,
      attachFileObjDefault: attachFileObjDefault ?? this.attachFileObjDefault,
      isHideHistoryUpload: isHideHistoryUpload ?? this.isHideHistoryUpload,
      attachFileObj: attachFileObj ?? this.attachFileObj,
      rowMatrix: rowMatrix ?? this.rowMatrix,
      colMatrix: colMatrix ?? this.colMatrix,
      condition: condition ?? this.condition,
    );
  }
}

class DropdownConfig {
  final String? type;
  final Map<String, dynamic>? expressions;
  final List<String>? displayFields;
  final List<String>? valueFields;

  const DropdownConfig({
    this.type,
    this.expressions,
    this.displayFields,
    this.valueFields,
  });

  factory DropdownConfig.fromJson(Map<String, dynamic> json) {
    return DropdownConfig(
      type: json['type']?.toString(),
      expressions: json['expressions'] is Map
          ? Map<String, dynamic>.from(json['expressions'])
          : null,
      displayFields: json['displayFields'] is List
          ? List<String>.from(json['displayFields'])
          : null,
      valueFields: json['valueFields'] is List
          ? List<String>.from(json['valueFields'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (type != null) json['type'] = type;
    if (expressions != null) json['expressions'] = expressions;
    if (displayFields != null) json['displayFields'] = displayFields;
    if (valueFields != null) json['valueFields'] = valueFields;
    return json;
  }
}
