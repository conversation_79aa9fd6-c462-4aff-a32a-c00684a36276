import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/ticket_other_action/consultation_opinion_model.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:eapprove/blocs/ticket_other_action/event/consultation_opinion_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/consultation_opinion_state.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';

class ConsultationOpinionBloc extends Bloc<ConsultationOpinionEvent, ConsultationOpinionState> {
  final TicketOtherActionRepository repository;

  ConsultationOpinionBloc({required this.repository}) : super(const ConsultationOpinionState()) {
    on<GetConsultationOpinions>(_onGetOpinions);
    on<DeleteConsultationOpinion>(_onDeleteOpinion);
    on<AddConsultationOpinion>(_onAddOpinion);
  }

  Future<void> _onGetOpinions(
    GetConsultationOpinions event,
    Emitter<ConsultationOpinionState> emit,
  ) async {
    emit(state.copyWith(status: ServiceStatus.loading));

    try {
      final response = await repository.getConsultationOpinionList(
        procInstId: event.procInstId,
        ticketId: event.ticketId,
        isAdditionalRequest: event.isAdditionalRequest,
      );

      final List<dynamic> rawContent = response.content;
      final opinions = rawContent.map((e) => ConsultationOpinionModel.fromJson(e)).toList();

      emit(state.copyWith(
        status: ServiceStatus.success,
        opinions: opinions,
        opinionsResponse: response,
      ));
    } catch (e, s) {
      log('Lỗi load tham vấn: $e', stackTrace: s);
      emit(state.copyWith(
        status: ServiceStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onAddOpinion(
    AddConsultationOpinion event,
    Emitter<ConsultationOpinionState> emit,
  ) async {
    emit(state.copyWith(status: ServiceStatus.loading));

    try {
      List<String>? filePaths;
      if (event.files != null && event.files!.isNotEmpty) {
        filePaths = [];
        for (var file in event.files!) {
          if (file.containsKey('filePath') && file['filePath'] != null) {
            filePaths.add(file['filePath']);
          }
        }
        if (filePaths.isEmpty) {
          filePaths = null;
        }
      }

      final success = await repository.addConsultationOpinion(
        procInstId: event.procInstId,
        ticketId: event.ticketId,
        content: event.content,
        isPrivate: event.isPrivate,
        filePaths: filePaths,
        isAdditionalRequest: event.isAdditionalRequest,
        isCreateUserAdditionalRequest: event.isCreateUserAdditionalRequest,
        discusId: event.discusId,
        idFilesDelete: event.idFilesDelete,
        groupId: event.groupId,
      );

      if (success) {
        SnackbarCore.success(event.isAdditionalRequest
            ? (event.discusId != null ? 'Sửa nội dung bổ sung thành công' : 'Gửi nội dung bổ sung thành công')
            : (event.discusId != null ? 'Sửa tham vấn thành công' : 'Gửi tham vấn thành công'));
        add(GetConsultationOpinions(
          procInstId: event.procInstId,
          ticketId: event.ticketId,
          isAdditionalRequest: event.isAdditionalRequest,
        ));
      } else {
        emit(state.copyWith(
          status: ServiceStatus.failure,
          errorMessage: event.isAdditionalRequest
              ? (event.discusId != null
                  ? 'Sửa nội dung bổ sung không thành công'
                  : 'Gửi nội dung bổ sung không thành công')
              : (event.discusId != null ? 'Sửa tham vấn không thành công' : 'Gửi tham vấn không thành công'),
        ));
      }
    } catch (e, s) {
      log('Lỗi ${event.discusId != null ? "sửa" : "thêm"} bình luận: $e', stackTrace: s);
      emit(state.copyWith(
        status: ServiceStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteOpinion(
    DeleteConsultationOpinion event,
    Emitter<ConsultationOpinionState> emit,
  ) async {
    emit(state.copyWith(status: ServiceStatus.loading));

    try {
      final success = await repository.deleteConsultationOpinion(
        discusId: event.discusId,
      );

      if (success) {
        add(GetConsultationOpinions(
          procInstId: event.procInstId,
          ticketId: event.ticketId,
          isAdditionalRequest: false,
        ));
      } else {
        emit(state.copyWith(
          status: ServiceStatus.failure,
          errorMessage: 'Xóa bình luận không thành công',
        ));
      }
    } catch (e, s) {
      log('Lỗi xóa bình luận: $e', stackTrace: s);
      emit(state.copyWith(
        status: ServiceStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }
}
