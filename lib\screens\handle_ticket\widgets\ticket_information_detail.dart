import 'dart:developer' as developer;
import 'dart:convert';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_phieu_response_model.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:shimmer/shimmer.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/common/form_v2/form_builder_v2.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';

class TicketInformationDetailBlock extends StatefulWidget {
  final String title;
  final String prevTask;
  final String procDefId;
  final String taskId;
  final String ticketId;
  final dynamic ticket;
  final String taskDefKey;

  const TicketInformationDetailBlock({
    super.key,
    required this.title,
    required this.prevTask,
    required this.procDefId,
    required this.taskId,
    required this.ticketId,
    required this.ticket,
    required this.taskDefKey,
  });

  @override
  State<TicketInformationDetailBlock> createState() => _TicketInformationDetailBlockState();
}

class _TicketInformationDetailBlockState extends State<TicketInformationDetailBlock> {
  final stateManager = FormStateManager();
  bool _isFormInitialized = false;
  FormResponse? _currentFormResponse;
  ThongTinPhieuResponseModel? _currentThongTinPhieu;

  late FormBloc _formBloc;
  late TicketProcessDetailBloc _ticketBloc;

  @override
  void initState() {
    super.initState();
    _formBloc = context.read<FormBloc>();
    _ticketBloc = context.read<TicketProcessDetailBloc>();
    developer.log('widget.taskId: ${widget.taskId}', name: 'TicketInformationDetailBlock');
    developer.log('widget.ticketId: ${widget.ticketId}', name: 'TicketInformationDetailBlock');
    developer.log('widget.procDefId: ${widget.procDefId}', name: 'TicketInformationDetailBlock');
    developer.log('widget.prevTask: ${widget.prevTask}', name: 'TicketInformationDetailBlock');
    developer.log('widget.title: ${widget.title}', name: 'TicketInformationDetailBlock');
    developer.log('widget.taskDefKey: ${widget.taskDefKey}', name: 'TicketInformationDetailBlock');
    developer.log('widget.ticket: ${widget.ticket}', name: 'TicketInformationDetailBlock');
    _loadForm();
  }

  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();
  // }

  void _loadForm() {
    _formBloc.add(
      LoadFormRequested(
        procDefId: widget.procDefId,
        isCreateTicket: false,
        isApprove: false,
        ticket: widget.ticket,
        taskDefKey:"start"
      ),
    );
  }

  void _handleFormField(String fieldName, dynamic value, String? type, String? additionalVal) {
    developer.log('fieldName: $fieldName', name: 'handleFormField');
    developer.log('handleFormFieldhandleFormFieldvalue: $value', name: 'handleFormField');
    developer.log('type: $type', name: 'handleFormField');
    // developer.log('additionalVal: $additionalVal', name: 'handleFormField');
    switch (type?.toLowerCase()) {
      case "file":
        _handleFileField(fieldName, additionalVal);
        break;
      case "date":
        // stateManager.setAllFields({fieldName: ""});
        break;
      case "json":
        break;
      default:
        stateManager.setFieldValue(fieldName, value);
        break;
    }

    // if (type?.toLowerCase() == "FILE".toLowerCase()) {
    //   _handleFileField(fieldName, additionalVal);
    // } else if (type?.toLowerCase() == "DATE".toLowerCase() ||
    //     type?.toLowerCase() == "JSON".toLowerCase()) {
    //   stateManager.setAllFields({fieldName: ""});
    // } else {
    //   stateManager.setAllFields({fieldName: value});
    // }
  }

  void _handleFileField(String fieldName, String? additionalVal) {
    try {
      if (additionalVal != null) {
        final additionalValMap = jsonDecode(additionalVal) as Map<String, dynamic>;
        if (additionalValMap['data'] != null) {
          final file = {
            'fileName': additionalValMap['data']?['displayName'],
            'path': additionalValMap['filename'],
            'fileSize': additionalValMap['data']?['fileSize'],
          };
          stateManager.setUploadedFiles({
            'name': fieldName,
            'files': [file],
            'fileType': "",
          });
          return;
        }
      }
      // stateManager.setAllFields({fieldName: ""});
    } catch (e) {
      // stateManager.setAllFields({fieldName: ""});
    }
  }

  void _initializeFormIfPossible(FormInfoState formState) {
    if (formState.formResponse == null || _isFormInitialized) {
      return;
    }
    // stateManager.setWidgets(formResponse.data.template.form);
    // stateManager.setAllFields();
    final formResponse = formState.formResponse!;
    final widgets = formResponse.data.template.form;
    final chartData = formState.chartData;
    final chartNodeCustomData = formState.chartNodeCustomData;
    final chartNodeData = formState.chartNodeData;
    developer.log('chartNodeData: $chartNodeData');
    final initialValues = <String, dynamic>{
      'system_chartId_service': chartData?.data.first.id,
      'system_chartNodeCustomId': chartNodeCustomData?.data.data.first.id,
      'system_chartCustomId': chartNodeCustomData?.data.data.first.chartId,
      'system_chartNodeId_ticket': chartNodeData?.data.first.chartNodeId,
    };

    for (var widget in widgets) {
      // if (widget.name == 'dtm_ngayLapPhieu') {
      //   initialValues[widget.name.toString()] =
      //       Utils.convertToUtcIso(DateTime.now().toIso8601String());
      // } else if (widget.name != 'slt_logo') {
      initialValues[widget.name.toString()] = widget.value;
      // }
    }
    _isFormInitialized = true;
    stateManager.setAllFields(initialValues);
    stateManager.setWidgets(widgets);
  }

  void _updateValueForForm(FormResponse? formResponse, ThongTinPhieuResponseModel? thongTinPhieu) {
    if (formResponse == null || thongTinPhieu == null) {
      return;
    }
    for (var field in formResponse.data.template.form) {
      final fieldName = field.name;
      if (fieldName == null) continue;

      for (var ticketField in thongTinPhieu.data) {
      if (ticketField.name.contains(fieldName) == true) {
          _handleFormField(
            fieldName,
            ticketField.value,
            ticketField.type,
            ticketField.additionalVal,
          );
          break;
        }
      }
    }
  }

  Widget _buildCard(Widget child) {
    return CustomExpandedList<String>(
      expandedSvgIconPath: StringImage.ic_arrow_up,
      collapsedSvgIconPath: StringImage.ic_arrow_right,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      title: widget.title,
      cardColor: Colors.white,
      isBuildLeading: true,
      svgIconPath: StringImage.ic_ticket_info_process,
      iconWidth: 24.w,
      iconHeight: 24.h,
      isExpanded: true,
      titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<FormBloc, FormInfoState>(
          listener: (context, formState) {
            if (formState.formResponse?.code == 1 && formState.formResponse?.data != null) {
              _currentFormResponse = formState.formResponse;
              _initializeFormIfPossible(formState);
              // Only load ticket info if we haven't loaded it yet
              if (_currentThongTinPhieu == null) {
                _ticketBloc.add(LoadThongTinPhieu(procInstId: widget.ticketId));
              }
            }
          },
        ),
        BlocListener<TicketProcessDetailBloc, TicketProcessDetailState>(
          listener: (context, ticketState) {
            if (ticketState is ThongTinPhieuLoaded) {
              // Only update if data is different
              if (_currentThongTinPhieu?.data != ticketState.thongTinPhieu.data) {
                _currentThongTinPhieu = ticketState.thongTinPhieu;
                if (_currentFormResponse != null && _currentThongTinPhieu?.data.isNotEmpty == true) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _updateValueForForm(_currentFormResponse, _currentThongTinPhieu);
                  });
                }
              }
            }
          },
        ),
      ],
      child: BlocBuilder<FormBloc, FormInfoState>(
        buildWhen: (prev, curr) {
          final prevResponse = prev.formResponse;
          final currResponse = curr.formResponse;
          if (prevResponse == null && currResponse == null) return false;
          if (prevResponse == null || currResponse == null) return true;
          return prevResponse.code != currResponse.code || prevResponse.data != currResponse.data;
        },
        builder: (context, formState) {
          if (formState.isFormLoading == true) {
            return _buildCard(_buildShimmerLoading());
          }

          if (formState.hasError) {
            return _buildCard(_buildErrorWidget(formState.errorMessage ?? 'Error occurred'));
          }

          return _buildCard(_buildFormContent(formState.formResponse));
        },
      ),
    );
  }

  Widget _buildFormContent(FormResponse? formResponse) {
    // if (formResponse == null) {
    //   return SizedBox.shrink();
    // }

    developer.log('_buildFormContentformResponse: ${_currentThongTinPhieu}', name: 'formResponse');

    if (!_isFormInitialized && _currentThongTinPhieu != null) {
      _updateValueForForm(formResponse, _currentThongTinPhieu);
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      child: FormBuilderV2(
        key: ValueKey(_isFormInitialized),
        widgets: formResponse?.data.template.form ?? [],
        stateManager: stateManager,
        isReadOnly: true,
        row: formResponse?.data.template.row ?? [],
        step: formResponse?.data.template.step ?? [],
        col: formResponse?.data.template.column ?? [],
        tab: formResponse?.data.template.tab ?? [],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Container(
      padding: EdgeInsets.all(16.w),
      alignment: Alignment.center,
      child: Text(message, style: TextStyle(color: Colors.red)),
    );
  }

  Widget _buildShimmerLoading() {
    return SafeArea(
        child: Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: List.generate(
            7, // Adjust number of form fields to simulate
            (index) => _buildShimmerFormField(index),
          ),
        ),
      ),
    ));
  }

  Widget _buildShimmerFormField(int index) {
    switch (index % 4) {
      case 0:
        // Text input field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 100.w + (index * 20).w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Text field
              Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
              ),
            ],
          ),
        );
      case 1:
        // Dropdown field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 120.w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Dropdown
              Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(child: SizedBox()),
                    Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: Container(
                        width: 16.w,
                        height: 16.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case 2:
        // Date picker field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 80.w + (index * 10).w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Date field
              Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(child: SizedBox()),
                    Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: Container(
                        width: 20.w,
                        height: 20.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case 3:
        // Textarea / multiline field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 150.w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Text area
              Container(
                height: 80.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
              ),
            ],
          ),
        );
      default:
        return SizedBox();
    }
  }

  @override
  void dispose() {
    stateManager.dispose();
    super.dispose();
  }
}
