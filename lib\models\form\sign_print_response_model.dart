class SignPrintResponseModel {
  final int code;
  final String message;
  final SignPrintData data;

  SignPrintResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory SignPrintResponseModel.fromJson(Map<String, dynamic> json) {
    return SignPrintResponseModel(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: SignPrintData.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class SignPrintData {
  final int id;
  final String name;
  final String? descr;
  final String procDefId;
  final String? html;
  final String pdfContent;
  final int printType;
  final String createdUser;
  final int createdDate;
  final String? updatedUser;
  final int? updatedDate;
  final List<dynamic> tasks;
  final List<SignInfo> signs;
  final String signedFile;
  final String storageUrl;

  SignPrintData({
    required this.id,
    required this.name,
    this.descr,
    required this.procDefId,
    this.html,
    required this.pdfContent,
    required this.printType,
    required this.createdUser,
    required this.createdDate,
    this.updatedUser,
    this.updatedDate,
    required this.tasks,
    required this.signs,
    required this.signedFile,
    required this.storageUrl,
  });

  factory SignPrintData.fromJson(Map<String, dynamic> json) {
    return SignPrintData(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      descr: json['descr'],
      procDefId: json['procDefId'] ?? '',
      html: json['html'],
      pdfContent: json['pdfContent'] ?? '',
      printType: json['printType'] ?? 0,
      createdUser: json['createdUser'] ?? '',
      createdDate: json['createdDate'] ?? 0,
      updatedUser: json['updatedUser'],
      updatedDate: json['updatedDate'],
      tasks: json['tasks'] ?? [],
      signs: (json['signs'] as List?)?.map((e) => SignInfo.fromJson(e)).toList() ?? [],
      signedFile: json['signedFile'] ?? '',
      storageUrl: json['storageUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'descr': descr,
      'procDefId': procDefId,
      'html': html,
      'pdfContent': pdfContent,
      'printType': printType,
      'createdUser': createdUser,
      'createdDate': createdDate,
      'updatedUser': updatedUser,
      'updatedDate': updatedDate,
      'tasks': tasks,
      'signs': signs.map((e) => e.toJson()).toList(),
      'signedFile': signedFile,
      'storageUrl': storageUrl,
    };
  }
}

class SignInfo {
  final int id;
  final String taskDefKey;
  final String procInstId;
  final int orderSign;
  final String email;
  final int page;
  final int x;
  final int y;
  final int w;
  final int h;
  final String firstName;
  final String lastName;
  final String position;
  final String createdUser;
  final int createdDate;
  final String? updatedUser;
  final int? updatedDate;
  final BpmSignResponse bpmSignResponse;
  final double scale;
  final String? comment;
  final String? signedFile;
  final int? signedDate;
  final String signType;
  final String chartNodeLevel;
  final String? wresize;
  final String? hresize;

  SignInfo({
    required this.id,
    required this.taskDefKey,
    required this.procInstId,
    required this.orderSign,
    required this.email,
    required this.page,
    required this.x,
    required this.y,
    required this.w,
    required this.h,
    required this.firstName,
    required this.lastName,
    required this.position,
    required this.createdUser,
    required this.createdDate,
    this.updatedUser,
    this.updatedDate,
    required this.bpmSignResponse,
    required this.scale,
    this.comment,
    this.signedFile,
    this.signedDate,
    required this.signType,
    required this.chartNodeLevel,
    this.wresize,
    this.hresize,
  });

  factory SignInfo.fromJson(Map<String, dynamic> json) {
    return SignInfo(
      id: json['id'] ?? 0,
      taskDefKey: json['taskDefKey'] ?? '',
      procInstId: json['procInstId'] ?? '',
      orderSign: json['orderSign'] ?? 0,
      email: json['email'] ?? '',
      page: json['page'] ?? 0,
      x: json['x'] ?? 0,
      y: json['y'] ?? 0,
      w: json['w'] ?? 0,
      h: json['h'] ?? 0,
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      position: json['position'] ?? '',
      createdUser: json['createdUser'] ?? '',
      createdDate: json['createdDate'] ?? 0,
      updatedUser: json['updatedUser'],
      updatedDate: json['updatedDate'],
      bpmSignResponse: (() {
        final bpmData = json['bpmSignResponse']; // 👈 Sửa lại đúng key
        print('SignInfo.fromJson - original bpmSignResponse: $bpmData');
        print('SignInfo.fromJson - bpmSignResponse type: ${bpmData.runtimeType}');

        final finalData = bpmData ?? <dynamic, dynamic>{};

        return BpmSignResponse.fromJson(finalData as Map<dynamic, dynamic>);
      })(),
      scale: (json['scale'] ?? 1.0).toDouble(),
      comment: json['comment'],
      signedFile: json['signedFile'],
      signedDate: json['signedDate'],
      signType: json['signType'] ?? '',
      chartNodeLevel: json['chartNodeLevel'] ?? '',
      wresize: json['wresize'],
      hresize: json['hresize'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskDefKey': taskDefKey,
      'procInstId': procInstId,
      'orderSign': orderSign,
      'email': email,
      'page': page,
      'x': x,
      'y': y,
      'w': w,
      'h': h,
      'firstName': firstName,
      'lastName': lastName,
      'position': position,
      'createdUser': createdUser,
      'createdDate': createdDate,
      'updatedUser': updatedUser,
      'updatedDate': updatedDate,
      'bpmSignResponse': bpmSignResponse.toJson(),
      'scale': scale,
      'comment': comment,
      'signedFile': signedFile,
      'signedDate': signedDate,
      'signType': signType,
      'chartNodeLevel': chartNodeLevel,
      'wresize': wresize,
      'hresize': hresize,
    };
  }
}

class BpmSignResponse {
  final String? id;
  final String? img;

  BpmSignResponse({
    this.id,
    this.img,
  });

  factory BpmSignResponse.fromJson(Map<dynamic, dynamic> json) {
    final result = BpmSignResponse(
      id: json['id']?.toString(),
      img: json['img']?.toString(),
    );

    return result;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'img': img,
    };
  }
}
