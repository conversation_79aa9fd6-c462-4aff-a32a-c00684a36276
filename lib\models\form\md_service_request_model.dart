import 'dart:convert';

class MdServiceRequestBody {
  final String masterDataId;
  final List<ConditionFilter> conditionFilter;

  const MdServiceRequestBody({
    required this.masterDataId,
    this.conditionFilter = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'masterDataId': masterDataId,
      'conditionFilter': conditionFilter.map((filter) => filter.toJson()).toList(),
    };
  }

  String toJsonString() {
    return json.encode(toJson());
  }

  factory MdServiceRequestBody.fromJson(Map<String, dynamic> json) {
    return MdServiceRequestBody(
      masterDataId: json['masterDataId']?.toString() ?? '',
      conditionFilter: (json['conditionFilter'] as List<dynamic>)
          .map((item) => ConditionFilter.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  factory MdServiceRequestBody.fromJsonString(String jsonString) {
    return MdServiceRequestBody.fromJson(
      json.decode(jsonString) as Map<String, dynamic>,
    );
  }
}

class ConditionFilter {
  final String condition;
  final String begin;
  final String operator;
  final String end;

  const ConditionFilter({
    required this.condition,
    required this.begin,
    required this.operator,
    required this.end,
  });

  Map<String, dynamic> toJson() {
    return {
      'condition': condition,
      'begin': begin,
      'operator': operator,
      'end': end,
    };
  }

  factory ConditionFilter.fromJson(Map<String, dynamic> json) {
    return ConditionFilter(
      condition: json['condition'] as String,
      begin: json['begin'] as String,
      operator: json['operator'] as String,
      end: json['end'] as String,
    );
  }
}