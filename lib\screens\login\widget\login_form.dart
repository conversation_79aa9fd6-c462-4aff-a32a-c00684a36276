import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/login/widget/first_time_login.dart';
import 'package:eapprove/screens/login/widget/forgot_pass.dart';
import 'package:eapprove/screens/login/widget/login_container.dart';
import 'package:eapprove/screens/login/widget/login_otp_verification.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_check_box.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_sdk/widgets/form.dart';
import 'package:go_router/go_router.dart';

class LoginForm extends ConsumerStatefulWidget {
  const LoginForm({super.key});

  @override
  ConsumerState<LoginForm> createState() => _FormLoginState();
}

enum FormType { login, forgotPassword, confirmForgotPassword, firstTimeLogin, otpChoice }

class _FormLoginState extends ConsumerState<LoginForm> {
  FormType _currentForm = FormType.login;
  bool _isLoading = false;
  String? _savedUsername;
  String? _savedPassword;
  String? _sessionState;
  String? _hash;
  bool _is2FA = false;
  String? _newPassword;
  String? _newRePassword;
  bool _isFirstTimeLogin = true;
  bool _rememberMe = false;
  String? _email;
  void _updateRememberMe(bool value) {
    setState(() {
      _rememberMe = value;
    });
  }

  void switchForm(
    FormType newForm, {
    String? username,
    String? password,
    String? sessionState,
    String? hash,
    bool? is2FA,
    String? newPassword,
    String? newRePassword,
    bool? isFirstTimeLogin,
    String? email,
    bool clearUsername = false,
  }) {
    FocusScope.of(context).unfocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _currentForm = newForm;

          if (clearUsername) {
            _savedUsername = null;
          } else if (username != null) {
            _savedUsername = username;
          }
          if (password != null) {
            _savedPassword = password;
          }
          if (sessionState != null) {
            _sessionState = sessionState;
          }
          if (hash != null) {
            _hash = hash;
          }
          if (is2FA != null) {
            _is2FA = is2FA;
          }
          if (newPassword != null) {
            _newPassword = newPassword;
          }
          if (newRePassword != null) {
            _newRePassword = newRePassword;
          }
          if (isFirstTimeLogin != null) {
            _isFirstTimeLogin = isFirstTimeLogin;
          }
          if (email != null) {
            _email = email;
          }
        });
      }
    });
  }

  void _showLoadingDialog() {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: Center(
              child: Container(
                padding: EdgeInsets.all(20.r),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AppConstraint.buildLoading(context),
                    SizedBox(height: 20.h),
                    Text(
                      'Đang xử lý...',
                      style: getTypoSkin().body2Regular.copyWith(
                            color: getColorSkin().primaryText,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _hideLoadingDialog() {
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    }
  }

  Widget _buildCurrentForm() {
    switch (_currentForm) {
      case FormType.login:
        return LoginFormWidget(
          isLoading: _isLoading,
          onForgotPassword: () => switchForm(FormType.forgotPassword),
          onSwitchForm: switchForm,
          initialUsername: _savedUsername,
          initialPassword: _savedPassword,
          onRememberMeChanged: _updateRememberMe,
        );
      case FormType.forgotPassword:
        return ForgotPasswordFormWidget(
          onBackToLogin: () => switchForm(FormType.login),
          onConfirm: () => switchForm(FormType.confirmForgotPassword, clearUsername: true),
          initialUsername: _savedUsername,
        );
      case FormType.confirmForgotPassword:
        return ConfirmForgotPasswordFormWidget(
          onBackToLogin: () => switchForm(FormType.login),
          onBackToForgotPassword: () => switchForm(FormType.forgotPassword, clearUsername: true),
        );
      case FormType.firstTimeLogin:
        return FirstTimeLogin(
          onBackToLogin: () => switchForm(FormType.login),
          onSelectLoginMethod: (password, rePassword) => switchForm(
            FormType.otpChoice,
            newPassword: password,
            newRePassword: rePassword,
          ),
          username: _savedUsername ?? '',
          hash: _hash ?? '',
          is2FA: _is2FA,
          sessionState: _sessionState,
          email: _email,
        );
      case FormType.otpChoice:
        return SelectLoginMethod(
          onBackToLogin: () => switchForm(FormType.login),
          onBackToFirstTimeLogin: _isFirstTimeLogin ? () => switchForm(FormType.firstTimeLogin) : null,
          sessionState: _sessionState ?? '',
          username: _savedUsername ?? '',
          hash: _hash ?? '',
          newPassword: _newPassword ?? '',
          newRePassword: _newRePassword ?? '',
          isFirstTimeLogin: _isFirstTimeLogin,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        debugPrint('state: $state');
        if (state is AuthenticationLoading) {
          _showLoadingDialog();
        } else {
          _hideLoadingDialog();

          if (state is TokenSuccess) {
            Future.delayed(const Duration(milliseconds: 300));

            // context.read<UserBloc>().add(FetchUserInfo());
            SnackbarCore.success('Đăng nhập thành công');

            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.go(BottomNavScreen.routeName);
            });
          } else if (state is FirstTimeLoginRequired) {
            switchForm(
              FormType.firstTimeLogin,
              username: state.username,
              sessionState: state.sessionState,
              hash: state.hash,
              is2FA: state.is2FA,
              isFirstTimeLogin: true,
              email: state.email,
            );
          } else if (state is FirstTimeLoginSuccess) {
            SnackbarCore.success('Đổi mật khẩu thành công, vui lòng đăng nhập lại');
            if (_rememberMe) {
              context.read<AuthenticationBloc>().add(
                    SaveLoginInfo(
                      username: _savedUsername ?? '',
                      rememberMe: true,
                    ),
                  );
            }

            switchForm(
              FormType.login,
              username: _savedUsername,
              password: _newPassword,
            );
          } else if (state is OtpVerificationRequired) {
            if (state.hash == null) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => Login2FA(
                    sessionState: state.sessionState,
                    email: state.email ?? '',
                  ),
                ),
              );
            } else {
              switchForm(
                FormType.otpChoice,
                username: state.username,
                sessionState: state.sessionState,
                hash: state.hash ?? '',
                is2FA: true,
                isFirstTimeLogin: false,
              );
            }
          } else if (state is AuthenticationFailure) {
            if (_currentForm == FormType.forgotPassword) {
              SnackbarCore.error(state.error);
            } else {
              SnackbarCore.error(state.error);
              switchForm(FormType.login);
            }
          }
        }
      },
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    // SizedBox(
                    //   width: double.infinity,
                    //   child: Align(
                    //     alignment: Alignment.topCenter,
                    //     child: Image.asset(
                    //       "assets/images/logo.png",
                    //       width: 247.w,
                    //       height: 128.h,
                    //     ),
                    //   ),
                    // ),
                    _buildCurrentForm(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Widget form đăng nhập
class LoginFormWidget extends ConsumerStatefulWidget {
  final bool isLoading;
  final VoidCallback onForgotPassword;
  final Function(FormType, {String? username})? onSwitchForm;
  final String? initialUsername;
  final String? initialPassword;
  final Function(bool)? onRememberMeChanged;

  const LoginFormWidget({
    super.key,
    required this.isLoading,
    required this.onForgotPassword,
    this.onSwitchForm,
    this.initialUsername,
    this.initialPassword,
    this.onRememberMeChanged,
  });

  @override
  ConsumerState<LoginFormWidget> createState() => _LoginFormWidgetState();
}

class _LoginFormWidgetState extends ConsumerState<LoginFormWidget> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _formKey = GlobalKey<FFormState>();
  Map<String, String?> _fieldErrors = {};
  bool _rememberMe = false;
  bool passVisible = false;

  String? usernameError;
  String? passwordError;

  @override
  void initState() {
    super.initState();
    _loadSavedLoginInfo();
    if(kDebugMode){
      _usernameController.text= 'employee';
      _passwordController.text= 'employee';
    }

    // if (widget.initialUsername != null) {
    //   _usernameController.text = widget.initialUsername!;
    // }
    //
    // if (widget.initialPassword != null) {
    //   _passwordController.text = widget.initialPassword!;
    // }
  }


  Future<void> _loadSavedLoginInfo() async {
    // final savedInfo = await context.read<AuthenticationBloc>().getSavedLoginInfo();
    // setState(() {
    //   if (savedInfo['rememberMe']) {
    //     _usernameController.text = savedInfo['username'];
    //   } else {
    //     _usernameController.text = 'employee';
    //   }
    //   _passwordController.text = 'employee';

    //   _rememberMe = savedInfo['rememberMe'];
    // });
  }

  void _handleLogin() {
    setState(() {
      _fieldErrors.clear();
    });
    if (_formKey.currentState?.validate() == true) {
      context.read<AuthenticationBloc>().add(
            LoginSubmitted(
              username: _usernameController.text.trim(),
              password: _passwordController.text,
              rememberMe: _rememberMe,
            ),
          );
    }
  }

  void _handleRememberMeChanged(bool value) {
    setState(() {
      _rememberMe = value;
    });
    widget.onRememberMeChanged?.call(_rememberMe);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is AuthenticationFailure) {
          _fieldErrors = state.fieldErrors ?? {};
          _formKey.currentState?.validate();
          if (state.error.isNotEmpty) {
            SnackbarCore.error(state.error);
          }
        }
      },
      child: LoginContainer(
        child: FForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 20.h),
              Text(
                'Đăng Nhập',
                style: getTypoSkin().title1Medium.copyWith(
                      color: getColorSkin().title,
                    ),
              ),
              SizedBox(height: 20.h),
              CustomTextField(
                backgroundColor: getColorSkin().white,
                controller: _usernameController,
                showClearIcon: true,
                labelText: 'Tài khoản',
                maxLength: 50,
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return FTextFieldStatus(status: TFStatus.error, message: 'Vui lòng nhập tài khoản');
                  }
                  return null;
                },
              ),
              SizedBox(height: 12.h),
              CustomTextField(
                controller: _passwordController,
                backgroundColor: getColorSkin().white,
                obscureText: !passVisible,
                showClearIcon: true,
                labelText: 'Mật khẩu',
                maxLength: 50,
                suffixIcon: FFilledButton.icon(
                  onPressed: () {
                    setState(() {
                      passVisible = !passVisible;
                    });
                  },
                  child: passVisible
                      ? Icon(Icons.visibility, color: getColorSkin().subtitle)
                      : Icon(Icons.visibility_off, color: getColorSkin().subtitle),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return FTextFieldStatus(status: TFStatus.error, message: 'Vui lòng nhập mật khẩu');
                  }
                  if (_fieldErrors.containsKey('password')) {
                    return FTextFieldStatus(status: TFStatus.error, message: _fieldErrors['password']);
                  }
                  return null;
                },
              ),
              SizedBox(height: 8.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomCheckbox(
                          value: _rememberMe,
                          onChanged: _handleRememberMeChanged,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            'Lưu cho lần đăng nhập sau',
                            style: getTypoSkin().label5Regular.copyWith(
                                  color: getColorSkin().primaryText,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      final username = _usernameController.text.trim();
                      widget.onForgotPassword();
                      if (username.isNotEmpty) {
                        widget.onSwitchForm?.call(FormType.forgotPassword, username: username);
                      }
                    },
                    child: Text(
                      'Quên mật khẩu',
                      style: getTypoSkin().buttonText2Regular.copyWith(
                            color: getColorSkin().secondaryColor1,
                          ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              FFilledButton(
                  onPressed: widget.isLoading ? null : _handleLogin, isMaxWith: true, child: const Text('Đăng nhập')),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
