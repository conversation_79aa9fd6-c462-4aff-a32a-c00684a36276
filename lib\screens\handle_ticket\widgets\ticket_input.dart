import 'dart:developer' as developer;

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/sign_print/sign_print_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/screens/ticket_other_action/consultation_opinion_screen.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/approve_tab.dart';
import 'package:eapprove/widgets/custom_expanded_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:shimmer/shimmer.dart';

import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/common/form_v2/form_builder_v2.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_content_response_model.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';

class TicketInputBlock extends StatefulWidget {
  final String title;
  final dynamic ticket;
  const TicketInputBlock({
    super.key,
    required this.title,
    required this.ticket,
  });

  @override
  State<TicketInputBlock> createState() => _TicketInputBlockState();
}

class _TicketInputBlockState extends State<TicketInputBlock> {
  final stateManager = FormStateManager();
  bool _isFormInitialized = false;
  final bool _isSigned = false; // Đã ký duyệt
  bool _isDone = false; // Đã kiểm duyệt (Block hết)

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
      buildWhen: (previous, current) {
        return current is ThongTinDauVaoLoaded ||
            current is TicketProcessDetailLoading ||
            current is TicketProcessDetailError;
      },
      builder: (context, ticketState) {
        return BlocBuilder<FormBloc, FormInfoState>(
          builder: (context, formState) {
            final bool isLoading = ticketState is TicketProcessDetailLoading || formState.isLoading;
            FormResponse? formResponse = formState.formResponse;
            ThongTinDauVaoResponseModel? thongTinDauVao =
                ticketState is ThongTinDauVaoLoaded ? ticketState.thongTinDauVao : null;

            // Initialize form when data becomes available
            if (!isLoading && formResponse != null && thongTinDauVao != null && !_isFormInitialized) {
              _initializeForm(formResponse, thongTinDauVao);
            }

            return _buildExpandableWidget(
                isLoading: isLoading,
                formResponse: formResponse,
                thongTinDauVao: thongTinDauVao,
                hasError: formState.hasError || ticketState is TicketProcessDetailError,
                ticket: widget.ticket);
          },
        );
      },
    );
  }

  void _initializeForm(FormResponse formResponse, ThongTinDauVaoResponseModel thongTinDauVao) {
    final formValues = <String, dynamic>{};

    for (var field in formResponse.data.template.form) {
      for (var ticketField in thongTinDauVao.data.listVariables) {
        if (field.name == ticketField.name) {
          formValues[field.name ?? ''] = ticketField.value;
          break;
        }
      }
    }

    stateManager.setAllFields(formValues);
    stateManager.setWidgets(formResponse.data.template.form);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isFormInitialized = true;
        });
      }
    });
  }

  void _navigateToServicePageOnTablet(BuildContext context) {
    try {
      Navigator.of(context, rootNavigator: true).popUntil((route) => route.isFirst);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          _triggerNavigateToServiceTab(context);
        }
      });

      developer.log('Navigated to service page on tablet after approval success');
    } catch (e) {
      developer.log('Error navigating to service page: $e');
    }
  }

  void _triggerNavigateToServiceTab(BuildContext context) {
    try {
      // Sử dụng BottomNavBloc để trigger navigation trên tablet
      context.read<BottomNavBloc>().add(const NavigateToTabletTab(0));
      developer.log('Triggered navigate to service tab via BottomNavBloc');
    } catch (e) {
      developer.log('Error triggering navigate to service tab: $e');
    }
  }

  Widget _buildExpandableWidget({
    required bool isLoading,
    FormResponse? formResponse,
    ThongTinDauVaoResponseModel? thongTinDauVao,
    bool hasError = false,
    dynamic ticket,
  }) {
    return CustomExpandedList<String>(
      expandedSvgIconPath: StringImage.ic_arrow_up,
      collapsedSvgIconPath: StringImage.ic_arrow_right,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      title: widget.title,
      cardColor: getColorSkin().white,
      isBuildLeading: true,
      svgIconPath: StringImage.ic_ticket_info_process,
      iconWidth: 24.w,
      iconHeight: 24.h,
      isExpanded: true,
      titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
      ticket: widget.ticket,
      isInputBlock: true,
      onReturnPressed: () {},
      onAdditionalRequestPressed: () {
        final myPycBloc = BlocProvider.of<PycBloc>(context);
        final ticketProcessDetailBloc = BlocProvider.of<TicketProcessDetailBloc>(context);
        final consultationOpinionBloc = BlocProvider.of<ConsultationOpinionBloc>(context);
        showDialog(
          context: context,
          barrierDismissible: true,
          builder: (dialogContext) => MultiBlocProvider(
            providers: [
              BlocProvider.value(
                value: myPycBloc,
              ),
              BlocProvider.value(
                value: ticketProcessDetailBloc,
              ),
              BlocProvider.value(
                value: consultationOpinionBloc,
              ),
            ],
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              child: Expanded(
                child: ConsultationOpinionScreen(
                  ticketId: TicketUtils.getCorrectTicketId(ticket),
                  ticketProcId: TicketUtils.getProcInstId(ticket),
                  isAdditionalContent: true,
                ),
              ),
            ),
          ),
        );
      },
      onApprovePressed: () {
        final isInPerformPhase = ticket is ProcContent;
        final dropdownBloc = BlocProvider.of<DropdownBloc>(context);
        final formBloc = BlocProvider.of<FormBloc>(context);
        final ticketProcessDetailBloc = BlocProvider.of<TicketProcessDetailBloc>(context);
        final bottomNavBloc = BlocProvider.of<BottomNavBloc>(context);
        final userListBloc = BlocProvider.of<UserBloc>(context);
        final chartBloc = BlocProvider.of<ChartBloc>(context);
        final signPrintBloc = BlocProvider.of<SignPrintBloc>(context);
        showDialog(
          context: context,
          barrierDismissible: true,
          builder: (context) => MultiBlocProvider(
            providers: [
              BlocProvider.value(
                value: dropdownBloc,
              ),
              BlocProvider.value(
                value: formBloc,
              ),
              BlocProvider.value(
                value: ticketProcessDetailBloc,
              ),
              BlocProvider.value(
                value: bottomNavBloc,
              ),
              BlocProvider.value(
                value: userListBloc,
              ),
              BlocProvider.value(
                value: chartBloc,
              ),
              BlocProvider.value(
                value: signPrintBloc,
              ),
            ],
            child: AlertDialog(
              backgroundColor: getColorSkin().white,
              contentPadding: EdgeInsets.all(16.w),
              insetPadding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 60.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              title: Stack(
                children: [
                  Center(
                    child: Text(
                      isInPerformPhase ? 'Thực hiện' : 'Phê duyệt',
                      style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
                    ),
                  ),
                  Positioned(
                    right: -12.w,
                    top: -12.h,
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(Icons.close, color: getColorSkin().ink1),
                    ),
                  ),
                ],
              ),
              content: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.6,
                child: Material(
                  color: Colors.transparent,
                  child: ApproveTab(
                    isDone: _isDone,
                    isSigned: _isSigned,
                    isInPerformPhase: isInPerformPhase,
                    ticket: widget.ticket,
                    isNotForHandleModal: true,
                    onApproveSuccess: () {
                      Navigator.of(context).pop();
                      if (DeviceUtils.isTablet && context.mounted) {
                        _navigateToServicePageOnTablet(context);
                      }
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
      child: _buildContent(
        isLoading: isLoading,
        formResponse: formResponse,
        thongTinDauVao: thongTinDauVao,
        hasError: hasError,
      ),
    );
  }

  Widget _buildContent({
    required bool isLoading,
    FormResponse? formResponse,
    ThongTinDauVaoResponseModel? thongTinDauVao,
    bool hasError = false,
  }) {
    if (isLoading) {
      developer.log('TicketInputBlock: Showing loading state');
      return _buildShimmerLoading();
    }

    if (hasError) {
      developer.log('TicketInputBlock: Error state');
      return _buildErrorWidget('Đã xảy ra lỗi khi tải dữ liệu');
    }

    if (formResponse == null || thongTinDauVao == null) {
      developer.log('TicketInputBlock: Missing formResponse or thongTinDauVaoResponseModel, hiding widget');
      return const SizedBox.shrink(); // Return empty widget to hide content
    }

    developer.log('TicketInputBlock: Rendering form content');
    return _buildFormContent(formResponse);
  }

  Widget _buildFormContent(FormResponse formResponse) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      child: FormBuilderV2(
        widgets: formResponse.data.template.form,
        isReadOnly: true,
        stateManager: stateManager,
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Container(
      padding: EdgeInsets.all(16.w),
      alignment: Alignment.center,
      child: Text(message, style: TextStyle(color: Colors.red)),
    );
  }

  Widget _buildShimmerLoading() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: List.generate(
            7, // Adjust number of form fields to simulate
            (index) => _buildShimmerFormField(index),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerFormField(int index) {
    // Different types of form fields based on index
    // Simulating different form field styles
    switch (index % 4) {
      case 0:
        // Text input field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 100.w + (index * 20).w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Text field
              Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
              ),
            ],
          ),
        );
      case 1:
        // Dropdown field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 120.w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Dropdown
              Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(child: SizedBox()),
                    Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: Container(
                        width: 16.w,
                        height: 16.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case 2:
        // Date picker field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 80.w + (index * 10).w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Date field
              Container(
                height: 40.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(child: SizedBox()),
                    Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: Container(
                        width: 20.w,
                        height: 20.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case 3:
        // Textarea / multiline field
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Container(
                width: 150.w,
                height: 16.h,
                margin: EdgeInsets.only(bottom: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              // Text area
              Container(
                height: 80.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
              ),
            ],
          ),
        );
      default:
        return SizedBox();
    }
  }
}
