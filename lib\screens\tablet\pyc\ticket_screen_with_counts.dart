import 'dart:async';
import 'dart:developer';
import 'dart:developer' as developer;

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/ticket_history_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/work_flow_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/work_flow_state.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_event.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/screens/handle_ticket/handle_ticket_screen.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_info_dialog.dart';
import 'package:eapprove/screens/handle_ticket/widgets/ticket_share.dart';
import 'package:eapprove/screens/ticket_other_action/approval_list_screen.dart';
import 'package:eapprove/screens/ticket_other_action/consultation_opinion_screen.dart';
import 'package:eapprove/screens/ticket_other_action/request_relation_ticket_history_screen.dart';
import 'package:eapprove/screens/ticket_screen/search_pyc_screen.dart';
import 'package:eapprove/screens/ticket_screen/widgets/filter_pyc_screen.dart';
import 'package:eapprove/screens/ticket_screen/widgets/tab_content.dart';
import 'package:eapprove/screens/ticket_screen/widgets/ticket_model.dart';
import 'package:eapprove/screens/payment_step/widgets/tabcontent/cancel_tab.dart';
import 'package:eapprove/screens/payment_step/widgets/handle_modal.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_sdk/widgets/form.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';

class TicketScreenWithCounts extends StatefulWidget {
  final TicketScreenModel screenModel;
  final int initialTabIndex;
  final int filterIndex;

  const TicketScreenWithCounts({
    super.key,
    required this.screenModel,
    this.initialTabIndex = 0,
    required this.filterIndex,
  });

  @override
  State<TicketScreenWithCounts> createState() => _TicketScreenWithCountsState();
}

List<CustomPopupItem> getOtherActionItems() {
  return [
    CustomPopupItem(title: 'Tạo', code: 'CREATE'),
    CustomPopupItem(title: 'Nhân bản', code: 'CLONE'),
    CustomPopupItem(title: 'Chia sẻ', code: 'SHARE'),
    CustomPopupItem(title: 'Thông tin chung', code: 'GENERAL_INFO'),
    CustomPopupItem(title: 'Thông tin phiếu', code: 'TICKET_INFO'),
    CustomPopupItem(title: 'Thông tin chi tiết bước', code: 'STEP_DETAIL_INFO'),
    CustomPopupItem(title: 'Thông tin đầu vào', code: 'INPUT_INFO'),
    CustomPopupItem(title: 'Lịch sử phiếu yêu cầu liên quan', code: 'RELATED_TICKET_HISTORY'),
  ];
}

class _TicketScreenWithCountsState extends State<TicketScreenWithCounts> {
  String? _selectedFilterLabel;
  int _currentTabIndex = 0;
  Map<String, dynamic> _currentFilters = {};
  bool _useSearchFilterResponse = false;
  bool _isFilterApplied = false;
  bool _forceReload = false;
  String _searchKeyword = '';
  bool _isPanelExpanded = false;
  dynamic _selectedTicket;
  ThongTinChungModel? _ticketData;

  HandleTicketScreen? _cachedHandleTicketScreen;
  dynamic _cachedTicketId;
  bool isSelectedWorkflow = false;
  bool _isPdfViewShowing = false;
  bool _showWorkflowInTicket = false;

  // Cancel functionality variables
  final _cancelForm = GlobalKey<FFormState>();
  final TextEditingController _reasonCancel = TextEditingController();
  final List<PlatformFile> _pickedCancelFile = [];
  bool _isCancelLoading = false;

  @override
  void initState() {
    super.initState();
    _currentTabIndex = widget.initialTabIndex;
    final statusCode = _getStatusCodeByFilterIndex(widget.filterIndex, widget.screenModel.tabTitles[_currentTabIndex]);
    context.read<PycBloc>().add(FetchStatusTicket(
          StatusTicketRequest(
            code: statusCode,
            type: "system",
            page: 1,
            limit: 99999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));
    final type = _getFilterType(widget.filterIndex);

    if (type != null) {
      context.read<PycBloc>().add(
            FetchFilter(FilterRequestModel(type: type)),
          );
    }

    // Initialize TicketDialogActionBloc
    context.read<TicketDialogActionBloc>().add(TicketDialogActionInitialEvent());
  }

  @override
  void dispose() {
    _cachedHandleTicketScreen = null;
    _cachedTicketId = null;
    _reasonCancel.dispose();
    super.dispose();
  }

  String _getStatusCodeByFilterIndex(int filterIndex, String tabTitle) {
    return 'STATUS_TICKET';
  }

  void _toggleSearch() async {
    final currentBloc = BlocProvider.of<PycBloc>(context);
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: currentBloc,
          child: PycSearchScreen(
            filterIndex: widget.filterIndex,
            tabIndex: _currentTabIndex,
          ),
        ),
      ),
    );

    if (result != null && result is Map && result['search'] is String) {
      setState(() {
        _searchKeyword = result['search'];
        _currentFilters = {'search': _searchKeyword};
        _useSearchFilterResponse = widget.filterIndex == 0;
        _forceReload = true;
        _isPanelExpanded = false;
        _selectedTicket = null;
        _cachedHandleTicketScreen = null;
        _cachedTicketId = null;
      });
      final box = Hive.box('authentication');
      final username = box.get('username', defaultValue: '');

      switch (widget.filterIndex) {
        case 0:
          currentBloc.add(FetchMyPycCount(MyPycCountRequestModel(false, _searchKeyword)));
          break;
        case 1:
          currentBloc.add(FetchProcAppCount(ProcAppCountRequestModel(
            type: "EXECUTION",
            filterChangeAssignee: false,
            search: _searchKeyword,
          )));
          break;
        case 2:
          currentBloc.add(FetchProcAppCount(ProcAppCountRequestModel(
            type: "APPROVAL",
            filterChangeAssignee: false,
            search: _searchKeyword,
          )));
          break;
        case 3:
          currentBloc.add(FetchAssisPycCount(AssisPycCountRequestModel(
            assistantEmail: username,
            searchKey: _searchKeyword,
          )));
          break;
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _forceReload = false;
        });
      });
    } else if (result == true) {
      _reloadCurrentTab();
    }
  }

  String? _getFilterType(int index) {
    switch (index) {
      case 0:
        return 'ticket';
      case 1:
        return 'execution';
      case 2:
        return 'approval';
      case 3:
        return 'assistant';
      default:
        return null;
    }
  }

  void _togglePanelExpansion() {
    if (_selectedTicket != null) {
      setState(() {
        _isPanelExpanded = !_isPanelExpanded;
      });
    }
  }

  void _handleFilter(BuildContext context, int filterIndex) {
    final filterTabIndex = getFilterTabIndex(widget.screenModel.tabTitles);
    final isFilterTab = _currentTabIndex == filterTabIndex;
    FilterPycBottomSheet.show(
      context: context,
      filterIndex: widget.filterIndex,
      isFilterTab: isFilterTab,
      initialFilters: _currentFilters,
      onApplyFilter: (filters) {
        final filterTabIndex = getFilterTabIndex(widget.screenModel.tabTitles);
        final silent = (filters.remove('silentClose') as bool?) ?? false;
        setState(() {
          _currentFilters = filters;
          _useSearchFilterResponse = filters.keys.length > 1;
          _isFilterApplied = !silent;
          if (!silent) {
            _isFilterApplied = true;
          }
          log('silent? $silent, keys=${filters.keys}');
          if (filterTabIndex != -1) {
            _currentTabIndex = filterTabIndex;
          }
          _selectedTicket = null;
          _isPanelExpanded = false;
          _cachedHandleTicketScreen = null;
          _cachedTicketId = null;
        });
        final statusCode =
            _getStatusCodeByFilterIndex(widget.filterIndex, widget.screenModel.tabTitles[_currentTabIndex]);
        context.read<PycBloc>().add(FetchStatusTicket(
              StatusTicketRequest(
                code: statusCode,
                type: "system",
                page: 1,
                limit: 99999,
                search: "",
                sortBy: "id",
                sortType: "DESC",
                chartId: "",
                status: ["active"],
              ),
            ));
      },
      onResetFilter: () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _isFilterApplied = false;
            _selectedTicket = null;
            _isPanelExpanded = false;
            _cachedHandleTicketScreen = null;
            _cachedTicketId = null;
          });
        });
      },
    );
  }

  void _reloadCurrentTab() {
    setState(() {
      _forceReload = true;
      _useSearchFilterResponse = _searchKeyword.isNotEmpty;
      if (_searchKeyword.isEmpty && !_useSearchFilterResponse) {
        _currentFilters.clear();
      }
      _isPanelExpanded = false;
      _selectedTicket = null;
      _cachedHandleTicketScreen = null;
      _cachedTicketId = null;

      final statusCode =
          _getStatusCodeByFilterIndex(widget.filterIndex, widget.screenModel.tabTitles[_currentTabIndex]);
      context.read<PycBloc>().add(FetchStatusTicket(
            StatusTicketRequest(
              code: statusCode,
              type: "system",
              page: 1,
              limit: 99999,
              search: "",
              sortBy: "id",
              sortType: "DESC",
              chartId: "",
              status: ["active"],
            ),
          ));

      final box = Hive.box('authentication');
      final username = box.get('username', defaultValue: '');
      if (_searchKeyword.isNotEmpty) {
        context.read<PycBloc>().add(FetchSearchFilterPyc(
              SearchFilterRequestModel(
                search: _searchKeyword,
                type: _getFilterType(widget.filterIndex),
                page: 1,
                limit: 1000,
              ),
            ));
      } else {
        switch (widget.filterIndex) {
          case 0:
            context.read<PycBloc>().add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
            break;
          case 1:
            context.read<PycBloc>().add(FetchProcAppCount(ProcAppCountRequestModel(
                  type: "EXECUTION",
                  filterChangeAssignee: false,
                  search: "",
                )));
            break;
          case 2:
            context.read<PycBloc>().add(FetchProcAppCount(ProcAppCountRequestModel(
                  type: "APPROVAL",
                  filterChangeAssignee: false,
                  search: "",
                )));
            break;
          case 3:
            context.read<PycBloc>().add(FetchAssisPycCount(AssisPycCountRequestModel(
                  assistantEmail: username,
                  searchKey: "",
                )));
            break;
        }
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _forceReload = false;
        if (!_useSearchFilterResponse) {
          _searchKeyword = '';
        }
      });
    });
  }

  void _handleTicketTap(dynamic ticket) async {
    log('Ticket tapped: runtimeType=${ticket.runtimeType}');

    if (_selectedTicket != null && _selectedTicket.id == ticket.id) {
      setState(() {
        _selectedTicket = null;
        _isPanelExpanded = false;
        _isPdfViewShowing = false;
        _showWorkflowInTicket = false; // Reset workflow state
      });
    } else {
      // Chỉ tạo mới HandleTicketScreen khi ticket thay đổi
      if (_cachedTicketId != ticket.id) {
        log('Creating new HandleTicketScreen for ticket ${ticket.id}');
        _cachedHandleTicketScreen = HandleTicketScreen(
          key: ValueKey('ticket-${ticket.id}'),
          ticket: ticket,
          isEmbedded: true,
          onTicketDataLoaded: (data) {
            if (mounted) {
              setState(() {
                _ticketData = data;
              });
            }
          },
          onPdfViewed: (isPdfShowing) {
            if (mounted) {
              setState(() {
                _isPdfViewShowing = isPdfShowing;
              });
            }
          },
          showWorkflow: _showWorkflowInTicket,
          onWorkflowChanged: (showWorkflow) {
            if (mounted) {
              setState(() {
                _showWorkflowInTicket = showWorkflow;
              });
            }
          },
        );
        _cachedTicketId = ticket.id;
      } else {
        log('Reusing cached HandleTicketScreen for ticket ${ticket.id}');
        _cachedHandleTicketScreen = HandleTicketScreen(
          key: ValueKey('ticket-${ticket.id}'),
          ticket: ticket,
          isEmbedded: true,
          onTicketDataLoaded: (data) {
            if (mounted) {
              setState(() {
                _ticketData = data;
              });
            }
          },
          onPdfViewed: (isPdfShowing) {
            if (mounted) {
              setState(() {
                _isPdfViewShowing = isPdfShowing;
              });
            }
          },
          showWorkflow: _showWorkflowInTicket,
          onWorkflowChanged: (showWorkflow) {
            if (mounted) {
              setState(() {
                _showWorkflowInTicket = showWorkflow;
              });
            }
          },
        );
      }

      try {
        final procDefId = ticket.ticketProcDefId ?? '';
        final procInstId = extractProcInstId(ticket);

        log('Loading workflow - procDefId: $procDefId, procInstId: $procInstId');

        context.read<WorkflowBloc>().add(
              LoadWorkflow(
                procInstId: procInstId,
                procDefId: procDefId,
                fromNodeId: '',
              ),
            );
      } catch (e) {
        log('Error loading workflow: $e');
      }

      setState(() {
        _selectedTicket = ticket;
        _isPanelExpanded = false;
        _isPdfViewShowing = false;
        _showWorkflowInTicket = false;
      });
    }
  }

  String extractProcInstId(dynamic ticket) {
    try {
      if (ticket is MyPycContent || ticket is AssisContent) {
        return (ticket as dynamic).ticketId?.toString() ?? '';
      } else if (ticket is ProcContent || ticket is ApproveContent) {
        return ticket.procInstId;
      }
    } catch (e) {
      developer.log('⚠️ Failed to extract procInstId from ticket: $e');
    }
    return '';
  }

  // Cancel functionality methods
  Future<void> _pickedCancelFileUpload() async {
    final result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result != null) {
      final newFiles = result.files.where((newFile) => !_pickedCancelFile.any((f) => f.name == newFile.name)).toList();

      setState(() {
        _pickedCancelFile.addAll(newFiles);
      });
    }
  }

  String? _getTaskDefKey(dynamic ticket) {
    if (ticket?.ticketTaskDtoList != null && ticket.ticketTaskDtoList!.isNotEmpty) {
      return ticket.ticketTaskDtoList!.first.taskDefKey;
    }
    return '';
  }

  void _handleCancel({required bool isDraft}) {
    if (_cancelForm.currentState?.validate() != true) return;

    setState(() => _isCancelLoading = true);

    if (_pickedCancelFile.isNotEmpty) {
      context.read<TicketDialogActionBloc>().add(
            UploadFileEvent(
              _pickedCancelFile,
              onSuccess: (filePaths) {
                _triggerCancelEvent(isDraft, filePaths.map((e) => e['fileName'] as String).toList());
              },
              onError: (message) {
                setState(() => _isCancelLoading = false);
                SnackbarCore.error(message);
              },
            ),
          );
    } else {
      _triggerCancelEvent(isDraft, []);
    }
  }

  void _triggerCancelEvent(bool isDraft, List<String> filePaths) {
    try {
      if (isDraft) {
        context.read<TicketDialogActionBloc>().add(
              CancelDraftEvent(
                ticketProcId: _ticketData?.ticketId ?? '',
                reason: _reasonCancel.text,
                filePath: filePaths.isNotEmpty ? filePaths.join(',') : null,
              ),
            );
      } else {
        context.read<TicketDialogActionBloc>().add(
              CancelSubmitEvent(
                ticketProcId: _ticketData?.ticketId ?? '',
                ticketId: _ticketData?.id ?? 0,
                taskDefKey: _getTaskDefKey(_selectedTicket) ?? '',
                reason: _reasonCancel.text,
                filePaths: filePaths,
              ),
            );
      }
    } catch (e) {
      setState(() => _isCancelLoading = false);
      SnackbarCore.error('Đã xảy ra lỗi: ${e.toString()}');
    }
  }

  void _showCancelDialog() {
    if (_selectedTicket == null || _ticketData == null) return;

    // Reset form state
    _reasonCancel.clear();
    _pickedCancelFile.clear();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<TicketDialogActionBloc>(),
        child: BlocListener<TicketDialogActionBloc, TicketDialogActionState>(
          listener: (context, state) {
            if (state is DraftCancelTicketLoaded || state is CancelTicketLoaded) {
              String message = '';
              if (state is DraftCancelTicketLoaded) {
                message = state.message;
              } else if (state is CancelTicketLoaded) {
                message = state.message;
              }

              setState(() => _isCancelLoading = false);
              Navigator.of(dialogContext).pop();
              SnackbarCore.success(message);

              // Reload current tab after successful cancel
              _reloadCurrentTab();
            }

            if (state is DraftCancelTicketError || state is CancelTicketError) {
              final errorMessage = (state as dynamic).message;
              setState(() => _isCancelLoading = false);
              SnackbarCore.error('Lỗi: $errorMessage');
            }
          },
          child: AlertDialog(
            backgroundColor: getColorSkin().white,
            title: Text(
              'Hủy phiếu',
              style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
            ),
            content: SizedBox(
              width: 400.w,
              child: CancelTab(
                formKey: _cancelForm,
                reasonCancel: _reasonCancel,
                pickedCancelFile: _pickedCancelFile,
                onPick: _pickedCancelFileUpload,
                onRemove: (file) => setState(() => _pickedCancelFile.remove(file)),
                onSaveDraft: () => _handleCancel(isDraft: true),
                onCancel: () => _handleCancel(isDraft: false),
                hideButtonsOnTablet: false,
              ),
            ),
          ),
        ),
      ),
    );
  }

  int getFilterTabIndex(List<String> tabTitles) {
    return tabTitles.indexWhere((title) {
      final normalized = title.trim().toLowerCase();
      return normalized == 'bộ lọc' || normalized == 'lọc' || normalized == 'filter';
    });
  }

  int? getCountForTab(PycState state, int filterIndex, String tabTitle) {
    final useFilteredCount = _searchKeyword.isNotEmpty;

    switch (filterIndex) {
      case 0:
        return getMyPycCountForTab(
          useFilteredCount ? state.myPycCountFiltered?.data : state.myPycCountResponseModel?.data,
          tabTitle,
        );
      case 1:
        return getExecutionCountForTab(
          useFilteredCount ? state.procCountFiltered?.data : state.procCountResponseModel?.data,
          tabTitle,
        );
      case 2:
        return getApprovalCountForTab(
          useFilteredCount ? state.appCountFiltered?.data : state.appCountResponseModel?.data,
          tabTitle,
        );
      case 3:
        final assistCountList =
            useFilteredCount ? state.assisPycCountFiltered?.data : state.assisPycCountResponseModel?.data;
        if (assistCountList == null || assistCountList.isEmpty) return null;

        String tabKey = _getAssistTabKey(tabTitle);

        for (var countModel in assistCountList) {
          if (countModel.tab == tabKey) {
            return countModel.total;
          }
        }
        return null;
      default:
        return null;
    }
  }

  int? getMyPycCountForTab(MyPycCountDataModel? countData, String tabTitle) {
    if (countData == null) return null;

    final lowercaseTitle = tabTitle.trim().toLowerCase();
    switch (lowercaseTitle) {
      case 'đang phê duyệt':
        return countData.ongoing;
      case 'hoàn thành':
        return countData.completed;
      case 'trả về/thu hồi':
        return countData.recalled;
      case 'hủy':
        return countData.cancel;
      case 'nháp':
        return countData.draft;
      case 'được chia sẻ/theo dõi':
        return countData.shared;
      case 'đã chia sẻ':
        return countData.share;
      case 'bộ lọc':
        final state = context.read<PycBloc>().state;
        final filterType = _getFilterType(widget.filterIndex);
        return state.filterList.where((item) => item.type == filterType && item.status == "pin").length;
      default:
        return null;
    }
  }

  int? getExecutionCountForTab(ProcAppCountDataModel? countData, String tabTitle) {
    if (countData == null) return null;

    final lowercaseTitle = tabTitle.trim().toLowerCase();
    switch (lowercaseTitle) {
      case 'xử lý':
        return countData.ongoing;
      case 'hoàn thành':
        return countData.completed;
      case 'trả về/thu hồi':
        return countData.recalled;
      case 'hủy':
        return countData.cancel;
      case 'bộ lọc':
        final state = context.read<PycBloc>().state;
        final filterType = _getFilterType(widget.filterIndex);
        return state.filterList.where((item) => item.type == filterType && item.status == "pin").length;
      default:
        return null;
    }
  }

  int? getApprovalCountForTab(ProcAppCountDataModel? countData, String tabTitle) {
    if (countData == null) return null;

    final lowercaseTitle = tabTitle.trim().toLowerCase();
    switch (lowercaseTitle) {
      case 'đang phê duyệt':
      case 'đang chờ phê duyệt':
        return countData.ongoing;
      case 'đã phê duyệt':
        return countData.completed;
      case 'trả về/thu hồi':
        return countData.recalled;
      case 'hủy':
        return countData.cancel;
      case 'bộ lọc':
        final state = context.read<PycBloc>().state;
        final filterType = _getFilterType(widget.filterIndex);
        return state.filterList.where((item) => item.type == filterType && item.status == "pin").length;
      default:
        return null;
    }
  }

  String _getAssistTabKey(String tabTitle) {
    final lowercaseTitle = tabTitle.trim().toLowerCase();
    switch (lowercaseTitle) {
      case 'xử lý':
        return 'PROCESSING';
      case 'hoàn thành':
        return 'COMPLETE';
      case 'hủy':
        return 'CANCEL';
      case 'theo dõi':
        return 'SHARED';
      case 'bộ lọc':
        return 'FILTER';
      default:
        return '';
    }
  }

  // String? _getTaskDefKey(dynamic ticket) {
  //   if (ticket == null) return null;

  //   if (ticket.ticketTaskDtoList != null && ticket.ticketTaskDtoList!.isNotEmpty) {
  //     return ticket.ticketTaskDtoList!.first.taskDefKey;
  //   }
  //   return null;
  // }

  void _handleTicketAction(BuildContext context, String action, dynamic ticket, ThongTinChungModel? data) {
    if (data == null) {
      final state = context.read<TicketProcessDetailBloc>().state;

      if (state is TicketProcessDetailByIdLoaded) {
        data = state.thongTinChungModel;

        if (data != null) {
          setState(() {
            _ticketData = data;
          });
        }
      }
    }

    if (ticket != null) {
      final ticketId = ticket.id?.toString() ?? '';

      switch (action) {
        case 'GENERAL_INFO':
          showGeneralInfoDialog(context, ticketId);
          break;
        case 'STEP_DETAIL_INFO':
          showStepDetailInfoDialog(context, ticketId);
          break;
        case 'SHARE':
          showDialog(
            context: context,
            builder: (dialogContext) => BlocProvider.value(
              value: BlocProvider.of<TicketProcessDetailBloc>(context),
              child: Dialog(
                backgroundColor: Colors.transparent,
                insetPadding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.8,
                    child: TicketShareScreen(id: data!.ticketId),
                  ),
                ),
              ),
            ),
          );
        case 'RELATED_TICKET_HISTORY':
          final dynamic ticketId = TicketUtils.getCorrectTicketId(ticket);
          final dynamic ticketProcId = TicketUtils.getTicketId(ticket);
          showDialog(
            context: context,
            builder: (dialogContext) => BlocProvider.value(
              value: BlocProvider.of<TicketHistoryBloc>(context),
              child: Dialog(
                backgroundColor: Colors.transparent,
                insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                child: Expanded(
                  child: RequestRelationTicketHistoryScreen(ticketId: ticketId.toString(), ticketProcId: ticketProcId),
                ),
              ),
            ),
          );
          break;
        default:
          log('Unhandled action: $action');
      }
    }
  }

  void showGeneralInfoDialog(BuildContext context, String ticketId) {
    showDialog(
      context: context,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
        ],
        child: InfoDialog(
          title: 'Thông tin chung',
          code: 'GENERAL_INFO',
          ticketId: ticketId,
        ),
      ),
    );
  }

  void showStepDetailInfoDialog(BuildContext context, String ticketId) {
    showDialog(
      context: context,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: BlocProvider.of<TicketProcessDetailBloc>(context),
          ),
          BlocProvider.value(
            value: BlocProvider.of<PycBloc>(context),
          ),
        ],
        child: InfoDialog(
          title: 'Thông tin chi tiết bước',
          code: 'STEP_DETAIL_INFO',
          ticketId: ticketId,
        ),
      ),
    );
  }

  Widget _buildMainAppBar() {
    return AppBar(
      automaticallyImplyLeading: false,
      elevation: 0,
      scrolledUnderElevation: 0,
      backgroundColor: getColorSkin().transparent,
      title: Text(
        widget.screenModel.title,
        style: getTypoSkin().medium20.copyWith(color: getColorSkin().ink1),
      ),
      actionsPadding: EdgeInsets.only(right: 16.w),
      actions: [
        Transform.translate(
          offset: Offset(20.w, 0),
          child: IconButton(
              iconSize: 24.w.h,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              icon: SvgPicture.asset(StringImage.ic_search),
              onPressed: _toggleSearch),
        ),
        Transform.translate(
          offset: Offset(10.w, 0),
          child: IconButton(
            icon: SvgPicture.asset(
              (getFilterTabIndex(widget.screenModel.tabTitles) == _currentTabIndex && _isFilterApplied)
                  ? StringImage.ic_filter
                  : StringImage.ic_unfilter,
            ),
            onPressed: () => _handleFilter(context, widget.filterIndex),
          ),
        ),
        if (_selectedTicket != null)
          IconButton(
            highlightColor: Colors.transparent,
            icon: SvgPicture.asset(StringImage.menu_fold),
            onPressed: _togglePanelExpansion,
          ),
      ],
    );
  }

  Widget _buildDetailAppBar() {
    return AppBar(
      elevation: 0,
      scrolledUnderElevation: 0,
      backgroundColor: Colors.transparent,
      automaticallyImplyLeading: false,
      leading: _isPanelExpanded
          ? IconButton(
              highlightColor: Colors.transparent,
              icon: SvgPicture.asset(StringImage.menu_fold),
              onPressed: () {
                setState(() {
                  if (isSelectedWorkflow) {
                    isSelectedWorkflow = false;
                    _showWorkflowInTicket = false;
                    _isPanelExpanded = false;

                    if (_selectedTicket != null) {
                      _cachedHandleTicketScreen = HandleTicketScreen(
                        key: ValueKey('ticket-${_selectedTicket.id}-no-workflow'),
                        ticket: _selectedTicket,
                        isEmbedded: true,
                        onTicketDataLoaded: (data) {
                          if (mounted) {
                            setState(() {
                              _ticketData = data;
                            });
                          }
                        },
                        onPdfViewed: (isPdfShowing) {
                          if (mounted) {
                            setState(() {
                              _isPdfViewShowing = isPdfShowing;
                            });
                          }
                        },
                        showWorkflow: false,
                      );
                    }
                  } else {
                    _isPanelExpanded = !_isPanelExpanded;
                  }
                });
              },
            )
          : null,
      title: Text(
        "Thông tin chi tiết phiếu",
        style: getTypoSkin().medium20.copyWith(color: getColorSkin().ink1),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: SvgPicture.asset(StringImage.ic_close_2),
          onPressed: () {
            setState(() {
              _selectedTicket = null;
              _isPanelExpanded = false;
              _isPdfViewShowing = false;
              isSelectedWorkflow = false;
              _showWorkflowInTicket = false; // Reset workflow state
            });
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final dynamic ticketProcId = TicketUtils.getProcInstId(_selectedTicket);
    final dynamic ticketId = TicketUtils.getCorrectTicketId(_selectedTicket);
    final myPycBloc = BlocProvider.of<PycBloc>(context);
    final ticketProcessDetailBloc = BlocProvider.of<TicketProcessDetailBloc>(context);
    final consultationOpinionBloc = BlocProvider.of<ConsultationOpinionBloc>(context);
    final isSuccess = _selectedTicket?.ticketStatus == 'COMPLETED' ||
        _selectedTicket?.ticketStatus == 'COMPLETE' ||
        _selectedTicket?.ticketStatus == 'CLOSED';
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        InkWell(
          onTap: () {
            setState(() {
              isSelectedWorkflow = !isSelectedWorkflow;
              _showWorkflowInTicket = isSelectedWorkflow;

              if (isSelectedWorkflow) {
                _isPanelExpanded = true;
              }

              if (_selectedTicket != null) {
                _cachedHandleTicketScreen = HandleTicketScreen(
                  key: ValueKey('ticket-${_selectedTicket.id}-workflow-$isSelectedWorkflow'),
                  ticket: _selectedTicket,
                  isEmbedded: true,
                  onTicketDataLoaded: (data) {
                    if (mounted) {
                      setState(() {
                        _ticketData = data;
                      });
                    }
                  },
                  onPdfViewed: (isPdfShowing) {
                    if (mounted) {
                      setState(() {
                        _isPdfViewShowing = isPdfShowing;
                      });
                    }
                  },
                  showWorkflow: _showWorkflowInTicket,
                  onWorkflowChanged: (showWorkflow) {
                    if (mounted) {
                      setState(() {
                        _showWorkflowInTicket = showWorkflow;
                      });
                    }
                  },
                );
              }
            });
          },
          child: Container(
            height: 24.h,
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            decoration: BoxDecoration(
              color: !isSelectedWorkflow ? getColorSkin().white : getColorSkin().primaryBlue,
              border: !isSelectedWorkflow ? Border.all(color: getColorSkin().primaryBlue) : null,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              height: 24.h,
              child: Text(
                'Luồng nghiệp vụ',
                style: getTypoSkin().regular12.copyWith(
                      color: !isSelectedWorkflow ? getColorSkin().primaryBlue : getColorSkin().white,
                      height: 1.0,
                    ),
              ),
            ),
          ),
        ),

        SizedBox(width: 3.w),

        SizedBox(
          height: 24.h,
          child: ElevatedButton(
            onPressed: () {
              if (_selectedTicket != null) {
                showDialog(
                  context: context,
                  barrierDismissible: true,
                  builder: (dialogContext) => MultiBlocProvider(
                    providers: [
                      BlocProvider.value(
                        value: ticketProcessDetailBloc,
                      ),
                      BlocProvider.value(
                        value: myPycBloc,
                      ),
                    ],
                    child: Dialog(
                      backgroundColor: Colors.transparent,
                      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                      child: Expanded(
                        child: ApproverListScreen(ticketId: _selectedTicket.id.toString()),
                      ),
                    ),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
              backgroundColor: getColorSkin().white,
              shape: RoundedRectangleBorder(
                side: BorderSide(color: getColorSkin().primaryBlue),
                borderRadius: BorderRadius.circular(4.r),
              ),
              elevation: 0,
              textStyle: getTypoSkin().regular12.copyWith(height: 1.0),
            ),
            child: Text(
              'Danh sách người phê duyệt',
              style: getTypoSkin().regular12.copyWith(height: 1.0, color: getColorSkin().primaryBlue),
            ),
          ),
        ),
        SizedBox(width: 3.w),
        SizedBox(
          height: 24.h,
          child: ElevatedButton(
            onPressed: () {
              showDialog(
                context: context,
                barrierDismissible: true,
                builder: (dialogContext) => MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: myPycBloc,
                    ),
                    BlocProvider.value(
                      value: ticketProcessDetailBloc,
                    ),
                    BlocProvider.value(
                      value: consultationOpinionBloc,
                    ),
                  ],
                  child: Dialog(
                    backgroundColor: Colors.transparent,
                    insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                    child: Expanded(
                      child: ConsultationOpinionScreen(
                        ticketId: ticketId,
                        ticketProcId: ticketProcId,
                      ),
                    ),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
              backgroundColor: getColorSkin().white,
              shape: RoundedRectangleBorder(
                side: BorderSide(color: getColorSkin().primaryBlue),
                borderRadius: BorderRadius.circular(4.r),
              ),
              elevation: 0,
              textStyle: getTypoSkin().regular12.copyWith(height: 1.0),
            ),
            child: Text(
              'Ý kiến tham vấn',
              style: getTypoSkin().regular12.copyWith(height: 1.0, color: getColorSkin().primaryBlue),
            ),
          ),
        ),
        SizedBox(width: 3.w),
        if (!isSuccess)
          SizedBox(
            height: 24.h,
            child: ElevatedButton(
              onPressed: () {
                if (_selectedTicket != null && _ticketData != null) {
                  _showCancelDialog();
                }
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
                backgroundColor: getColorSkin().red,
                foregroundColor: getColorSkin().white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4.r),
                ),
                elevation: 0,
                textStyle: getTypoSkin().regular12.copyWith(height: 1.0),
              ),
              child: Text(
                'Hủy phiếu',
                style: getTypoSkin().regular12.copyWith(height: 1.0),
              ),
            ),
          ),

        // Custom Popup Menu
        Container(
          height: 24.h,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: getColorSkin().white,
            border: Border.all(color: getColorSkin().primaryBlue),
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: CustomPopupMenu(
              items: getOtherActionItems(),
              child: Container(
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                height: 24.h,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Hành động khác ',
                      style: getTypoSkin().regular12.copyWith(
                            color: getColorSkin().primaryBlue,
                            height: 1.0,
                          ),
                    ),
                    SizedBox(width: 4.w),
                    SizedBox(
                      width: 12.w,
                      height: 12.h,
                      child: SvgPicture.asset(
                        StringImage.ic_arrow_down,
                        width: 12.w,
                        height: 12.h,
                        colorFilter: ColorFilter.mode(getColorSkin().primaryBlue, BlendMode.srcIn),
                      ),
                    )
                  ],
                ),
              ),
              onTap: (key) {
                if (_selectedTicket != null && _ticketData != null) {
                  _handleTicketAction(context, key, _selectedTicket, _ticketData);
                } else {
                  log('Cannot handle action: ${_selectedTicket == null ? 'ticket is null' : 'data is null'}');
                }
              }),
        ),
      ],
    );
  }

  Widget _buildTabBar(PycState state) {
    return Container(
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: SingleChildScrollView(
        padding: EdgeInsets.only(left: 12.w, bottom: 8.h),
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: List.generate(widget.screenModel.tabTitles.length, (i) {
            final isSelected = i == _currentTabIndex;
            final title = widget.screenModel.tabTitles[i];
            final count = getCountForTab(state, widget.filterIndex, title);

            return TabWithCount(
              title: title,
              count: count,
              isSelected: isSelected,
              onTap: () {
                if (_currentTabIndex != i) {
                  setState(() {
                    _currentTabIndex = i;
                    if (i != getFilterTabIndex(widget.screenModel.tabTitles) && _searchKeyword.isEmpty) {
                      _currentFilters.clear();
                      _useSearchFilterResponse = false;
                      _isFilterApplied = false;
                    }
                    _isPanelExpanded = false;
                  });
                }
                _reloadCurrentTab();
              },
            );
          }),
        ),
      ),
    );
  }

  Widget _buildFilterDropdown(PycState state) {
    final filterTabIndex = getFilterTabIndex(widget.screenModel.tabTitles);
    final currentType = _getFilterType(widget.filterIndex);
    final filteredList = state.filterList.where((item) => item.type == currentType).toList();

    final pinnedItem = filteredList.firstWhere(
      (item) => item.status == "pin",
      orElse: () => const FilterData(),
    );

    if (pinnedItem.id != null && _currentFilters['filterId'] == null) {
      _currentFilters['filterId'] = pinnedItem.id;
      _selectedFilterLabel = pinnedItem.name ?? '';

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    }

    final showDropdown = filterTabIndex != -1 && _currentTabIndex == filterTabIndex;

    if (!showDropdown) return SizedBox.shrink();

    final options = filteredList.map((item) {
      return SelectItem(
        label: item.name ?? 'Không tên',
        value: item.id?.toString() ?? '',
      );
    }).toList();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: CustomDropdownMenu(
        dropdownHeight: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: Colors.white,
        ),
        placeholderStyle: getTypoSkin().buttonRegular14.copyWith(color: getColorSkin().ink1),
        label: '',
        placeholder: _selectedFilterLabel ?? 'Chọn bộ lọc',
        options: options,
        onSelected: (value) {
          final selected = filteredList.firstWhere(
            (item) => item.id.toString() == value?.value,
            orElse: () => const FilterData(),
          );
          setState(() {
            _currentFilters['filterId'] = int.tryParse(value?.value ?? '');
            _selectedFilterLabel = selected.name ?? '';
            if (widget.screenModel.tabTitles[_currentTabIndex].trim().toLowerCase() == 'bộ lọc') {
              _useSearchFilterResponse = false;
            }
          });
        },
        isDisabled: options.isEmpty,
        validator: (value) => null,
      ),
    );
  }

  Widget _buildTicketList() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: _buildMainAppBar(),
        ),

        BlocBuilder<PycBloc, PycState>(
          builder: (context, state) => _buildTabBar(state),
        ),

        BlocBuilder<PycBloc, PycState>(
          builder: (context, state) => _buildFilterDropdown(state),
        ),

        // Content
        Expanded(
          child: TabContent(
            key: ValueKey('TabContent-${widget.filterIndex}-$_currentTabIndex'),
            tabTitle: widget.screenModel.tabTitles[_currentTabIndex],
            filterIndex: widget.filterIndex,
            currentFilters: _currentFilters,
            useSearchFilterResponse: _useSearchFilterResponse,
            forceReload: _forceReload,
            onTicketTap: _handleTicketTap,
            selectedTicket: _selectedTicket,
            onResetToInitialState: _resetToInitialState,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailPanel({required bool showActions}) {
    if (_selectedTicket == null) return Container();

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: _buildDetailAppBar(),
        ),
        if (showActions && !_isPdfViewShowing) _buildActionButtons(),
        Expanded(
          child: _cachedHandleTicketScreen ??
              Center(
                child: Text('lỗi'),
              ),
        ),
      ],
    );
  }

  Widget _buildSplitScreenLayout() {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Left panel (list of tickets)
          Expanded(
            flex: _selectedTicket != null ? 2 : 1,
            child: _buildTicketList(),
          ),

          // Right panel - Detail view (nếu có ticket selected)
          if (_selectedTicket != null) ...[
            VerticalDivider(
              width: 1,
              thickness: 1,
              color: getColorSkin().grey3Background,
            ),
            Expanded(
              flex: 5,
              child: _buildDetailPanel(showActions: true),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullScreenDetail() {
    return Scaffold(
      backgroundColor: getColorSkin().transparent,
      body: _buildDetailPanel(showActions: true),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      showBottomImage: false,
      showUpperImage: false,
      child: SafeArea(
        child: IndexedStack(
          index: (_selectedTicket != null && _isPanelExpanded) ? 1 : 0,
          children: [
            //split
            _buildSplitScreenLayout(),

            //full
            if (_selectedTicket != null) _buildFullScreenDetail() else Container(),
          ],
        ),
      ),
    );
  }

  void _resetToInitialState() {
    setState(() {
      _currentTabIndex = 0;
      _currentFilters.clear();
      _useSearchFilterResponse = false;
      _isFilterApplied = false;
      _forceReload = false;
      _searchKeyword = '';
      _isPanelExpanded = false;
      _selectedTicket = null;
      _cachedHandleTicketScreen = null;
      _cachedTicketId = null;
      _isPdfViewShowing = false;
      _showWorkflowInTicket = false;
      _reasonCancel.clear();
      _pickedCancelFile.clear();
      _isCancelLoading = false;
    });
    _reloadCurrentTab();
  }
}

class TabWithCount extends StatelessWidget {
  final String title;
  final int? count;
  final bool isSelected;
  final VoidCallback onTap;

  const TabWithCount({
    super.key,
    required this.title,
    this.count,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isFilterTab = title.trim().toLowerCase() == 'bộ lọc';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 4.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xffF2FBFE) : getColorSkin().grey3Background,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: getTypoSkin().buttonText2Regular.copyWith(
                    color: isSelected ? getColorSkin().secondaryColor1 : getColorSkin().subtitle,
                  ),
            ),
            if (isFilterTab || (count != null && count! > 0))
              Text(
                ' (${isFilterTab && count == null ? '0' : count.toString()})',
                style: getTypoSkin().buttonText2Regular.copyWith(
                      color: isSelected ? getColorSkin().secondaryColor1 : getColorSkin().subtitle,
                    ),
              ),
          ],
        ),
      ),
    );
  }
}

class TicketViewTablet extends StatefulWidget {
  final Key? key;
  final int filterIndex;

  const TicketViewTablet({this.key, required this.filterIndex}) : super(key: key);

  @override
  State<TicketViewTablet> createState() => _TicketViewTabletState();
}

class _TicketViewTabletState extends State<TicketViewTablet> {
  @override
  void initState() {
    super.initState();

    final box = Hive.box('authentication');
    final username = box.get('username', defaultValue: '');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      switch (widget.filterIndex) {
        case 0:
          context.read<PycBloc>().add(FetchMyPycCount(MyPycCountRequestModel(false, "")));
          break;
        case 1:
          context.read<PycBloc>().add(FetchProcAppCount(ProcAppCountRequestModel(
                type: "EXECUTION",
                filterChangeAssignee: false,
                search: "",
              )));
          break;
        case 2:
          context.read<PycBloc>().add(FetchProcAppCount(ProcAppCountRequestModel(
                type: "APPROVAL",
                filterChangeAssignee: false,
                search: "",
              )));
          break;
        case 3:
          context.read<PycBloc>().add(FetchAssisPycCount(AssisPycCountRequestModel(
                assistantEmail: username,
                searchKey: "",
              )));
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return TicketScreenWithCounts(
      screenModel: TicketScreenModel(
        title: _getScreenTitle(widget.filterIndex),
        tabTitles: _getTabTitles(widget.filterIndex),
      ),
      initialTabIndex: 0,
      filterIndex: widget.filterIndex,
    );
  }

  String _getScreenTitle(int filterIndex) {
    switch (filterIndex) {
      case 0:
        return "Phiếu yêu cầu của tôi";
      case 1:
        return "Phiếu yêu cầu cần thực hiện";
      case 2:
        return "Phiếu yêu cầu cần phê duyệt";
      case 3:
        return "Phiếu yêu cầu trợ lý";
      default:
        return "Phiếu yêu cầu";
    }
  }

  List<String> _getTabTitles(int filterIndex) {
    switch (filterIndex) {
      case 0:
        return [
          "Đang phê duyệt",
          "Hoàn thành",
          "Trả về/Thu hồi",
          "Hủy",
          "Nháp",
          "Được chia sẻ/Theo dõi",
          "Đã chia sẻ",
          "Bộ lọc"
        ];
      case 1:
        return ["Xử lý", "Hoàn thành", "Trả về/Thu hồi", "Hủy", "Bộ lọc"];
      case 2:
        return ["Đang phê duyệt", "Đã phê duyệt", "Trả về/Thu hồi", "Hủy", "Bộ lọc"];
      case 3:
        return ["Xử lý", "Hoàn thành", "Hủy", "Theo dõi", "Bộ lọc"];
      default:
        return [];
    }
  }
}

class FilterTicketRequestModel {
  final String? search;
  final int? page;
  final int? limit;
  final String? sortBy;
  final String? sortType;
  final String? type;
  final int? filterId;

  FilterTicketRequestModel({
    this.search,
    this.page,
    this.limit,
    this.sortBy,
    this.sortType,
    this.type,
    this.filterId,
  });

  Map<String, dynamic> toMap() {
    return {
      'search': search,
      'page': page,
      'limit': limit,
      'sortBy': sortBy,
      'sortType': sortType,
      'type': type,
      'filterId': filterId,
    };
  }
}

class CustomPopupItem {
  String title;
  String? icon;
  Function()? onTap;
  bool? isHidden;
  String? code;
  CustomPopupItem({required this.title, this.icon, this.onTap, this.isHidden = false, this.code});
}

class CustomPopupMenu extends StatelessWidget {
  final List<CustomPopupItem> items;
  final Widget child;
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final Function(String)? onTap;

  const CustomPopupMenu({
    super.key,
    required this.items,
    required this.child,
    this.backgroundColor = Colors.white,
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      offset: Offset(15.w, 24.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: borderRadius),
      color: backgroundColor,
      itemBuilder: (context) => items
          .map((item) => PopupMenuItem<String>(
                value: item.code,
                onTap: item.onTap,
                child: Text(item.title),
              ))
          .toList(),
      onSelected: (value) {
        if (onTap != null) {
          onTap!(value);
        }
      },
      child: child,
    );
  }
}
