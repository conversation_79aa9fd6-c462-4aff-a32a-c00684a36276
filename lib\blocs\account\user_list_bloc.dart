import 'package:eapprove/models/account/account_model.dart';
import 'package:eapprove/models/role_model.dart';
import 'package:eapprove/repositories/account_repository.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'user_list_event.dart';
import 'user_list_state.dart';

class UserListBloc extends Bloc<UserListEvent, UserListState> {
  final AccountRepository accountRepository;
  UserListBloc({required this.accountRepository}) : super(UserListInitial()) {
    on<FetchUserList>(_onFetchUserList);
    on<FetchUserInfoByToken>(_onFetchUserInfoByToken);
    on<CheckUserRoles>(_onCheckUserRoles);
    on<ResetUserList>(_onResetUserList);
  }

  Future<void> _onFetchUserInfoByToken(
    FetchUserInfoByToken event,
    Emitter<UserListState> emit,
  ) async {
    emit(UserListLoading());

    try {
      Box box = Hive.box('authentication');
      final String username = box.get('username', defaultValue: 'default_user');

      LoggerConfig.logger.d('📤 Retrieved username from local storage: $username');
      emit(UserListLoaded([], null, username: username));

      add(CheckUserRoles());
    } catch (e, stackTrace) {
      LoggerConfig.logger.e('⛔ Error retrieving username from storage: $e', error: e, stackTrace: stackTrace);

      emit(UserListLoaded([], null, username: 'default_user'));
      add(CheckUserRoles());
    }
  }

  Future<void> _onResetUserList(ResetUserList event, Emitter<UserListState> emit) async {
    emit(UserListInitial());
  }

  Future<void> _onFetchUserList(FetchUserList event, Emitter<UserListState> emit) async {
    emit(UserListLoading());
    try {
      final Data userData = await accountRepository.fetchUserData(); // Lấy toàn bộ `Data`
      final List<UserList> users = userData.userList ?? []; // Chỉ lấy `userList`
      final UserADM? userADM = userData.userADM; //Lấy `userADM` nếu có

      LoggerConfig.logger.d('📥 Fetched ${users.length} users');
      LoggerConfig.logger.d('👤 UserADM: ${userADM?.fullName ?? "Không có dữ liệu"}');

      emit(UserListLoaded(users, userADM));
    } catch (e, stackTrace) {
      LoggerConfig.logger.e('⛔ Error fetching user list: $e', error: e, stackTrace: stackTrace);
      emit(UserListError('Lỗi khi tải danh sách người dùng: $e'));
    }
  }

  Future<void> _onCheckUserRoles(
    CheckUserRoles event,
    Emitter<UserListState> emit,
  ) async {
    try {
      final RoleResponseModel roleModel = await accountRepository.getUserRoles();

      Box box = Hive.box('authentication');
      final username = box.get('username', defaultValue: '');
      final isAdmin = roleModel.data.contains('ROLE_ADMIN');

      LoggerConfig.logger.d('👤 User $username roles: ${roleModel.data}');
      LoggerConfig.logger.d('👑 Is Admin: $isAdmin');

      if (state is UserListLoaded) {
        final currentState = state as UserListLoaded;
        emit(UserListLoaded(
          currentState.users,
          currentState.userADM,
          username: currentState.username,
          roles: roleModel.data,
          isAdmin: isAdmin,
        ));
      } else {
        emit(UserListLoaded(
          [], 
          null, 
          username: username,
          roles: roleModel.data,
          isAdmin: isAdmin,
        ));
      }
    } catch (e, stackTrace) {
      LoggerConfig.logger.e('⛔ Error checking user roles: $e', error: e, stackTrace: stackTrace);
    }
  }
}
