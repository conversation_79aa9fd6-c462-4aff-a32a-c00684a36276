import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_text_form.dart';
import 'dart:developer' as developer;

class FormulaParser {
  static const String TABLE_PREFIX = '@tbl_';
  static const String MATRIX_PREFIX = '@mtx_';
  
  static bool isTableFormula(String formula) => formula.startsWith(TABLE_PREFIX);
  static bool isMatrixFormula(String formula) => formula.startsWith(MATRIX_PREFIX);
  
  static String extractTableName(String formula) {
    final parts = formula.split(':');
    return parts[0].replaceAll(TABLE_PREFIX, '');
  }
  
  static String extractMatrixName(String formula) {
    final parts = formula.split('.');
    return parts[0].replaceAll(MATRIX_PREFIX, '');
  }
}

class FormulaWidget extends StatefulWidget {
  final FormItemInfo data;
  final FormStateManager stateManager;

  const FormulaWidget({
    Key? key,
    required this.data,
    required this.stateManager,
  }) : super(key: key);

  @override
  State<FormulaWidget> createState() => _FormulaWidgetState();
}

class _FormulaWidgetState extends State<FormulaWidget> {
  String? _formulaResult;
  final controller = TextEditingController();
  List<String> _referencedFields = [];

  @override
  void initState() {
    super.initState();
    _extractReferencedFields();
    widget.stateManager.addListener(_onStateChanged);
    _calculateFormula();
  }

  @override
  void dispose() {
    controller.dispose();
    widget.stateManager.removeListener(_onStateChanged);
    super.dispose();
  }

  void _extractReferencedFields() {
    _referencedFields = [];
    if (widget.data.condition != null) {
      for (var condition in widget.data.condition!) {
        if (condition['type'] == 'condition' && condition['text'] != 'sum') {
          _referencedFields.add(condition['text'] as String);
        }
      }
    }
  }

  void _onStateChanged() {
    debugPrint('Formula widget state changed. Referenced fields: $_referencedFields',
        );

    // Nếu có bất kỳ field nào trong _referencedFields thay đổi, tính toán lại
    _calculateFormula();
  }

  @override
  void didUpdateWidget(FormulaWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      developer.log(
          'Widget data changed. Old: ${oldWidget.data.name}, New: ${widget.data.name}',
          name: 'FormulaWidget');
      _extractReferencedFields();
      _calculateFormula();
    }
  }

  /// Calculates the formula result based on the strCondition
  void _calculateFormula() {
    if (widget.data.strCondition == null) return;

    try {
      double result = 0;

      // If condition array is available, use it for calculation
      if (widget.data.condition != null && widget.data.condition!.isNotEmpty) {
        debugPrint(
            'Calculating from condition array: ${widget.data.condition}',
            );
        result = _calculateFromConditionArray();
      } else {
        // Fallback to string-based calculation
        final formula = widget.data.strCondition ?? '';
        debugPrint('Calculating from string formula: $formula',
           );
        if (formula.contains('sum(')) {
          result = _calculateSum(formula);
        } else {
          result = _calculateBasicOperation(formula);
        }
      }

      // Format the result to remove decimal places for integers
      final formattedResult = result.truncateToDouble() == result
          ? result.toInt().toString()
          : result.toString();

      developer.log('Formula result: $formattedResult', name: 'FormulaWidget');
      setState(() {
        _formulaResult = formattedResult;
        controller.text =
            Utils.formatCurrency(double.parse(_formulaResult ?? '0')) ?? '';
      });

      // Update the value in state manager
      widget.stateManager
          .setFieldValue(widget.data.name ?? '', formattedResult);
    } catch (e) {
      developer.log('Error calculating formula: $e', name: 'FormulaWidget');
      setState(() {
        _formulaResult = '0';
        controller.text = _formulaResult ?? '';
      });
    }
  }

  /// Calculates result from condition array
  double _calculateFromConditionArray() {
    if (widget.data.condition == null || widget.data.condition!.isEmpty) return 0;

    final conditions = widget.data.condition!;
    
    // Xử lý công thức trong table
    if (_isTableFormula(conditions)) {
      return _calculateTableFormula(conditions);
    }
    
    // Xử lý công thức trong matrix
    if (_isMatrixFormula(conditions)) {
      return _calculateMatrixFormula(conditions);
    }
    
    // Xử lý công thức thông thường
    return _calculateBasicFormula(conditions);
  }

  bool _isTableFormula(List<Map<String, dynamic>> conditions) {
    return conditions.isNotEmpty && 
           conditions[0]['type'] == 'condition' &&
           conditions[0]['text'].toString().endsWith(':');
  }

  bool _isMatrixFormula(List<Map<String, dynamic>> conditions) {
    return conditions.isNotEmpty &&
           conditions[0]['type'] == 'condition' &&
           conditions[0]['text'].toString().startsWith('@mtx_');
  }

  double _calculateTableFormula(List<Map<String, dynamic>> conditions) {
    final condition = conditions[0];
    final tableName = condition['text'].toString().replaceAll(':', '');
    final tableData = widget.stateManager.getFieldValue(tableName);
    debugPrint('Table data: $tableData');
    if (tableData is! List) return 0;
    
    for (var row in tableData) {
      if (row is Map) {
        return _calculateRowFormula(Map<String, dynamic>.from(row), conditions);
      }
    }
    return 0;
  }

  double _calculateMatrixFormula(List<Map<String, dynamic>> conditions) {
    final matrixName = conditions[0]['text'].toString();
    final matrixData = widget.stateManager.getFieldValue(matrixName);
    
    if (matrixData is! Map) return 0;
    
    final values = <double>[];
    matrixData.forEach((key, value) {
      if (value is Map) {
        for (var i = 1; i < conditions.length; i++) {
          final fieldName = conditions[i]['text'].toString();
          if (value.containsKey(fieldName)) {
            values.add(double.tryParse(value[fieldName]?.toString() ?? '0') ?? 0);
          }
        }
      }
    });
    
    return _calculateFunctionResult(conditions[0]['text'].toString(), values);
  }

  double _calculateFunctionResult(String functionName, List<double> values) {
    if (values.isEmpty) return 0;
    
    switch (functionName) {
      case 'sum':
        return values.reduce((a, b) => a + b);
      case 'count':
        return values.length.toDouble();
      case 'avg':
      case 'average':
        return values.reduce((a, b) => a + b) / values.length;
      case 'min':
        return values.reduce((a, b) => a < b ? a : b);
      case 'max':
        return values.reduce((a, b) => a > b ? a : b);
      case 'quotient':
        return values.length >= 2 ? (values[0] / values[1]).truncateToDouble() : 0;
      case 'mod':
        return values.length >= 2 ? values[0] % values[1] : 0;
      case 'percentage':
        return values.length >= 2 ? (values[0] / values[1]) * 100 : 0;
      default:
        return 0;
    }
  }

  /// Calculates the sum of values in a table
  double _calculateSum(String formula) {
    double result = 0;
    final startIndex = formula.indexOf('(') + 1;
    final endIndex = formula.lastIndexOf(')');

    if (startIndex > 0 && endIndex > startIndex) {
      final fieldName =
          formula.substring(startIndex, endIndex).replaceAll('@', '').trim();

      final tableData =
          widget.stateManager.getFieldValue(fieldName.split('.')[0]);
      if (tableData is List) {
        for (var row in tableData) {
          if (row is Map) {
            final value = row[fieldName.split('.')[1]]?.toString() ?? '0';
            result += double.tryParse(value) ?? 0;
          }
        }
      }
    }
    return result;
  }

  /// Calculates basic arithmetic operations (+, -, *, /)
  double _calculateBasicOperation(String formula) {
    final values = <String, double>{};
    final parts = formula.split('@');

    if (parts.length < 2) return 0;

    // Extract field values
    for (var i = 1; i < parts.length; i++) {
      final fieldName = parts[i].trim();
      final value = widget.stateManager
              .getFieldValueByName(fieldName, widget.data.parentName) ??
          '0';
      developer
          .log('_calculateBasicOperation fieldName: $fieldName, value: $value');
      values[fieldName] = double.tryParse(value) ?? 0;
    }

    // Handle subtraction
    if (formula.contains('-')) {
      final operands = formula.split('-');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        return (values[firstField] ?? 0) - (values[secondField] ?? 0);
      }
    }
    // Handle addition
    else if (formula.contains('+')) {
      final operands = formula.split('+');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        return (values[firstField] ?? 0) + (values[secondField] ?? 0);
      }
    }
    // Handle multiplication
    else if (formula.contains('*')) {
      final operands = formula.split('*');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        return (values[firstField] ?? 0) * (values[secondField] ?? 0);
      }
    }
    // Handle division
    else if (formula.contains('/')) {
      final operands = formula.split('/');
      if (operands.length == 2) {
        final firstField = operands[0].replaceAll('@', '').trim();
        final secondField = operands[1].replaceAll('@', '').trim();
        final divisor = values[secondField] ?? 0;
        return divisor != 0 ? (values[firstField] ?? 0) / divisor : 0;
      }
    }

    return 0;
  }

  double _calculateRowFormula(Map<String, dynamic> row, List<Map<String, dynamic>> conditions) {
    debugPrint('Row: $row');
    debugPrint('Conditions: $conditions');
    // Tìm cặp ngoặc đơn và tính toán phần trong ngoặc trước
    for (int i = 0; i < conditions.length; i++) {
        if (conditions[i]['text'] == '(') {
            // Tìm dấu đóng ngoặc tương ứng
            int closeIndex = i;
            int openCount = 1;
            for (int j = i + 1; j < conditions.length; j++) {
                if (conditions[j]['text'] == '(') openCount++;
                if (conditions[j]['text'] == ')') openCount--;
                if (openCount == 0) {
                    closeIndex = j;
                    break;
                }
            }
            
            // Tính toán phần trong ngoặc
            double bracketResult = _calculateRowFormula(row, conditions.sublist(i + 1, closeIndex));
            
            // Thay thế phần trong ngoặc bằng kết quả
            conditions[i] = {'text': bracketResult.toString(), 'type': 'condition'};
            conditions.removeRange(i + 1, closeIndex + 1);
        }
    }
    
    // Tính toán các phép nhân và chia trước
    for (int i = 1; i < conditions.length - 1; i += 2) {
        if (conditions[i]['text'] == '*' || conditions[i]['text'] == '/') {
            // Lấy giá trị từ row cho field
            double left = double.tryParse(row[conditions[i-1]['text']]?.toString() ?? '0') ?? 0;
            double right = double.tryParse(row[conditions[i+1]['text']]?.toString() ?? '0') ?? 0;
            double result = conditions[i]['text'] == '*' ? left * right : left / right;
            conditions[i-1] = {'text': result.toString(), 'type': 'condition'};
            conditions.removeRange(i, i + 2);
            i -= 2;
        }
    }
    
    // Tính toán các phép cộng và trừ
    double result = double.tryParse(row[conditions[0]['text']]?.toString() ?? '0') ?? 0;
    for (int i = 1; i < conditions.length; i += 2) {
        double value = double.tryParse(row[conditions[i+1]['text']]?.toString() ?? '0') ?? 0;
        if (conditions[i]['text'] == '+') {
            result += value;
        } else if (conditions[i]['text'] == '-') {
            result -= value;
        }
    }
    debugPrint('Row result: $result');
    return result;
  }

  double _calculateBasicFormula(List<Map<String, dynamic>> conditions) {
    double result = 0;
    String? currentOperator;
    
    for (var condition in conditions) {
      if (condition['type'] == 'condition') {
        final fieldName = condition['text'] as String;
        final value = double.tryParse(widget.stateManager.getFieldValueByName(fieldName, widget.data.parentName) ?? '0') ?? 0;
        
        if (currentOperator == null) {
          result = value;
        } else {
          switch (currentOperator) {
            case '+': result += value; break;
            case '-': result -= value; break;
            case '*': result *= value; break;
            case '/': if (value != 0) result /= value; break;
          }
        }
      } else if (condition['type'] == '') {
        currentOperator = condition['text'] as String;
      }
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        FormLabel(
            displayName: widget.data.displayName,
            label: widget.data.label,
            suggestText: widget.data.suggestText,
            isRequired: widget.data.validations?['required'] == true,
          ),
        Container(  
          margin: EdgeInsets.only(top: 8.h),
          child:CustomTextForm(
      edtController: controller,
      hintText: _formulaResult ?? "",
      onChanged: (value) {},
      initialValue: _formulaResult,
      filled: true,
      fillColor: getColorSkin().grey4Background,
      textInputAction: TextInputAction.done,
      onTapOutside: (_) {},
      autoFocus: false,
      showDeleteButton: false,
      enabled: false,
      keyboardType: widget.data.type == FormItemType.number ||
              widget.data.type == FormItemType.currency
          ? TextInputType.number
          : TextInputType.text,
    )
        )
      ],
    );
  }
}
