import 'package:eapprove/models/pyc/ticket_task_model.dart';

class FilterData {
  final int? id;
  final String? name;
  final String? status;
  final String? type;
  const FilterData({this.id, this.name, this.status, this.type});

  factory FilterData.fromJson(Map<String, dynamic> json) {
    return FilterData(
      id: json['id'],
      name: json['name'],
      status: json['status'],
      type: json['type'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status,
      'type': type,
    };
  }
}

class TicketContentModel {
  final String? companyCode;
  final String? ticketTitle;
  final String? startKey;
  final int? ticketCreatedTime;
  final String? procInstId;
  final String? serviceName;
  final String? procDefId;
  final List<TicketTask>? ticketTaskDtoList;
  final int? chartId;
  final String? ticketPriority;
  final String? chartNodeName;
  final String? ticketStatus;
  final String? cancelUser;
  final String? requestCode;
  final int? ticketEditTime;
  final int? id;
  final int? serviceId;
  final String? createdUser;

  TicketContentModel({
    this.companyCode,
    this.ticketTitle,
    this.startKey,
    this.ticketCreatedTime,
    this.procInstId,
    this.serviceName,
    this.procDefId,
    this.ticketTaskDtoList,
    this.chartId,
    this.ticketPriority,
    this.chartNodeName,
    this.ticketStatus,
    this.cancelUser,
    this.requestCode,
    this.ticketEditTime,
    this.id,
    this.serviceId,
    this.createdUser,
  });

  factory TicketContentModel.fromJson(Map<String, dynamic> json) {
    return TicketContentModel(
      companyCode: json['companyCode'],
      ticketTitle: json['ticketTitle'],
      startKey: json['startKey'],
      ticketCreatedTime: json['ticketCreatedTime'],
      procInstId: json['procInstId'],
      serviceName: json['serviceName'],
      procDefId: json['procDefId'],
      ticketTaskDtoList: (json['ticketTaskDtoList'] as List<dynamic>?)
          ?.map((e) => TicketTask.fromJson(e))
          .toList(),
      chartId: json['chartId'],
      ticketPriority: json['ticketPriority'],
      chartNodeName: json['chartNodeName'],
      ticketStatus: json['ticketStatus'],
      cancelUser: json['cancelUser'],
      requestCode: json['requestCode'],
      ticketEditTime: json['ticketEditTime'],
      id: json['id'],
      serviceId: json['serviceId'],
      createdUser: json['createdUser'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'companyCode': companyCode,
      'ticketTitle': ticketTitle,
      'startKey': startKey,
      'ticketCreatedTime': ticketCreatedTime,
      'procInstId': procInstId,
      'serviceName': serviceName,
      'procDefId': procDefId,
      'ticketTaskDtoList':
          ticketTaskDtoList?.map((e) => e.toJson()).toList(),
      'chartId': chartId,
      'ticketPriority': ticketPriority,
      'chartNodeName': chartNodeName,
      'ticketStatus': ticketStatus,
      'cancelUser': cancelUser,
      'requestCode': requestCode,
      'ticketEditTime': ticketEditTime,
      'id': id,
      'serviceId': serviceId,
      'createdUser': createdUser,
    };
  }
}
