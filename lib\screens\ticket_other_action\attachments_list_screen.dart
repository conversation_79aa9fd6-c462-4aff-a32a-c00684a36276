import 'dart:developer';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/attachment_list_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/attachment_list_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/attachment_list_state.dart';
import 'package:eapprove/common/collapse_view.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/common/file_viewer.dart';
import 'package:eapprove/screens/ticket_other_action/widget/attachment_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/svg.dart';

class AttachmentsListScreen extends StatefulWidget {
  const AttachmentsListScreen({super.key, required this.ticketId, required this.ticketStartUserId});
  final int ticketId;
  final String ticketStartUserId;

  @override
  State<AttachmentsListScreen> createState() => _AttachmentsListScreenState();
}

class _AttachmentsListScreenState extends State<AttachmentsListScreen> {
  List<DocumentModel> documents = [];
  List<DocumentModel> otherDocuments = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    log('ticketId3333333333333: ${widget.ticketId}');
    context
        .read<AttachmentListBloc>()
        .add(GetAttachmentList(ticketId: widget.ticketId, ticketStartUserId: widget.ticketStartUserId));

    // _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void mappingAttachmentList(List<DocumentModel> content, List<DocumentModel> contentOther) {
    documents = content;
    otherDocuments = contentOther;
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return GradientBackground(
      showBottomImage: false,
      // showUpperImage: false,
      child: Scaffold(
        backgroundColor: getColorSkin().transparent,
        appBar: CustomAppBar(
          automaticallyImplyLeading: isTablet ? false : true,
          centerTitle: true,
          title: 'Danh sách tệp đính kèm',
          titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          textColor: getColorSkin().black,
          actionsPadding: EdgeInsets.only(right: 18.w),
          actions: [
            if (isTablet)
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: SvgPicture.asset(
                  StringImage.ic_close,
                  width: 24.w,
                  height: 24.h,
                  colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
                ),
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
              )
          ],
        ),
        body: BlocBuilder<AttachmentListBloc, AttachmentListState>(buildWhen: (previous, current) {
          return previous.status != current.status ||
              previous.contentFile != current.contentFile ||
              previous.contentFileOther != current.contentFileOther ||
              previous.errorMessage != current.errorMessage;
        }, builder: (context, state) {
          if (state.status == ServiceStatus.success && (state.contentFile != null || state.contentFileOther != null)) {
            mappingAttachmentList(state.contentFile, state.contentFileOther);
          } else if (state.status == ServiceStatus.failure && state.errorMessage != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              SnackbarCore.error(state.errorMessage ?? '');
            });
          }
          return RefreshIndicator(
            color: getColorSkin().primaryBlue,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CollapseView(
                    title: 'Danh sách tài liệu đính kèm của người đệ trình',
                    isExpanded: true,
                    backgroundColor: isTablet ? getColorSkin().blue2 : getColorSkin().white,
                    children: [
                      isTablet
                          ? GridView.builder(
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  crossAxisSpacing: 8.w,
                                  mainAxisSpacing: 8.h,
                                  childAspectRatio: 310.w / 200.h),
                              itemCount: documents.length,
                              itemBuilder: (context, index) {
                                return AttachmentItem(
                                  document: documents[index],
                                  onView: (document) {
                                    context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => FileViewer(
                                          item: document,
                                        ),
                                      ),
                                    );
                                  },
                                  onDownload: (document) async {
                                    await document.downloadFile();
                                  },
                                );
                              },
                            )
                          : ListView.separated(
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: documents.length,
                              separatorBuilder: (context, index) => SizedBox(height: 12.h),
                              itemBuilder: (context, index) {
                                return AttachmentItem(
                                  document: documents[index],
                                  onView: (document) {
                                    context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => FileViewer(
                                          item: document,
                                        ),
                                      ),
                                    );
                                  },
                                  onDownload: (document) async {
                                    await document.downloadFile();
                                  },
                                );
                              },
                            ),
                    ],
                  ),
                  CollapseView(
                    title: 'Danh sách tài liệu đính kèm khác',
                    isExpanded: true,
                    backgroundColor: isTablet ? getColorSkin().blue2 : getColorSkin().white,
                    children: [
                      isTablet
                          ? GridView.builder(
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 8.w,
                                mainAxisSpacing: 8.h,
                              ),
                              itemCount: otherDocuments.length,
                              itemBuilder: (context, index) {
                                return AttachmentItem(
                                  document: otherDocuments[index],
                                );
                              },
                            )
                          : ListView.separated(
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: otherDocuments.length,
                              separatorBuilder: (context, index) => SizedBox(height: 12.h),
                              itemBuilder: (context, index) {
                                return AttachmentItem(
                                  document: otherDocuments[index],
                                );
                              },
                            ),
                    ],
                  ),
                ],
              ),
            ),
            onRefresh: () async {
              context.read<AttachmentListBloc>().add(GetAttachmentList(ticketId: widget.ticketId));
            },
          );
        }),
      ),
    );
  }
}
