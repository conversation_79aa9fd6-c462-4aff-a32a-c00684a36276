import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/main.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/services/token.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:eapprove/utils/logger.dart';

class CardDocumentAuthorize extends StatefulWidget {
  final AuthorizeItemData request;
  final DocumentModel document;
  final Function(DocumentModel)? onDownload;

  const CardDocumentAuthorize(
      {super.key,
      required this.request,
      required this.document,
      this.onDownload});

  @override
  State<CardDocumentAuthorize> createState() => _CardDocumentAuthorizeState();
}

class _CardDocumentAuthorizeState extends State<CardDocumentAuthorize> {
  late List<String> authorizationFiles;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // Handle the file names more robustly

    final fileNames = widget.request.fileName;
    if (fileNames == null || fileNames.isEmpty) {
      authorizationFiles = [];
      return;
    }

    // Split and clean the file names
    authorizationFiles = fileNames
        .split('|')
        .map((name) => name.trim())
        .where((name) => name.isNotEmpty)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return CustomExpandedList<String>(
      expandedSvgIconPath: StringImage.ic_arrow_up,
      collapsedSvgIconPath: StringImage.ic_arrow_right,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      childPadding: EdgeInsets.symmetric(horizontal: 12.w),
      title: "3. Chứng từ ủy quyền",
      isBuildLeading: false,
      isExpanded: true,
      titleStyle:
          getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
      child: _buildFormContent(),
    );
  }

  Widget _buildFormContent() {
    if (authorizationFiles.isEmpty) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Center(
          child: Text(
            'Không có chứng từ ủy quyền',
            style: getTypoSkin()
                .label4Regular
                .copyWith(color: getColorSkin().secondaryText),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...authorizationFiles.asMap().entries.map((entry) {
          final fileName = entry.value;
          final displayName = fileName.substring(fileName.lastIndexOf('/') + 1);
          return GestureDetector(
            onTap: () {
              widget.onDownload?.call(widget.document);
            },
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.only(bottom: 8.h),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: getColorSkin().ink5),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      displayName,
                      style: getTypoSkin().buttonText2Regular.copyWith(
                          color: getColorSkin().primaryBlue,
                          decoration: TextDecoration.underline),
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }
}
