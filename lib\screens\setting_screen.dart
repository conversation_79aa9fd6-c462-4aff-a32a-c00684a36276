import 'dart:developer' as developer;
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:eapprove/blocs/feature/feature_bloc.dart';
import 'package:eapprove/blocs/feature/feature_event.dart';
import 'package:eapprove/blocs/feature/feature_state.dart';
import 'package:eapprove/blocs/notification/notification_bloc.dart';
import 'package:eapprove/repositories/switch_account_repository.dart';
import 'package:eapprove/services/global_navigation.dart';
import 'package:eapprove/widgets/bottom_sheet.dart';
import 'package:eapprove/widgets/custom_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'package:eapprove/widgets/profile_header.dart';
import 'package:eapprove/screens/authorize_management/authorize_management_screen.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';

class EAppSettingScreen extends StatefulWidget {
  static const routeName = "/eapprove/setting_screen";
  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => const EAppSettingScreen());
  }

  final VoidCallback? onHomeScreenBack;

  const EAppSettingScreen({super.key, this.onHomeScreenBack});

  @override
  State<EAppSettingScreen> createState() => _EAppSettingScreenState();
}

class _EAppSettingScreenState extends State<EAppSettingScreen> {
  bool isNotify = false;
  bool isLogin = false;
  bool isReceiveOTP = false;
  bool isLoading = true;
  bool _isUpdatingNotification = false;
  bool _firstTime = true;
  final isTablet = DeviceUtils.isTablet;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
    });

    if (_firstTime) {
      _firstTime = false;
      // Load user list
      context.read<FeatureBloc>().add(const LoadFeatures());
    }

    try {
      // Load data in parallel
      await Future.wait([
        _fetchUserList(),
        _loadNotificationSettings(),
      ]);
    } catch (e) {
      developer.log('Error loading data: $e', name: 'EAppSettingScreen');
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _loadNotificationSettings() async {
    try {
      final notiBloc = context.read<NotiBloc>();
      final isEnabled = await notiBloc.getNotificationStatus();
      if (mounted) {
        setState(() {
          isNotify = isEnabled;
        });
      }
    } catch (e) {
      developer.log('Error loading notification settings: $e', name: 'EAppSettingScreen');
    }
  }

  Future<void> _updateNotificationStatus(bool value) async {
    if (_isUpdatingNotification) return;

    setState(() {
      _isUpdatingNotification = true;
    });

    try {
      final notiBloc = context.read<NotiBloc>();
      final success = await notiBloc.updateNotificationStatus(value);

      if (success) {
        setState(() {
          isNotify = value;
        });
      } else {
        SnackbarCore.error(
          'Lỗi khi cập nhật thông báo',
        );
        setState(() {
          isNotify = !value;
        });
      }
    } catch (e) {
      developer.log('Error updating notification status: $e', name: 'EAppSettingScreen');
      SnackbarCore.error(
        'Lỗi khi cập nhật thông báo',
      );
      setState(() {
        isNotify = !value;
      });
    } finally {
      setState(() {
        _isUpdatingNotification = false;
      });
    }
  }

  Future<void> _fetchUserList() async {
    return Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        context.read<UserListBloc>().add(FetchUserList());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: CustomAppBar(
          automaticallyImplyLeading: isTablet ? false : true,
          title: 'Cài đặt',
          titleTextStyle: getTypoSkin().title3Medium.copyWith(
                color: getColorSkin().title,
              ),
          centerTitle: true,
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: isLoading ? _buildLoadingLayout() : _buildContentLayout(),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingLayout() {
    return Column(
      children: [
        _buildProfileHeaderShimmer(),
        _buildSettingsContainerShimmer(),
        _buildIntroductionContainerShimmer(),
        _buildLogoutContainerShimmer(),
      ],
    );
  }

  Widget _buildContentLayout() {
    return Column(
      children: [
        ProfileHeader(
          onTrailingIconPressed: () {
            developer.log('doi tai khoan', name: 'EAppSettingScreen');
            final switchAccountRepo = context.read<SwitchAccountRepository>();
            showAccountSelectionSheet(context, switchAccountRepo);
          },
        ),
        _buildSettingsContainer(),
        _buildIntroductionContainer(),
        _buildLogoutContainer(),
      ],
    );
  }

  Widget _buildProfileHeaderShimmer() {
    return Container(
      margin: EdgeInsets.only(bottom: 4.h, top: 10.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // User info section shimmer
          Padding(
            padding: EdgeInsets.only(left: 18.w, right: 18.w, top: 15.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar shimmer
                AppConstraint.buildShimmer(
                  child: Container(
                    width: 48.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: getColorSkin().white,
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name shimmer
                      AppConstraint.buildShimmer(
                        child: Container(
                          width: 150.w,
                          height: 20.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: getColorSkin().white,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      // Job title shimmer
                      AppConstraint.buildShimmer(
                        child: Container(
                          width: 100.w,
                          height: 16.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: getColorSkin().white,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      // Position shimmer
                      AppConstraint.buildShimmer(
                        child: Container(
                          width: 120.w,
                          height: 14.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: getColorSkin().white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 15.h),
          CustomDivider(
            indent: 0,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          // User details shimmer
          _buildDetailTileShimmer('Công ty'),
          CustomDivider(
            indent: 0,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          _buildDetailTileShimmer('Email'),
          CustomDivider(
            indent: 0,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          _buildDetailTileShimmer('Điện thoại'),
        ],
      ),
    );
  }

  Widget _buildDetailTileShimmer(String label) {
    return ListTile(
      leading: Text(
        label,
        style: getTypoSkin().title6Regular.copyWith(
              color: getColorSkin().title,
            ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          AppConstraint.buildShimmer(
            child: Container(
              width: 150.w,
              height: 16.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: getColorSkin().white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContainerShimmer() {
    return Container(
      padding: isTablet ? EdgeInsets.zero : EdgeInsets.symmetric(horizontal: 4.h),
      margin: EdgeInsets.only(top: 10.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          // Authorization Management shimmer
          isTablet
              ? Container(
                  decoration: BoxDecoration(
                    color: getColorSkin().secondaryColor1TagBackground,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16.r),
                    ),
                  ),
                  child: _buildSettingTileShimmer(withArrow: true),
                )
              : _buildSettingTileShimmer(withArrow: true),
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          // Notification shimmer
          _buildSettingTileShimmer(withSwitch: true),
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          // Login persistance shimmer
          _buildSettingTileShimmer(withSwitch: true),
        ],
      ),
    );
  }

  Widget _buildSettingTileShimmer({bool withArrow = false, bool withSwitch = false}) {
    return ListTile(
      leading: AppConstraint.buildShimmer(
        child: Container(
          width: 24.w,
          height: 24.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: getColorSkin().white,
          ),
        ),
      ),
      title: AppConstraint.buildShimmer(
        child: Container(
          width: 120.w,
          height: 16.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: getColorSkin().white,
          ),
        ),
      ),
      trailing: withArrow
          ? AppConstraint.buildShimmer(
              child: Container(
                width: 20.w,
                height: 20.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: getColorSkin().white,
                ),
              ),
            )
          : withSwitch
              ? AppConstraint.buildShimmer(
                  child: Container(
                    width: 40.w,
                    height: 20.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: getColorSkin().white,
                    ),
                  ),
                )
              : null,
    );
  }

  Widget _buildIntroductionContainerShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.h),
      margin: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          ListTile(
            leading: AppConstraint.buildShimmer(
              child: Container(
                width: 24.w,
                height: 24.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: getColorSkin().white,
                ),
              ),
            ),
            title: AppConstraint.buildShimmer(
              child: Container(
                width: 100.w,
                height: 16.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: getColorSkin().white,
                ),
              ),
            ),
            subtitle: AppConstraint.buildShimmer(
              child: Container(
                width: 80.w,
                height: 14.h,
                margin: EdgeInsets.only(top: 8.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: getColorSkin().white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutContainerShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.h),
      margin: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w, bottom: 20.h),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: ListTile(
        leading: AppConstraint.buildShimmer(
          child: Container(
            width: 24.w,
            height: 24.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: getColorSkin().white,
            ),
          ),
        ),
        title: AppConstraint.buildShimmer(
          child: Container(
            width: 100.w,
            height: 16.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: getColorSkin().white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsContainer() {
    return Container(
      padding: isTablet ? EdgeInsets.zero : EdgeInsets.symmetric(horizontal: 4.h),
      margin: EdgeInsets.only(top: 10.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          BlocBuilder<FeatureBloc, FeatureState>(
            builder: (context, state) {
              if (state is FeatureLoaded &&
                  state.featureData.data?.featureCheckData?.countDataByAssign is int &&
                  state.featureData.data?.featureCheckData!.countDataByAssign as int > 0) {
                return isTablet
                    ? Container(
                        decoration: BoxDecoration(
                          color: getColorSkin().secondaryColor1TagBackground,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(16.r),
                          ),
                        ),
                        child: ListTile(
                          leading: SvgPicture.asset(
                            StringImage.ic_assign,
                            width: 20.w,
                            height: 20.h,
                          ),
                          title: Text(
                            'Quản lý ủy quyền',
                            style: getTypoSkin().title6Regular.copyWith(
                                  color: getColorSkin().title,
                                ),
                          ),
                          trailing: SvgPicture.asset(
                            StringImage.ic_arrow_right,
                            width: 20.w,
                            height: 20.h,
                          ),
                          onTap: () {},
                        ),
                      )
                    : ListTile(
                        leading: SvgPicture.asset(
                          StringImage.ic_assign,
                          width: 20.w,
                          height: 20.h,
                        ),
                        title: Text(
                          'Quản lý ủy quyền',
                          style: getTypoSkin().title6Regular.copyWith(
                                color: getColorSkin().title,
                              ),
                        ),
                        trailing: SvgPicture.asset(
                          StringImage.ic_arrow_right,
                          width: 20.w,
                          height: 20.h,
                        ),
                        onTap: () {
                          // Ẩn bottom navigation bar trước khi điều hướng
                          context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));

                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const AuthorizeManagementScreen()),
                          ).then((_) {
                            // Hiển thị lại bottom navigation bar khi quay lại
                            context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
                          });
                        },
                      );
              } else {
                return SizedBox.shrink();
              }
            },
          ),
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          ListTile(
            leading: SvgPicture.asset(
              StringImage.ic_bell,
              width: 24.w,
              height: 24.h,
            ),
            title: Text(
              'Nhận thông báo',
              style: getTypoSkin().title6Regular.copyWith(
                    color: getColorSkin().title,
                  ),
            ),
            trailing: FSwitch(
              value: isNotify,
              onChanged: _updateNotificationStatus,
            ),
          ),
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          ListTile(
            leading: SvgPicture.asset(
              StringImage.ic_people,
              width: 24.w,
              height: 24.h,
            ),
            title: Text(
              'Duy trì đăng nhập',
              style: getTypoSkin().title6Regular.copyWith(
                    color: getColorSkin().title,
                  ),
            ),
            trailing: FSwitch(
              value: isLogin,
              onChanged: (bool value) => setState(() {
                isLogin = !isLogin;
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIntroductionContainer() {
    Box boxSetting = Hive.box('setting');
    String buildNumber = boxSetting.get('buildNumber', defaultValue: '');
    String buildVersion = boxSetting.get('buildVersion', defaultValue: '');
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.h),
      margin: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          CustomDivider(
            indent: 30,
            endIndent: 0,
            thickness: 1,
            color: getColorSkin().grey3Background,
          ),
          ListTile(
            leading: SvgPicture.asset(
              StringImage.ic_introduce,
              width: 24.w,
              height: 24.h,
            ),
            title: Text(
              'Phiên bản ứng dụng',
              style: getTypoSkin().title6Regular.copyWith(
                    color: getColorSkin().title,
                  ),
            ),
            subtitle: Text(
              'Version: $buildVersion (Build: $buildNumber)',
              style: getTypoSkin().subtitle3Regular.copyWith(
                    color: getColorSkin().secondaryText,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutContainer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.h),
      margin: EdgeInsets.only(top: 12.h, left: 16.w, right: 16.w, bottom: 20.h),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: ListTile(
        onTap: () {
          developer.log('Đăng xuất', name: 'EAppSettingScreen');
          GlobalNavigation().handleLogout(context).then((value) {
            if (value == true) {
                Navigator.of(context, rootNavigator: true).pop('logout');
            }
          });
        },
        leading: SvgPicture.asset(
          isTablet ? StringImage.logout : StringImage.ic_logout,
          width: 24.w,
          height: 24.h,
        ),
        title: Text(
          'Đăng xuất',
          style: getTypoSkin().title6Regular.copyWith(
                color: getColorSkin().title,
              ),
        ),
      ),
    );
  }
}
