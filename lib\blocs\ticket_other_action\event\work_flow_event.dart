import 'package:equatable/equatable.dart';

abstract class WorkflowEvent extends Equatable {
  const WorkflowEvent();

  @override
  List<Object?> get props => [];
}

class LoadWorkflow extends WorkflowEvent {
  final String? procDefId;
  final String? procInstId;
  final String? fromNodeId;

  const LoadWorkflow({this.procDefId, this.procInstId, this.fromNodeId});

  @override
  List<Object?> get props => [procDefId, procInstId, fromNodeId];
}
