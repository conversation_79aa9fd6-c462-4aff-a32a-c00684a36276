import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/screens/login/login_screen.dart';
import 'package:flutter/material.dart';

class FlashScreen extends StatefulWidget {
  const FlashScreen({super.key});

  @override
  State<FlashScreen> createState() => _FlashScreenState();
}

class _FlashScreenState extends State<FlashScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 3), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const LoginScreen(),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              StringImage.background,
              fit: BoxFit.fill,
            ),
          ),
          Positioned(
            top: 50,
            bottom: 50,
            left: 0,
            right: 0,
            child: Center(
              child: Image.asset(
                StringImage.logo,
                fit: BoxFit.fill,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
