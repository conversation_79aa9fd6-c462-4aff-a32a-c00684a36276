part of 'print_file_template_bloc.dart';

abstract class PrintFileTemplateState extends Equatable {
  const PrintFileTemplateState();

  @override
  List<Object> get props => [];
}

class PrintFileTemplateInitial extends PrintFileTemplateState {}

class PrintFileTemplateLoading extends PrintFileTemplateState {}

class PrintFileTemplateSuccess extends PrintFileTemplateState {
  final dynamic response;

  const PrintFileTemplateSuccess(this.response);

  @override
  List<Object> get props => [response];
}

class PrintFileTemplateFailure extends PrintFileTemplateState {
  final String error;

  const PrintFileTemplateFailure(this.error);

  @override
  List<Object> get props => [error];
} 