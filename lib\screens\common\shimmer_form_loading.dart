import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

Widget buildFormShimmerLoading() {
  return Shimmer.fromColors(
    baseColor: Colors.grey[300]!,
    highlightColor: Colors.grey[100]!,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 50,
          width: double.infinity,
          color: Colors.white,
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 8.h),
        Container(
          height: 30,
          width: double.infinity,
          color: Colors.white,
        ),
        // Add more shimmer items as needed
      ],
    ),
  );
}