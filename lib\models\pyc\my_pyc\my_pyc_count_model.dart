class MyPycCountRequestModel {
  final bool? ownerProcess;
  final dynamic? search;

  MyPycCountRequestModel(this.ownerProcess, this.search);

  Map<String, dynamic> toJson() {
    return {
      'ownerProcess': false,
      'search': search,
    };
  }
}

class MyPycCountDataModel {
  final int? completed;
  final int? shared;
  final int? draft;
  final int? cancel;
  final int? ongoing;
  final int? recalled;
  final int? share;

  MyPycCountDataModel({
    this.completed,
    this.shared,
    this.draft,
    this.cancel,
    this.ongoing,
    this.recalled,
    this.share,
  });

  factory MyPycCountDataModel.fromJson(Map<String, dynamic> json) {
    return MyPycCountDataModel(
      completed: json['COMPLETED'],
      shared: json['SHARED'],
      draft: json['DRAFT'],
      cancel: json['CANCEL'],
      ongoing: json['ONGOING'],
      recalled: json['RECALLED'],
      share: json['SHARE'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'COMPLETED': completed,
      'SHARED': shared,
      'DRAFT': draft,
      'CANCEL': cancel,
      'ONGOING': ongoing,
      'RECALLED': recalled,
      'SHARE': share,
    };
  }
}
