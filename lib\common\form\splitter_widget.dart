import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:eapprove/enum/enum.dart';

import 'common_form_widget.dart';
import 'dart:developer' as developer;

// Splitter Widget using CustomExpandedList
class SplitterWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  // final List<FormItemInfo>? childFormItems;
  final FormStateManager stateManager;
  final List<FormItemInfo>? groupedWidgets;

  const SplitterWidget({
    Key? key,
    required this.data,
    this.onChange,
    // this.childFormItems,
    required this.stateManager,
    this.groupedWidgets,
  }) : super(key: key);

  @override
  State<SplitterWidget> createState() => _SplitterWidgetState();
}

class _SplitterWidgetState extends State<SplitterWidget> {
  @override
  Widget build(BuildContext context) {
    // Don't render if display is false
    if (widget.data.display == false) {
      return const SizedBox.shrink();
    }

//     // Find child items that belong to this splitter
//     final List<FormItemInfo> children = [];

//     // Bước 1: Lọc ra các con thực sự của splitter hiện tại
//     if (widget.childFormItems != null) {
//       for (var item in widget.childFormItems!) {
//         final splitterId = item.splitter;
//         if (splitterId == widget.data.id && item.display != false) {
//           if (item.type == FormItemType.splitter && item.id == widget.data.id) {
//             continue; // tránh lặp vô hạn do splitter lồng chính nó
//           }
//           children.add(item);
//         }
//       }
//     }

//     // Bước 2: Group children theo row
//     final Map<String, List<FormItemInfo>> rowGroups = {};
//     for (var item in children) {
//       if (item.row != null) {
//         rowGroups.putIfAbsent(item.row!, () => []).add(item);
//       }
//     }

//     // Bước 3: Sắp xếp từng hàng, sau đó cột, rồi đến fieldSortOrder
//     final List<List<List<FormItemInfo>>> groupedRenderOrder = [];

//     final sortedRowGroups = rowGroups.entries.toList()
//       ..sort((a, b) => (a.value.first.rowSortOrder ?? 0)
//           .compareTo(b.value.first.rowSortOrder ?? 0));

//     for (var rowEntry in sortedRowGroups) {
//       final rowItems = rowEntry.value;

//       // Group by column
//       final Map<String, List<FormItemInfo>> colGroups = {};
//       for (var item in rowItems) {
//         if (item.col != null) {
//           colGroups.putIfAbsent(item.col!, () => []).add(item);
//         }
//       }

//       // Sort columns
//       final sortedColGroups = colGroups.entries.toList()
//         ..sort((a, b) => (a.value.first.colSortOrder ?? 0)
//             .compareTo(b.value.first.colSortOrder ?? 0));

//       final List<List<FormItemInfo>> row = [];

//       for (var colEntry in sortedColGroups) {
//         final colItems = colEntry.value;
//         colItems.sort(
//             (a, b) => (a.fieldSortOrder ?? 0).compareTo(b.fieldSortOrder ?? 0));
//         row.add(colItems);
//       }

//       groupedRenderOrder.add(row);
//     }

// // 🪄 groupedRenderOrder = List of rows → List of columns in each row → List of fields in each column

//     developer.log('Rows to render: ${groupedRenderOrder.length}');
//     for (var i = 0; i < groupedRenderOrder.length; i++) {
//       developer.log('  Row $i has ${groupedRenderOrder[i].length} columns');
//     }

//     // if (children.isEmpty) {
//     //   return const SizedBox.shrink();
//     // }

    // Determine initial expanded state based on splitterOpen value
    // 1 = closed, 2 = open
    bool isInitiallyExpanded = widget.data.splitterOpen == 2;

    developer.log(
        'widget.groupedWidgets - build splitter: ${widget.groupedWidgets?.map((e) => e.label).toList()}');
    // developer.log(
    //     'Splitter types: ${widget.childFormItems?.where((e) => e.type == FormItemType.splitter).map((e) => e.label).toList()}');


    Widget childContent = Column(
      children: [
        if (widget.data.splitterDesc != null &&
            widget.data.splitterDesc!.isNotEmpty)
          Text(
            widget.data.splitterDesc!,
            style: TextStyle(
              fontStyle: FontStyle.italic,
              fontSize: 14.sp,
              color: getColorSkin().ink2,
            ),
          ),

        // Render child form items
        ...(() {
          widget.groupedWidgets?.sort((a, b) =>
              (a.type == FormItemType.splitter ? 1 : 0)
                  .compareTo(b.type == FormItemType.splitter ? 1 : 0));
    
          return widget.groupedWidgets?.map((child) => _buildWidget(child)) ??
              [];
        })(),
      ],
    );

    // Use CustomExpandedList for the splitter
    return CustomExpandedList<String>(
      childrenPadding: EdgeInsets.zero,
      expandedSvgIconPath: StringImage.ic_arrow_up,
      collapsedSvgIconPath: StringImage.ic_arrow_right,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      cardElevation: 1,
      cardMargin: EdgeInsets.only(bottom: 8.h),
      childPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 18.w),
      title: widget.data.label ?? '',
      isExpanded: isInitiallyExpanded,
      titleStyle: TextStyle(
        fontWeight: widget.data.fontWeight == 'bold' ||
                widget.data.fontWeight == 'font-bold'
            ? FontWeight.bold
            : FontWeight.normal,
        fontStyle: widget.data.fontWeight == 'italic'
            ? FontStyle.italic
            : FontStyle.normal,
        decoration: widget.data.fontWeight == 'underline'
            ? TextDecoration.underline
            : TextDecoration.none,
        color: getColorSkin().ink1,
        fontSize: 16.sp,
      ),
      isBuildLeading: false,
      child: childContent,
    );
  }

  Widget _buildWidget(FormItemInfo formWidget) {
   
    final eventResults =
        widget.stateManager.getEventExpressionResults(formWidget.id.toString());
    // Xử lý ten_truong
    final tenTruong = eventResults?['ten_truong']?.toString();
    final currentLabel = tenTruong ?? formWidget.label;

    // Xử lý bat_buoc
    final batBuoc = eventResults?['bat_buoc']?.toString();
    final currentRequired = batBuoc != null
        ? batBuoc.toLowerCase() == 'true'
        : formWidget.validations?['required'] ?? false;

    // Xử lý chi_duoc_doc
    final chiDuocDoc = eventResults?['chi_duoc_doc']?.toString();
    final currentReadonly = chiDuocDoc != null
        ? chiDuocDoc.toLowerCase() == 'true'
        : formWidget.readonly;

    // Xử lý huong_dan
    final huongDan = eventResults?['huong_dan']?.toString();
    final currentSuggestText = huongDan ?? formWidget.suggestText;

    // Xử lý hiển thị widget theo thứ tự ưu tiên
    final hienThi = eventResults?['hien_thi']?.toString();

    final shouldDisplay = hienThi != null
        ? hienThi.toLowerCase() == 'true'
        : formWidget.display ?? true;
    // developer.log('formWidget - _buildWidget display: ${formWidget.display}',
    //     name: "splitter-widget");
    // developer.log('formWidget - _buildWidget readonly: ${formWidget.readonly}',
    //     name: "splitter-widget");
    // developer.log(
    //     'formWidget - _buildWidget suggestText: ${formWidget.suggestText}',
    //     name: "splitter-widget");
    // developer.log(
    //     'formWidget - _buildWidget validations: ${formWidget.validations}',
    //     name: "splitter-widget");
    // developer.log('formWidget - _buildWidget splitter: ${formWidget.splitter}',
    //     name: "splitter-widget");
    // developer.log(
    //     'formWidget - _buildWidget fieldSortOrder: ${formWidget.fieldSortOrder}',
    //     name: "splitter-widget");
    developer.log(
        '------------------------------------------------------------------------',
        name: "splitter-widget");

    // Create a copy of the widget with current values
    final currentWidget = FormItemInfo(
      id: formWidget.id,
      name: formWidget.name,
      type: formWidget.type,
      label: currentLabel,
      readonly: currentReadonly,
      suggestText: currentSuggestText,
      validations: {...?formWidget.validations, 'required': currentRequired},
      display: shouldDisplay,
      splitter: formWidget.splitter,
      fieldSortOrder: formWidget.fieldSortOrder,
      eventExpression: formWidget.eventExpression,
      placeholder: formWidget.placeholder,
      value: formWidget.value,
      useTimeDefault: formWidget.useTimeDefault,
      fontWeight: formWidget.fontWeight,
    );

    // Don't render if display is false
    // if (formWidget.display == false) {
    //   return const SizedBox.shrink();
    // }

    return Container(
      margin: EdgeInsets.only(top: 12.h),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                currentWidget.label ?? '',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (currentWidget.validations?['required'] == true)
                const Text(
                  ' *',
                  style: TextStyle(color: Colors.red),
                ),
              if (currentWidget.suggestText != null &&
                  currentWidget.suggestText!.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.help_outline, size: 16),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(currentWidget.suggestText!)),
                    );
                  },
                ),
            ],
          ),
          _buildFormItem(currentWidget),
        ],
      ),
    );
  }

  Widget _buildFormItem(FormItemInfo item) {
    try {
      if (item.type == FormItemType.splitter) {
        developer.log(
            'item - _buildFormItem id: ${item.id} type: ${item.type} label: ${item.label}',
            name: "splitter-widget");

        // final children = widget.groupedWidgets?[item.id];

        return Container(
          // margin: EdgeInsets.only(top: 12.h),
          // child: SplitterWidget(
          //   data: item,
          //   onChange: widget.onChange,
          //   stateManager: widget.stateManager,
          //   childFormItems: children,
          //   groupedWidgets: widget.groupedWidgets,
          // ),
        );
      } else {
        return FormItemWidget(
          data: item,
          stateManager: widget.stateManager,
          // childFormItems: widget.childFormItems,
        );
      }
    } catch (e) {
      // Return an error widget instead of crashing
      return Text('Error rendering field: ${item.name ?? 'Unknown'}');
    }
  }
}
