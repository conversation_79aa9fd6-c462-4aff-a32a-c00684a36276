import 'package:eapprove/models/meta.dart';

class OtpVerifyRequest {
  final String otp;
  final String sessionState;

  OtpVerifyRequest({
    required this.otp,
    required this.sessionState,
  });

  Map<String, dynamic> toJson() => {
        'otp': otp,
        'session_state': sessionState,
      };
}

class OtpVerifyResponse {
  final Meta meta;
  final OtpVerifyData? data;

  OtpVerifyResponse({
    required this.meta,
    this.data,
  });

  factory OtpVerifyResponse.fromJson(Map<String, dynamic> json) {
    return OtpVerifyResponse(
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
      data: json['data'] != null
          ? OtpVerifyData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  factory OtpVerifyResponse.error(int code, String message) {
    return OtpVerifyResponse(
      meta: Meta(code: code, message: message),
    );
  }
}

class OtpVerifyData {
  final String? email;
  final String? code;
  final String? sessionState;
  final bool? is2FA;
  final String? redirectUri;

  OtpVerifyData({
    this.email,
    this.code,
    this.sessionState,
    this.is2FA,
    this.redirectUri,
  });

  factory OtpVerifyData.fromJson(Map<String, dynamic> json) {
    return OtpVerifyData(
      email: json['email'] as String?,
      code: json['code'] as String?,
      sessionState: json['session_state'] as String?,
      is2FA: json['is2FA'] as bool?,
      redirectUri: json['redirect_uri'] as String?,
    );
  }
}
