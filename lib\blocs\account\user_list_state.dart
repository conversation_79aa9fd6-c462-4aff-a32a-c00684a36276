import 'package:eapprove/models/account/account_model.dart';
import 'package:equatable/equatable.dart';

abstract class UserListState extends Equatable {
  @override
  List<Object?> get props => [];
}

class UserListInitial extends UserListState {}

class UserListLoading extends UserListState {}

// class UserListLoaded extends UserListState {
//   final List<UserList> users;

//   UserListLoaded(this.users);

//   @override
//   List<Object?> get props => [users];
// }

class UserListLoaded extends UserListState {
  final List<UserList> users;
  final UserADM? userADM;
  final String? username;
  final List<String> roles;
  final bool isAdmin;
  final bool isDefaultUser;

  UserListLoaded(this.users, this.userADM,
      {this.username, this.roles = const [], this.isAdmin = false, this.isDefaultUser = false});

  UserListLoaded copyWith({
    List<UserList>? users,
    UserADM? userADM,
    String? username,
    List<String>? roles,
    bool? isAdmin,
    bool? isDefaultUser,
  }) {
    return UserListLoaded(
      users ?? this.users,
      userADM ?? this.userADM,
      username: username ?? this.username,
      roles: roles ?? this.roles,
      isAdmin: isAdmin ?? this.isAdmin,
      isDefaultUser: isDefaultUser ?? this.isDefaultUser,
    );
  }
}

class UserListError extends UserListState {
  final String message;

  UserListError(this.message);

  @override
  List<Object?> get props => [message];
}
