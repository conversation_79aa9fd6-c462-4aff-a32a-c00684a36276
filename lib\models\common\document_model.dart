import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:eapprove/main.dart';
import 'package:eapprove/models/ticket_other_action/ticket_history_model.dart';
import 'package:eapprove/repositories/ticket_process_detail_repository.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/services/token.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class DocumentModel {
  final String id;
  final String name;
  final String url;
  final FileType type;
  final String size;
  final String extension;
  final String? description;
  final String? createdTime;
  final String? createdBy;
  final String? fileType;

  DocumentModel({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
    required this.extension,
    this.description,
    this.createdTime,
    this.createdBy,
    this.fileType,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type,
      'size': size,
      'extension': extension,
      'description': description,
      'createdTime': createdTime,
      'createdBy': createdBy,
      'fileType': fileType,
    };
  }

  static DocumentModel fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      id: json['id'],
      name: json['name'],
      url: json['url'],
      type: json['type'],
      size: json['size'],
      extension: json['extension'],
      description: json['description'],
      createdTime: json['createdTime'],
      createdBy: json['createdBy'],
      fileType: json['fileType'],
    );
  }

  static DocumentModel fromJsonAPIAttachmentList(
      {id, name, url, type, size, extension, description, createdTime, createdBy, fileType}) {
    log('id11111: $id');
    log('name11111: $name');
    log('url11111: $url');
    log('type11111: $type');
    log('size11111: $size');
    log('extension11111: $extension');
    log('description11111: $description');
    return DocumentModel(
      id: id,
      name: name,
      url: url,
      type: type,
      size: size,
      extension: extension,
      description: description,
      createdTime: createdTime,
      createdBy: createdBy,
      fileType: fileType,
    );
  }

  Future<String> getFileUrl() async {
    try {
      final tokenManager = TokenManager();
      final interceptedClient = InterceptedClient.build(
        interceptors: [
          AuthorizationInterceptor(
            tokenManager: tokenManager,
            navigatorKey: navigatorKey,
          ),
        ],
      );
      final apiService = ApiService(httpClient: interceptedClient, tokenManager: tokenManager);
      final endpoint = 'business-process/file/getFileUrl?fileName=${url}';
      final response = await apiService.post(endpoint, {});

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        if (jsonData['code'] == 1) {
          return jsonData['data'] as String;
        } else {
          throw Exception('Failed to get file URL: ${jsonData['message']}');
        }
      } else {
        throw Exception('Failed to get file URL: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting file URL: $e');
    }
  }

  Future<String?> downloadFile() async {
    try {
      // 1. Xin quyền lưu file nếu là Android
      final url = await getFileUrl();
      if (Platform.isAndroid) {
        final status = await Permission.manageExternalStorage.request();
        if (!status.isGranted) {
          throw Exception("Permission denied");
        }
      }

      // 2. Tạo Dio client
      Dio dio = Dio();

      // 3. Lấy thư mục lưu file
      Directory dir;
      if (Platform.isAndroid) {
        dir = Directory('/storage/emulated/0/Download'); // Thư mục Downloads
      } else {
        dir = await getApplicationDocumentsDirectory(); // iOS
      }

      // 4. Tạo đường dẫn file đích
      String name = '${this.name}_${DateTime.now().millisecondsSinceEpoch}.${this.extension}';
      String fullPath = '${dir.path}/$name';

      // 5. Tải file về
      await dio.download(
        url,
        fullPath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            print('Download progress: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        },
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: false,
          validateStatus: (status) => status! < 500,
        ),
      );
      SnackbarCore.success("Tải về thành công");
      return fullPath;
    } catch (e) {
      SnackbarCore.error("Tải về thất bại");
      log('Error downloading file11111111111: $e');
      LoggerConfig.logger.e('Error downloading file11111111111: $e');
      return null;
    }
  }
}

enum FileType {
  pdf('pdf', 'PDF', true, true),
  doc('doc', 'WORD', true, true),
  docx('docx', 'WORD', true, true),
  xls('xls', 'EXCEL', true, true),
  xlsx('xlsx', 'EXCEL', true, true),
  txt('txt', 'TXT', true, true),
  jpg('jpg', 'IMAGE', true, true),
  jpeg('jpeg', 'IMAGE', true, true),
  png('png', 'IMAGE', true, true),
  gif('gif', 'IMAGE', true, true),
  bmp('bmp', 'IMAGE', true, true),
  webp('webp', 'IMAGE', true, true),
  tiff('tiff', 'IMAGE', true, true),
  svg('svg', 'IMAGE', true, true),
  ico('ico', 'IMAGE', true, true),
  heic('heic', 'IMAGE', true, true),
  heif('heif', 'IMAGE', true, true),
  raw('raw', 'IMAGE', true, true),
  cr2('cr2', 'IMAGE', true, true),
  nef('nef', 'IMAGE', true, true),
  arw('arw', 'IMAGE', true, true),
  dng('dng', 'IMAGE', true, true),
  defaulted('default', 'UNKNOWN', false, true);

  final String extension;
  final String name;
  final bool preview;
  final bool download;

  const FileType(this.extension, this.name, this.preview, this.download);

  static FileType fromCode(String extension) {
    return FileType.values.firstWhere(
      (action) => action.extension == extension,
      orElse: () => FileType.defaulted,
    );
  }

  static String checkTypeDocument(String type) {
    if (type == 'pdf' || type == 'doc' || type == 'docx' || type == 'xls' || type == 'xlsx' || type == 'txt') {
      return 'document';
    } else if (type == 'jpg' ||
        type == 'jpeg' ||
        type == 'png' ||
        type == 'gif' ||
        type == 'bmp' ||
        type == 'webp' ||
        type == 'tiff' ||
        type == 'svg' ||
        type == 'ico' ||
        type == 'heic' ||
        type == 'heif' ||
        type == 'raw' ||
        type == 'cr2' ||
        type == 'nef' ||
        type == 'arw' ||
        type == 'dng') {
      return 'image';
    } else {
      return 'unknown';
    }
  }
}
