// overlay_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';

abstract class OverlayEvent {}

class ShowOverlay extends OverlayEvent {
  final Widget overlayContent;
  ShowOverlay(this.overlayContent);
}

class HideOverlay extends OverlayEvent {}

class OverlayState {
  final bool show;
  final Widget? content;
  OverlayState({required this.show, this.content});
}

class OverlayBloc extends Bloc<OverlayEvent, OverlayState> {
  OverlayBloc() : super(OverlayState(show: false, content: null)) {
    on<ShowOverlay>((event, emit) {
      emit(OverlayState(show: true, content: event.overlayContent));
    });
    on<HideOverlay>((event, emit) {
      emit(OverlayState(show: false, content: null));
    });
  }
}