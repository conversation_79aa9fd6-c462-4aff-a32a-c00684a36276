import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AttachmentItem extends StatelessWidget {
  const AttachmentItem({super.key, required this.document, this.onView, this.onDownload});

  final DocumentModel document;
  final Function(DocumentModel)? onView;
  final Function(DocumentModel)? onDownload;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(22.w, 8.h, 22.w, 8.h),
      decoration: BoxDecoration(
        color: getColorSkin().white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            document.name ?? '',
            style: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          ),
          Sized<PERSON><PERSON>(height: 4.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                document.createdTime ?? '',
                style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink1),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (document.type.preview)
                    IconButton(
                      icon: SvgPicture.asset(StringImage.ic_eye, width: 22.w, height: 22.w),
                      onPressed: () {
                        onView?.call(document);
                      },
                    ),
                  if (document.type.download)
                    IconButton(
                      icon: SvgPicture.asset(StringImage.ic_download, width: 22.w, height: 22.w),
                      onPressed: () {
                        onDownload?.call(document);
                      },
                    ),
                ],
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            document.description ?? '',
            style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink1),
          ),
          SizedBox(height: 4.h),
          Text(
            document.createdBy ?? '',
            style: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().ink1),
          ),
        ],
      ),
    );
  }
}
