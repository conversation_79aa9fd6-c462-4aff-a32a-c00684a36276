const testFormData = {
  "form": [
    {
      "id": "spl__383d21fd-b7e0-4922-b5e0-478f4d4d2f79",
      "step": "",
      "row": "row_2140fbb4-1f35-4ef3-b0d2-72419c15ad46",
      "col": "column_a2f5706a-2e83-49e0-a4cb-949b0b26d43b",
      "rowSortOrder": 2,
      "colSortOrder": 0,
      "fieldSortOrder": 2,
      "tab": "",
      "tabItem": "",
      "parentName": "",
      "splitter": "",
      "validations": {"required": false},
      "type": "splitter",
      "label": "2. Chi tiết phiếu",
      "name": "spl_2ChiTietPhieu",
      "placeholder": "",
      "eventExpression": [],
      "isHorizontal": 2,
      "fontWeight": "font-bold",
      "display": true,
      "suggestText": "",
      "value": "",
      "readonly": false,
      "forceView": false,
      "useTimeDefault": false,
      "splitterDesc": "",
      "splitterOpen": 2
    },
    {
      "splitter": "spl__383d21fd-b7e0-4922-b5e0-478f4d4d2f79",
      "id": "txt_text1",
      "name": "txt_text1",
      "label": "Text 1",
      "type": "text",
      "display": true,
      "eventExpression": [
        {
          "condition": "if",
          "expression": "@txt_text2==0",
          "expressionObject": [
            {
              "type": "paragraph",
              "children": [
                {"text": ""},
                {
                  "type": "mention",
                  "character": "txt_text2",
                  "children": [
                    {"text": ""}
                  ]
                },
                {"text": "==0"}
              ]
            }
          ],
          "properties": [
            {
              "field": "@txt_text1=",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "txt_text1",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "=''"}
                  ]
                }
              ]
            },
            {
              "field": "@valid='valid nếu text 2 rỗng'",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "valid",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "='valid nếu text 2 rỗng'"}
                  ]
                }
              ]
            }
          ]
        },
        {
          "condition": "elseif",
          "expression": "@txt_text2<10",
          "expressionObject": [
            {
              "type": "paragraph",
              "children": [
                {"text": ""},
                {
                  "type": "mention",
                  "character": "txt_text2",
                  "children": [
                    {"text": ""}
                  ]
                },
                {"text": "<10"}
              ]
            }
          ],
          "properties": [
            {
              "field": "@txt_text1='<10 va ten truong thay doi'",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "txt_text1",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "='<10 va ten truong thay doi'"}
                  ]
                }
              ]
            },
            {
              "field": "@ten_truong='ten thay doi'",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "ten_truong",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "='ten thay doi'"}
                  ]
                }
              ]
            },
            {
              "field": "@bat_buoc=true",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "bat_buoc",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "=true"}
                  ]
                }
              ]
            }
          ]
        },
        {
          "condition": "elseif",
          "expression": "@txt_text2<20",
          "expressionObject": [
            {
              "type": "paragraph",
              "children": [
                {"text": ""},
                {
                  "type": "mention",
                  "character": "txt_text2",
                  "children": [
                    {"text": ""}
                  ]
                },
                {"text": "<20"}
              ]
            }
          ],
          "properties": [
            {
              "field": "@txt_text1='<20 va disabled'",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "txt_text1",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "='<20 va disabled'"}
                  ]
                }
              ]
            },
            {
              "field": "@chi_duoc_doc=true",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "chi_duoc_doc",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "=true"}
                  ]
                }
              ]
            },
            {
              "field": "@huong_dan='123'",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "huong_dan",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "='123'"}
                  ]
                }
              ]
            }
          ]
        },
        {
          "condition": "elseif",
          "expression": "@txt_text2<30",
          "expressionObject": [
            {
              "type": "paragraph",
              "children": [
                {"text": ""},
                {
                  "type": "mention",
                  "character": "txt_text2",
                  "children": [
                    {"text": ""}
                  ]
                },
                {"text": "<30"}
              ]
            }
          ],
          "properties": [
            {
              "field": "@txt_text1=",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "txt_text1",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "=''"}
                  ]
                }
              ]
            },
            {
              "field": "@hien_thi=false",
              "fieldObject": [
                {
                  "type": "paragraph",
                  "children": [
                    {"text": ""},
                    {
                      "type": "mention",
                      "character": "hien_thi",
                      "children": [
                        {"text": ""}
                      ]
                    },
                    {"text": "=false"}
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "splitter": "spl__383d21fd-b7e0-4922-b5e0-478f4d4d2f79",
      "id": "txt_text2",
      "name": "txt_text2",
      "label": "Text 2",
      "type": "text",
      "display": true,
      "default_value": ""
    },
    {
      "id": "fieldItem_75f5aac7-97df-43fb-8194-62f09164b789",
      "step": "",
      "row": "row__4350207f-d0dd-4224-aa44-32a78fea82dc",
      "col": "column_80a4f48e-82a3-49b3-8d2f-c96369cf149e",
      "rowSortOrder": 4,
      "colSortOrder": 0,
      "fieldSortOrder": 3,
      "tab": "",
      "tabItem": "",
      "parentName": "",
      "splitter": "spl__383d21fd-b7e0-4922-b5e0-478f4d4d2f79",
      "validations": {"required": true},
      "type": "select",
      "label": "Hình thức",
      "name": "slt_hinhThuc",
      "eventExpression": [],
      "isHorizontal": 2,
      "fontWeight": "",
      "display": true,
      "displayName": "Hình thức",
      "isClearOnClone": false,
      "typeOption": "normal",
      "optionType": "normal",
      "option": [
        {"label": "Đề nghị thanh toán", "value": "D"},
        {"label": "Đề nghị tạm ứng", "tooltip": "", "value": "B"}
      ],
      "value": "D",
      "optionConfig": {
        "isMulti": false,
        "orgchart": [],
        "isLoadAll": false,
        "orgchartType": "",
        "valueField": "",
        "feedDataTo": [
          {
            "input": "@txt_soTienAbc",
            "fieldFeed": "Nguyên giá",
            "objectInput": [
              {
                "type": "paragraph",
                "children": [
                  {"text": ""},
                  {
                    "type": "mention",
                    "character": "txt_soTienAbc",
                    "children": [
                      {"text": ""}
                    ]
                  },
                  {"text": ""}
                ]
              }
            ]
          },
          {
            "input": "@txt_bangChu",
            "fieldFeed": "Bằng chữ",
            "objectInput": [
              {
                "type": "paragraph",
                "children": [
                  {"text": ""},
                  {
                    "type": "mention",
                    "character": "txt_bangChu",
                    "children": [
                      {"text": ""}
                    ]
                  },
                  {"text": ""}
                ]
              }
            ]
          }
        ],
        "orgchartExpression": [],
        "onlyLoadOrgChartByUserLogin": false,
        "masterDataType": "ALL",
        "masterDataName": "",
        "masterDataId": 1036,
        "masterDataDisplay": ["Mã tài sản"],
        "masterDataValue": "Mã tài sản",
        "mdExpression": [],
        "apiURLBase": "",
        "apiURL": "",
        "apiMethod": "POST",
        "apiValue": "",
        "fieldGetData": "",
        "addHeaderTemplate": "",
        "responseTemplate": "",
        "apiExpression": [],
        "selectInherit": [
          {
            "processId": "",
            "ticketProcDefId": "",
            "ticketId": "",
            "taskId": "",
            "taskKey": "",
            "formKey": "",
            "inputName": ""
          }
        ]
      },
      "isMulti": false,
      "displayFieldObject": [],
      "isUserCurrentUserLogin": false,
      "valueField": "",
      "readonly": false,
      "specialField": ["share"]
    }
  ]
};

const testTable = {
        "id": "table_693e40fc-c3a8-4b6b-88e4-551ba5698135",
        "step": "",
        "row": "row_56f70409-5f4b-45d1-b4b6-32ed9fe5f730",
        "col": "col_3ed433da-d49e-43b9-a450-17638514b2b3",
        "rowSortOrder": 0,
        "colSortOrder": 0,
        "fieldSortOrder": 0,
        "tab": "",
        "tabItem": "",
        "parentName": "",
        "splitter": "",
        "columns": [
            {
                "id": "fieldItem_1d94fd4e-ae0e-4375-bc8d-00daefd91a1d",
                "step": "",
                "row": "",
                "col": "colTable_ca8636f8-e2f4-4080-91a7-6195ff8dfff1",
                "rowSortOrder": 0,
                "colSortOrder": 0,
                "fieldSortOrder": 0,
                "tabItem": "",
                "tab": "",
                "name": "txt_text1",
                "label": "Text1",
                "value": "",
                "type": "text",
                "disabled": false,
                "readonly": false,
                "isHorizontal": 2,
                "display": true,
                "validateType": "string",
                "validations": {
                    "required": false
                },
                "parentName": "tbl_table1",
                "fontWeight": "",
                "isMulti": false,
                "fileType": "normal",
                "isUserCurrentUserLogin": false,
                "isClearOnClone": false,
                "strCondition": "",
                "strConditionConvert": "",
                "chkCurrencyType": true,
                "width": "",
                "valueField": "",
                "typeOption": "normal"
            },
            {
                "id": "fieldItem_ad85ba78-f106-45d0-8e76-6577305ce295",
                "step": "",
                "row": "",
                "col": "colTable_a35542df-cff7-4264-beb5-c34984140ac9",
                "rowSortOrder": 0,
                "colSortOrder": 0,
                "fieldSortOrder": 0,
                "tabItem": "",
                "tab": "",
                "name": "txt_text2",
                "label": "Text2",
                "value": "",
                "type": "text",
                "disabled": false,
                "readonly": false,
                "isHorizontal": 2,
                "display": true,
                "validateType": "string",
                "validations": {
                    "required": false
                },
                "parentName": "tbl_table1",
                "fontWeight": "",
                "isMulti": false,
                "fileType": "normal",
                "isUserCurrentUserLogin": false,
                "isClearOnClone": false,
                "strCondition": "",
                "strConditionConvert": "",
                "chkCurrencyType": true,
                "width": "",
                "valueField": "",
                "typeOption": "normal"
            }
        ],
        "validations": {
            "required": false
        },
        "type": "table",
        "label": "Table1",
        "name": "tbl_table1",
        "placeholder": "",
        "eventExpression": [],
        "isHorizontal": 2,
        "fontWeight": "",
        "display": true,
        "suggestText": "",
        "isCloneInSpecFlow": false,
        "displayName": "Table1",
        "isClearOnClone": false,
        "isConditionAuth": false,
        "hideTableTitle": true,
        "optionType": "normal",
        "typeOption": "normal",
        "option": [
            {
                "label": "",
                "value": ""
            }
        ],
        "value": "",
        "optionConfig": {
            "isMulti": false,
            "orgchart": [],
            "orgchartType": "",
            "displayField": "",
            "valueField": "",
            "orgchartExpression": [],
            "masterDataType": "",
            "masterDataName": "",
            "masterDataId": "",
            "masterDataValue": "",
            "mdExpression": [],
            "apiURLBase": "",
            "apiURL": "",
            "apiMethod": "POST",
            "apiValue": "",
            "fieldGetData": "",
            "addHeaderTemplate": "",
            "responseTemplate": "",
            "apiExpression": [],
            "tableColumns": [
                {
                    "id": 0,
                    "col": "colTable_ca8636f8-e2f4-4080-91a7-6195ff8dfff1",
                    "typeOption": "normal",
                    "label": "value0",
                    "width": "",
                    "valueField": ""
                },
                {
                    "id": "fieldItem_ad85ba78-f106-45d0-8e76-6577305ce295",
                    "col": "colTable_a35542df-cff7-4264-beb5-c34984140ac9",
                    "typeOption": "normal",
                    "label": "Text2",
                    "width": "",
                    "valueField": ""
                }
            ],
            "matrixDataConfig": [
                []
            ]
        },
        "displayField": "",
        "valueField": "",
        "readonly": false,
        "isCallApiHandWork": false,
        "callApiButtonName": "Gọi API",
        "isHideAddColumnTable": false,
        "isHideDeleteColumnTable": false,
        "isHideGetTemplateTable": false,
        "isHideImportExcelTable": false,
        "isHideStt": false,
        "isAddRowCountInData": false,
        "specialField": [],
        "isHideSearchTable": false
    };