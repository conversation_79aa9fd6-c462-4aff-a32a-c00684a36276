
import 'package:eapprove/models/service/service_data_model.dart';

class ServiceResponseModel {
  final int code;
  final String message;
  final List<ServiceData> data;

  const ServiceResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ServiceResponseModel.fromJson(Map<String, dynamic> json) {
    return ServiceResponseModel(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: (json['data'] as List<dynamic>?)
          ?.map((item) => ServiceData.fromJson(item))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((d) => d.toJson()).toList(),
    };
  }
}

class ServiceSpecialResponseModel {
  final int code;
  final String message;
  final ServiceData data;

  const ServiceSpecialResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ServiceSpecialResponseModel.fromJson(Map<String, dynamic> json) {
    return ServiceSpecialResponseModel(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: ServiceData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }
}