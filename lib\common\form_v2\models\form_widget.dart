import 'package:flutter/material.dart';

enum WidgetType {
  text,
  number,
  date,
  dropdown,
  checkbox,
  radio,
  splitter,
  custom
}

class FormWidget {
  final String id;
  final String name;
  final String label;
  final WidgetType type;
  final String? step;
  final String? row;
  final String? col;
  final int? rowSortOrder;
  final int? colSortOrder;
  final int? fieldSortOrder;
  final String? tab;
  final String? tabItem;
  final String? parentName;
  final String? splitter;
  final Map<String, dynamic>? validations;
  final String? placeholder;
  final List<dynamic>? eventExpression;
  final int? isHorizontal;
  final String? fontWeight;
  final bool display;
  final String? suggestText;
  final String? value;
  final bool readonly;
  final bool forceView;
  final bool useTimeDefault;
  final String? splitterDesc;
  final int? splitterOpen;
  final String? default_value;

  FormWidget({
    required this.id,
    required this.name,
    required this.label,
    required this.type,
    this.step,
    this.row,
    this.col,
    this.rowSortOrder,
    this.colSortOrder,
    this.fieldSortOrder,
    this.tab,
    this.tabItem,
    this.parentName,
    this.splitter,
    this.validations,
    this.placeholder,
    this.eventExpression,
    this.isHorizontal,
    this.fontWeight,
    this.display = true,
    this.suggestText,
    this.value,
    this.readonly = false,
    this.forceView = false,
    this.useTimeDefault = false,
    this.splitterDesc,
    this.splitterOpen,
    this.default_value,
  });

  factory FormWidget.fromJson(Map<String, dynamic> json) {
    return FormWidget(
      id: json['id'] as String,
      name: json['name'] as String,
      label: json['label'] as String,
      type: WidgetType.values.firstWhere(
        (e) => e.toString() == 'WidgetType.${json['type']}',
        orElse: () => WidgetType.custom,
      ),
      step: json['step'] as String?,
      row: json['row'] as String?,
      col: json['col'] as String?,
      rowSortOrder: json['rowSortOrder'] as int?,
      colSortOrder: json['colSortOrder'] as int?,
      fieldSortOrder: json['fieldSortOrder'] as int?,
      tab: json['tab'] as String?,
      tabItem: json['tabItem'] as String?,
      parentName: json['parentName'] as String?,
      splitter: json['splitter'] as String?,
      validations: json['validations'] as Map<String, dynamic>?,
      placeholder: json['placeholder'] as String?,
      eventExpression: json['eventExpression'] as List<dynamic>?,
      isHorizontal: json['isHorizontal'] as int?,
      fontWeight: json['fontWeight'] as String?,
      display: json['display'] as bool? ?? true,
      suggestText: json['suggestText'] as String?,
      value: json['value'] as String?,
      readonly: json['readonly'] as bool? ?? false,
      forceView: json['forceView'] as bool? ?? false,
      useTimeDefault: json['useTimeDefault'] as bool? ?? false,
      splitterDesc: json['splitterDesc'] as String?,
      splitterOpen: json['splitterOpen'] as int?,
      default_value: json['default_value'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'label': label,
      'type': type.toString().split('.').last,
      'step': step,
      'row': row,
      'col': col,
      'rowSortOrder': rowSortOrder,
      'colSortOrder': colSortOrder,
      'fieldSortOrder': fieldSortOrder,
      'tab': tab,
      'tabItem': tabItem,
      'parentName': parentName,
      'splitter': splitter,
      'validations': validations,
      'placeholder': placeholder,
      'eventExpression': eventExpression,
      'isHorizontal': isHorizontal,
      'fontWeight': fontWeight,
      'display': display,
      'suggestText': suggestText,
      'value': value,
      'readonly': readonly,
      'forceView': forceView,
      'useTimeDefault': useTimeDefault,
      'splitterDesc': splitterDesc,
      'splitterOpen': splitterOpen,
      'default_value': default_value,
    };
  }

  FormWidget copyWith({
    String? id,
    String? name,
    String? label,
    WidgetType? type,
    String? step,
    String? row,
    String? col,
    int? rowSortOrder,
    int? colSortOrder,
    int? fieldSortOrder,
    String? tab,
    String? tabItem,
    String? parentName,
    String? splitter,
    Map<String, dynamic>? validations,
    String? placeholder,
    List<dynamic>? eventExpression,
    int? isHorizontal,
    String? fontWeight,
    bool? display,
    String? suggestText,
    String? value,
    bool? readonly,
    bool? forceView,
    bool? useTimeDefault,
    String? splitterDesc,
    int? splitterOpen,
    String? default_value,
  }) {
    return FormWidget(
      id: id ?? this.id,
      name: name ?? this.name,
      label: label ?? this.label,
      type: type ?? this.type,
      step: step ?? this.step,
      row: row ?? this.row,
      col: col ?? this.col,
      rowSortOrder: rowSortOrder ?? this.rowSortOrder,
      colSortOrder: colSortOrder ?? this.colSortOrder,
      fieldSortOrder: fieldSortOrder ?? this.fieldSortOrder,
      tab: tab ?? this.tab,
      tabItem: tabItem ?? this.tabItem,
      parentName: parentName ?? this.parentName,
      splitter: splitter ?? this.splitter,
      validations: validations ?? this.validations,
      placeholder: placeholder ?? this.placeholder,
      eventExpression: eventExpression ?? this.eventExpression,
      isHorizontal: isHorizontal ?? this.isHorizontal,
      fontWeight: fontWeight ?? this.fontWeight,
      display: display ?? this.display,
      suggestText: suggestText ?? this.suggestText,
      value: value ?? this.value,
      readonly: readonly ?? this.readonly,
      forceView: forceView ?? this.forceView,
      useTimeDefault: useTimeDefault ?? this.useTimeDefault,
      splitterDesc: splitterDesc ?? this.splitterDesc,
      splitterOpen: splitterOpen ?? this.splitterOpen,
      default_value: default_value ?? this.default_value,
    );
  }
} 