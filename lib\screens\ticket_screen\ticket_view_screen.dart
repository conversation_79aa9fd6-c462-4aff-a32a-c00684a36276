import 'dart:developer';
import 'dart:developer' as developer;

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/work_flow_event.dart';
import 'package:eapprove/models/form/service_by_id_with_submission.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/screens/handle_ticket/handle_ticket_screen.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/setting_screen.dart';
import 'package:eapprove/screens/ticket_screen/search_pyc_screen.dart';
import 'package:eapprove/screens/ticket_screen/test/tab_bar_test.dart';
import 'package:eapprove/screens/ticket_screen/widgets/filter_pyc_screen.dart';
import 'package:eapprove/screens/ticket_screen/widgets/tab_content.dart';
import 'package:eapprove/screens/ticket_screen/widgets/ticket_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

class TicketViewScreen extends StatefulWidget {
  final TicketScreenModel screenModel;
  final int initialTabIndex;
  final int filterIndex;

  const TicketViewScreen({
    Key? key,
    required this.screenModel,
    this.initialTabIndex = 0,
    required this.filterIndex,
  }) : super(key: key);

  @override
  State<TicketViewScreen> createState() => _TicketViewScreenState();
}

class _TicketViewScreenState extends State<TicketViewScreen> {
  // UI state
  bool _isResetAction = false;
  bool _forceReload = false;
  int _currentTabIndex = 0;
  Map<String, dynamic> _currentFilters = {};
  bool _useSearchFilterResponse = false;
  bool _isFilterApplied = false;

  // 1. GlobalKey list for each TabContent
  late final List<GlobalKey<TabContentState>> _tabKeys;

  @override
  void initState() {
    super.initState();
    _currentTabIndex = widget.initialTabIndex;
    _tabKeys = List.generate(
      widget.screenModel.tabTitles.length,
      (_) => GlobalKey<TabContentState>(),
    );

    // Initial fetch filter-list
    final type = _getFilterType(widget.filterIndex);
    if (type != null) {
      context.read<PycBloc>().add(FetchFilter(FilterRequestModel(type: type)));
    }

    // Initial fetch tickets for first tab
    final title = widget.screenModel.tabTitles[_currentTabIndex];
    final code = _getStatusCodeByFilterIndex(widget.filterIndex, title);
    context.read<PycBloc>().add(FetchStatusTicket(
          StatusTicketRequest(
            code: code,
            type: "system",
            page: 1,
            limit: 9999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));
  }

  // 2. Handle search icon tap
  Future<void> _toggleSearch() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (_) => PycSearchScreen(
          filterIndex: widget.filterIndex,
          tabIndex: _currentTabIndex,
        ),
      ),
    );
    if (result == true) {
      _reloadCurrentTab();
    }
  }

  Widget _buildFilterDropdown(BuildContext context) {
    return BlocBuilder<PycBloc, PycState>(
      builder: (context, state) {
        final currentType = _getFilterType(widget.filterIndex);
        final filteredList =
            state.filterList.where((item) => item.type == currentType).toList();
        final pinnedList = filteredList
            .where((item) => item.status?.toLowerCase() == 'pin')
            .toList();

        final options = filteredList.map((item) {
          return SelectItem(
            label: item.name ?? 'Không tên',
            value: item.id?.toString() ?? '',
          );
        }).toList();

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;

          final hasOldFilter = _currentFilters.containsKey('filterId');
          final isDropdownEmpty = filteredList.isEmpty;

          if (isDropdownEmpty && hasOldFilter) {
            setState(() {
              _currentFilters.remove('filterId');
            });
            return;
          }

          if (pinnedList.isNotEmpty && _currentFilters['filterId'] == null) {
            final firstPinned = pinnedList.first;
            setState(() {
              _currentFilters['filterId'] = firstPinned.id;
            });

            WidgetsBinding.instance.addPostFrameCallback((_) {
              _tabKeys[_currentTabIndex].currentState?.refreshTickets();
            });
          }
        });

        return Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          padding: EdgeInsets.only(bottom: 8.h),
          child: CustomDropdownMenu(
            imgSearchPath: StringImage.ic_search,
            searchHint: 'Tìm kiếm tên dịch vụ',
            useRootNavigator: true,
            enableSearch: true,
            dropdownHeight: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: Colors.white,
            ),
            placeholderStyle: getTypoSkin()
                .buttonRegular14
                .copyWith(color: getColorSkin().ink1),
            label: '',
            showLabel: false,
            options: options,
            onSelected: (value) {
              filteredList.firstWhere(
                (item) => item.id.toString() == value?.value,
                orElse: () => const FilterData(),
              );
              setState(() {
                _currentFilters['filterId'] = int.tryParse(value?.value ?? '');
                _useSearchFilterResponse = false;
                _isFilterApplied = true;
              });
              _tabKeys[_currentTabIndex].currentState?.refreshTickets();
            },
            isDisabled: options.isEmpty,
            validator: (value) => null,
          ),
        );
      },
    );
  }

  void _dispatchOnTabChange(int newIndex) {
    final newTitle = widget.screenModel.tabTitles[newIndex];
    final newCode = _getStatusCodeByFilterIndex(widget.filterIndex, newTitle);
    context.read<PycBloc>().add(FetchStatusTicket(
          StatusTicketRequest(
            code: newCode,
            type: "system",
            page: 1,
            limit: 9999,
            search: "",
            sortBy: "id",
            sortType: "DESC",
            chartId: "",
            status: ["active"],
          ),
        ));

    final oldTitle = widget.screenModel.tabTitles[_currentTabIndex];
    final wasFilter = oldTitle.trim().toLowerCase().contains('lọc');
    final isFilter = newTitle.trim().toLowerCase().contains('lọc');
    if (wasFilter && !isFilter) {
      context.read<PycBloc>().add(FetchStatusTicket(
            StatusTicketRequest(
              code: newCode,
              type: "system",
              page: 1,
              limit: 9999,
              search: "",
              sortBy: "id",
              sortType: "DESC",
              chartId: "",
              status: ["active"],
            ),
          ));
    } else if (isFilter && !wasFilter) {
      // entered filter tab
      final type = _getFilterType(widget.filterIndex);
      if (type != null)
        context
            .read<PycBloc>()
            .add(FetchFilter(FilterRequestModel(type: type)));
    }
  }

  @override
  Widget build(BuildContext context) {
    final fIdx = widget.screenModel.tabTitles
        .indexWhere((t) => t.trim().toLowerCase().contains('lọc'));
    final dropdownIdx = fIdx != -1 ? [fIdx] : <int>[];

    return GradientBackground(
      showBottomImage: false,
      showUpperImage: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          elevation: 0,
          scrolledUnderElevation: 0,
          backgroundColor: Colors.transparent,
          leading: IconButton(
            iconSize: 24.w.h,
            highlightColor: Colors.transparent,
            icon: SvgPicture.asset(StringImage.ic_arrow_left),
            onPressed: () => Navigator.pop(context, true),
          ),
          title: Text(
            widget.screenModel.title,
            style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w500,
                color: getColorSkin().ink1,
                height: 24.h,
                overflow: TextOverflow.ellipsis),
          ),
          titleSpacing: -10.w,
          actions: [
            Transform.translate(
              offset: Offset(20.w, 0),
              child: IconButton(
                  iconSize: 24.w.h,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  icon: SvgPicture.asset(StringImage.ic_search),
                  onPressed: _toggleSearch),
            ),
            Transform.translate(
              offset: Offset(10.w, 0),
              child: IconButton(
                iconSize: 24.w.h,
                highlightColor: Colors.transparent,
                icon: SvgPicture.asset(
                  _isFilterApplied
                      ? StringImage.ic_filter
                      : StringImage.ic_unfilter,
                ),
                onPressed: () {
                  _handleFilter(context, widget.filterIndex);
                },
              ),
            ),
            IconButton(
              highlightColor: Colors.transparent,
              icon: SvgPicture.asset(StringImage.ic_user),
              onPressed: () {
                GoRouter.of(context).push(EAppSettingScreen.routeName);
                // ModuleNavigationHandler.navigateToScreen(
                //     context, EAppSettingScreen.routeName);
              },
            ),
          ],
        ),
        body: RefreshIndicator(
          backgroundColor: getColorSkin().white,
          color: getColorSkin().primaryBlue,
          onRefresh: () async {
            await _tabKeys[_currentTabIndex].currentState?.refreshTickets();
          },
          child: CustomTabBarTest(
            dropdownTabIndex: dropdownIdx,
            dropdown: _buildFilterDropdown(context),
            tabTitles: widget.screenModel.tabTitles,
            initialIndex: _currentTabIndex,
            tabContents:
                List.generate(widget.screenModel.tabTitles.length, (i) {
              final title = widget.screenModel.tabTitles[i];
              return TabContent(
                key: _tabKeys[i],
                tabTitle: title,
                filterIndex: widget.filterIndex,
                currentFilters: _currentFilters,
                useSearchFilterResponse: _useSearchFilterResponse,
                forceReload: _forceReload,
                onTicketTap: (ticket) async {
                  debugPrint("onTicketTap");
                  final currentTabTitle = widget
                      .screenModel.tabTitles[_currentTabIndex]
                      .trim()
                      .toLowerCase();
                  final procDefId = ticket.ticketProcDefId ?? '';
                  final procInstId = extractProcInstId(ticket);
                  context.read<WorkflowBloc>().add(LoadWorkflow(
                        procInstId: procInstId,
                        procDefId: procDefId,
                        fromNodeId: '',
                      ));
                  if (currentTabTitle == "nháp" && ticket is MyPycContent) {
                    final serviceData = ServiceData(
                      id: ticket.serviceId,
                      serviceName: ticket.procServiceName,
                      procDefId: ticket.ticketProcDefId,
                      processId: ticket.processId,
                    );
                    log('Service Id: ${ticket.serviceId}');
                    final formBloc = BlocProvider.of<FormBloc>(context);
                    context
                        .read<BottomNavBloc>()
                        .add(const SetBottomNavVisibility(false));
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => BlocProvider.value(
                          value: formBloc,
                          child: TicketFormScreen(
                            procDefId: serviceData.procDefId!,
                            title: serviceData.serviceName ?? '',
                            processId: serviceData.processId,
                            ticketId: serviceData.id,
                          ),
                        ),
                      ),
                    );
                    context
                        .read<BottomNavBloc>()
                        .add(const SetBottomNavVisibility(true));
                    _tabKeys[_currentTabIndex].currentState?.refreshTickets();
                    return;
                  }
                  debugPrint(
                      'Ticket tapped: runtimeType=${ticket.runtimeType}');
                  debugPrint('Ticket properties: ${ticket.toString()}');

                  try {
                    debugPrint('ID=${ticket.id}');
                    if (ticket.name != null) debugPrint('name=${ticket.name}');
                    if (ticket.title != null)
                      debugPrint('title=${ticket.title}');
                    if (ticket.ticketTitle != null)
                      debugPrint('ticketTitle=${ticket.ticketTitle}');
                  } catch (e) {
                    debugPrint('Error accessing properties: $e');
                  }

                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (_) => HandleTicketScreen(ticket: ticket)),
                  );
                  final title = widget.screenModel.tabTitles[_currentTabIndex];
                  final code =
                      _getStatusCodeByFilterIndex(widget.filterIndex, title);
                  context.read<PycBloc>().add(FetchStatusTicket(
                        StatusTicketRequest(
                          code: code,
                          type: "system",
                          page: 1,
                          limit: 9999,
                          status: ["active"],
                          sortBy: "id",
                          sortType: "DESC",
                          chartId: "",
                          search: "",
                        ),
                      ));

                  _tabKeys[_currentTabIndex].currentState?.refreshTickets();
                },
              );
            }),
            onTabChanged: (index) {
              if (_currentTabIndex == index) return;
              setState(() {
                _currentTabIndex = index;
                _currentFilters.clear();
                _useSearchFilterResponse = false;
                _isFilterApplied = false;
              });
              _dispatchOnTabChange(index);
              _tabKeys[index].currentState?.refreshTickets();
            },
            selectedBackgroundColor: const Color(0xffF2FBFE),
            unselectedBackgroundColor: getColorSkin().grey3Background,
            selectedTextColor: getColorSkin().secondaryColor1,
            unselectedTextColor: getColorSkin().subtitle,
            tabBarPadding: EdgeInsets.only(left: 12.w, bottom: 8.h),
            labelPadding: EdgeInsets.symmetric(horizontal: 4.h),
            animationDuration: const Duration(milliseconds: 300),
            contentPadding:
                EdgeInsets.symmetric(horizontal: 10.w, vertical: 16.h),
          ),
        ),
      ),
    );
  }

  void _handleFilter(BuildContext context, int filterIndex) {
    final filterTabIndex = getFilterTabIndex(widget.screenModel.tabTitles);
    final isFilterTab = _currentTabIndex == filterTabIndex;
    FilterPycBottomSheet.show(
      context: context,
      filterIndex: widget.filterIndex,
      isFilterTab: isFilterTab,
      initialFilters: _currentFilters,
      onApplyFilter: (filters) {
        final filterTabIndex = getFilterTabIndex(widget.screenModel.tabTitles);
        final isDefault = _checkFilterIsDefault(filters);

        setState(() {
          _currentFilters = filters;
          _useSearchFilterResponse = filters.keys.length > 1;
          _isFilterApplied = true;
          _isFilterApplied = _isResetAction ? !isDefault : true;
          if (filterTabIndex != -1) {
            _currentTabIndex = filterTabIndex;
          }
        });
        _isResetAction = false;
        final statusCode = _getStatusCodeByFilterIndex(
            widget.filterIndex, widget.screenModel.tabTitles[_currentTabIndex]);
        context.read<PycBloc>().add(FetchStatusTicket(
              StatusTicketRequest(
                code: statusCode,
                type: "system",
                page: 1,
                limit: 99999,
                search: "",
                sortBy: "id",
                sortType: "DESC",
                chartId: "",
                status: ["active"],
              ),
            ));
      },
      onResetFilter: () {
        setState(() {
          _isFilterApplied = false;
          _isResetAction = true;
        });
      },
    );
  }

  void _reloadCurrentTab() {
    setState(() {
      _forceReload = true;
      _useSearchFilterResponse = false;
      _currentFilters.clear();

      final statusCode = _getStatusCodeByFilterIndex(
          widget.filterIndex, widget.screenModel.tabTitles[_currentTabIndex]);
      context.read<PycBloc>().add(FetchStatusTicket(
            StatusTicketRequest(
              code: statusCode,
              type: "system",
              page: 1,
              limit: 99999,
              search: "",
              sortBy: "id",
              sortType: "DESC",
              chartId: "",
              status: ["active"],
            ),
          ));
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _forceReload = false;
      });
      _tabKeys[_currentTabIndex].currentState?.refreshTickets();
    });
  }

  String? _getFilterType(int idx) {
    switch (idx) {
      case 0:
        return 'ticket';
      case 1:
        return 'execution';
      case 2:
        return 'approval';
      case 3:
        return 'assistant';
      default:
        return null;
    }
  }

  String _getStatusCodeByFilterIndex(int filterIdx, String title) {
    // final n = title.trim().toLowerCase();
    // final isTaskTab = (filterIdx == 1 && n == 'xử lý')  (filterIdx == 2 && n.contains('chờ'));
    return 'STATUS_TICKET';
  }

  bool _checkFilterIsDefault(Map<String, dynamic> filters) {
    for (final entry in filters.entries) {
      final value = entry.value;
      if (value == null) continue;
      if (value is bool && value == false) continue;
      if (value is List &&
          (value.isEmpty || (value.length == 1 && value.first == '-1')))
        continue;
      if (value.toString().isEmpty) continue;

      return false;
    }
    return true;
  }

  int getFilterTabIndex(List<String> tabs) {
    return tabs.indexWhere((t) => t.trim().toLowerCase().contains('lọc'));
  }

  String extractProcInstId(dynamic ticket) {
    try {
      if (ticket is MyPycContent || ticket is AssisContent) {
        return (ticket as dynamic).ticketId?.toString() ?? '';
      } else if (ticket is ProcContent || ticket is ApproveContent) {
        return ticket.procInstId;
      }
    } catch (e) {
      developer.log('⚠️ Failed to extract procInstId from ticket: $e');
    }
    return '';
  }
}
