import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_bloc.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_state.dart';
import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/screens/authorize_management/authorize_information_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_info_column.dart';
import 'package:flutter_sdk/widgets/custom_status_label.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_state.dart';
import 'package:eapprove/models/authorize_management/list_user_info_response.dart';
import 'package:eapprove/utils/user_info_display_utils.dart';

class AuthorizeCard extends StatefulWidget {
  final List<AuthorizeItemData> data;
  final Future<void> Function()? onRefresh;
  final Future<void> Function()? updateAuthorizeManagementList;

  const AuthorizeCard(
      {super.key,
      this.data = const [],
      this.onRefresh,
      this.updateAuthorizeManagementList});

  @override
  State<AuthorizeCard> createState() => _AuthorizeCardState();
}

class _AuthorizeCardState extends State<AuthorizeCard> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_isBottom && widget.updateAuthorizeManagementList != null) {
      if (context.read<AuthorizeManagementBloc>().state.isLoadingMore &&
          !context.read<AuthorizeManagementBloc>().state.isPaginating) {
        widget.updateAuthorizeManagementList!();
      }
    }
  }

  bool get _isBottom {
    if (_scrollController.position.maxScrollExtent == 0) {
      return false;
    }
    return _scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 300;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: RefreshIndicator(
            onRefresh: widget.onRefresh ?? () async {}, // Handle null case here
            child: widget.data.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.assignment_outlined,
                          size: 48,
                          color: getColorSkin().secondaryText,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Không có dữ liệu',
                          style: getTypoSkin()
                              .medium16
                              .copyWith(color: getColorSkin().secondaryText),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
                    physics: const AlwaysScrollableScrollPhysics(),
                    itemCount: context
                            .read<AuthorizeManagementBloc>()
                            .state
                            .isLoadingMore
                        ? widget.data.length + 1
                        : widget.data.length,
                    itemBuilder: (context, index) {
                      if (context
                              .read<AuthorizeManagementBloc>()
                              .state
                              .isLoadingMore &&
                          index == widget.data.length) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      return PaymentRequestCard(request: widget.data[index]);
                    },
                  ),
          ),
        ),
      ],
    );
  }
}

class PaymentRequestCard extends StatefulWidget {
  final AuthorizeItemData request;
  final Function(AuthorizeItemData)? onTap;

  const PaymentRequestCard({
    super.key,
    required this.request,
    this.onTap,
  });

  @override
  State<PaymentRequestCard> createState() => _PaymentRequestCardState();
}

class _PaymentRequestCardState extends State<PaymentRequestCard> {
  Map<String, UserInfo> _userInfoMap = {};

  @override
  void initState() {
    super.initState();
  }

  String _getUserDisplayName(String userId) {
    final userInfo = _userInfoMap[userId];
    if (userInfo == null) return userId;

    return UserInfoDisplayUtils.getUserDisplayName(userInfo);
  }

  String _getUserDisplayTitle(String userId) {
    final userInfo = _userInfoMap[userId];
    if (userInfo == null) return userId;

    return UserInfoDisplayUtils.getUserSubtitle(userInfo);
  }

  String _formatDate(dynamic timestamp) {
    debugPrint('Timestamp: $timestamp');
    if (timestamp == null) return "Không có ngày";
    final date = DateTime.parse(timestamp);
    return DateFormat("dd/MM/yyyy").format(date);
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return BlocBuilder<UserInfoBloc, UserInfoState>(
      builder: (context, userState) {
        if (userState is UserInfoLoaded) {
          _userInfoMap = {
            for (var user in userState.response.data.content)
              user.username: user
          };
        }

        final isActive = widget.request.status == 0;
        final statusColor = isActive ? Colors.blue : Colors.red;
        final statusText = isActive ? 'Đang hiệu lực' : 'Hết hiệu lực';
        return GestureDetector(
          onTap: () {
            if (widget.onTap != null) {
              widget.onTap!(widget.request);
            }
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AuthorizationDetail(
                  title: isTablet
                      ? " ${widget.request.requestCode} - ${widget.request.assignName}"
                      : "${widget.request.assignName}",
                  request: widget.request,
                ),
              ),
            ).then((result) {
              if (result != null) {
                debugPrint('Selected request: $result');
              }
            });
          },
          child: Card(
            elevation: 0,
            margin: EdgeInsets.only(bottom: 8.h),
            color: getColorSkin().white,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomColumnInfo(
                    title:
                        "${widget.request.requestCode} - ${widget.request.assignName}",
                    subtitle: widget.request.serviceRange[0].serviceName,
                    subtitleStyle: getTypoSkin()
                        .label5Regular
                        .copyWith(color: getColorSkin().secondaryText),
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            StringImage.ic_calendar,
                            width: 16.w,
                            height: 16.h,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDate(widget.request.createdDate),
                            style: getTypoSkin().label5Regular.copyWith(
                                  color: getColorSkin().secondaryText,
                                ),
                          ),
                        ],
                      ),
                      CustomStatusLabel(
                        label: statusText,
                        backgroundColor: statusColor,
                        borderSide: BorderSide.none,
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  CustomColumnInfo(
                    title: _getUserDisplayName(widget.request.assignUser ?? ''),
                    subtitle:
                        _getUserDisplayTitle(widget.request.assignUser ?? ''),
                    subtitleStyle: getTypoSkin()
                        .label4Regular
                        .copyWith(color: getColorSkin().ink1),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'Ủy quyền cho',
                    style: getTypoSkin()
                        .label5Regular
                        .copyWith(color: getColorSkin().primaryText),
                  ),
                  SizedBox(height: 4.h),
                  CustomColumnInfo(
                    subtitleStyle: getTypoSkin()
                        .label4Regular
                        .copyWith(color: getColorSkin().ink1),
                    title:
                        _getUserDisplayName(widget.request.assignedUser ?? ''),
                    subtitle:
                        _getUserDisplayTitle(widget.request.assignedUser ?? ''),
                    titleStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
