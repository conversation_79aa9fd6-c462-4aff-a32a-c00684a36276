class MyPycRequestModel {
  final String? search;
  final int? page;
  final int? limit;
  final String? ticketStatus;
  final String? sortBy;
  final String? sortType;
  final List<String>? listService;
  final List<String>? listTicketTitle;
  final List<String>? chartNodeNames;
  final List<String>? listChartNodeName;
  final List<String>? charts;
  final List<String>? listUser;
  final List<String>? listStatus;
  final String? pageType;
  final String? fromDate;
  final String? toDate;
  final bool? ownerProcess;
  final List<dynamic>? listRating;
  final String? companyCode;
  final List<String>? chartNames;
  final List<String>? chartCodes;
  final bool? isJoinBpmTask;
  final int? serviceId;

  const MyPycRequestModel({
    this.search,
    this.page,
    this.limit,
    this.ticketStatus,
    this.sortBy,
    this.sortType,
    this.listService,
    this.listTicketTitle,
    this.chartNodeNames,
    this.listChartNodeName,
    this.charts,
    this.listUser,
    this.listStatus,
    this.pageType,
    this.fromDate,
    this.toDate,
    this.ownerProcess,
    this.listRating,
    this.companyCode,
    this.chartNames,
    this.chartCodes,
    this.isJoinBpmTask,
    this.serviceId,
  });

  factory MyPycRequestModel.fromCategory(MyPycTicketCategory category,
      {int page = 1, int limit = 5, String? search, Map<String, dynamic>? additionalParams}) {
    final data = myPycTicketStatusMapping[category]!;

    final requestData = {
      "ticketStatus": data["ticketStatus"],
      "listStatus": List<String>.from(data["listStatus"]),
      "search": search ?? "",
      "page": page,
      "limit": limit,
      "sortBy": "ticketCreatedTime",
      "sortType": "DESC",
      "listService": ["-1"],
      "pageType": "",
      "fromDate": "",
      "toDate": "",
      "ownerProcess": false,
      "listRating": ["-1", 0, 1],
      "companyCode": "",
      "isJoinBpmTask": true,
    };

    return MyPycRequestModel(
      search: requestData["search"],
      page: requestData["page"],
      limit: requestData["limit"],
      ticketStatus: requestData["ticketStatus"],
      sortBy: requestData["sortBy"],
      sortType: requestData["sortType"],
      listService: requestData["listService"],
      pageType: requestData["pageType"],
      fromDate: requestData["fromDate"],
      toDate: requestData["toDate"],
      ownerProcess: requestData["ownerProcess"],
      listRating: requestData["listRating"],
      companyCode: requestData["companyCode"],
      isJoinBpmTask: requestData["isJoinBpmTask"],
      listStatus: requestData["listStatus"],
      serviceId: additionalParams?["serviceId"],
    );
  }
  MyPycRequestModel copyWith({
    String? search,
    int? page,
    int? limit,
    String? ticketStatus,
    String? sortBy,
    String? sortType,
    List<String>? listService,
    List<String>? listTicketTitle,
    List<String>? chartNodeNames,
    List<String>? listChartNodeName,
    List<String>? charts,
    List<String>? listUser,
    List<String>? listStatus,
    String? pageType,
    String? fromDate,
    String? toDate,
    bool? ownerProcess,
    List<dynamic>? listRating,
    String? companyCode,
    List<String>? chartNames,
    List<String>? chartCodes,
    bool? isJoinBpmTask,
    int? serviceId,
  }) {
    return MyPycRequestModel(
      search: search ?? this.search,
      page: page ?? this.page,
      limit: limit ?? this.limit,
      ticketStatus: ticketStatus ?? this.ticketStatus,
      sortBy: sortBy ?? this.sortBy,
      sortType: sortType ?? this.sortType,
      listService: listService ?? this.listService,
      listTicketTitle: listTicketTitle ?? this.listTicketTitle,
      chartNodeNames: chartNodeNames ?? this.chartNodeNames,
      listChartNodeName: listChartNodeName ?? this.listChartNodeName,
      charts: charts ?? this.charts,
      listUser: listUser ?? this.listUser,
      listStatus: listStatus ?? this.listStatus,
      pageType: pageType ?? this.pageType,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      ownerProcess: ownerProcess ?? this.ownerProcess,
      listRating: listRating ?? this.listRating,
      companyCode: companyCode ?? this.companyCode,
      chartNames: chartNames ?? this.chartNames,
      chartCodes: chartCodes ?? this.chartCodes,
      isJoinBpmTask: isJoinBpmTask ?? this.isJoinBpmTask,
      serviceId: serviceId ?? this.serviceId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "search": search,
      "page": page,
      "limit": limit,
      "ticketStatus": ticketStatus,
      "sortBy": sortBy,
      "sortType": sortType,
      "listService": listService,
      "listTicketTitle": listTicketTitle,
      "chartNodeNames": chartNodeNames,
      "listChartNodeName": listChartNodeName,
      "charts": charts,
      "listUser": listUser,
      "listStatus": listStatus,
      "pageType": pageType,
      "fromDate": fromDate,
      "toDate": toDate,
      "ownerProcess": ownerProcess,
      "listRating": listRating,
      "companyCode": companyCode,
      "chartNames": chartNames,
      "chartCodes": chartCodes,
      "isJoinBpmTask": isJoinBpmTask,
      "serviceId": serviceId,
    };
  }
}

enum MyPycTicketCategory {
  approving,
  completed,
  returned,
  canceled,
  draft,
  sharedFollowing,
  shared
}

const Map<MyPycTicketCategory, Map<String, dynamic>> myPycTicketStatusMapping =
    {
  MyPycTicketCategory.approving: {
    "ticketStatus": "ONGOING",
    "listStatus": [
      "-1",
      "PROCESSING",
      "RECALLING",
      "ADDITIONAL_REQUEST",
      "OPENED"
    ],
  },
  MyPycTicketCategory.completed: {
    "ticketStatus": "COMPLETED",
    "listStatus": ["COMPLETED", "CLOSED", "-1"],
  },
  MyPycTicketCategory.returned: {
    "ticketStatus": "RECALLED",
    "listStatus": ["DELETED_BY_RU", "RECALLED", "-1"],
  },
  MyPycTicketCategory.canceled: {
    "ticketStatus": "CANCEL",
    "listStatus": ["CANCEL", "-1"],
  },
  MyPycTicketCategory.draft: {
    "ticketStatus": "DRAFT",
    "listStatus": ["DRAFT", "-1"],
  },
  MyPycTicketCategory.sharedFollowing: {
    "ticketStatus": "SHARED",
    "listStatus": [
      "COMPLETED",
      "CANCEL",
      "RECALLED",
      "RECALLING",
      "DELETED_BY_RU",
      "PROCESSING",
      "ADDITIONAL_REQUEST",
      "OPENED",
      "-1",
      "OPENED",
      "CLOSED",
      "RECALLING",
      "FOLLOWED",
      "SHARED"
    ],
  },
  MyPycTicketCategory.shared: {
    "ticketStatus": "SHARE",
    "listStatus": [
      "COMPLETED",
      "CANCEL",
      "RECALLED",
      "RECALLING",
      "DELETED_BY_RU",
      "PROCESSING",
      "ADDITIONAL_REQUEST",
      "OPENED",
      "-1",
      "CLOSED",
      "FOLLOWED",
      "SHARED"
    ],
  },
};
