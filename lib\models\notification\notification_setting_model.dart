class NotificationSettingsModel {
  final String? code;
  final String? message;
  final NotificationSettingsData? data;

  NotificationSettingsModel({
    this.code,
    this.message,
    this.data,
  });

  factory NotificationSettingsModel.fromJson(Map<String, dynamic> json) {
    return NotificationSettingsModel(
      code: json['code'],
      message: json['message'],
      data: json['data'] != null ? NotificationSettingsData.fromJson(json['data']) : null,
    );
  }
}

class NotificationSettingsData {
  final String? username;
  final bool enableMobileNotification;

  NotificationSettingsData({
    this.username,
    this.enableMobileNotification = false,
  });

  factory NotificationSettingsData.fromJson(Map<String, dynamic> json) {
    return NotificationSettingsData(
      username: json['username'],
      enableMobileNotification: json['enableMobileNotification'] ?? false,
    );
  }
}

class UpdateNotificationResponse {
  final String? code;
  final String? message;
  final dynamic data;

  UpdateNotificationResponse({
    this.code,
    this.message,
    this.data,
  });

  factory UpdateNotificationResponse.from<PERSON><PERSON>(Map<String, dynamic> json) {
    return UpdateNotificationResponse(
      code: json['code'],
      message: json['message'],
      data: json['data'],
    );
  }
}
