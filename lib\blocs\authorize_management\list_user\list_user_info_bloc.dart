import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/repositories/list_user_info_repository.dart';
import 'list_user_info_event.dart';
import 'list_user_info_state.dart';

class UserInfoBloc extends Bloc<UserInfoEvent, UserInfoState> {
  final UserInfoRepository _repository;

  UserInfoBloc(this._repository) : super(UserInfoInitial()) {
    on<GetUserInfo>(_onGetUserInfo);
  }

  Future<void> _onGetUserInfo(
    GetUserInfo event,
    Emitter<UserInfoState> emit,
  ) async {
    try {
      emit(UserInfoLoading());
      final response = await _repository.getUserInfo(
        concurrently: event.concurrently,
        managerLevel: event.managerLevel,
        search: event.search,
        sortBy: event.sortBy,
        sortType: event.sortType,
        page: event.page,
        limit: event.limit,
        size: event.size,
      );
      emit(UserInfoLoaded(response));
    } catch (e) {
      emit(UserInfoError(e.toString()));
    }
  }
}
