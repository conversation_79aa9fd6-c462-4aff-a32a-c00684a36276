import 'dart:developer';

import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/event/attachment_list_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/attachment_list_state.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AttachmentListBloc
    extends Bloc<AttachmentListEvent, AttachmentListState> {
  final TicketOtherActionRepository _ticketOtherActionRepository;
  int currentPage = 1;
  int pageSize = 100;

  AttachmentListBloc(
      {required TicketOtherActionRepository ticketOtherActionRepository})
      : _ticketOtherActionRepository = ticketOtherActionRepository,
        super(const AttachmentListState()) {
    on<GetAttachmentList>(_onLoadAttachmentList);
  }

  Future<void> _onLoadAttachmentList(
      GetAttachmentList event, Emitter<AttachmentListState> emit) async {
    try {
      emit(state.copyWith(status: ServiceStatus.loading));
      log('event.ticketId: ${event.ticketId}');
      // final currentData = state.response?.content ?? [];

      final allAttachmentList = (await _ticketOtherActionRepository
              .getTicketAttachmentList(event.ticketId))
          .content;
      final List<DocumentModel> attachmentList = [];
      final List<DocumentModel> attachmentListOther = [];

      for (var i = 0; i < allAttachmentList.length; i++) {
        final item = allAttachmentList[i];
        final fileName = item['displayName'];
        final fileType = fileName.split('.').last;
        if (item['isStartStep'] == true ||
            item['createdUser'] == event.ticketStartUserId) {
              
          attachmentList.add(DocumentModel.fromJsonAPIAttachmentList(
            id: i.toString(),
            name: fileName,
            url: item['downloadUrl'],
            type: FileType.fromCode(fileType),
            size: item['fileSize'],
            extension: fileType,
            description: item['description'],
            createdTime: item['uploadTime'],
            createdBy: item['createdUser'],
            fileType: item['fileType'],
          ));
        } else {
          attachmentListOther.add(DocumentModel.fromJsonAPIAttachmentList(
            id: i,
            name: fileName,
            url: item['downloadUrl'],
            type: FileType.fromCode(fileType),
            size: item['fileSize'],
            extension: fileType,
            description: item['description'],
            createdTime: item['uploadTime'],
            createdBy: item['createdUser'],
            fileType: item['fileType'],
          ));
        }
      }
      emit(state.copyWith(
        status: ServiceStatus.success,
        contentFile: attachmentList,
        contentFileOther: attachmentListOther,
      ));
    } catch (e) {
      emit(state.copyWith(
          status: ServiceStatus.failure, errorMessage: e.toString()));
    }
  }
}
