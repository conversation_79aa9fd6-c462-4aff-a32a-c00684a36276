import 'package:eapprove/models/authorize_management/authorize_management_model.dart';
import 'package:eapprove/models/authorize_management/generic_data_model.dart';
import 'package:eapprove/models/generic_respone_model.dart';
import 'package:equatable/equatable.dart';

enum AuthorizeManagementStatus { initial, loading, loaded, error }

class AuthorizeManagementState extends Equatable {
  // get the list of authorize management
  final AuthorizeManagementStatus status;
  final List<AuthorizeItemData> authorizeManagementList;
  final List<AuthorizeItemData> authorizeManagementListAll;
  final List<AuthorizeItemData> authorizeManagementListOngoing;
  final List<AuthorizeItemData> authorizeManagementListExpired;
  final List<AuthorizeItemData> authorizeManagementListFilter;
  final GenericResponseModel<GenericDataAuthorize<AuthorizeItemData>>?
      authorizeManagementResponseModel;

  final bool isPaginating;
  final bool isLoadingMore;
  final String? errorMessage;

  const AuthorizeManagementState(
      {this.status = AuthorizeManagementStatus.initial,
      this.errorMessage,
      this.authorizeManagementResponseModel,
      this.isPaginating = false,
      this.isLoadingMore = false,
      this.authorizeManagementList = const [],
      this.authorizeManagementListAll = const [],
      this.authorizeManagementListOngoing = const [],
      this.authorizeManagementListExpired = const [],
      this.authorizeManagementListFilter = const []});

  AuthorizeManagementState copyWith({
    AuthorizeManagementStatus? status,
    String? errorMessage,
    List<AuthorizeItemData>? authorizeManagementList,
    List<AuthorizeItemData>? authorizeManagementListAll,
    List<AuthorizeItemData>? authorizeManagementListOngoing,
    List<AuthorizeItemData>? authorizeManagementListExpired,
    List<AuthorizeItemData>? authorizeManagementListFilter,
    GenericResponseModel<GenericDataAuthorize<AuthorizeItemData>>?
        authorizeManagementResponseModel,
    bool? isPaginating,
    bool? isLoadingMore,
  }) {
    return AuthorizeManagementState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      authorizeManagementResponseModel: authorizeManagementResponseModel ??
          this.authorizeManagementResponseModel,
      authorizeManagementList:
          authorizeManagementList ?? this.authorizeManagementList,
      authorizeManagementListAll:
          authorizeManagementListAll ?? this.authorizeManagementListAll,
      authorizeManagementListOngoing:
          authorizeManagementListOngoing ?? this.authorizeManagementListOngoing,
      authorizeManagementListExpired:
          authorizeManagementListExpired ?? this.authorizeManagementListExpired,
      authorizeManagementListFilter:
          authorizeManagementListFilter ?? this.authorizeManagementListFilter,
      isPaginating: isPaginating ?? this.isPaginating,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        authorizeManagementList,
        authorizeManagementListAll,
        authorizeManagementListOngoing,
        authorizeManagementListExpired,
        authorizeManagementListFilter,
        isPaginating,
        isLoadingMore,
        authorizeManagementResponseModel,
        authorizeManagementList.length,
        authorizeManagementListFilter.length,
      ];
}
