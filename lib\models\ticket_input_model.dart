class TicketInputModel {
  final String? requestSubject;
  final String? companyCode;
  final String? status;
  final String? submitTime;
  final String? priority;
  final String? requestType;
  final String? creator;
  final String code;
  final List<TicketVariable> listVariables;
  final String message;

  TicketInputModel({
    this.requestSubject,
    this.companyCode,
    this.status,
    this.submitTime,
    this.priority,
    this.requestType,
    this.creator,
    required this.code,
    required this.listVariables,
    required this.message,
  });

  String? getRequestSubject() => requestSubject;
  String? getCompanyCode() => companyCode;
  String? getStatus() => status;
  String? getSubmitTime() => submitTime;
  String? getPriority() => priority;
  String? getRequestType() => requestType;
  String? getCreator() => creator;

  factory TicketInputModel.fromJson(Map<String, dynamic> json) {
    return TicketInputModel(
      requestSubject: json['requestSubject'],
      companyCode: json['companyCode'],
      status: json['status'],
      submitTime: json['submitTime'],
      priority: json['priority'],
      requestType: json['requestType'],
      creator: json['creator'],
      code: json['code'].toString(),
      listVariables: (json['data']['listVariables'] as List)
          .map((e) => TicketVariable.fromJson(e))
          .toList(),
      message: json['message'],
    );
  }
}

class TicketVariable {
  final String? additionalVal;
  final String name;
  final String type;
  final String? value;
  final String? taskId;

  TicketVariable({
    this.additionalVal,
    required this.name,
    required this.type,
    this.value,
    this.taskId,
  });

  factory TicketVariable.fromJson(Map<String, dynamic> json) {
    return TicketVariable(
      additionalVal: json['additionalVal'],
      name: json['name'],
      type: json['type'],
      value: json['value'],
      taskId: json['taskId'],
    );
  }
} 