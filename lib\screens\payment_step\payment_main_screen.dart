import 'package:eapprove/common/sliver_appbar_delegate.dart';
import 'package:eapprove/screens/payment_proposal/widgets/advanced_bottom_bar.dart';
import 'package:eapprove/screens/payment_step/widgets/circular_progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';

abstract class BasePaymentStepScreen extends StatefulWidget {
  final int currentStep;
  final int totalSteps;
  final String stepTitle;
  final String stepDescription;

  const BasePaymentStepScreen({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitle,
    required this.stepDescription,
  });
}

abstract class BasePaymentStepState<T extends BasePaymentStepScreen> extends State<T> {
  Widget buildStepContent();
  void handleNextStep();

  Widget _buildStepHeader() {
    return Container(
      color: getColorSkin().primaryBlue,
      child: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 14.h, top: 10.h),
        child: Row(
          children: [
            CustomCircularProgress(
              progress: widget.currentStep / widget.totalSteps,
              size: 50.w,
              progressColor: Colors.white,
              backgroundColor: Colors.white.withOpacity(0.3),
              strokeWidth: 2.w,
              child: Text(
                '${widget.currentStep}/${widget.totalSteps}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.stepTitle,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  widget.stepDescription,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Icon(
              Icons.keyboard_arrow_down,
              color: Colors.white,
              size: 24.sp,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: getColorSkin().transparent,
        body: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverAppBar(
                elevation: 0,
                scrolledUnderElevation: 0,
                pinned: true,
                floating: false,
                backgroundColor: getColorSkin().primaryBlue,
                expandedHeight: 56.h,
                toolbarHeight: 56.h,
                automaticallyImplyLeading: false,
                title: Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 20.sp,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                    Text(
                      'Trở lại',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              SliverPersistentHeader(
                pinned: true,
                delegate: SliverAppBarDelegate(
                  minHeight: 70.h,
                  maxHeight: 70.h,
                  child: _buildStepHeader(),
                ),
              ),
            ];
          },
          body: SingleChildScrollView(
            padding: EdgeInsets.all(10.w),
            child: buildStepContent(),
          ),
        ),
        bottomNavigationBar: buildBottomBar(
          context,
          buttonText: widget.currentStep < widget.totalSteps ? 'Tiếp tục' : 'Hoàn thành',
          onButtonPressed: handleNextStep,
        ),
      ),
    );
  }
}
