import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:eapprove/screens/common/network_error.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:uuid/uuid.dart';

// Custom exception for network errors
class NetworkException implements Exception {
  final String message;
  final String requestId;

  NetworkException(this.message, this.requestId);

  @override
  String toString() => message;
}

class NetworkManager {
  static final NetworkManager _instance = NetworkManager._internal();
  factory NetworkManager() => _instance;
  NetworkManager._internal();

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  bool _isConnected = true;
  bool _isShowingDialog = false;
  GlobalKey<NavigatorState>? _navigatorKey;
  final _uuid = Uuid();

  final Map<String, Function> _pendingRequests = {};

  Future<void> initialize({required GlobalKey<NavigatorState> navigatorKey}) async {
    _navigatorKey = navigatorKey;

    final initialConnectivity = await Connectivity().checkConnectivity();
    _isConnected = initialConnectivity.any((result) => result != ConnectivityResult.none);

    await _connectivitySubscription?.cancel();

    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((results) {
      final wasConnected = _isConnected;
      _isConnected = results.any((result) => result != ConnectivityResult.none);

      if (wasConnected && !_isConnected) {
        // Lost connectivity
        _showNetworkErrorDialog();
      } else if (!wasConnected && _isConnected) {
        // Regained connectivity
        _dismissNetworkErrorDialog();
        // SnackbarCore.success('Đã kết nối lại mạng');

        // Retry all pending requests automatically
        _retryAllPendingRequests();
      }
    });
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _pendingRequests.clear();
  }

  // Add a request to the pending queue
  String addPendingRequest(Function retryCallback) {
    final requestId = _uuid.v4();
    _pendingRequests[requestId] = retryCallback;
    return requestId;
  }

  // Remove a request from the pending queue
  void removePendingRequest(String requestId) {
    _pendingRequests.remove(requestId);
  }

  // Retry all pending requests
  void _retryAllPendingRequests() {
    if (_pendingRequests.isEmpty) return;

    // Create a copy to avoid concurrent modification
    final requestsToRetry = Map<String, Function>.from(_pendingRequests);
    _pendingRequests.clear();

    // Execute each retry callback
    for (final callback in requestsToRetry.values) {
      callback();
    }
  }

  // Show network error dialog
  void _showNetworkErrorDialog() {
    if (_isShowingDialog || _navigatorKey?.currentContext == null) return;

    _isShowingDialog = true;

    showNetworkErrorDialog(
      context: _navigatorKey!.currentContext!,
      title: 'Mất kết nối mạng',
      message: 'Bạn vui lòng kiểm tra lại internet và kết nối lại',
      onRetry: _checkConnectionAndRetry,
    );
  }

  void _dismissNetworkErrorDialog() {
    if (_isShowingDialog && _navigatorKey?.currentContext != null) {
      Navigator.of(_navigatorKey!.currentContext!, rootNavigator: true).pop();
      _isShowingDialog = false;
    }
  }

  Future<void> _checkConnectionAndRetry() async {
    final connectivity = await Connectivity().checkConnectivity();
    _isConnected = connectivity.any((result) => result != ConnectivityResult.none);

    _isShowingDialog = false;

    if (_isConnected) {
      _retryAllPendingRequests();
    } else {
      Future.delayed(const Duration(milliseconds: 500), _showNetworkErrorDialog);
    }
  }

  Future<bool> checkConnectivity() async {
    final results = await Connectivity().checkConnectivity();
    _isConnected = results.any((result) => result != ConnectivityResult.none);
    return _isConnected;
  }

  void showNetworkErrorDialogIfNeeded() {
    if (!_isConnected && !_isShowingDialog && _navigatorKey?.currentContext != null) {
      _showNetworkErrorDialog();
    }
  }

  Future<T> executeWithRetry<T>({
    required Future<T> Function() action,
    String? requestIdentifier,
  }) async {
    final requestId = requestIdentifier ?? _uuid.v4();

    final isConnected = await checkConnectivity();
    if (!isConnected) {
      addPendingRequest(() => executeWithRetry(
            action: action,
            requestIdentifier: requestId,
          ));

      showNetworkErrorDialogIfNeeded();

      throw NetworkException('No internet connection', requestId);
    }

    try {
      final result = await action();

      removePendingRequest(requestId);

      return result;
    } on SocketException catch (e) {
      addPendingRequest(() => executeWithRetry(
            action: action,
            requestIdentifier: requestId,
          ));

      showNetworkErrorDialogIfNeeded();
      throw NetworkException('Network error: ${e.message}', requestId);
    } on TimeoutException catch (e) {
      addPendingRequest(() => executeWithRetry(
            action: action,
            requestIdentifier: requestId,
          ));

      showNetworkErrorDialogIfNeeded();
      throw NetworkException('Request timed out', requestId);
    } catch (e) {
      // For non-network errors, just rethrow
      if (e is! NetworkException) {
        removePendingRequest(requestId);
      }
      rethrow;
    }
  }
}
