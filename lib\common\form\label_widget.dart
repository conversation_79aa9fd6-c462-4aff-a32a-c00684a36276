import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

/// A widget that displays a form label with optional value, error message, and tooltip.
/// Supports both horizontal and vertical layouts.
class LabelWidget extends StatelessWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;

  const LabelWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stateManager,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // if (data.display == false) return const SizedBox.shrink();

    return _buildLabel();
  }

  Widget _buildLabel() {
    final labelText = data.displayName?.isNotEmpty == true ? data.displayName! : data.label ?? '';
    return Expanded(child: Text(
      labelText,
      style: _getLabelStyle(),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    ));
  }

  Widget _buildTooltip() {
    return Tooltip(
      message: data.tooltip!,
      child: Icon(
        Icons.info_outline,
        size: 16.sp,
        color: Colors.grey,
      ),
    );
  }

  TextStyle _getLabelStyle() {
    final baseStyle = TextStyle(
      fontSize: getTypoSkin().bodyRegular14.fontSize,
      color: data.readonly == true ? Colors.black54 : Colors.black,
      fontWeight: data.fontWeight == 'font-bold' ? FontWeight.bold : FontWeight.normal,
      fontStyle: data.fontWeight == 'font-italic' ? FontStyle.italic : FontStyle.normal,
      decoration: data.fontWeight == 'font-underline' ? TextDecoration.underline : TextDecoration.none,
    );

    return baseStyle;
  }
}