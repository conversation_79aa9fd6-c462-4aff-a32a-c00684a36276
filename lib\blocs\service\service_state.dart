import 'package:eapprove/models/service/service_request_model.dart';
import 'package:eapprove/models/service/service_response_model.dart';
import 'package:equatable/equatable.dart';

enum ServiceStatus { initial, loading, success, failure }
enum PdfDownloadStatus { initial, downloading, success, failure }

class ServiceState extends Equatable {
  final ServiceStatus status;
  final ServiceRequestModel? serviceRequestModel;
  final ServiceResponseModel? serviceModels;
  final String? searchKey;
  final String? errorMessage;
  // final bool isNetworkError;
  final String? pdfFilePath;
  final PdfDownloadStatus pdfDownloadStatus;
  final String? lastDownloadUrl;

  const ServiceState({
    this.status = ServiceStatus.initial,
    this.serviceRequestModel,
    this.serviceModels,
    this.searchKey,
    this.errorMessage,
    // this.isNetworkError = false,
    this.pdfFilePath,
    this.pdfDownloadStatus = PdfDownloadStatus.initial,
    this.lastDownloadUrl,
  });

  ServiceState copyWith({
    ServiceStatus? status,
    ServiceRequestModel? serviceRequestModel,
    ServiceResponseModel? serviceModels,
    String? searchKey,
    String? errorMessage,
    // bool? isNetworkError,
    String? pdfFilePath,
    PdfDownloadStatus? pdfDownloadStatus,
    String? lastDownloadUrl,
  }) {
    return ServiceState(
      status: status ?? this.status,
      serviceRequestModel: serviceRequestModel ?? this.serviceRequestModel,
      serviceModels: serviceModels ?? this.serviceModels,
      searchKey: searchKey ?? this.searchKey,
      errorMessage: errorMessage ?? this.errorMessage,
      // isNetworkError: isNetworkError ?? this.isNetworkError,
      pdfFilePath: pdfFilePath ?? this.pdfFilePath,
      pdfDownloadStatus: pdfDownloadStatus ?? this.pdfDownloadStatus,
      lastDownloadUrl: lastDownloadUrl ?? this.lastDownloadUrl,
    );
  }

  List<Object?> get props => [
    status,
    serviceRequestModel,
    serviceModels,
    searchKey,
    errorMessage,
    // isNetworkError,
    pdfFilePath,
    pdfDownloadStatus,
    lastDownloadUrl,
  ];
}