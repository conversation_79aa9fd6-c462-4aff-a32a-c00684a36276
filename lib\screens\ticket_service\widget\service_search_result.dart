import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/service/service_bloc.dart';
import 'package:eapprove/blocs/service/service_event.dart';
import 'package:eapprove/models/service/service_data_model.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/common/error_handler.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/ticket_service/widget/service_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class ServiceSearchResults extends StatelessWidget {
  final List<ServiceData> allServices;
  final List<ServiceData> matchingServices;

  const ServiceSearchResults({
    super.key,
    required this.allServices,
    required this.matchingServices,
  });

  String? _getParentPath(ServiceData service, List<ServiceData> allServices) {
    List<String> parentNames = [];
    ServiceData? currentService = service;

    while (currentService != null && currentService.parentId != null) {
      final parentService = allServices.firstWhere(
        (s) => s.id == currentService!.parentId,
        orElse: () => ServiceData(),
      );

      if (parentService.id == null || parentService.notShowingMoblie == true) {
        return null;
      }

      if (parentService.serviceName != null) {
        parentNames.insert(0, parentService.serviceName!);
      }

      currentService = parentService.id != null ? parentService : null;
    }

    if (parentNames.isEmpty) {
      return ""; // No parents found
    }

    return parentNames.join(" > ");
  }

  // Future<void> _handleServiceTap(BuildContext context,
  //     ServiceData service) async {
  //   switch (service.serviceType.toString()) {
  //     case "3": // URL type
  //       if (service.url != null && service.url!.isNotEmpty) {
  //         try {
  //           final uri = Uri.parse(service.url!);
  //           await launchUrl(
  //             uri,
  //             mode: LaunchMode.externalApplication,
  //           );
  //         } catch (e) {
  //           if (context.mounted) {
  //             SnackbarCore.error('Lỗi khi mở link web.');
  //           }
  //         }
  //       } else {
  //         if (context.mounted) {
  //           SnackbarCore.error('Không có link cho dịch vụ này.');
  //         }
  //       }
  //       break;

  //     case "4": // PDF type
  //       if (service.url != null && service.url!.isNotEmpty) {
  //         // Clear any previous PDF status
  //         context.read<ServiceBloc>().add(const ClearPdfStatus());
  //         // Start download
  //         context.read<ServiceBloc>().add(DownloadPdf(service.url!));
  //       } else {
  //         if (context.mounted) {
  //           ErrorHandler.showErrorSnackBar(
  //               context, 'No PDF URL provided for this service');
  //         }
  //       }
  //       break;

  //     default:
  //     // Handle normal form navigation
  //     // if (service.procDefId != null) {
  //     //   Navigator.pop(context);
  //     //   Navigator.push(
  //     //     context,
  //     //     MaterialPageRoute(
  //     //       builder: (context) => TicketFormScreen(
  //     //         procDefId: service.procDefId!,
  //     //         title: service.serviceName ?? '',
  //     //       ),
  //     //     ),
  //     //   );
  //     // }
  //   }
  // }

  Future<void> _handleServiceTap(
      BuildContext context, ServiceData serviceData) async {
    final allData = context.read<ServiceBloc>().state.serviceModels?.data ?? [];
    final hasChildren = allData.any((item) =>
        item.parentId == serviceData.id && item.notShowingMoblie == false);

    if (hasChildren) {
      showServiceBottomSheet(context, serviceData);
      return;
    }

     // Handle special flow case
    if (serviceData.specialFlow == true) {
      try {
        final specialService = await context.read<ServiceBloc>().repository.getServiceSpecialByParentId(serviceData.id!);
        debugPrint('specialService: ${specialService.data.procDefId}');
        if (specialService.data != null) {
          // If the special service has a procDefId, navigate to the form
          if (specialService.data.procDefId != null) {
            final currentBloc = BlocProvider.of<FormBloc>(context);
            final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
            final printFileTemplateBloc = BlocProvider.of<PrintFileTemplateBloc>(context);
            final bpmProcInstBloc = BlocProvider.of<BpmProcInstBloc>(context);
            final dropdownBloc = BlocProvider.of<DropdownBloc>(context);
            final chartBloc = BlocProvider.of<ChartBloc>(context);
            final userListBloc = BlocProvider.of<UserListBloc>(context);

            context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MultiBlocProvider( 
                    providers: [
                      BlocProvider.value(
                        value: currentBloc,
                      ),
                      BlocProvider.value(
                        value: checkTypeBloc,
                      ),
                      BlocProvider.value(
                        value: printFileTemplateBloc,
                      ),
                      BlocProvider.value(
                        value: bpmProcInstBloc,
                      ),
                      BlocProvider.value(
                        value: dropdownBloc,
                      ),
                      BlocProvider.value(
                        value: chartBloc,
                      ),
                      BlocProvider.value(
                        value: userListBloc,
                      ),
                    ],
                    child: TicketFormScreen(
                      procDefId: specialService.data.procDefId!,
                      title: specialService.data.serviceName ?? '',
                      processId: specialService.data.processId,
                      ticketId: specialService.data.id,
                    ),
                  ),
                )).then(
              (_) {
                context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
              },
            );
          } else {
            // If no procDefId, show the special service in bottom sheet
            showServiceBottomSheet(context, specialService.data);
          }
          return;
        }
      } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading special services: $e'),
              backgroundColor: getColorSkin().red,
            ),
          );
        return;
      }
    }

    // Handle different service types
    switch (serviceData.serviceType.toString()) {
      case "3": // URL type
        if (serviceData.url != null && serviceData.url!.isNotEmpty) {
          try {
            final uri = Uri.parse(serviceData.url!);
            await launchUrl(
              uri,
              mode: LaunchMode.externalApplication,
            );
          } catch (e) {
            // if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Invalid URL format: ${serviceData.url}'),
                backgroundColor: getColorSkin().red,
              ),
            );
            // }
          }
        } else {
          // if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No URL provided for this service'),
              backgroundColor: getColorSkin().red,
            ),
          );
          // }
        }
        break;

      case "4": // PDF type
        if (serviceData.url != null && serviceData.url!.isNotEmpty) {
          context.read<ServiceBloc>().add(const ClearPdfStatus());
          context.read<ServiceBloc>().add(DownloadPdf(serviceData.url!));
        } else {
          // if (mounted) {
          ErrorHandler.showErrorSnackBar(
              context, 'No PDF URL provided for this service');
          // }
        }
        break;
      default:
        if (serviceData.procDefId != null) {
          final currentBloc = BlocProvider.of<FormBloc>(context);
          final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
          context.read<BottomNavBloc>().add(const SetBottomNavVisibility(false));
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: currentBloc,
                    ),
                    BlocProvider.value(
                      value: checkTypeBloc,
                    ),
                  ],
                  child: TicketFormScreen(
                    procDefId: serviceData.procDefId!,
                    title: serviceData.serviceName ?? '',
                    processId: serviceData.processId,
                    ticketId: serviceData.id,
                  ),
                ),
              )).then(
            (_) {
              context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
            },
          );
        }
    }
  }

  Widget _buildSearchResultItem(BuildContext context, ServiceData service) {
    final parentPath = _getParentPath(service, allServices);

    if (parentPath == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () => _handleServiceTap(context, service),
      child: Card(
        color: getColorSkin().white,
        elevation: 0.2,
        margin: EdgeInsets.only(bottom: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
          side: BorderSide(color: getColorSkin().grey4Background, width: 1),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                service.serviceName ?? '',
                style: getTypoSkin().bodyRegular14Bold.copyWith(
                      color: getColorSkin().ink1,
                    ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              if (parentPath.isNotEmpty) ...[
                SizedBox(height: 4.h),
                Text(
                  parentPath,
                  style: getTypoSkin().regular12.copyWith(
                        color: getColorSkin().ink3,
                      ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void showServiceBottomSheet(BuildContext context, ServiceData serviceData) {
    final allServices =
        context.read<ServiceBloc>().state.serviceModels?.data ?? [];
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => BlocProvider.value(
        value: context.read<ServiceBloc>(),
        child: ServiceBottomSheet(
          initialServiceData: serviceData,
          allServices: allServices,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final sortedServices = List<ServiceData>.from(matchingServices)
      ..sort(
          (a, b) => (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));

    return ListView.builder(
      itemCount: sortedServices.length,
      padding: EdgeInsets.only(top: 8.h),
      itemBuilder: (context, index) {
        final service = sortedServices[index];
        return _buildSearchResultItem(context, service);
      },
    );
  }
}
