import 'package:shared_preferences/shared_preferences.dart';

class SearchHistoryManager {
  static const int _maxHistoryItems = 10;
  static const String _defaultHistoryKey = 'search_history';

  static Future<List<String>> getSearchHistory({String historyKey = _defaultHistoryKey}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(historyKey) ?? [];
  }

  static Future<void> addSearchTerm(String term, {String historyKey = _defaultHistoryKey}) async {
    if (term.trim().isEmpty) return;
    
    final prefs = await SharedPreferences.getInstance();
    final List<String> history = prefs.getStringList(historyKey) ?? [];
    
    // Remove the term if it already exists to avoid duplicates
    history.remove(term);
    
    // Add the term to the beginning of the list
    history.insert(0, term);
    
    // Limit the history to maxHistoryItems
    if (history.length > _maxHistoryItems) {
      history.removeRange(_maxHistoryItems, history.length);
    }
    
    await prefs.setStringList(historyKey, history);
  }

  static Future<void> clearSearchHistory({String historyKey = _defaultHistoryKey}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(historyKey);
  }

  static Future<void> removeSearchTerm(String term, {String historyKey = _defaultHistoryKey}) async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> history = prefs.getStringList(historyKey) ?? [];
    
    history.remove(term);
    
    await prefs.setStringList(historyKey, history);
  }
}