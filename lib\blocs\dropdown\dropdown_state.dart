import 'package:equatable/equatable.dart';

class BaseResponseModel {
  final bool isSuccess;
  final String? errorMessage;
  final List<dynamic> data;
  
  const BaseResponseModel({
    this.isSuccess = true,
    this.errorMessage,
    this.data = const [],
  });
}

class DropdownState extends Equatable {
  final Map<String, bool> isLoading;
  final Map<String, BaseResponseModel> dropdownResponses;
  
  const DropdownState({
    this.isLoading = const {},
    this.dropdownResponses = const {},
  });

  @override
  List<Object?> get props => [
    isLoading,
    dropdownResponses,
  ];

  DropdownState copyWith({
    Map<String, bool>? isLoading,
    Map<String, BaseResponseModel>? dropdownResponses,
  }) {
    return DropdownState(
      isLoading: isLoading ?? this.isLoading,
      dropdownResponses: dropdownResponses ?? this.dropdownResponses,
    );
  }
} 