
//14.5 thong tin phieu
class ThongTinPhieuResponseModel {
  final dynamic code;
  final List<Data> data;
  final dynamic? message;

  ThongTinPhieuResponseModel({
    required this.code,
    required this.data,
    this.message,
  });

  factory ThongTinPhieuResponseModel.fromJson(Map<dynamic, dynamic> json) {
    return ThongTinPhieuResponseModel(
      code: json['code'],
      data: (json['data'] as List<dynamic>)
          .map((e) => Data.fromJson(e as Map<dynamic, dynamic>))
          .toList(),
      message: json['message'],
    );
  }
}

class Data {
  final dynamic additionalVal;
  final dynamic name;
  final dynamic type;
  final dynamic value;
  final dynamic taskId;

  Data({
    required this.additionalVal,
    required this.name,
    required this.type,
    required this.value,
    required this.taskId,
  });

  factory Data.fromJson(Map<dynamic, dynamic> json) {
    return Data(
      additionalVal: json['additionalVal'],
      name: json['name'],
      type: json['type'],
      value: json['value'],
      taskId: json['taskId'],
    );
  }
}