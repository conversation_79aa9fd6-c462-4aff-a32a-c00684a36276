import 'dart:convert';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/models/form/sign_print_response_model.dart';
import 'package:flutter/foundation.dart';

class SignPrintRepository {
  final ApiService _apiService;

  SignPrintRepository({required ApiService apiService}) : _apiService = apiService;

  Future<SignPrintResponseModel> getSignPrintData(int bpmTpSignZone, String procInstId) async {
    try {
      final response = await _apiService.get(
        'business-process/print/sign/$bpmTpSignZone/$procInstId',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return SignPrintResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to get sign print data: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting sign print data: $e');
      rethrow;
    }
  }
} 