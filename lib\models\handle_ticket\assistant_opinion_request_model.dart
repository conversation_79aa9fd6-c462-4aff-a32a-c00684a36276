class AssistantOpinionRequestModel {
  final int ticketId;
  final int page;
  final int limit;
  final String sortBy;
  final String sortType;
  final int size;

  AssistantOpinionRequestModel({
    required this.ticketId,
    required this.page,
    required this.limit,
    required this.sortBy,
    required this.sortType,
    required this.size,
  });

  factory AssistantOpinionRequestModel.fromJson(Map<dynamic, dynamic> json) {
    return AssistantOpinionRequestModel(
      ticketId: json['ticketId'] as int,
      page: json['page'] as int,
      limit: json['limit'] as int,
      sortBy: json['sortBy'] as String,
      sortType: json['sortType'] as String,
      size: json['size'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ticketId': ticketId,
      'page': page,
      'limit': limit,
      'sortBy': sortBy,
      'sortType': sortType,
      'size': size,
    };
  }
}