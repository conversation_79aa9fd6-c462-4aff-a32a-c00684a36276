import 'dart:developer';
import 'dart:io';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/svg.dart';
import 'package:eapprove/common/form/form_label.dart';

class FileUploadWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final FormStateManager stateManager;
  final int? rowIndex;
  final bool? isShowLabel;

  const FileUploadWidget({
    super.key,
    required this.data,
    this.onChange,
    required this.stateManager,
    this.rowIndex,
    this.isShowLabel = true,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  String _fileType = 'normal';
  List<String> _acceptedExtensions = [];
  int _maxFileSize = 1024;
  bool _isMulti = true;

  @override
  void initState() {
    super.initState();
    _parseConfiguration();
    _initializeUploadedFiles();
  }

  void _parseConfiguration() {
    // Parse fileType
    if (widget.data.fileType != null) {
      _fileType = widget.data.fileType.toString();
    }

    // Parse accepted file extensions
    if (widget.data.acceptFile != null &&
        widget.data.acceptFile.toString().isNotEmpty) {
      final acceptFile = widget.data.acceptFile.toString();
      _acceptedExtensions = acceptFile
          .split(',')
          .map((ext) => ext.trim().replaceAll('.', ''))
          .where((ext) => ext.isNotEmpty)
          .toList();

      // If no extensions specified but acceptFile exists, assume PDF is default
      if (_acceptedExtensions.isEmpty && acceptFile.contains('pdf')) {
        _acceptedExtensions = ['pdf'];
      }
    }

    // If no extensions specified at all, use default set
    if (_acceptedExtensions.isEmpty) {
      _acceptedExtensions = ['pdf', 'doc', 'docx', 'jpg', 'png'];
    }

    // Parse file size limit
    if (widget.data.fileSize != null) {
      try {
        _maxFileSize = int.parse(widget.data.fileSize.toString());
      } catch (e) {
        debugPrint('Error parsing fileSize: $e');
      }
    }

    // Parse multi-file support
    if (widget.data.isMulti != null) {
      _isMulti = widget.data.isMulti.toString().toLowerCase() == 'true';
    }

    // log('File upload configuration: fileType=$_fileType, acceptedExtensions=$_acceptedExtensions, maxFileSize=$_maxFileSize KB, isMulti=$_isMulti');
  }

  void _initializeUploadedFiles() {
    if (widget.data.attachFileObjDefault?.isNotEmpty == true) {
      final file = {
        'fileName': widget.data.attachFileObjDefault?['fileName'],
        'path': widget.data.attachFileObjDefault?['downloadUrl'],
        'fileSize': widget.data.attachFileObjDefault?['size'],
        'idHistory': widget.data.attachFileObjDefault?['idHistory'],
        'displayName': widget.data.attachFileObjDefault?['displayName'],
        'isSave': widget.data.attachFileObjDefault?['isSave'],
        'createdUser': widget.data.attachFileObjDefault?['createdUser'],
        'createdTime': widget.data.attachFileObjDefault?['createdTime'],
        'uploadTime': widget.data.attachFileObjDefault?['uploadTime'],
      };
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        widget.stateManager.setUploadedFiles({
          'name': widget.data.name,
          'files': [file],
          'fileType': _fileType,
          'isDefault': true,
          'rowIndex': widget.rowIndex,
        });
      });
    }
  }

  @override
  void didUpdateWidget(FileUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.data != oldWidget.data) {
      _parseConfiguration();
    }

    if (widget.data.value != oldWidget.data.value) {
      // log('widget.data.value changed: ${widget.data.value}');
      _initializeUploadedFiles();
    }
  }

  bool get _shouldShowUploadButton {
    if (widget.data.readonly == true) return false;

    final uploadedFiles = widget.stateManager.getUploadedFiles();
    // log('_shouldShowUploadButtonuploadedFiles: $uploadedFiles', name: 'FileUploadWidget');
    final hasFiles = uploadedFiles.any((file) =>
        file['name'] == widget.data.name &&
        file['files'].isNotEmpty &&
        file['rowIndex'] == widget.rowIndex);
    // log('hasFiles: $hasFiles', name: 'FileUploadWidget');
    if (!_isMulti && hasFiles) return false;

    return true;
  }

  Future<void> _pickAndUploadFile() async {
    if (widget.data.readonly == true) return;

    final uploadedFiles = widget.stateManager.getUploadedFiles();
    final hasFiles = uploadedFiles.any((file) =>
        file['name'] == widget.data.name &&
        file['files'].isNotEmpty &&
        file['rowIndex'] == widget.rowIndex);

    if (!_isMulti && hasFiles) {
      // If single file mode and already has a file, return
      return;
    }

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowMultiple: _isMulti,
        allowedExtensions: _acceptedExtensions,
      );

      if (result != null) {
        final newFiles = <Map<String, dynamic>>[];

        for (var file in result.files) {
          final filePath = file.path;
          if (filePath == null) continue;

          // File size check
          final fileBytes = await File(filePath).length();
          final fileSizeKB = fileBytes / 1024;

          if (fileSizeKB > _maxFileSize) {
            // Show size error dialog
            continue;
          }

          final newFile = {
            'fileName': file.name,
            'path': filePath,
            'fileSize': fileSizeKB.toStringAsFixed(1),
          };

          newFiles.add(newFile);
        }

        if (newFiles.isNotEmpty) {
          // Update form state directly - this is the safest approach
          widget.stateManager.setUploadedFiles({
            'name': widget.data.name,
            'files': newFiles,
            'fileType': _fileType,
            'rowIndex': widget.rowIndex,
          }, rowIndex: widget.rowIndex);
        }
      }
    } catch (e) {
      debugPrint('Error picking file: $e');
    }
  }

  void _removeFile(int index) {
    if (widget.data.readonly == true) return;

    // log('Removing file at index $index');
    final uploadedFiles = widget.stateManager.getUploadedFiles();
    final fileData = uploadedFiles.firstWhere(
      (file) =>
          file['name'] == widget.data.name &&
          file['rowIndex'] == widget.rowIndex,
      orElse: () => {'files': []},
    );

    final files = List<Map<String, dynamic>>.from(fileData['files'] ?? []);
    if (index < files.length) {
      files.removeAt(index);

      widget.stateManager.setUploadedFiles({
        'name': widget.data.name,
        'files': files,
        'fileType': _fileType,
        'rowIndex': widget.rowIndex,
      }, rowIndex: widget.rowIndex);
    }
  }

  Widget _buildFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    String iconAsset;
    switch (extension) {
      case 'pdf':
        iconAsset = StringImage.ic_pdf;
        break;
      default:
        iconAsset = StringImage.ic_pdf;
    }

    return Center(
      child: SvgPicture.asset(
        iconAsset,
        width: 32.sp,
        height: 32.sp,
      ),
    );
  }

  String _getAcceptedFileTypesLabel() {
    if (_acceptedExtensions.isEmpty) return '';
    return 'Định dạng: ${_acceptedExtensions.map((e) => '.$e').join(', ')}';
  }

  @override
  Widget build(BuildContext context) {
    // if (widget.data.display == false) return const SizedBox.shrink();

    final uploadButton = Container(
      width: 120.w,
      child: ElevatedButton.icon(
        onPressed: _pickAndUploadFile,
        icon: SvgPicture.asset(
          StringImage.ic_upload,
          width: 16.sp,
          height: 16.sp,
        ),
        label: Text("Upload"),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          elevation: 0,
          side: BorderSide(color: Colors.grey.shade300),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
      ),
    );

    final uploadedFiles = widget.stateManager.getUploadedFiles();
    final fileData = uploadedFiles.firstWhere(
      (file) =>
          file['name'] == widget.data.name &&
          file['rowIndex'] == widget.rowIndex,
      orElse: () => {'files': []},
    );
    final files = List<Map<String, dynamic>>.from(fileData['files'] ?? []);

    Widget fileItem(int index) => Container(
          margin: EdgeInsets.only(top: index != 0 ? 12.h : 0),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.withOpacity(0.2)),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFileIcon(files[index]['fileName'] ?? ''),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            files[index]['fileName'] ?? '',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 2.w,
                        ),
                        if (widget.data.readonly != true)
                          GestureDetector(
                            onTap: () => _removeFile(index),
                            child: SvgPicture.asset(
                              StringImage.ic_red_trash,
                              width: 16.w,
                              height: 16.h,
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                  ],
                ),
              ),
            ],
          ),
        );

    return Container(
      margin: EdgeInsets.only(top: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if(widget.isShowLabel == true)
          FormLabel(
            displayName: widget.data.displayName,
              label: widget.data.label,
              suggestText: widget.data.suggestText,
              isRequired: widget.data.validations?['required'] == true,
            ),
          Container(
            margin: EdgeInsets.only(top: 8.h),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _shouldShowUploadButton ? uploadButton : SizedBox.shrink(),
                  ],
                ),

                // Files display underneath
                if (files.any((e) => e['path'] != null && e['path'].isNotEmpty))
                  ...files
                      .asMap()
                      .entries
                      .map((entry) => fileItem(entry.key))
                      .toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
