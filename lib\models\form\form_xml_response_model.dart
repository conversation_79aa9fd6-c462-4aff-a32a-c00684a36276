import 'package:xml/xml.dart';

// Main response model
class BpmnFormXMLResponse {
  final String id;
  final String bpmn20Xml;

  BpmnFormXMLResponse({
    required this.id,
    required this.bpmn20Xml,
  });

  factory BpmnFormXMLResponse.fromJson(Map<String, dynamic> json) {
    // Handle potential type mismatches in the JSON response
    String extractId(dynamic value) {
      if (value == null) return '';
      // Handle the case where id might be an int in the API response
      return value.toString();
    }

    String extractBpmnXml(dynamic value) {
      if (value == null) return '';
      if (value is String) return value;
      return value.toString();
    }

    return BpmnFormXMLResponse(
      id: extractId(json['id']),
      bpmn20Xml: extractBpmnXml(json['bpmn20Xml']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bpmn20Xml': bpmn20Xml,
    };
  }

  // Parse the XML content and return a BpmnDefinition
  BpmnDefinition parseXml() {
    if (bpmn20Xml.isEmpty) {
      throw Exception('BPMN XML content is empty');
    }
    return BpmnDefinition.fromXml(bpmn20Xml);
  }
}

// Model for BPMN definitions
class BpmnDefinition {
  final String id;
  final String targetNamespace;
  final String exporter;
  final String exporterVersion;
  final BpmnProcess process;

  BpmnDefinition({
    required this.id,
    required this.targetNamespace,
    required this.exporter,
    required this.exporterVersion,
    required this.process,
  });

  // Parse the XML string to create a BpmnDefinition
  factory BpmnDefinition.fromXml(String xmlString) {
    final document = XmlDocument.parse(xmlString);

    // Get the BPMN definitions element
    final definitionsElement = document.findAllElements('bpmn:definitions').first;

    // Extract attributes
    final id = definitionsElement.getAttribute('id') ?? '';
    final targetNamespace = definitionsElement.getAttribute('targetNamespace') ?? '';
    final exporter = definitionsElement.getAttribute('exporter') ?? '';
    final exporterVersion = definitionsElement.getAttribute('exporterVersion') ?? '';

    // Find and parse the process element
    final processElement = definitionsElement.findAllElements('bpmn:process').first;
    final process = BpmnProcess.fromXmlElement(processElement);

    return BpmnDefinition(
      id: id,
      targetNamespace: targetNamespace,
      exporter: exporter,
      exporterVersion: exporterVersion,
      process: process,
    );
  }
}

// Model for BPMN process
class BpmnProcess {
  final String id;
  final bool isExecutable;
  final List<BpmnElement> elements;
  final List<BpmnSequenceFlow> flows;

  BpmnProcess({
    required this.id,
    required this.isExecutable,
    required this.elements,
    required this.flows,
  });

  // Create a BpmnProcess from an XML element
  factory BpmnProcess.fromXmlElement(XmlElement element) {
    final id = element.getAttribute('id') ?? '';
    final isExecutableStr = element.getAttribute('isExecutable') ?? 'false';
    final isExecutable = isExecutableStr.toLowerCase() == 'true';

    final elements = <BpmnElement>[];
    final flows = <BpmnSequenceFlow>[];

    // Parse different types of elements
    for (final child in element.childElements) {
      final elementId = child.getAttribute('id') ?? '';
      final elementName = child.getAttribute('name') ?? '';

      if (child.name.local == 'startEvent') {
        final formKey = child.getAttribute('camunda:formKey');
        elements.add(BpmnStartEvent(id: elementId, name: elementName, formKey: formKey));
      }
      else if (child.name.local == 'userTask') {
        final formKey = child.getAttribute('camunda:formKey');
        final assignee = child.getAttribute('camunda:assignee');

        // Extract properties
        final properties = <String, String>{};
        final extensionElements = child.findElements('bpmn:extensionElements').firstOrNull;
        if (extensionElements != null) {
          final camundaProperties = extensionElements.findElements('camunda:properties').firstOrNull;
          if (camundaProperties != null) {
            for (final prop in camundaProperties.findElements('camunda:property')) {
              final name = prop.getAttribute('name') ?? '';
              final value = prop.getAttribute('value') ?? '';
              if (name.isNotEmpty) {
                properties[name] = value;
              }
            }
          }
        }

        elements.add(BpmnUserTask(
          id: elementId,
          name: elementName,
          formKey: formKey,
          assignee: assignee,
          properties: properties,
        ));
      }
      else if (child.name.local == 'endEvent') {
        elements.add(BpmnEndEvent(id: elementId, name: elementName));
      }
      else if (child.name.local == 'sequenceFlow') {
        final sourceRef = child.getAttribute('sourceRef') ?? '';
        final targetRef = child.getAttribute('targetRef') ?? '';
        flows.add(BpmnSequenceFlow(
          id: elementId,
          name: elementName,
          sourceRef: sourceRef,
          targetRef: targetRef,
        ));
      }
    }

    return BpmnProcess(
      id: id,
      isExecutable: isExecutable,
      elements: elements,
      flows: flows,
    );
  }

  // Find an element by its ID
  BpmnElement? getElementById(String id) {
    try {
      return elements.firstWhere((element) => element.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get next elements after a given element
  List<BpmnElement> getNextElements(String elementId) {
    final nextElements = <BpmnElement>[];

    // Find flows that have this element as source
    for (final flow in flows) {
      if (flow.sourceRef == elementId) {
        final target = getElementById(flow.targetRef);
        if (target != null) {
          nextElements.add(target);
        }
      }
    }

    return nextElements;
  }
}

// Base class for all BPMN elements
abstract class BpmnElement {
  final String id;
  final String name;

  BpmnElement({
    required this.id,
    required this.name,
  });
}

// Start event
class BpmnStartEvent extends BpmnElement {
  final String? formKey;

  BpmnStartEvent({
    required super.id,
    required super.name,
    this.formKey,
  });
}

// User task
class BpmnUserTask extends BpmnElement {
  final String? formKey;
  final String? assignee;
  final Map<String, String> properties;

  BpmnUserTask({
    required super.id,
    required super.name,
    this.formKey,
    this.assignee,
    this.properties = const {},
  });

  // Get a specific property value
  String? getProperty(String propertyName) {
    return properties[propertyName];
  }

  // Check if this task has SLA requirements
  bool hasSla() {
    return properties.containsKey('setting_slaFinish');
  }

  // Get SLA in days (if available)
  int? getSlaInDays() {
    final sla = properties['setting_slaFinish'];
    return sla != null ? int.tryParse(sla) : null;
  }
}

// End event
class BpmnEndEvent extends BpmnElement {
  BpmnEndEvent({
    required super.id,
    required super.name,
  });
}

// Sequence flow
class BpmnSequenceFlow {
  final String id;
  final String name;
  final String sourceRef;
  final String targetRef;

  BpmnSequenceFlow({
    required this.id,
    required this.name,
    required this.sourceRef,
    required this.targetRef,
  });
}

// Example usage:
/*
void main() {
  final String jsonData = '{"id": "EADMT001_1000:5:ac024ede-2a05-11ef-99f0-aab5bcef8f51", "bpmn20Xml": "...XML content..."}';

  // Parse the JSON
  final Map<String, dynamic> jsonMap = jsonDecode(jsonData);
  final BpmnFormXMLResponse response = BpmnFormXMLResponse.fromJson(jsonMap);

  // Parse the XML content
  final definition = response.parseXml();

  // Access process data
  print('Process ID: ${definition.process.id}');

  // List all tasks
  for (final element in definition.process.elements) {
    if (element is BpmnUserTask) {
      print('Task: ${element.name}, Assignee: ${element.assignee}');

      // Access properties
      if (element.hasSla()) {
        print('SLA: ${element.getSlaInDays()} days');
      }
    }
  }

  // Visualize the process flow
  BpmnElement? startElement;
  for (final element in definition.process.elements) {
    if (element is BpmnStartEvent) {
      startElement = element;
      break;
    }
  }

  if (startElement != null) {
    print('Starting from: ${startElement.name}');
    _traverseProcess(definition.process, startElement.id);
  }
}

void _traverseProcess(BpmnProcess process, String currentElementId, [int depth = 0]) {
  final indent = '  ' * depth;
  final currentElement = process.getElementById(currentElementId);

  if (currentElement == null) return;

  print('$indent→ ${currentElement.name} (${currentElement.id})');

  final nextElements = process.getNextElements(currentElementId);
  for (final nextElement in nextElements) {
    _traverseProcess(process, nextElement.id, depth + 1);
  }
}
*/