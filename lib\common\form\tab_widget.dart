import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'common_form_widget.dart';
import 'dart:developer' as developer;

class TabWidget extends StatefulWidget {
  final FormItemInfo data;
  final List<FormItemInfo> childFormItems;
  final FormStateManager stateManager;

  const TabWidget({
    Key? key,
    required this.data,
    required this.childFormItems,
    required this.stateManager,
  }) : super(key: key);

  @override
  State<TabWidget> createState() => _TabWidgetState();
}

class _TabWidgetState extends State<TabWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<TabItem> _tabs;
  late int _selectedTabIndex;

  @override
  void initState() {
    super.initState();
    _initializeTabData();
  }

  void _initializeTabData() {
    // Parse tab configuration from data
    _tabs = _parseTabItems();

    // Initialize tab controller
    _tabController = TabController(length: _tabs.length, vsync: this);
    _selectedTabIndex = 0;

    // Listen for tab changes
    _tabController.addListener(() {
      if (_tabController.indexIsChanging ||
          _tabController.index != _selectedTabIndex) {
        setState(() {
          _selectedTabIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<TabItem> _parseTabItems() {
    final tabData = widget.data.optionConfig?['tab'] as List?;
    if (tabData == null || tabData.isEmpty) {
      // Default to one tab if no tab config found
      return [TabItem(id: 'default', label: 'Tab 1')];
    }

    List<TabItem> tabs = [];

    // Extract the tab items from the first tab configuration
    if (tabData.isNotEmpty) {
      final firstTabConfig = tabData[0];
      final items = firstTabConfig['items'] as List?;

      if (items != null && items.isNotEmpty) {
        tabs = items.map((item) {
          return TabItem(
            id: item['id']?.toString() ?? '',
            label: item['label']?.toString() ?? 'Tab',
          );
        }).toList();
      }
    }

    return tabs.isEmpty ? [TabItem(id: 'default', label: 'Tab 1')] : tabs;
  }

  // Get form items for the current selected tab
  List<FormItemInfo> _getTabFormItems() {
    if (_tabs.isEmpty || _selectedTabIndex >= _tabs.length) {
      return [];
    }
    final tabId = widget.data.id; // id tab cha
    final tabItemId = _tabs[_selectedTabIndex].id; // id tab con
    developer.log('tabId:::: $tabId', name: "tabWidget");
    developer.log('tabItemId:::: $tabItemId', name: "tabWidget");
    developer.log(
        'widget.childFormItems:::: ${widget.childFormItems.map((e) => e.name)}',
        name: "tabWidget");
    return widget.childFormItems
        .where((item) => item.tab == tabId && item.tabItem == tabItemId)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    // Don't render if display is false
    if (widget.data.display == false) {
      return const SizedBox.shrink();
    }

    // Apply font styling based on fontWeight property
    TextStyle titleStyle = TextStyle(
      fontSize: 16.sp,
      color: getColorSkin().ink1,
    );

    if (widget.data.fontWeight == 'bold' ||
        widget.data.fontWeight == 'font-bold') {
      titleStyle = titleStyle.copyWith(fontWeight: FontWeight.bold);
    } else if (widget.data.fontWeight == 'italic') {
      titleStyle = titleStyle.copyWith(fontStyle: FontStyle.italic);
    } else if (widget.data.fontWeight == 'underline') {
      titleStyle = titleStyle.copyWith(decoration: TextDecoration.underline);
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab header
          Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorColor: Theme.of(context).primaryColor,
              labelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.normal,
              ),
              tabs: _tabs.map((tab) => Tab(text: tab.label)).toList(),
            ),
          ),

          // Tab content
          Container(
            padding: EdgeInsets.all(16.r),
            child: _buildTabContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    final tabItems = _getTabFormItems();
    developer.log('tabItems:::: ${tabItems.map((e) => e.name)}');
    if (tabItems.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 32.h),
          child: Text(
            'No form items in this tab',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14.sp,
            ),
          ),
        ),
      );
    }

    // Sort items by their order
    tabItems.sort((a, b) {
      // First sort by step if available
      final aStep = double.tryParse(a.step ?? '') ?? 0;
      final bStep = double.tryParse(b.step ?? '') ?? 0;
      if (aStep != bStep) {
        return aStep.compareTo(bStep);
      }

      // Then sort by row order if available
      final aRowOrder = a.rowSortOrder ?? 0;
      final bRowOrder = b.rowSortOrder ?? 0;
      if (aRowOrder != bRowOrder) {
        return aRowOrder.compareTo(bRowOrder);
      }

      // Then sort by column order if available
      final aColOrder = a.colSortOrder ?? 0;
      final bColOrder = b.colSortOrder ?? 0;
      if (aColOrder != bColOrder) {
        return aColOrder.compareTo(bColOrder);
      }

      // Then sort by field order
      final aFieldOrder = a.fieldSortOrder ?? 0;
      final bFieldOrder = b.fieldSortOrder ?? 0;
      if (aFieldOrder != bFieldOrder) {
        return aFieldOrder.compareTo(bFieldOrder);
      }

      return 0;
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: tabItems.map((item) {
        return _buildWidget(item);
      }).toList(),
    );
  }

  Widget _buildWidget(FormItemInfo formWidget) {
    // Get event expression results before building the widget
    final eventResults = formWidget.eventExpression?.isNotEmpty == true
        ? widget.stateManager
            .getEventExpressionResults(formWidget.name.toString())
        : null;

    // --- BẮT ĐẦU xử lý switchField ---
    FormItemInfo currentWidget = formWidget;

    // Process event expression results
    bool shouldDisplay = currentWidget.display ?? true;
    String currentLabel = currentWidget.label ?? '';
    bool currentReadonly = currentWidget.readonly ?? false;
    String currentSuggestText = currentWidget.suggestText ?? '';
    bool currentRequired = currentWidget.validations?['required'] ?? false;

    if (eventResults != null) {
      // Process ten_truong
      final tenTruong = eventResults['ten_truong']?.toString();
      if (tenTruong != null) {
        currentLabel = tenTruong;
      }

      // Process bat_buoc
      final batBuoc = eventResults['bat_buoc']?.toString();
      if (batBuoc != null) {
        currentRequired = batBuoc.toLowerCase() == 'true';
      }

      // Process chi_duoc_doc
      final chiDuocDoc = eventResults['chi_duoc_doc']?.toString();

      if (chiDuocDoc != null) {
        currentReadonly = chiDuocDoc.toLowerCase() == 'true';
      }

      // Process huong_dan
      final huongDan = eventResults['huong_dan']?.toString();
      if (huongDan != null && huongDan.isNotEmpty) {
        currentSuggestText = huongDan;
      } else if (formWidget.suggestText?.isNotEmpty == true) {
        currentSuggestText = formWidget.suggestText!;
      } else if (formWidget.tooltip != null) {
        currentSuggestText = formWidget.tooltip!;
      }

      // Process hien_thi
      final hienThi = eventResults['hien_thi'];
      if (hienThi != null) {
        shouldDisplay = hienThi is bool
            ? hienThi
            : hienThi.toString().toLowerCase() == 'true';
      }
    }

    if (shouldDisplay == false) {
      return const SizedBox.shrink();
    }

    // Sử dụng currentWidget đã merge config nếu có switchField
    final mergedWidget = currentWidget.copyWith(
      id: currentWidget.id,
      name: currentWidget.name,
      type: currentWidget.type,
      label: currentLabel,
      readonly: currentReadonly,
      suggestText: currentSuggestText,
      validations: {...?currentWidget.validations, 'required': currentRequired},
      display: shouldDisplay,
      splitter: currentWidget.splitter,
      fieldSortOrder: currentWidget.fieldSortOrder,
      eventExpression: currentWidget.eventExpression,
      placeholder: currentWidget.placeholder,
      value: currentWidget.value,
      useTimeDefault: currentWidget.useTimeDefault,
      fontWeight: currentWidget.fontWeight,
      optionConfig: currentWidget.optionConfig,
      displayName: currentWidget.displayName,
    );

    String? currentError =
        widget.stateManager.getError(currentWidget.name.toString());

    Widget formContent = Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (mergedWidget.type != FormItemType.checkbox)
            Container(
              decoration: BoxDecoration(),
              child: Row(
                children: [
                  Expanded(
                    child: RichText(
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      softWrap: true,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: mergedWidget.displayName != null &&
                                    mergedWidget.displayName != ""
                                ? mergedWidget.displayName.toString()
                                : '${mergedWidget.label}',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400,
                              color: Colors.black,
                            ),
                          ),
                          if (mergedWidget.validations?['required'] == true)
                            const TextSpan(
                              text: ' *',
                              style: TextStyle(color: Colors.red),
                            ),
                          if (mergedWidget.suggestText != null &&
                              mergedWidget.suggestText!.isNotEmpty)
                            WidgetSpan(
                              child: Padding(
                                padding: EdgeInsets.only(left: 4.w),
                                child: GestureDetector(
                                  onTap: () {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          content:
                                              Text(mergedWidget.suggestText!)),
                                    );
                                  },
                                  child: Icon(Icons.help_outline, size: 20),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          Container(
            margin: EdgeInsets.only(
                top: mergedWidget.type != FormItemType.checkbox ? 8.h : 0),
            child: _buildFormItem(mergedWidget),
          ),
          if (currentError != null)
            Padding(
              padding: EdgeInsets.only(top: 4.h),
              child: Text(
                currentError,
                style: TextStyle(color: Colors.red, fontSize: 12.sp),
              ),
            ),
        ],
      ),
    );

    return formContent;
  }

  Widget _buildFormItem(FormItemInfo formWidget) {
    try {
      return FormItemWidget(
        data: formWidget,
        stateManager: widget.stateManager,
      );
    } catch (e) {
      // Return an error widget instead of crashing
      return Text('Error rendering field: ${formWidget.name ?? 'Unknown'}');
    }
  }
}

class TabItem {
  final String id;
  final String label;

  TabItem({
    required this.id,
    required this.label,
  });
}
