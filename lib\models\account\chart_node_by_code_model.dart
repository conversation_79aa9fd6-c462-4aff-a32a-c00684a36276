import 'dart:convert';

class ChartNodeByCodeModel {
  final String code;
  final String message;
  final ChartNodeDataWrapper data;

  const ChartNodeByCodeModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ChartNodeByCodeModel.fromJson(Map<String, dynamic> json) {
    return ChartNodeByCodeModel(
      code: json['code'] as String,
      message: json['message'] as String,
      data: ChartNodeDataWrapper.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  factory ChartNodeByCodeModel.fromJsonString(String jsonString) {
    return ChartNodeByCodeModel.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }

  bool get isSuccess => code == 'SUCCESS';
}

class ChartNodeDataWrapper {
  final List<ChartNodeData> data;

  const ChartNodeDataWrapper({
    required this.data,
  });

  factory ChartNodeDataWrapper.fromJson(Map<String, dynamic> json) {
    return ChartNodeDataWrapper(
      data: (json['data'] as List<dynamic>)
          .map((item) => ChartNodeData.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

class ChartNodeData {
  final int id;
  final String code;
  final String type;
  final int chartNodeId;
  final int chartId;
  final String name;
  final int level;
  final String status;
  final String description;
  final String? color;
  final bool colorApplied;
  final int? chartLinkId;
  final int? nodeLinkId;
  final String createdDate;
  final String createdUser;
  final String modifiedDate;
  final String modifiedUser;
  final String shortName;
  final String costCenter;
  final String costCenterName;
  final String parentCode;
  final String orgLayer;
  final bool isOrgChartNode;
  final int chartCustomId;
  final bool isDeleted;
  final bool isSyncUser;
  final bool isSyncOrgLayer;
  final bool isSyncChartNode;
  final bool isSyncManager;
  final dynamic parent;

  const ChartNodeData({
    required this.id,
    required this.code,
    required this.type,
    required this.chartNodeId,
    required this.chartId,
    required this.name,
    required this.level,
    required this.status,
    required this.description,
    this.color,
    required this.colorApplied,
    this.chartLinkId,
    this.nodeLinkId,
    required this.createdDate,
    required this.createdUser,
    required this.modifiedDate,
    required this.modifiedUser,
    required this.shortName,
    required this.costCenter,
    required this.costCenterName,
    required this.parentCode,
    required this.orgLayer,
    required this.isOrgChartNode,
    required this.chartCustomId,
    required this.isDeleted,
    required this.isSyncUser,
    required this.isSyncOrgLayer,
    required this.isSyncChartNode,
    required this.isSyncManager,
    this.parent,
  });

  factory ChartNodeData.fromJson(Map<String, dynamic> json) {
    return ChartNodeData(
      id: json['id'] as int,
      code: json['code'] as String,
      type: json['type'] as String,
      chartNodeId: json['chartNodeId'] as int,
      chartId: json['chartId'] as int,
      name: json['name'] as String,
      level: json['level'] as int,
      status: json['status'] as String,
      description: json['description'] as String,
      color: json['color'] as String?,
      colorApplied: json['colorApplied'] as bool,
      chartLinkId: json['chartLinkId'] as int?,
      nodeLinkId: json['nodeLinkId'] as int?,
      createdDate: json['createdDate'] as String,
      createdUser: json['createdUser'] as String,
      modifiedDate: json['modifiedDate'] as String,
      modifiedUser: json['modifiedUser'] as String,
      shortName: json['shortName'] as String,
      costCenter: json['costCenter'] as String,
      costCenterName: json['costCenterName'] as String,
      parentCode: json['parentCode'] as String,
      orgLayer: json['orgLayer'] as String,
      isOrgChartNode: json['isOrgChartNode'] as bool,
      chartCustomId: json['chartCustomId'] as int,
      isDeleted: json['isDeleted'] as bool,
      isSyncUser: json['isSyncUser'] as bool,
      isSyncOrgLayer: json['isSyncOrgLayer'] as bool,
      isSyncChartNode: json['isSyncChartNode'] as bool,
      isSyncManager: json['isSyncManager'] as bool,
      parent: json['parent'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'type': type,
      'chartNodeId': chartNodeId,
      'chartId': chartId,
      'name': name,
      'level': level,
      'status': status,
      'description': description,
      'color': color,
      'colorApplied': colorApplied,
      'chartLinkId': chartLinkId,
      'nodeLinkId': nodeLinkId,
      'createdDate': createdDate,
      'createdUser': createdUser,
      'modifiedDate': modifiedDate,
      'modifiedUser': modifiedUser,
      'shortName': shortName,
      'costCenter': costCenter,
      'costCenterName': costCenterName,
      'parentCode': parentCode,
      'orgLayer': orgLayer,
      'isOrgChartNode': isOrgChartNode,
      'chartCustomId': chartCustomId,
      'isDeleted': isDeleted,
      'isSyncUser': isSyncUser,
      'isSyncOrgLayer': isSyncOrgLayer,
      'isSyncChartNode': isSyncChartNode,
      'isSyncManager': isSyncManager,
      'parent': parent,
    };
  }
}