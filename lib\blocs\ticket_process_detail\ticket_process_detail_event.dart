import 'package:equatable/equatable.dart';

abstract class TicketProcessDetailEvent extends Equatable {
  const TicketProcessDetailEvent();

  @override
  List<Object?> get props => [];
}

class LoadTicketProcessDetail extends TicketProcessDetailEvent {
  final dynamic procInstId;
  final String taskDefKey;
  final String user;
  final String? status;

  const LoadTicketProcessDetail({
    required this.procInstId,
    required this.taskDefKey,
    required this.user,
    this.status,
  });

  @override
  List<Object?> get props => [procInstId, taskDefKey, user, status];
}

class LoadTicketProcessDetailById extends TicketProcessDetailEvent {
  final dynamic ticketId;

  const LoadTicketProcessDetailById({required this.ticketId});
}

class LoadThongTinPhieu extends TicketProcessDetailEvent {
  final String procInstId;

  const LoadThongTinPhieu({required this.procInstId});

  @override
  List<Object?> get props => [procInstId];
}

class LoadThongTinDauVao extends TicketProcessDetailEvent {
  final String taskId;
  final String type;

  const LoadThongTinDauVao({required this.taskId, required this.type});

  @override
  List<Object?> get props => [taskId, type];
}

class CheckToTrinhHandleTicket extends TicketProcessDetailEvent {
  final String procDefId;
  final int serviceId;
  final String? taskDefKey;

  CheckToTrinhHandleTicket({
    required this.procDefId,
    required this.serviceId,
    this.taskDefKey,
  });

  @override
  List<Object> get props => [procDefId, serviceId, taskDefKey ?? ''];
}

class GetSignedFileUrl extends TicketProcessDetailEvent {
  final String fileName;
  final String originalFileName;

  const GetSignedFileUrl({required this.fileName, required this.originalFileName});
}

class LoadUserTaskInfo extends TicketProcessDetailEvent {
  final dynamic ticketId;

  const LoadUserTaskInfo({required this.ticketId});
}

//chia sẻ
class LoadUserDropdown extends TicketProcessDetailEvent {}

class LoadShareTickets extends TicketProcessDetailEvent {
  final String procInstId;
  final String search;
  final int page;
  final int limit;
  final String type;

  const LoadShareTickets({
    required this.procInstId,
    this.search = '',
    this.page = 1,
    this.limit = 10,
    this.type = 'SHARED',
  });
}

class DeleteShareTicket extends TicketProcessDetailEvent {
  final int shareId;
  final String procInstId;

  const DeleteShareTicket({
    required this.shareId,
    required this.procInstId,
  });
}

class AddShareTicket extends TicketProcessDetailEvent {
  final String procInstId;
  final List<String> sharedUsers;

  const AddShareTicket({
    required this.procInstId,
    required this.sharedUsers,
  });
}

class LoadAssistantOpinion extends TicketProcessDetailEvent {
  final dynamic ticketId;

  const LoadAssistantOpinion({required this.ticketId});
}

class ApproveTicket extends TicketProcessDetailEvent {
  final dynamic taskId;
  final dynamic procInstId;
  final bool signComplete;
  final dynamic body;

  const ApproveTicket({
    required this.taskId,
    required this.procInstId,
    required this.signComplete,
    required this.body,
  });
}

class SignPrintZone extends TicketProcessDetailEvent {
  final Map<String, dynamic> requestBody;

  const SignPrintZone({
    required this.requestBody,
  });
}

class LoadInheritableTasksList extends TicketProcessDetailEvent {
  final String procDefId;
  final String ticketId;

  const LoadInheritableTasksList({
    required this.procDefId,
    required this.ticketId,
  });

  @override
  List<Object?> get props => [procDefId, ticketId];
}

class SaveDraft extends TicketProcessDetailEvent {
  final String taskId;
  final String procInstId;
  final Map<String, dynamic> variables;

  const SaveDraft({
    required this.taskId,
    required this.procInstId,
    required this.variables,
  });

  @override
  List<Object?> get props => [taskId, procInstId, variables];
}
