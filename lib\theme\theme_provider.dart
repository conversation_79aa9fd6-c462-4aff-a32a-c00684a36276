import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/theme/app_theme_bloc.dart';

class AppThemeProvider extends StatelessWidget {
  final Widget child;

  const AppThemeProvider({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AppThemeBloc>(
      create: (_) => AppThemeBloc(),
      child: child,
    );
  }
} 