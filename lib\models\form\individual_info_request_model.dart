import 'dart:convert';

class IndividualInfoRequestModel {
  final List<dynamic>? orgchart;
  final String? infoType;
  final List<Condition>? condition;

  IndividualInfoRequestModel({
    this.orgchart,
    this.infoType,
    this.condition,
  });

  Map<String, dynamic> toJson() {
    return {
      if (orgchart != null) 'orgchart': orgchart,
      if (infoType != null) 'infoType': infoType,
      if (condition != null) 'condition': condition!.map((c) => c.toJson()).toList(),
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }

  // Factory constructor to create a request with default values matching your example
  factory IndividualInfoRequestModel.defaultFilter() {
    return IndividualInfoRequestModel(
      orgchart: [],
      infoType: "user",
      condition: [
        Condition(
            expression: "and",
            filterType: "user",
            filterField: "chartNodeId",
            operator: "=",
            value: "7167"
        ),
      ],
    );
  }
}

class Condition {
  final String? expression;
  final String? filterType;
  final String? filterField;
  final String? operator;
  final String? value;

  Condition({
    this.expression,
    this.filterType,
    this.filterField,
    this.operator,
    this.value,
  });

  Map<String, dynamic> toJson() {
    return {
      if (expression != null) 'expression': expression,
      if (filterType != null) 'filterType': filterType,
      if (filterField != null) 'filterField': filterField,
      if (operator != null) 'operator': operator,
      if (value != null) 'value': value,
    };
  }
}