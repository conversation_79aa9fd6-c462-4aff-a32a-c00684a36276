import 'dart:async';
import 'dart:developer' as developer;
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/blocs/user/user_state.dart';
import 'package:eapprove/models/handle_ticket/assistant_opinion_response_model.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_avatar_component.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_sdk/widgets/custom_info_row.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';

class TicketCommentBlock extends StatefulWidget {
  final String title;

  const TicketCommentBlock({
    super.key,
    required this.title,
  });

  @override
  State<TicketCommentBlock> createState() => _TicketCommentBlockState();
}

class _TicketCommentBlockState extends State<TicketCommentBlock> {
  late Map<String, String> ticketInformationData;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    ticketInformationData = {
      'Loại yêu cầu': 'Tờ trình đề nghị thanh toán',
      'Trạng thái': 'Đang phê duyệt',
      'Mã công ty': '1000_DXG',
      'Thời gian đề trình': '03/01/2023 10:37',
      'Độ ưu tiên': 'Trung bình',
      'Loại tờ trình': 'Đề nghị thanh toán',
      'Người tạo': 'Nve10000001 - Nguyễn Văn A - Nhân viên',
    };
  }

  void _fetchUserInfo(AssistantOpinionResponseModel? assistantOpinion) {
    if (assistantOpinion?.data.content.isNotEmpty ?? false) {
      final userBloc = context.read<UserBloc>();
      for (var content in assistantOpinion!.data.content) {
        userBloc.add(GetUserInfoByUsername(content.assistantEmail));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
      buildWhen: (previous, current) {
        return current is AssistantOpinionLoaded || current is TicketProcessDetailError;
      },
      builder: (context, state) {
        if (state is AssistantOpinionLoaded && state.assistantOpi.data.content.isNotEmpty) {
          // Fetch user info when assistant opinion is loaded
          _fetchUserInfo(state.assistantOpi);
          return Column(
            children: [
              CustomExpandedList<String>(
                expandedSvgIconPath: StringImage.ic_arrow_up,
                collapsedSvgIconPath: StringImage.ic_arrow_right,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                title: widget.title,
                cardColor: Colors.white,
                isBuildLeading: true,
                svgIconPath: StringImage.ic_ticket_comment,
                iconWidth: 24.w,
                iconHeight: 24.h,
                isExpanded: true,
                titleStyle: getTypoSkin().title5Medium.copyWith(color: getColorSkin().ink1),
                child: _buildFormContent(state.assistantOpi),
              ),
              SizedBox(height: 16.h),
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildFormContent(AssistantOpinionResponseModel? assistantOpinion) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display assistant opinions if available
          if (assistantOpinion?.data.content.isNotEmpty ?? false) ...[
            SizedBox(height: 8.h),
            ...assistantOpinion!.data.content.map((content) => BlocBuilder<UserBloc, UserState>(
              buildWhen: (previous, current) {
                if (current is UserInfoByUsername) {
                  return current.userInfoByUserNameResponse.data.username == content.assistantEmail;
                }
                return false;
              },
              builder: (context, state) {
                String userName = "";
                if (state is UserInfoByUsername) {
                  final userInfo = state.userInfoByUserNameResponse.data;
                  userName = "${userInfo.firstname} ${userInfo.lastname}";
                } else {
                  userName = content.assistantEmail.split('@')[0];
                }

                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomCircleAvatar(name: userName),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 8.w,
                            children: [
                              Text(
                                userName,
                                style: getTypoSkin().medium14.copyWith(
                                  color: getColorSkin().ink1,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                content.formatedCreateAt,
                                style: getTypoSkin().label5Regular.copyWith(
                                  color: getColorSkin().ink3,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            content.opinion.replaceAll(RegExp(r'<[^>]+>'), ''),
                            style: getTypoSkin().label4Regular.copyWith(
                              color: getColorSkin().ink1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            )),
          ],
        ],
      ),
    );
  }
}