import 'dart:convert';

class CallServiceRequestModel {
  String? url;
  String? method;
  Map<String, dynamic>? params;
  Headers? headers;
  bool? isSaveLog;

  CallServiceRequestModel({
    this.url,
    this.method,
    this.params,
    this.headers,
    this.isSaveLog,
  });

  factory CallServiceRequestModel.fromJson(Map<String, dynamic> json) {
    return CallServiceRequestModel(
      url: json['url']?.toString(),
      method: json['method']?.toString(),
      params: json['params'] is Map ? Map<String, dynamic>.from(json['params']) : null,
      headers: json['headers'] != null && json['headers'] is Map 
          ? Headers.fromJson(json['headers']) 
          : Headers(),
      isSaveLog: json['isSaveLog'] is bool ? json['isSaveLog'] : false,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['url'] = url;
    data['method'] = method;
    if (params != null) {
      data['params'] = params;
    }
    if (headers != null) {
      data['headers'] = headers!.toJson();
    }
    data['isSaveLog'] = isSaveLog ?? false;
    return data;
  }

  // Helper method to get the actual params string
  String? getParamsString() {
    if (params == null) return null;

    // Convert the numbered map back to a string
    final List<MapEntry<int, dynamic>> sortedEntries = params!.entries
        .map((e) => MapEntry(int.tryParse(e.key) ?? 0, e.value))
        .toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    return sortedEntries.map((e) => e.value.toString()).join('');
  }

  // Helper method to parse the params string into a usable object
  Map<String, dynamic>? getParsedParams() {
    final String? paramsString = getParamsString();
    if (paramsString == null) return null;

    try {
      return jsonDecode(paramsString);
    } catch (e) {
      print('Error parsing params: $e');
      return null;
    }
  }
}

class Headers {
  Map<dynamic, dynamic>? headers;

  Headers({
    this.headers,
  });

  factory Headers.fromJson(Map<dynamic, dynamic> json) {
    return Headers(
      headers: json.isNotEmpty ? Map<String, dynamic>.from(json) : {},
    );
  }

  Map<dynamic, dynamic> toJson() {
    final Map<dynamic, dynamic> data = <dynamic, dynamic>{};
    if (headers != null) {
      data.addAll(headers!);
    }
    return data;
  }
}