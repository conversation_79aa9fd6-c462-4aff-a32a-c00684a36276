import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class StepWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final Map<String, List<Widget>> stepContents;

  const StepWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.stepContents,
  }) : super(key: key);

  @override
  State<StepWidget> createState() => _StepWidgetState();
}

class _StepWidgetState extends State<StepWidget> {
  int _currentStep = 0;
  late List<Map<String, dynamic>> _steps;
  final TextEditingController _stepTitleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeSteps();
  }

  @override
  void dispose() {
    _stepTitleController.dispose();
    super.dispose();
  }

  void _initializeSteps() {
    if (widget.data.value != null && widget.data.value is List) {
      _steps = List<Map<String, dynamic>>.from(widget.data.value);
    } else {
      // Initialize with a default step if no steps exist
      _steps = [
        {"title": "Step 01", "id": "step_${DateTime.now().millisecondsSinceEpoch}"}
      ];
      // Update the form data
      widget.data.value = _steps;
      widget.onChange?.call(widget.data.name ?? '', _steps);
    }
  }

  void _addStep() {
    setState(() {
      final newStepIndex = _steps.length + 1;
      final newStepId = "step_${DateTime.now().millisecondsSinceEpoch}";
      _steps.add({
        "title": "Step ${newStepIndex.toString().padLeft(2, '0')}",
        "id": newStepId,
      });

      // Update the form data
      widget.data.value = _steps;
      widget.onChange?.call(widget.data.name ?? '', _steps);
    });
  }

  void _removeStep(int index) {
    if (_steps.length <= 1) {
      // Don't remove the last step
      return;
    }

    setState(() {
      _steps.removeAt(index);

      // Adjust current step if needed
      if (_currentStep >= _steps.length) {
        _currentStep = _steps.length - 1;
      }

      // Update the form data
      widget.data.value = _steps;
      widget.onChange?.call(widget.data.name ?? '', _steps);
    });
  }

  void _editStepTitle(int index) {
    _stepTitleController.text = _steps[index]["title"] ?? "";

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Step Title'),
        content: TextField(
          controller: _stepTitleController,
          decoration: InputDecoration(
            labelText: 'Step Title',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _steps[index]["title"] = _stepTitleController.text;

                // Update the form data
                widget.data.value = _steps;
                widget.onChange?.call(widget.data.name ?? '', _steps);
              });
              Navigator.pop(context);
            },
            child: Text('Save'),
          ),
        ],
      ),
    );
  }

  void _duplicateStep(int index) {
    setState(() {
      final originalStep = Map<String, dynamic>.from(_steps[index]);
      final newStepId = "step_${DateTime.now().millisecondsSinceEpoch}";

      // Create a copy with a new ID
      final duplicatedStep = {
        ...originalStep,
        "id": newStepId,
        "title": "${originalStep["title"]} (Copy)",
      };

      _steps.insert(index + 1, duplicatedStep);

      // Update the form data
      widget.data.value = _steps;
      widget.onChange?.call(widget.data.name ?? '', _steps);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and actions
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.data.label ?? 'Steps',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.data.readonly != true)
                ElevatedButton.icon(
                  onPressed: _addStep,
                  icon: Icon(Icons.add),
                  label: Text('Add Step'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: getColorSkin().primaryBlue,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
        ),

        // Step indicators
        _buildStepHeader(),

        // Current step content
        _buildCurrentStepContent(),

        // Navigation buttons
        _buildNavigationButtons(),
      ],
    );
  }

  Widget _buildStepHeader() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Row(
            children: List.generate(
              _steps.length,
                  (index) => _buildStepIndicator(index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int index) {
    final bool isActive = index == _currentStep;
    final bool isCompleted = index < _currentStep;

    Color backgroundColor;
    Color textColor;

    if (isActive) {
      backgroundColor = getColorSkin().primaryBlue;
      textColor = Colors.white;
    } else if (isCompleted) {
      backgroundColor = getColorSkin().primaryBlue.withOpacity(0.7);
      textColor = Colors.white;
    } else {
      backgroundColor = Colors.grey.shade200;
      textColor = Colors.black;
    }

    return Expanded(
      child: InkWell(
        onTap: widget.data.readonly == true ? null : () {
          setState(() {
            _currentStep = index;
          });
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: index == 0
                ? BorderRadius.only(topLeft: Radius.circular(8.r), bottomLeft: Radius.circular(8.r))
                : index == _steps.length - 1
                ? BorderRadius.only(topRight: Radius.circular(8.r), bottomRight: Radius.circular(8.r))
                : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 24.w,
                height: 24.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isActive ? Colors.white : (isCompleted ? Colors.white70 : Colors.grey.shade400),
                ),
                child: Center(
                  child: isCompleted && !isActive
                      ? Icon(
                    Icons.check,
                    size: 16.sp,
                    color: getColorSkin().primaryBlue,
                  )
                      : Text(
                    "${index + 1}",
                    style: TextStyle(
                      color: isActive ? getColorSkin().primaryBlue : (isCompleted ? getColorSkin().primaryBlue : Colors.black),
                      fontWeight: FontWeight.bold,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Flexible(
                child: Text(
                  _steps[index]["title"] ?? "Step ${index + 1}",
                  style: TextStyle(
                    color: textColor,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    fontSize: 12.sp,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (widget.data.readonly != true)
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color: textColor,
                    size: 16.sp,
                  ),
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editStepTitle(index);
                    } else if (value == 'duplicate') {
                      _duplicateStep(index);
                    } else if (value == 'delete') {
                      _removeStep(index);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16.sp),
                          SizedBox(width: 8.w),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy, size: 16.sp),
                          SizedBox(width: 8.w),
                          Text('Duplicate'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16.sp, color: Colors.red),
                          SizedBox(width: 8.w),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                      enabled: _steps.length > 1,
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentStepContent() {
    if (_steps.isEmpty) {
      return SizedBox.shrink();
    }

    String currentStepId = _steps[_currentStep]["id"] ?? "";
    List<Widget> content = widget.stepContents[currentStepId] ?? [];

    if (content.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 32.h),
        child: Center(
          child: Text(
            'No content for this step',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16.sp,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: content,
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_currentStep > 0)
            ElevatedButton.icon(
              onPressed: widget.data.readonly == true ? null : () {
                setState(() {
                  _currentStep--;
                });
              },
              icon: Icon(Icons.arrow_back),
              label: Text('Previous'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black,
              ),
            )
          else
            SizedBox.shrink(),

          if (_currentStep < _steps.length - 1)
            ElevatedButton.icon(
              onPressed: widget.data.readonly == true ? null : () {
                setState(() {
                  _currentStep++;
                });
              },
              icon: Icon(Icons.arrow_forward),
              label: Text('Next'),
              style: ElevatedButton.styleFrom(
                backgroundColor: getColorSkin().primaryBlue,
                foregroundColor: Colors.white,
              ),
            )
          else
            SizedBox.shrink(),
        ],
      ),
    );
  }
}