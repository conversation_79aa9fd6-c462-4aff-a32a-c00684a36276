import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/models/ticket_other_action/ticket_other_action_respone.dart';
import 'package:equatable/equatable.dart';

class RequestRelationTicketHistoryState extends Equatable {
  final ServiceStatus status;
  final String? errorMessage;
  final TicketOtherActionResponse? response;
  final TicketOtherActionResponse? responseFrom;

  const RequestRelationTicketHistoryState({
    this.status = ServiceStatus.initial,
    this.errorMessage,
    this.response,
    this.responseFrom,
  });

  RequestRelationTicketHistoryState copyWith({
    ServiceStatus? status,
    String? errorMessage,
    TicketOtherActionResponse? response,
    TicketOtherActionResponse? responseFrom,
  }) {
    return RequestRelationTicketHistoryState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      response: response ?? this.response,
    );
  }

  @override
  List<Object?> get props => [status, errorMessage, response];
}
