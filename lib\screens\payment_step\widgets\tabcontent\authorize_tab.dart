import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_event.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_state.dart';
import 'package:eapprove/screens/payment_step/widgets/handle_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/widgets/custom_text_input.dart';
import 'package:flutter_sdk/widgets/form.dart';

class AuthorizeTab extends StatelessWidget {
  final GlobalKey<FFormState> formKey;
  final TextEditingController delegateReasonController;
  final SelectItem? selectedDelegate;
  final Function(SelectItem?) onSelected;
  final Function(String) onSearchChanged;
  final String ticketId;
  final String taskId;
  final String taskDefKey;
  final int id;
  final bool? isNotForDelegate;
  final bool hideButtonsOnTablet;

  const AuthorizeTab({
    super.key,
    required this.formKey,
    required this.delegateReasonController,
    required this.selectedDelegate,
    required this.onSelected,
    required this.onSearchChanged,
    required this.ticketId,
    required this.taskId,
    required this.taskDefKey,
    required this.id,
    this.isNotForDelegate = false,
    this.hideButtonsOnTablet = false,
  });

  @override
  Widget build(BuildContext context) {
    return FForm(
      key: formKey,
      child: SingleChildScrollView(
        padding: isNotForDelegate == true
            ? null
            : EdgeInsets.only(
                left: 15.w,
                right: 15.w,
                top: 8.h,
                bottom: MediaQuery.of(context).viewInsets.bottom + 32.h,
              ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FormUtils.buildLabel('Chọn người thừa ủy quyền', required: true),
            SizedBox(height: 4.h),
            BlocBuilder<TicketDialogActionBloc, TicketDialogActionState>(
              buildWhen: (_, __) => true,
              builder: (context, state) {
                final listAuthorized = state is AuthorizationListLoaded ? state.data : [];
                final isLoading = state is AuthorizationListLoading;
                final hasError = state is AuthorizationListError;
                final options = listAuthorized.map((item) {
                  return SelectItem(label: item["name"] ?? '', value: item["username"] ?? '', data: item);
                }).toList();
                return SizedBox(
                  height: 44.h,
                  child: CustomDropdownMenu(
                    label: 'Chọn người thừa ủy quyền',
                    placeholder: "Chọn người thừa ủy quyền",
                    showLabel: false,
                    options: options,
                    enableSearch: true,
                    isLoading: isLoading,
                    errorMessage: hasError ? state.message : null,
                    isRequired: true,
                    disableDefaultSearch: true,
                    onSelected: onSelected,
                    onSearchChanged: onSearchChanged,
                    isFilled: true,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: getColorSkin().grey4Background,
                        width: 1,
                      ),
                    ),
                    imgSearchPath: StringImage.ic_search,
                    showDeleteIcon: false,
                    dropdownHeight: 40.h,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return "Vui lòng chọn người thừa ủy quyền";
                      }
                      return null;
                    },
                  ),
                );
              },
            ),
            SizedBox(height: 12.h),
            FormUtils.buildLabel('Lý do', required: true),
            SizedBox(height: 4.h),
            CustomTextField(
              controller: delegateReasonController,
              maxLines: 3,
              isRequired: true,
              backgroundColor: getColorSkin().white,
              hintText: 'Nhập lý do',
              showClearIcon: false,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return FTextFieldStatus(status: TFStatus.error, message: 'Vui lòng nhập lý do');
                }
                return null;
              },
              onChanged: (v) {},
            ),
            SizedBox(height: 16.h),
            if (!hideButtonsOnTablet)
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                    onPressed: () => context.read<TicketDialogActionBloc>().add(
                          AuthorizeEvent(
                            email: selectedDelegate?.value ?? '',
                            id: id,
                            reason: delegateReasonController.text,
                            taskDefKey: taskDefKey,
                            taskId: taskId,
                            ticketId: ticketId,
                            title: selectedDelegate?.data?["title"] ?? '',
                          ),
                        ),
                    style: FormUtils.elevatedBtnStyle(),
                    child: Text('Gửi', style: getTypoSkin().medium14.copyWith(color: getColorSkin().white)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
