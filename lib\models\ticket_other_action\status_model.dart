import 'package:flutter/material.dart';

enum StatusCode {
  additional('ADDITIONAL', '<PERSON><PERSON><PERSON> bổ sung', color: '#FA8C16', bgColor: '#FFF4E8'),
  approving('APPROVING', '<PERSON><PERSON> phê du<PERSON>t',
      color: '#00AEEF', bgColor: '#E8F4FF'),
  cancelled('CANCELLED', 'Hủy', color: '#FF4D4F', bgColor: '#FFEEEE'),
  completed('COMPLETED', 'Hoàn thành', color: '#389E0D', bgColor: '#F6FFED'),
  draft('DRAFT', 'Nháp', color: '#595959', bgColor: '#F3F3F3'),
  returned('RETURNED', 'Trả về', color: '#FA8C16', bgColor: '#FFF4E8'),
  recalled('RECALLED', '<PERSON><PERSON><PERSON> c<PERSON>u thu hồi', color: '#FA8C16', bgColor: '#FFF4E8'),
  defaulted('DEFAULT', 'Ch<PERSON> xử lý', color: '#000000', bgColor: '#FFFFFF');

  final String code;
  final String name;
  final String? color;
  final String? bgColor;

  const StatusCode(this.code, this.name, {this.color, this.bgColor});

  static StatusCode fromCode(String code) {
    return StatusCode.values.firstWhere(
      (status) => status.code == code,
      orElse: () => StatusCode.additional,
    );
  }

  static StatusCode fromName(String name) {
    return StatusCode.values.firstWhere(
      (status) => status.name == name,
      orElse: () => StatusCode.additional,
    );
  }

  String getColor() {
    return color ?? '#000000';
  }

  Widget getStatusTag() {
    Color borderColor = color != null ? Color(int.parse('0xFF${color!.replaceAll('#', '')}')) : Colors.grey;
    Color backgroundColor = color != null ? Color(int.parse('0xFF${color!.replaceAll('#', '')}')).withOpacity(0.1) : Colors.grey.withOpacity(0.1);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: borderColor,
          width: 1,
        ),
      ),
      child: Text(
        name,
        style: TextStyle(
          color: borderColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
