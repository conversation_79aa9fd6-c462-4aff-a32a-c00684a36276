import 'dart:convert';
import 'dart:math';

import 'package:eapprove/models/form/form_item_info.dart';
import 'package:eapprove/models/form/form_model.dart';
import 'package:eapprove/utils/form_parser.dart';
import 'package:flutter/foundation.dart';

/// A model representing the complete API response for form content
class FormResponse {
  /// The response code (1 = success)
  final int code;

  /// The response data containing the form template
  final FormContentData data;

  /// The response message
  final String message;

  FormResponse({
    required this.code,
    required this.data,
    required this.message,
  });

  /// Create a FormResponse from JSON data
  factory FormResponse.fromJson(Map<String, dynamic> json) {
    try {
      // Validate and parse code
      final code = json['code'];
      if (code == null) {
        // Response code is null, defaulting to 0
      }

      // Validate and parse message
      final message = json['message']?.toString() ?? '';
      
      // Validate and parse data
      final dataJson = json['data'];
      if (dataJson == null || dataJson is! Map<String, dynamic>) {
        // Invalid or missing data field in response
        return FormResponse(
          code: 0,
          data: FormContentData.empty(),
          message: 'Invalid response format',
        );
      }

      return FormResponse(
        code: code is int ? code : 0,
        data: FormContentData.fromJson(dataJson),
        message: message,
      );
    } catch (e, stackTrace) {
      return FormResponse(
        code: 0,
        data: FormContentData.empty(),
        message: 'Error parsing response: $e',
      );
    }
  }

  /// Create a FormResponse from raw JSON string
  factory FormResponse.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return FormResponse.fromJson(json);
  }

  /// Convert the FormResponse to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'data': data.toJson(),
      'message': message,
    };
  }

  /// Check if the response is successful
  bool get isSuccess => code == 1;

  /// Get the form model for easy use
  FormModel get formModel => data.formModel;
}

/// A model representing the data field in the form content response
class FormContentData {
  /// The template containing the form structure
  final TemplateContent template;

  /// The form model for easy access to form functionality
  final FormModel formModel;

  FormContentData({
    required this.template,
    required this.formModel,
  });

  /// Create an empty FormContentData
  factory FormContentData.empty() {
    return FormContentData(
      template: TemplateContent.empty(),
      formModel: FormModel.empty(),
    );
  }

  /// Create a FormContentData from JSON data
  factory FormContentData.fromJson(Map<String, dynamic> json) {
    try {
      // Handle template that might be a string or an object
      final templateData = json['template'];
      
      late Map<String, dynamic> templateJson;

      if (templateData is String) {
        try {
          // Clean and parse the template string
          String cleanedJson = templateData.trim();
          if (cleanedJson.startsWith('"') && cleanedJson.endsWith('"')) {
            cleanedJson = cleanedJson.substring(1, cleanedJson.length - 1);
            cleanedJson = cleanedJson.replaceAll('\\"', '"').replaceAll('\\\\', '\\');
          }
          
          templateJson = jsonDecode(cleanedJson) as Map<String, dynamic>;
        } catch (e) {
          templateJson = {};
        }
      } else if (templateData is Map) {
        templateJson = Map<String, dynamic>.from(templateData);
      } else {
        templateJson = {};
      }

      // Parse the template content
      final template = TemplateContent.fromJson(templateJson);

      // Use FormParser to get the FormModel
      final formModel = FormParser.parseFormJson({
        'template': templateJson,
        'id': json['id']?.toString() ?? 'default',
      });

      return FormContentData(
        template: template,
        formModel: formModel,
      );
    } catch (e, stackTrace) {
      return FormContentData.empty();
    }
  }

  /// Convert the FormContentData to JSON
  Map<String, dynamic> toJson() {
    return {
      'template': template.toJson(),
    };
  }
}

/// A model representing the template structure in the API response
class TemplateContent {
  /// Form items list (FormItemInfo objects)
  final List<FormItemInfo> form;

  /// Step information
  final List<dynamic> step;

  /// Column information
  final List<dynamic> column;

  /// Row information
  final List<dynamic> row;

  /// Tab information
  final List<dynamic> tab;

  TemplateContent({
    required this.form,
    required this.step,
    required this.column,
    required this.row,
    required this.tab,
  });

  /// Create an empty TemplateContent
  factory TemplateContent.empty() {
    return TemplateContent(
      form: [],
      step: [],
      column: [],
      row: [],
      tab: [],
    );
  }

  /// Create a TemplateContent from JSON data
  factory TemplateContent.fromJson(Map<String, dynamic> json) {
    // Safe parsing for form items
    List<FormItemInfo> parseFormItems(dynamic formData) {
      if (formData is List) {
        return formData
            .map((item) => item is Map<String, dynamic>
            ? FormItemInfo.fromJson(item)
            : FormItemInfo.fromJson({}))
            .toList();
      } else if (formData is String) {
        try {
          final decoded = jsonDecode(formData) as List;
          return decoded
              .map((item) => item is Map<String, dynamic>
              ? FormItemInfo.fromJson(item)
              : FormItemInfo.fromJson({}))
              .toList();
        } catch (e) {
          // If we can't parse it, return an empty list
          return [];
        }
      }
      return [];
    }

    // Safe parsing for other dynamic lists
    List<dynamic> parseDynamicList(dynamic data) {
      if (data is List) {
        return data;
      } else if (data is String) {
        try {
          return jsonDecode(data) as List<dynamic>;
        } catch (e) {
          // If we can't parse it, return an empty list
          return [];
        }
      }
      return [];
    }

    return TemplateContent(
      form: parseFormItems(json['form']),
      step: parseDynamicList(json['step']),
      column: parseDynamicList(json['column']),
      row: parseDynamicList(json['row']),
      tab: parseDynamicList(json['tab']),
    );
  }

  /// Convert the TemplateContent to JSON
  Map<String, dynamic> toJson() {
    return {
      'form': form.map((item) => item.toJson()).toList(),
      'step': step,
      'column': column,
      'row': row,
      'tab': tab,
    };
  }
}

Map<String, dynamic> _parseLargeJson(String jsonString) {
  try {
    // Clean the string before parsing
    String cleanedJson = jsonString.trim();
    if (cleanedJson.startsWith('"') && cleanedJson.endsWith('"')) {
      cleanedJson = cleanedJson.substring(1, cleanedJson.length - 1);
      cleanedJson = cleanedJson.replaceAll('\\"', '"').replaceAll('\\\\', '\\');
    }
    
    return jsonDecode(cleanedJson) as Map<String, dynamic>;
  } catch (e) {
    return {};
  }
}