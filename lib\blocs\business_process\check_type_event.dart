part of 'check_type_bloc.dart';

abstract class CheckTypeEvent extends Equatable {
  const CheckTypeEvent();

  @override
  List<Object> get props => [];
}

class CheckTypeRequested extends CheckTypeEvent {
  final int serviceId;
  final String procDefId;

  const CheckTypeRequested({
    required this.serviceId,
    required this.procDefId,
  });

  @override
  List<Object> get props => [serviceId, procDefId];
}

class ClearCheckTypeState extends CheckTypeEvent {
  const ClearCheckTypeState();
} 