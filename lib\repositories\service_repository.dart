import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:eapprove/models/service/service_request_model.dart';
import 'package:eapprove/models/service/service_response_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/screens/common/error_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class ServiceRepository {
  final ApiService _apiService;

  ServiceRepository({required ApiService apiService}) : _apiService = apiService;

  Future<ServiceResponseModel> getServices({
    required ServiceRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/service-pack/getAll',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body) as Map<String, dynamic>;
        return ServiceResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load services: ${response.statusCode}');
      }
    } on NoInternetException {
      rethrow;
    } catch (e, stackTrace) {
      debugPrint('Error in getServices: $e, stackTrace: $stackTrace');
      throw Exception(ErrorHandler.getErrorMessage(e));
    }
  }

  Future<ServiceSpecialResponseModel> getServiceSpecialByParentId(int parentId) async {
    try {
      final response = await _apiService.get(
        'business-process/service-pack/getServiceSpecialByParentId?id=$parentId',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body) as Map<String, dynamic>;
        return ServiceSpecialResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load special services: ${response.statusCode}');
      }
    } on NoInternetException {
      rethrow;
    } catch (e, stackTrace) {
      debugPrint('Error in getServiceSpecialByParentId: $e, stackTrace: $stackTrace');
      throw Exception(ErrorHandler.getErrorMessage(e));
    }
  }

  Future<bool> checkPermission() async {
    if (!Platform.isAndroid) return true;

    final androidInfo = await DeviceInfoPlugin().androidInfo;
    final sdkVersion = androidInfo.version.sdkInt ?? 0;
    debugPrint('Android SDK version: $sdkVersion');

    if (sdkVersion >= 30) {
      final status = await Permission.manageExternalStorage.status;
      debugPrint('MANAGE_EXTERNAL_STORAGE status: $status');

      if (status != PermissionStatus.granted) {
        final result = await Permission.manageExternalStorage.request();
        debugPrint('MANAGE_EXTERNAL_STORAGE request result: $result');

        if (result != PermissionStatus.granted) {
          final choice = await _showPermissionDialog();
          if (choice) {
            await openAppSettings();
          }
          return false;
        }
      }
      return true;
    }
    else if (sdkVersion >= 23) {
      final status = await Permission.storage.status;
      debugPrint('STORAGE status: $status');

      if (status != PermissionStatus.granted) {
        final result = await Permission.storage.request();
        debugPrint('STORAGE request result: $result');

        if (result != PermissionStatus.granted) {
          final choice = await _showPermissionDialog();
          if (choice) {
            await openAppSettings();
          }
          return false;
        }
      }
      return true;
    }

    return true;
  }

  Future<bool> _showPermissionDialog() async {
    debugPrint('Should show permission dialog to user');
    return true;
  }

  Future<String> downloadPdf({required String fileName}) async {
    try {
      debugPrint('Starting download for file: $fileName');

      final encoded = Uri.encodeComponent(fileName);
      final Uint8List fileBytes = await _apiService.getBinary(
        'business-process/file/download?filename=$encoded',
      );

      debugPrint('Downloaded ${fileBytes.length} bytes');

      late final String filePath;
      late final Directory downloadDir;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkVersion = androidInfo.version.sdkInt;
        final manufacturer = androidInfo.manufacturer;
        final model = androidInfo.model;

        debugPrint('Device info: $manufacturer $model, Android SDK $sdkVersion');

        if (!await checkPermission()) {
          throw Exception('Storage permission is required to save files');
        }

        if (sdkVersion >= 30) {
          try {
            downloadDir = await _getAndroid11DownloadDir();
          } catch (e) {
            debugPrint('Failed to get primary download dir: $e, trying fallback');
            downloadDir = await _getAndroid11FallbackDir();
          }
        } else {
          downloadDir = await _getLegacyDownloadDir();
        }

        debugPrint('Using download directory: ${downloadDir.path}');

        if (!await downloadDir.exists()) {
          await downloadDir.create(recursive: true);
          debugPrint('Created directory: ${downloadDir.path}');
        }

        filePath = p.join(downloadDir.path, fileName);
        final file = File(filePath);
        await file.writeAsBytes(fileBytes, flush: true);
        debugPrint('File saved to: $filePath');

        await AndroidScanner.scanFile(filePath);
        debugPrint('File scanned successfully');
      } else if (Platform.isIOS) {
        final docDir = await getApplicationDocumentsDirectory();
        filePath = p.join(docDir.path, fileName);
        final file = File(filePath);
        await file.writeAsBytes(fileBytes, flush: true);
        debugPrint('File saved to: $filePath (iOS)');
      } else {
        throw Exception('Unsupported platform');
      }

      return filePath;
    } catch (e, stackTrace) {
      debugPrint('Error in downloadPdf: $e\nStack trace: $stackTrace');
      throw Exception('Failed to download file: ${e.toString()}');
    }
  }

  Future<Directory> _getAndroid11DownloadDir() async {
    try {
      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir == null) {
        throw Exception('Could not access external storage');
      }

      final String path = externalDir.path;
      debugPrint('External storage path: $path');

      final List<String> segments = path.split('/');
      final int androidIndex = segments.indexOf('Android');

      if (androidIndex == -1 || androidIndex < 2) {
        throw Exception('Could not determine storage root');
      }

      final List<String> rootSegments = segments.sublist(0, androidIndex);
      final String rootPath = rootSegments.join('/');
      final String downloadPath = '$rootPath/Download';

      debugPrint('Determined download path: $downloadPath');
      return Directory(downloadPath);
    } catch (e) {
      debugPrint('Error in _getAndroid11DownloadDir: $e');
      rethrow;
    }
  }

  Future<Directory> _getAndroid11FallbackDir() async {
    try {
      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir == null) {
        throw Exception('Could not access external storage');
      }

      final String downloadPath = p.join(externalDir.path, 'Downloads');
      final Directory downloadDir = Directory(downloadPath);

      debugPrint('Using fallback download path: $downloadPath');
      return downloadDir;
    } catch (e) {
      debugPrint('Error in _getAndroid11FallbackDir: $e');

      final Directory docDir = await getApplicationDocumentsDirectory();
      final String downloadPath = p.join(docDir.path, 'Downloads');
      debugPrint('Using last resort download path: $downloadPath');
      return Directory(downloadPath);
    }
  }

  Future<Directory> _getLegacyDownloadDir() async {
    try {
      final Directory? extDir = await getExternalStorageDirectory();
      if (extDir == null) {
        throw Exception('Could not access external storage');
      }

      final List<String> parts = extDir.path.split('/');
      final List<String> rootParts = [];

      for (final String part in parts) {
        rootParts.add(part);
        if (part == 'Android') break;
      }

      rootParts.removeLast(); 
      final String rootPath = rootParts.join('/');
      final String downloadPath = '$rootPath/Download';

      debugPrint('Legacy download path: $downloadPath');
      return Directory(downloadPath);
    } catch (e) {
      debugPrint('Error in _getLegacyDownloadDir: $e');

      final List<Directory>? extDirs = await getExternalCacheDirectories();
      if (extDirs == null || extDirs.isEmpty) {
        throw Exception('Could not access external directories');
      }

      final String downloadPath = p.join(extDirs.first.path, 'Downloads');
      debugPrint('Legacy fallback download path: $downloadPath');
      return Directory(downloadPath);
    }
  }

  Future<void> openDownloadedFile(String filePath) async {
    try {
      final result = await OpenFile.open(filePath);
      if (result.type != ResultType.done) {
        throw Exception('Could not open file: ${result.message}');
      }
    } catch (e) {
      debugPrint('Error opening file: $e');
      throw Exception('Failed to open file: ${e.toString()}');
    }
  }
}

class FileHelper {
  Future<String> get _localPath async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Future<File> _localFile(String fileName) async {
    final path = await _localPath;
    return File('$path/$fileName');
  }

  Future<File> writeBytes(Uint8List bytes, String fileName) async {
    final file = await _localFile(fileName);
    return file.writeAsBytes(bytes, flush: true);
  }

  Future<Uint8List> readBytes(String fileName) async {
    final file = await _localFile(fileName);
    return file.readAsBytes();
  }
}

class AndroidScanner {
  static const _channel = MethodChannel('app.scan/media');

  static Future<void> scanFile(String path) async {
    try {
      await _channel.invokeMethod('scanFile', {'path': path});
      debugPrint('Media scan requested for: $path');
    } on PlatformException catch (e) {
      debugPrint('Failed to scan file: ${e.message}');
    }
  }
}
