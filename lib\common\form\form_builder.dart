// // import 'package:eapprove/common/form/splitter_widget.dart';
// // import 'package:eapprove/common/form_v2/form_state_manager.dart';
// // import 'package:eapprove/enum/enum.dart';
// // import 'package:eapprove/models/form/form_item_info.dart';
// // import 'package:eapprove/models/form/form_model.dart';
// // import 'package:flutter/material.dart';

// // import 'common_form_widget.dart';
// // import 'dart:developer' as developer;

// // class FormBuilder  {
// //   /// Builds a form from the provided FormModel.
// //   Widget buildFormFromModel(FormModel model, BuildContext context,{
// //     Function(String, dynamic)? onChange,
// //     Function(String, dynamic)? onSubmit,
// //     FormStateManager? stateManager,
// //   }) {
// //     if (model.formItems.isEmpty) {
// //       return Center(
// //         child: Column(
// //           mainAxisSize: MainAxisSize.min,
// //           children: const [
// //             Icon(Icons.info_outline, size: 48, color: Colors.grey),
// //             SizedBox(height: 16),
// //             Text(
// //               'Không tìm thấy biểu mẫu nào',
// //               style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
// //             ),
// //             SizedBox(height: 8),
// //             Text(
// //               'Vui lòng kiểm tra lại dữ liệu',
// //               textAlign: TextAlign.center,
// //             ),
// //           ],
// //         ),
// //       );
// //     }

// //     // Group items by their different containers
// //     final formSections = <String, List<FormItemInfo>>{};
// //     final tabs = <String, List<FormItemInfo>>{};
// //     final topLevelItems = <FormItemInfo>[];

// //     try {
// //       // First, identify all containers (formSections, tabs)
// //       for (var item in model.formItems) {
// //         if (item.type == FormItemType.splitter && item.id != null) {
// //           formSections[item.id.toString()] = [];
// //         }
// //         if (item.type == FormItemType.tab && item.id != null) {
// //           tabs[item.id.toString()] = [];
// //         }
// //       }

// //       // Then, organize items by their parent container
// //       for (var item in model.formItems) {
// //         if (item.type != FormItemType.splitter &&
// //             item.type != FormItemType.tab) {
// //           final splitterId =
// //               item.splitter is String ? item.splitter as String : null;
// //           final tabId = item.tab is String ? item.tab as String : null;

// //           if (splitterId != null &&
// //               splitterId.isNotEmpty &&
// //               formSections.containsKey(splitterId)) {
// //             formSections[splitterId]?.add(item);
// //           } else if (tabId != null &&
// //               tabId.isNotEmpty &&
// //               tabs.containsKey(tabId)) {
// //             tabs[tabId]?.add(item);
// //           } else {
// //             // Only add items that should be displayed
// //             if (item.display != false) {
// //               topLevelItems.add(item);
// //             }
// //           }
// //         }
// //       }
// //       // If no items to display after filtering, show a message
// //       if (topLevelItems.isEmpty &&
// //           formSections.values.every((list) => list.isEmpty) &&
// //           tabs.values.every((list) => list.isEmpty)) {
// //         return Center(
// //           child: Column(
// //             mainAxisSize: MainAxisSize.min,
// //             children: const [
// //               Icon(Icons.visibility_off, size: 48, color: Colors.grey),
// //               SizedBox(height: 16),
// //               Text(
// //                 'No visible form fields',
// //                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
// //               ),
// //               SizedBox(height: 8),
// //               Text(
// //                 'All form fields are currently hidden',
// //                 textAlign: TextAlign.center,
// //               ),
// //             ],
// //           ),
// //         );
// //       }

// //       // Log summary of organized items
// //       developer.log('Form organization summary:', name: 'FormBuilder');
// //       developer.log('- Top level items: ${topLevelItems.length}',
// //           name: 'FormBuilder');
    

// //       // Build the form UI
// //       return Column(
// //         crossAxisAlignment: CrossAxisAlignment.stretch,
// //         children: [
// //           // Render top-level items
// //           ...topLevelItems.map((item) => _buildFormItemWithErrorHandling(
// //                   item, model.formItems, (name, value) {
// //                 //TODO: Handle field value changes
// //                 _updateFieldValue(model, name, value);
// //               }, stateManager ?? FormStateManager())),
          
// //           // Render splitters with their child items
// //           ...(() {
// //             final sortedSplitters = model.formItems
// //                 .where((item) =>
// //                     item.type == FormItemType.splitter && item.display != false)
// //                 .toList();

// //             sortedSplitters.sort(
// //                 (a, b) => (a.rowSortOrder ?? 0).compareTo(b.rowSortOrder ?? 0));
                
// //             return sortedSplitters.map((splitter) => _buildSplitterWithChildren(
// //                   splitter,
// //                   formSections[splitter.id.toString()] ?? [],
// //                   model.formItems,
// //                   (name, value) {
// //                     _updateFieldValue(model, name, value);
// //                   },
// //                   stateManager ?? FormStateManager(),
// //                 ));
// //           })(),

// //           // Render tabs with their child items
// //           ...model.formItems
// //               .where((item) =>
// //                   item.type == FormItemType.tab && item.display != false)
// //               .map((tab) => _buildTabWithChildren(
// //                     tab,
// //                     tabs[tab.id.toString()] ?? [],
// //                     model.formItems,
// //                     (name, value) {
// //                       // Handle field value changes
// //                       _updateFieldValue(model, name, value);
// //                     },
// //                     stateManager ?? FormStateManager(),
// //                   )),
// //         ],
// //       );
// //     } catch (e) {
// //       // Handle any exceptions during form building
// //       return Center(
// //         child: Padding(
// //           padding: const EdgeInsets.all(16.0),
// //           child: Column(
// //             mainAxisSize: MainAxisSize.min,
// //             children: [
// //               const Icon(Icons.error_outline, size: 48, color: Colors.red),
// //               const SizedBox(height: 16),
// //               const Text(
// //                 'Error building form',
// //                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
// //               ),
// //               const SizedBox(height: 8),
// //               Text(
// //                 'An error occurred while building the form: $e',
// //                 textAlign: TextAlign.center,
// //                 style: const TextStyle(color: Colors.red),
// //               ),
// //               const SizedBox(height: 16),
// //               // Debug information
// //               Container(
// //                 padding: const EdgeInsets.all(8),
// //                 decoration: BoxDecoration(
// //                   color: Colors.grey[200],
// //                   borderRadius: BorderRadius.circular(8),
// //                 ),
// //                 child: Column(
// //                   crossAxisAlignment: CrossAxisAlignment.start,
// //                   children: [
// //                     const Text('Form structure:',
// //                         style: TextStyle(fontWeight: FontWeight.bold)),
// //                     const SizedBox(height: 4),
// //                     Text('Total items: ${model.formItems.length}'),
// //                     Text(
// //                         'Types: ${model.formItems.map((e) => e.type.toString()).toSet().join(", ")}'),
// //                   ],
// //                 ),
// //               ),
// //             ],
// //           ),
// //         ),
// //       );
// //     }
// //   }

// //   /// onChange handler for form items?
// //   void _updateFieldValue(FormModel model, String name, dynamic value) {
// //     // Find the form item by name and update its value
// //     for (var item in model.formItems) {
// //       if (item.name == name) {
// //         item.value = value;
// //         break;
// //       }
// //     }
// //   }

// //   //region private widget

// //   /// Wrapper method that catches exceptions for individual form items
// //   Widget _buildFormItemWithErrorHandling(
// //     FormItemInfo item,
// //     List<FormItemInfo> allItems,
// //     Function(String, dynamic) onChange,
// //     FormStateManager stateManager,
// //   ) {
// //     try {
// //       return _buildFormItem(item, allItems, onChange, stateManager);
// //     } catch (e) {
// //       // Return an error widget instead of crashing
// //       return Padding(
// //         padding: const EdgeInsets.symmetric(vertical: 8.0),
// //         child: Container(
// //           padding: const EdgeInsets.all(8),
// //           decoration: BoxDecoration(
// //             border: Border.all(color: Colors.red),
// //             borderRadius: BorderRadius.circular(4),
// //           ),
// //           child: Column(
// //             crossAxisAlignment: CrossAxisAlignment.start,
// //             children: [
// //               Row(
// //                 children: [
// //                   const Icon(Icons.warning, color: Colors.red, size: 16),
// //                   const SizedBox(width: 8),
// //                   Text('Error rendering field: ${item.name ?? 'Unknown'}',
// //                       style: const TextStyle(fontWeight: FontWeight.bold)),
// //                 ],
// //               ),
// //               const SizedBox(height: 4),
// //               Text('Type: ${item.type}'),
// //               if (item.label != null) Text('Label: ${item.label}'),
// //               Text('Error: $e',
// //                   style: const TextStyle(fontSize: 12, color: Colors.red)),
// //             ],
// //           ),
// //         ),
// //       );
// //     }
// //   }

// //   Widget _buildFormItem(
// //     FormItemInfo item,
// //     List<FormItemInfo> allItems,
// //     Function(String, dynamic) onChange,
// //     FormStateManager stateManager,
// //   ) {
// //     // Check if item should be displayed
// //     if (item.display == false) {
// //       return const SizedBox.shrink();
// //     }

// //     return FormItemWidget(
// //       data: item,
// //       onChange: onChange,
// //       stateManager: stateManager,
// //       childFormItems: allItems,
// //     );
// //   }

// //   Widget _buildSplitterWithChildren(
// //     FormItemInfo splitter,
// //     List<FormItemInfo> children,
// //     List<FormItemInfo> allItems,
// //     Function(String, dynamic) onChange,
// //     FormStateManager stateManager,
// //   ) {
// //     try {
// //       // Filter out hidden children
// //       final visibleChildren =
// //           children.where((child) => child.display != false).toList();

// //       // If no visible children, hide the splitter too
// //       if (visibleChildren.isEmpty) {
// //         return const SizedBox.shrink();
// //       }

// //       // Use the new SplitterWidget
// //       return SplitterWidget(
// //         data: splitter,
// //         onChange: onChange,
// //         childFormItems: allItems,
// //         stateManager: stateManager,
// //       );
      
// //     } catch (e) {
// //       // Return error widget for splitter
// //       return Container(
// //         margin: const EdgeInsets.symmetric(vertical: 8.0),
// //         padding: const EdgeInsets.all(12),
// //         decoration: BoxDecoration(
// //           color: Colors.red[50],
// //           border: Border.all(color: Colors.red),
// //           borderRadius: BorderRadius.circular(8),
// //         ),
// //         child: Column(
// //           crossAxisAlignment: CrossAxisAlignment.start,
// //           children: [
// //             Row(
// //               children: [
// //                 const Icon(Icons.error_outline, color: Colors.red),
// //                 const SizedBox(width: 8),
// //                 Text('Error in splitter: ${splitter.label ?? 'Unnamed'}',
// //                     style: const TextStyle(fontWeight: FontWeight.bold)),
// //               ],
// //             ),
// //             const SizedBox(height: 8),
// //             Text('Error: $e'),
// //           ],
// //         ),
// //       );
// //     }
// //   }

// //   Widget _buildTabWithChildren(
// //     FormItemInfo tab,
// //     List<FormItemInfo> children,
// //     List<FormItemInfo> allItems,
// //     Function(String, dynamic) onChange,
// //     FormStateManager stateManager,
// //   ) {
// //     try {
// //       // Use the FormItemWidget for tabs (it should handle the tab rendering internally)
// //       return FormItemWidget(
// //         data: tab,
// //         onChange: onChange,
// //         childFormItems: allItems,
// //         stateManager: stateManager,
// //       );
// //     } catch (e) {
// //       // Return error widget for tab
// //       return Container(
// //         margin: const EdgeInsets.symmetric(vertical: 8.0),
// //         padding: const EdgeInsets.all(12),
// //         decoration: BoxDecoration(
// //           color: Colors.red[50],
// //           border: Border.all(color: Colors.red),
// //           borderRadius: BorderRadius.circular(8),
// //         ),
// //         child: Column(
// //           crossAxisAlignment: CrossAxisAlignment.start,
// //           children: [
// //             Row(
// //               children: [
// //                 const Icon(Icons.error_outline, color: Colors.red),
// //                 const SizedBox(width: 8),
// //                 Text('Error in tab: ${tab.label ?? 'Unnamed'}',
// //                     style: const TextStyle(fontWeight: FontWeight.bold)),
// //               ],
// //             ),
// //             const SizedBox(height: 8),
// //             Text('Error: $e'),
// //           ],
// //         ),
// //       );
// //     }
// //   }

// // }



// import 'package:eapprove/common/form/checkbox_widget.dart';
// import 'package:eapprove/common/form/date_picker_widget.dart';
// import 'package:eapprove/common/form/radio_button_widget.dart';
// import 'package:eapprove/common/form/splitter_widget.dart';
// import 'package:eapprove/common/form/text_area_widget.dart';
// import 'package:eapprove/common/form/text_input_widget.dart';
// import 'package:eapprove/common/form_v2/form_state_manager.dart';
// import 'package:eapprove/common/form_v2/widget/form_dropdown_mixin.dart';
// import 'package:eapprove/common/form_v2/widget/form_text_input.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// // import 'form_state_manager.dart';
// import 'package:eapprove/models/form/form_item_info.dart';
// import 'package:eapprove/enum/enum.dart';
// // import 'widget/form_text_input.dart';

// class FormBuilderV2 extends StatefulWidget {
//   final List<FormItemInfo> widgets;
//   final FormStateManager stateManager;
//   final Function(String, dynamic)? onFieldChanged;

//   const FormBuilderV2({
//     Key? key,
//     required this.widgets,
//     required this.stateManager,
//     this.onFieldChanged,
//   }) : super(key: key);

//   @override
//   State<FormBuilderV2> createState() => _FormBuilderV2State();
// }

// class _FormBuilderV2State extends State<FormBuilderV2> {
//   @override
//   void initState() {
//     super.initState();
//     widget.stateManager.setWidgets(widget.widgets);
//     widget.stateManager.addListener(_handleStateChange);
//   }

//   @override
//   void dispose() {
//     widget.stateManager.removeListener(_handleStateChange);
//     super.dispose();
//   }

//   void _handleStateChange() {
//     if (mounted) {
//       setState(() {});
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Group widgets by splitter and tab
//     final Map<String, List<FormItemInfo>> groupedWidgets = {};
//     final List<FormItemInfo> splitters = [];
//     final List<FormItemInfo> tabs = [];

//     // First pass: Identify all splitters and tabs
//     for (final widget in widget.widgets) {
//       if (widget.type == FormItemType.splitter) {
//         splitters.add(widget);
//         groupedWidgets[widget.id.toString()] = [];
//       } else if (widget.type == FormItemType.tab) {
//         tabs.add(widget);
//         groupedWidgets[widget.id.toString()] = [];
//       }
//     }

//     // Second pass: Group items by their parent (splitter or tab)
//     for (final widget in widget.widgets) {
//       if (widget.type != FormItemType.splitter && widget.type != FormItemType.tab) {
//         if (widget.splitter != null) {
//           groupedWidgets[widget.splitter.toString()] ??= [];
//           groupedWidgets[widget.splitter.toString()]!.add(widget);
//         } else if (widget.tab != null) {
//           groupedWidgets[widget.tab.toString()] ??= [];
//           groupedWidgets[widget.tab.toString()]!.add(widget);
//         }
//       }
//     }

//     // Third pass: Handle nested splitters
//     for (final splitter in splitters) {
//       if (splitter.splitter != null && splitter.splitter!.isNotEmpty) {
//         // This is a nested splitter, add it to its parent's children
//         final parentId = splitter.splitter.toString();
//         if (groupedWidgets.containsKey(parentId)) {
//           groupedWidgets[parentId]!.add(splitter);
//         }
//       }
//     }

//     // Filter and sort splitters
//     final sortedSplitters = splitters
//         .where((item) => item.display != false && (item.splitter == null || item.splitter!.isEmpty))
//         .toList();

//     sortedSplitters.sort(
//         (a, b) => (a.rowSortOrder ?? 0).compareTo(b.rowSortOrder ?? 0));

//     // Filter and sort tabs
//     final sortedTabs = tabs
//         .where((item) => item.display != false)
//         .toList();

//     sortedTabs.sort(
//         (a, b) => (a.rowSortOrder ?? 0).compareTo(b.rowSortOrder ?? 0));

//     // Sort children within each group
//     for (final group in groupedWidgets.values) {
//       group.sort((a, b) => (a.fieldSortOrder ?? 0).compareTo(b.fieldSortOrder ?? 0));
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.stretch,
//       children: [
//         // Render splitters with their child items
//         ...sortedSplitters.map((splitter) {
//           final children = groupedWidgets[splitter.id.toString()] ?? [];
//           children.sort(
//               (a, b) => (a.fieldSortOrder ?? 0).compareTo(b.fieldSortOrder ?? 0));
//           return _buildSplitter(splitter, children);
//         }).toList(),

//         // Render tabs with their child items
//         ...sortedTabs.map((tab) {
//           final children = groupedWidgets[tab.id.toString()] ?? [];
//           children.sort(
//               (a, b) => (a.fieldSortOrder ?? 0).compareTo(b.fieldSortOrder ?? 0));
//           return _buildTab(tab, children);
//         }).toList(),
//       ],
//     );
//   }

//   Widget _buildSplitter(FormItemInfo splitter, List<FormItemInfo> children) {
    
//       try {
//       // Filter out hidden children
//       final visibleChildren =
//           children.where((child) => child.display != false).toList();

//       // If no visible children, hide the splitter too
//       if (visibleChildren.isEmpty) {
//         return const SizedBox.shrink();
//       }
//       // Use the new SplitterWidget
//       return SplitterWidget(
//         data: splitter,
//         onChange: widget.onFieldChanged,
//         childFormItems: children,
//         stateManager: widget.stateManager,
//       );
      
//     } catch (e) {
//       // Return error widget for splitter
//       return Container(
//         margin: const EdgeInsets.symmetric(vertical: 8.0),
//         padding: const EdgeInsets.all(12),
//         decoration: BoxDecoration(
//           color: Colors.red[50],
//           border: Border.all(color: Colors.red),
//           borderRadius: BorderRadius.circular(8),
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               children: [
//                 const Icon(Icons.error_outline, color: Colors.red),
//                 const SizedBox(width: 8),
//                 Text('Error in splitter: ${splitter.label ?? 'Unnamed'}',
//                     style: const TextStyle(fontWeight: FontWeight.bold)),
//               ],
//             ),
//             const SizedBox(height: 8),
//             Text('Error: $e'),
//           ],
//         ),
//       );
//     }
    
    
//     // return Column(
//     //   crossAxisAlignment: CrossAxisAlignment.start,
//     //   children: [
//     //     Padding(
//     //       padding: const EdgeInsets.symmetric(vertical: 16.0),
//     //       child: Text(
//     //         splitter.label ?? '',
//     //         style: TextStyle(
//     //           fontSize: 18,
//     //           fontWeight: splitter.fontWeight == 'font-bold'
//     //               ? FontWeight.bold
//     //               : FontWeight.normal,
//     //         ),
//     //       ),
//     //     ),
//     //     ...children.map((child) => _buildWidget(child)).toList(),
//     //   ],
//     // );
//   }

//   Widget _buildTab(FormItemInfo tab, List<FormItemInfo> children) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: const EdgeInsets.symmetric(vertical: 16.0),
//           child: Text(
//             tab.label ?? '',
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: tab.fontWeight == 'font-bold'
//                   ? FontWeight.bold
//                   : FontWeight.normal,
//             ),
//           ),
//         ),
//         ...children.map((child) => _buildWidget(child)).toList(),
//       ],
//     );
//   }

//   Widget _buildWidget(FormItemInfo formWidget) {
//     // Get event expression results
//     final eventResults =
//         widget.stateManager.getEventExpressionResults(formWidget.id.toString());
//     print('formWidget - _buildWidget 1: ${formWidget.type}');
//     // Xử lý ten_truong
//     final tenTruong = eventResults?['ten_truong']?.toString();
//     final currentLabel = tenTruong ?? formWidget.label;

//     // Xử lý bat_buoc
//     final batBuoc = eventResults?['bat_buoc']?.toString();
//     final currentRequired = batBuoc != null
//         ? batBuoc.toLowerCase() == 'true'
//         : formWidget.validations?['required'] ?? false;

//     // Xử lý chi_duoc_doc
//     final chiDuocDoc = eventResults?['chi_duoc_doc']?.toString();
//     final currentReadonly = chiDuocDoc != null
//         ? chiDuocDoc.toLowerCase() == 'true'
//         : formWidget.readonly;

//     // Xử lý huong_dan
//     final huongDan = eventResults?['huong_dan']?.toString();
//     final currentSuggestText = huongDan ?? formWidget.suggestText;

//     // Xử lý hiển thị widget theo thứ tự ưu tiên
//     final hienThi = eventResults?['hien_thi']?.toString();
//     final shouldDisplay = hienThi != null
//         ? hienThi.toLowerCase() != 'false'
//         : formWidget.display ?? true;
        
//     if (shouldDisplay == false) {
//       return const SizedBox.shrink();
//     }
//     // Create a copy of the widget with current values
//     final currentWidget = FormItemInfo(
//       id: formWidget.id,
//       name: formWidget.name,
//       type: formWidget.type,
//       label: currentLabel,
//       readonly: currentReadonly,
//       suggestText: currentSuggestText,
//       validations: {...?formWidget.validations, 'required': currentRequired},
//       display: shouldDisplay,
//       splitter: formWidget.splitter,
//       fieldSortOrder: formWidget.fieldSortOrder,
//       eventExpression: formWidget.eventExpression,
//       placeholder: formWidget.placeholder,
//       value: formWidget.value,
//       useTimeDefault: formWidget.useTimeDefault,
//       fontWeight: formWidget.fontWeight,
//     );

//     return Container(
//       margin: EdgeInsets.only(top: 12.h),
//       child: Column(
//         children: [
//           Row(
//             children: [
//               Text(
//                 currentWidget.label ?? '',
//                 style: TextStyle(
//                   fontSize: 14.sp,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//               if (currentWidget.validations?['required'] == true)
//                 const Text(
//                   ' *',
//                   style: TextStyle(color: Colors.red),
//                 ),
//               if (currentWidget.suggestText != null)
//                 IconButton(
//                   icon: const Icon(Icons.help_outline, size: 16),
//                   onPressed: () {
//                     // Show tooltip
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       SnackBar(content: Text(currentWidget.suggestText!)),
//                     );
//                   },
//                 ),
//             ],
//           ),
//           _buildFormItem(currentWidget),
//         ],
//       ),
//     );
//   }

//   Widget _buildFormItem(FormItemInfo formWidget) {
//     debugPrint('formWidget - _buildFormItem: ${formWidget.type}');
//     switch (formWidget.type) {
//       case FormItemType.input:
//         return TextInputForm(
//           formWidget: formWidget,
//           stateManager: widget.stateManager,
//           onFieldChanged: widget.onFieldChanged,
//         );
    
//       case FormItemType.datePicker:
//         return DatePickerWidget(
//          stateManager: widget.stateManager,
//           data: formWidget,
//           onChange: widget.onFieldChanged,
//         );
//       case FormItemType.radioButton:
//         return RadioButtonWidget(
//           stateManager: widget.stateManager,
//           data: formWidget,
//           onChange:widget.onFieldChanged,
//         );

//       default:
//         return const SizedBox.shrink();
//     }
//   }
// }
