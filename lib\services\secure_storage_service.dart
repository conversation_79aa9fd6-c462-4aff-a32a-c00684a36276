import 'dart:convert';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

class SecureStorageService {
  final FlutterSecureStorage _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock,
    ),
  );

  final Logger _logger = LoggerConfig.logger;

  Future<void> writeString(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e, stackTrace) {
      _logger.e(
        'Error writing string to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<String?> readString(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e, stackTrace) {
      _logger.e(
        'Error reading string from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<void> writeBool(String key, bool value) async {
    try {
      await _storage.write(key: key, value: value.toString());
    } catch (e, stackTrace) {
      _logger.e(
        'Error writing boolean to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<bool> readBool(String key, {bool defaultValue = false}) async {
    try {
      final value = await _storage.read(key: key);
      if (value == null) return defaultValue;
      return value.toLowerCase() == 'true';
    } catch (e, stackTrace) {
      _logger.e(
        'Error reading boolean from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return defaultValue;
    }
  }

  Future<void> writeInt(String key, int value) async {
    try {
      await _storage.write(key: key, value: value.toString());
    } catch (e, stackTrace) {
      _logger.e(
        'Error writing integer to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<int?> readInt(String key) async {
    try {
      final value = await _storage.read(key: key);
      if (value == null) return null;
      return int.tryParse(value);
    } catch (e, stackTrace) {
      _logger.e(
        'Error reading integer from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<void> writeDouble(String key, double value) async {
    try {
      await _storage.write(key: key, value: value.toString());
    } catch (e, stackTrace) {
      _logger.e(
        'Error writing double to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<double?> readDouble(String key) async {
    try {
      final value = await _storage.read(key: key);
      if (value == null) return null;
      return double.tryParse(value);
    } catch (e, stackTrace) {
      _logger.e(
        'Error reading double from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<void> writeMap(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      await _storage.write(key: key, value: jsonString);
    } catch (e, stackTrace) {
      _logger.e(
        'Error writing map to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> readMap(String key) async {
    try {
      final jsonString = await _storage.read(key: key);
      if (jsonString == null || jsonString.isEmpty) return null;
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e, stackTrace) {
      _logger.e(
        'Error reading map from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<void> writeList<T>(String key, List<T> value) async {
    try {
      final jsonString = jsonEncode(value);
      await _storage.write(key: key, value: jsonString);
    } catch (e, stackTrace) {
      _logger.e(
        'Error writing list to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<List<dynamic>?> readList(String key) async {
    try {
      final jsonString = await _storage.read(key: key);
      if (jsonString == null || jsonString.isEmpty) return null;
      return jsonDecode(jsonString) as List<dynamic>;
    } catch (e, stackTrace) {
      _logger.e(
        'Error reading list from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<bool> containsKey(String key) async {
    try {
      final allValues = await _storage.readAll();
      return allValues.containsKey(key);
    } catch (e, stackTrace) {
      _logger.e(
        'Error checking if key exists in secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  Future<void> delete(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e, stackTrace) {
      _logger.e(
        'Error deleting key from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<void> deleteAll() async {
    try {
      await _storage.deleteAll();
    } catch (e, stackTrace) {
      _logger.e(
        'Error deleting all keys from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<Set<String>> getAllKeys() async {
    try {
      final allValues = await _storage.readAll();
      return allValues.keys.toSet();
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting all keys from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return {};
    }
  }

  Future<void> addKeyToGroup(String groupName, String key) async {
    final keys = await getKeysInGroup(groupName);
    if (!keys.contains(key)) {
      keys.add(key);
      await writeList('${groupName}_keys', keys);
    }
  }

  Future<List<String>> getKeysInGroup(String groupName) async {
    final List<dynamic>? keys = await readList('${groupName}_keys');
    return keys?.cast<String>() ?? [];
  }

// Khi xóa key
  Future<void> removeKeyFromGroup(String groupName, String key) async {
    final keys = await getKeysInGroup(groupName);
    if (keys.contains(key)) {
      keys.remove(key);
      await writeList('${groupName}_keys', keys);
    }
    await delete(key);
  }
}
