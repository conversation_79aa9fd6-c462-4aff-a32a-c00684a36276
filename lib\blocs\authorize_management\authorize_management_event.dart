import 'package:eapprove/models/authorize_management/authorize_management_request_model.dart';
import 'package:equatable/equatable.dart';

abstract class AuthorizeManagementEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class FetchAuthorizeList extends AuthorizeManagementEvent {
  final AuthorizeManagementRequestModel? requestModel;
  final bool? isLoadMore;
  FetchAuthorizeList({this.requestModel, this.isLoadMore});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class FetchAuthorizeListFilter extends AuthorizeManagementEvent {
  final AuthorizeManagementRequestModel? requestModel;
  final bool? isLoadMore;
  FetchAuthorizeListFilter({this.requestModel, this.isLoadMore});

  @override
  List<Object?> get props => [requestModel, isLoadMore];
}

class ClearAuthorizeListFilter extends AuthorizeManagementEvent {}

class FetchAuthorizeManagement extends AuthorizeManagementEvent {}
