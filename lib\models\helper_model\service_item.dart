import 'package:eapprove/models/helper_model/service_child_item_model.dart';

class RelatedServiceItem {
  final String title;
  final String? iconPath;
  final bool hasChevron;
  final List<TicketChildItem>? children;
  final String? procDefId;
  bool isExpanded;

  RelatedServiceItem({
    required this.title,
    this.iconPath,
    this.hasChevron = false,
    this.children,
    this.procDefId,
    this.isExpanded = false,
  });

  factory RelatedServiceItem.fromMap(Map<String, dynamic> map) {
    return RelatedServiceItem(
      title: map['title'],
      iconPath: map['iconPath'],
      hasChevron: map['hasChevron'] ?? false,
      isExpanded: map['isExpanded'] ?? false,
      children: map['children'] != null
          ? (map['children'] as List)
          .map((item) => TicketChildItem.fromJson(item as Map<String, dynamic>))
          .toList()
          : null,
      procDefId: map['procDefId'],
    );
  }

  @override
  String toString() => title;
}

List<RelatedServiceItem> convertMockToServiceItems(List<Map<String, dynamic>> mockData) {
  return mockData.map((item) => RelatedServiceItem.fromMap(item)).toList();
}

