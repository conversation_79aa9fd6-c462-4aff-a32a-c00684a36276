import 'dart:convert';

class ChartResponseModel {
  final int code;
  final String message;
  final List<ChartData> data;

  const ChartResponseModel({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ChartResponseModel.fromJson(Map<String, dynamic> json) {
    return ChartResponseModel(
      code: json['code'] as int,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((item) => ChartData.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  factory ChartResponseModel.fromJsonString(String jsonString) {
    return ChartResponseModel.fromJson(
        json.decode(jsonString) as Map<String, dynamic>);
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.map((info) => info.toJson()).toList(),
    };
  }

  bool get isSuccess => code == 200;
}

class ChartData {
  final int id;
  final String? code;
  final String? companyCode;
  final String? name;
  final String? shortName;
  final String? type;
  final String? description;
  final String? status;
  final int? userTotal;
  final int? linkTotal;
  final dynamic version;
  final String? createdDate;
  final String? createdUser;
  final String? modifiedDate;
  final String? modifiedUser;
  final List<ChartSharedUser>? chartSharedUserDtoList;
  final dynamic chartNodeDtoList;
  final String? userInfoName;
  final String? chartLinkName;
  final String? errorMessage;
  final String? applyFor;

  const ChartData({
    required this.id,
    this.code,
    this.companyCode,
    this.name,
    this.shortName,
    this.type,
    this.description,
    this.status,
    this.userTotal,
    this.linkTotal,
    this.version,
    this.createdDate,
    this.createdUser,
    this.modifiedDate,
    this.modifiedUser,
    this.chartSharedUserDtoList,
    this.chartNodeDtoList,
    this.userInfoName,
    this.chartLinkName,
    this.errorMessage,
    this.applyFor,
  });

  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      id: json['id'] as int,
      code: json['code'] as String?,
      companyCode: json['companyCode'] as String?,
      name: json['name'] as String?,
      shortName: json['shortName'] as String?,
      type: json['type'] as String?,
      description: json['description'] as String?,
      status: json['status'] as String?,
      userTotal: json['userTotal'] as int?,
      linkTotal: json['linkTotal'] as int?,
      version: json['version'],
      createdDate: json['createdDate'] as String?,
      createdUser: json['createdUser'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      modifiedUser: json['modifiedUser'] as String?,
      chartSharedUserDtoList: json['chartSharedUserDtoList'] != null
          ? (json['chartSharedUserDtoList'] as List<dynamic>)
          .map((item) => ChartSharedUser.fromJson(item as Map<String, dynamic>))
          .toList()
          : null,
      chartNodeDtoList: json['chartNodeDtoList'],
      userInfoName: json['userInfoName'] as String?,
      chartLinkName: json['chartLinkName'] as String?,
      errorMessage: json['errorMessage'] as String?,
      applyFor: json['applyFor'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'companyCode': companyCode,
      'name': name,
      'shortName': shortName,
      'type': type,
      'description': description,
      'status': status,
      'userTotal': userTotal,
      'linkTotal': linkTotal,
      'version': version,
      'createdDate': createdDate,
      'createdUser': createdUser,
      'modifiedDate': modifiedDate,
      'modifiedUser': modifiedUser,
      'chartSharedUserDtoList': chartSharedUserDtoList?.map((user) => user.toJson()).toList(),
      'chartNodeDtoList': chartNodeDtoList,
      'userInfoName': userInfoName,
      'chartLinkName': chartLinkName,
      'errorMessage': errorMessage,
      'applyFor': applyFor,
    };
  }

  // Convenience method to check if the item is valid
  bool get isValid => id != 0 && name != null && status == 'active';
}

class ChartSharedUser {
  final int id;
  final int? chartId;
  final String? chartName;
  final String? name;
  final String? userEmail;

  const ChartSharedUser({
    required this.id,
    this.chartId,
    this.chartName,
    this.name,
    this.userEmail,
  });

  factory ChartSharedUser.fromJson(Map<String, dynamic> json) {
    return ChartSharedUser(
      id: json['id'] as int,
      chartId: json['chartId'] as int?,
      chartName: json['chartName'] as String?,
      name: json['name'] as String?,
      userEmail: json['userEmail'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chartId': chartId,
      'chartName': chartName,
      'name': name,
      'userEmail': userEmail,
    };
  }
}