import 'dart:developer' as developer;

import 'package:flutter/material.dart';

class ExpressionEvaluator {
  static bool evaluateExpression(
      String expression, Map<String, dynamic> formValues) {
    try {
      if (expression.isEmpty) return false;
      String processed = _substituteVariables(expression, formValues);
      // developer.log("formValues: $formValues", name: 'formValues');
      // Log for debugging expression evaluation
      // developer.log(
      //   '=== Expression Evaluation ===',
      // );

      // Log the evaluation steps
      final parts = processed.split('||');
      // developer.log('Expression parts: $parts');

      for (var part in parts) {
        // developer.log('Evaluating part: $part');
        if (part.contains('@in')) {
          final regex = RegExp(r"^'?(.*?)'?\s*@in\s*\((.*?)\)$");
          final match = regex.firstMatch(part);
          if (match != null) {
            final value = match.group(1)!.trim().replaceAll("'", "");
            final list = _parseList(match.group(2)!);
            // developer.log('@in operation - value: $value, list: $list',
            //     name: 'evaluateExpression');
            // developer.log('@in operation - value type: ${value.runtimeType}');
            // developer.log('@in operation - list type: ${list.runtimeType}');
            // developer.log('@in result: ${list.contains(value)}');
          }
        } else if (part.contains('==')) {
          final comparisonParts = part.split('==');
          // developer.log(
          //     '== comparison - left: ${comparisonParts[0]}, right: ${comparisonParts[1]}',
          //     name: 'evaluateExpression');
          // developer.log(
          //     '== comparison - left type: ${comparisonParts[0].runtimeType}',
          //     name: 'evaluateExpression');
          // developer.log(
          //     '== comparison - right type: ${comparisonParts[1].runtimeType}',
          //     name: 'evaluateExpression');
          // developer.log(
          //     '== result: ${comparisonParts[0] == comparisonParts[1]}',
          //     name: 'evaluateExpression');
        }
      }

      final result = _evaluateComplexExpression(processed);
      // developer.log('Final result: $result', name: 'evaluateExpression');
      // developer.log(
      //   '=== End Evaluation ===',
      // );

      return result;
    } catch (e) {
      // developer.log('Error during evaluation $expression: $e',
      //     name: 'evaluateExpression');
      return false;
    }
  }

  static String _substituteVariables(
      String expression, Map<String, dynamic> formValues) {
    // Preserve @in and @notin operators
    expression = expression
        .replaceAll('@in', '###IN###')
        .replaceAll('@notin', '###NOTIN###');

    final regex = RegExp(r'@([a-zA-Z0-9_.]+)');

    String result = expression.replaceAllMapped(regex, (match) {
      final key = match.group(1)!;
      final parts = key.split('.');
      
      // developer.log('Processing key: $key', name: 'substituteVariables');
      // developer.log('Key parts: $parts', name: 'substituteVariables');

      dynamic value;
      if (parts.length > 1) {
        // Handle table field
        final tableName = parts[0];
        final fieldName = parts[1];
        
        // developer.log('Table name: $tableName', name: 'substituteVariables');
        // developer.log('Field name: $fieldName', name: 'substituteVariables');
        
        final tableData = formValues[tableName];
        // developer.log('Table data: $tableData', name: 'substituteVariables');

        if (tableData is List && tableData.isNotEmpty) {
          // Get all values from table rows
          final values = tableData.map((row) => row[fieldName]).toList();
          // developer.log('Table field values: $values', name: 'substituteVariables');
          
          // Join values with comma for comparison
          value = values.join(',');
          // developer.log('Joined table values: $value', name: 'substituteVariables');
        } else {
          value = null;
        }
      } else {
        value = formValues[key];
      }

      // Log variable substitution
      // developer.log('Substituting variable: $key = $value', name: 'substituteVariables');
      // developer.log('Value type: ${value.runtimeType}', name: 'substituteVariables');

      if (value == null) return "''"; // Convert null to empty string
      if (value is List && (value.isEmpty || value.length == 0))
        return "''"; // Convert empty array to empty string
      if (value.toString().isEmpty) return "''";
      if (value is num) return value.toString();
      return value is String ? "'$value'" : value.toString();
    });

    // Restore @in and @notin operators
    result = result
        .replaceAll('###IN###', '@in')
        .replaceAll('###NOTIN###', '@notin');

    // developer.log('Final processed expression: $result', name: 'substituteVariables');
    return result;
  }

  static bool _evaluate(String exp) {
    exp = exp.trim();
    // developer.log('Evaluating expression: $exp', name: 'evaluateExpression');

    // developer.log(
    //   'Evaluating expression: $exp',
    // );

    // Regex @in / @notin (áp dụng cho 1 phần tử)
    final inRegex = RegExp(r"^'?(.*?)'?\s*@in\s*\((.*?)\)$");
    final notInRegex = RegExp(r"^'?(.*?)'?\s*@notin\s*\((.*?)\)$");

    final inMatch = inRegex.firstMatch(exp);
    if (inMatch != null) {
      final value = inMatch.group(1)!.trim().replaceAll("'", "");
      final list = _parseList(inMatch.group(2)!);
      // developer.log(
      //   '@in operation: value=$value, list=$list',
      // );
      // Nếu giá trị là rỗng hoặc list rỗng, trả về false
      if (value.isEmpty || list.isEmpty) return false;
      return list.contains(value);
    }

    final notInMatch = notInRegex.firstMatch(exp);
    if (notInMatch != null) {
      final value = notInMatch.group(1)!.trim().replaceAll("'", "");
      final list = _parseList(notInMatch.group(2)!);
      // developer.log(
      //   '@notin operation: value=$value, list=$list',
      // );
      // Nếu giá trị là rỗng hoặc list rỗng, trả về true
      if (value.isEmpty || list.isEmpty) return true;
      return !list.contains(value);
    }

    // Xử lý ngoặc sau khi đã xử lý @in/@notin
    while (exp.contains('(')) {
      final start = exp.lastIndexOf('(');
      final end = exp.indexOf(')', start);
      if (start < 0 || end < 0) break;
      final inner = exp.substring(start + 1, end);
      final result = _evaluate(inner);
      exp = exp.replaceRange(start, end + 1, result.toString());
      // developer.log(
      //   'After parentheses evaluation: $exp',
      // );
    }

    // Xử lý toán tử so sánh
    String operator = '';
    if (exp.contains('>='))
      operator = '>=';
    else if (exp.contains('<='))
      operator = '<=';
    else if (exp.contains('>'))
      operator = '>';
    else if (exp.contains('<'))
      operator = '<';
    else if (exp.contains('=='))
      operator = '==';
    else if (exp.contains('!=')) 
      operator = '!=';

    if (operator.isNotEmpty) {
      final parts = exp.split(operator).map((e) => e.trim()).toList();
      // developer.log('Expression parts: $parts', name: 'evaluateExpression');
      
      if (parts.length != 2) {
        // developer.log('Invalid expression format: expected 2 parts, got ${parts.length}', 
        //   name: 'evaluateExpression');
        return false;
      }

      final left = _parseValue(parts[0]);
      final right = _parseValue(parts[1]);
      
      // developer.log('Left value: $left (${left.runtimeType})', name: 'evaluateExpression');
      // developer.log('Right value: $right (${right.runtimeType})', name: 'evaluateExpression');
      // developer.log('Operator: $operator', name: 'evaluateExpression');

      // Xử lý null values
      if (left == null || right == null) {
        // developer.log('Null value detected: left=$left, right=$right', name: 'evaluateExpression');
        return operator == '!=' ? true : false;
      }

      // Xử lý chuỗi có nhiều giá trị (từ table)
      if (left is String && left.contains(',')) {
        final leftValues = left.split(',');
        // developer.log('Multiple left values: $leftValues', name: 'evaluateExpression');
        
        // Kiểm tra nếu có ít nhất một giá trị khác rỗng
        if (operator == '!=' && right == '') {
          return leftValues.any((value) => value.isNotEmpty);
        }
        // Kiểm tra nếu tất cả giá trị đều khác rỗng
        if (operator == '==' && right == '') {
          return leftValues.every((value) => value.isNotEmpty);
        }
      }

      // Xử lý số
      if (left is num && right is num) {
        final result = _compareNumbers(left, right, operator);
        // developer.log('Numeric comparison result: $result', name: 'evaluateExpression');
        return result;
      }

      // Xử lý chuỗi
      final result = operator == '==' ? left == right : left != right;
      // developer.log('String comparison result: $result', name: 'evaluateExpression');
      return result;
    }

    // Giá trị boolean
    final trimmed = _cleanValue(exp).toLowerCase();
    if (trimmed == 'true') {
      // developer.log('Boolean value: true', name: 'evaluateExpression');
      return true;
    }
    if (trimmed == 'false') {
      // developer.log('Boolean value: false', name: 'evaluateExpression');
      return false;
    }

    // Chuỗi đơn
    if (exp.startsWith("'") && exp.endsWith("'")) {
      final result = exp.substring(1, exp.length - 1).isNotEmpty;
      // developer.log('Single string value: $result', name: 'evaluateExpression');
      return result;
    }

    final result = exp.isNotEmpty;
    // developer.log('Default evaluation result: $result', name: 'evaluateExpression');
    return result;
  }

  static bool _compareNumbers(num left, num right, String operator) {
    switch (operator) {
      case '>':
        return left > right;
      case '>=':
        return left >= right;
      case '<':
        return left < right;
      case '<=':
        return left <= right;
      case '==':
        return left == right;
      case '!=':
        return left != right;
      default:
        // developer.log('Invalid operator: $operator', name: 'evaluateExpression');
        return false;
    }
  }

  static dynamic _parseValue(String value) {
    value = _cleanValue(value);
    // developer.log('Parsing value: $value', name: 'evaluateExpression');

    // Try to parse the entire string as a number first
    final number = num.tryParse(value);
    if (number != null) {
      // developer.log('Parsed as number: $number', name: 'evaluateExpression');
      return number;
    }

    // If not a direct number, return the string value
    // developer.log('Parsed as string: $value', name: 'evaluateExpression');
    return value;
  }

  static String _cleanValue(String value) {
    // Remove all quotes and trim
    return value.replaceAll("'", "").trim();
  }

  static List<String> _parseList(String raw) {
    // Remove outer parentheses if present
    String cleaned = raw.trim();
    if (cleaned.startsWith('(') && cleaned.endsWith(')')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }

    // Split by comma and clean each element
    return cleaned.split(',').map((item) {
      String value = item.trim();
      // Remove quotes if present
      if (value.startsWith("'") && value.endsWith("'")) {
        value = value.substring(1, value.length - 1);
      }
      return value;
    }).toList();
  }

  static bool _evaluateComplexExpression(String expression) {
    // Ưu tiên || (or) ngoài cùng
    if (expression.contains('||')) {
      final parts = expression.split('||');
      for (var part in parts) {
        final result = _evaluateComplexExpression(part.trim());
        // developer.log(
        //   'OR part: "$part" => $result',
        // );
        if (result) return true;
      }
      return false;
    }

    // Nếu không có ||, xử lý &&
    if (expression.contains('&&')) {
      final parts = expression.split('&&');
      for (var part in parts) {
        final result = _evaluateComplexExpression(part.trim());
        // developer.log(
        //   'AND part: "$part" => $result',
        // );
        if (!result) return false;
      }
      return true;
    }

    // Không có && || nữa, xử lý biểu thức đơn
    final result = _evaluate(expression.trim());
    // developer.log(
    //   'Single expression: "$expression" => $result',
    // );
    return result;
  }

  static bool evaluateEventExpression(
      Map<String, dynamic> eventExpression, Map<String, dynamic> formValues) {
    final expression = eventExpression['expression'] as String?;
    if (expression == null || expression.trim().isEmpty) return false;

    return evaluateExpression(expression, formValues);
  }
}
