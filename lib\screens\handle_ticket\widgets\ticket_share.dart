import 'dart:developer';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/models/handle_ticket/share_ticket_model.dart';
import 'package:eapprove/models/handle_ticket/user_drop_down_model.dart';
import 'package:eapprove/widgets/custom_dropdown_multi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';

class TicketShareScreen extends StatefulWidget {
  final String id;
  const TicketShareScreen({super.key, required this.id});

  @override
  State<TicketShareScreen> createState() => _TicketShareScreenState();
}

class _TicketShareScreenState extends State<TicketShareScreen> {
  List<UserDropdownItem> userList = [];

  List<ShareDataModel> shareEntries = [];

  List<SelectItem> selectedUsers = [];

  int currentPage = 1;
  int totalPages = 1;
  bool isLoading = false;

  bool isInitialLoading = true;
  bool isUserListLoaded = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_scrollListener);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  void _loadInitialData() {
    _loadUserDropdown();
    _loadShareTickets();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (!_scrollController.hasClients) return;

    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      if (currentPage < totalPages && !isLoading) {
        _loadMoreShareTickets();
      }
    }
  }

  void _loadUserDropdown() {
    log('Loading user dropdown');
    context.read<TicketProcessDetailBloc>().add(LoadUserDropdown());
  }

  void _loadShareTickets() {
    log('Loading share tickets for ${widget.id}, page: 1');
    setState(() {
      isInitialLoading = true;
    });

    context.read<TicketProcessDetailBloc>().add(LoadShareTickets(
          procInstId: widget.id,
          page: 1,
          type: 'SHARED',
        ));
  }

  void _loadMoreShareTickets() {
    if (currentPage < totalPages) {
      setState(() {
        isLoading = true;
        currentPage += 1;
      });

      log('Loading more share tickets for ${widget.id}, page: $currentPage');
      context.read<TicketProcessDetailBloc>().add(LoadShareTickets(
            procInstId: widget.id,
            page: currentPage,
            type: 'SHARED',
          ));
    }
  }

  void _shareTicket() {
    if (selectedUsers.isEmpty) {
      SnackbarCore.error('Vui lòng chọn ít nhất một người dùng để chia sẻ');
      return;
    }

    final List<String> usernames = selectedUsers.map((user) => user.value).toList();

    setState(() {
      isLoading = true;
    });

    context.read<TicketProcessDetailBloc>().add(AddShareTicket(
          procInstId: widget.id,
          sharedUsers: usernames,
        ));

    setState(() {
      selectedUsers = [];
    });
  }

  void _confirmAndDeleteShare(int shareId) {
    showDialog(
      context: context,
      builder: (context) => CustomDialog(
        title: 'Xác nhận',
        content: 'Bạn có chắc chắn muốn xóa chia sẻ này?',
        cancelButtonText: 'Không',
        confirmButtonText: 'Xóa',
        confirmButtonColor: getColorSkin().red,
        confirmTextColor: getColorSkin().white,
        cancelButtonColor: getColorSkin().ink3,
        cancelTextColor: getColorSkin().white,
        onCancel: () => Navigator.pop(context),
        onConfirm: () {
          Navigator.pop(context);
          log('Deleting share ID: $shareId');

          setState(() {
            isLoading = true;
          });

          context.read<TicketProcessDetailBloc>().add(DeleteShareTicket(
                shareId: shareId,
                procInstId: widget.id,
              ));
        },
      ),
    );
  }

  List<SelectItem> _convertToSelectItems(List<UserDropdownItem> users) {
    return users
        .map((user) => SelectItem(
              value: user.username,
              label: '${user.username} - ${user.name} - ${user.title} - ${user.chartName}',
            ))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return GradientBackground(
      child: Scaffold(
        backgroundColor: isTablet ? getColorSkin().white : getColorSkin().transparent,
        appBar: CustomAppBar(
          backgroundColor: isTablet ? getColorSkin().lightBlue : getColorSkin().transparent,
          automaticallyImplyLeading: isTablet ? false : true,
          leading: isTablet
              ? IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: SvgPicture.asset(
                    StringImage.ic_close,
                    width: 24.w,
                    height: 24.h,
                    colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
                  ),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                )
              : null,
          centerTitle: true,
          title: 'Chia sẻ',
          titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          textColor: getColorSkin().black,
          actionsPadding: EdgeInsets.only(right: 16.w),
          actions: [
            IconButton(
              onPressed: _shareTicket,
              icon: SvgPicture.asset(
                StringImage.ic_share,
                width: 36.w,
                height: 36.h,
              ),
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(),
            ),
          ],
        ),
        body: BlocConsumer<TicketProcessDetailBloc, TicketProcessDetailState>(
          listenWhen: (previous, current) {
            return current is UserDropdownLoaded ||
                current is ShareTicketsLoaded ||
                current is ShareTicketActionSuccess ||
                current is TicketProcessDetailError;
          },
          listener: (context, state) {
            if (state is UserDropdownLoaded) {
              setState(() {
                userList = state.userResponse.data;
                isUserListLoaded = true;
              });
            } else if (state is ShareTicketsLoaded) {
              setState(() {
                // Chỉ thay thế danh sách nếu là trang đầu tiên
                if (state.currentPage == 1) {
                  shareEntries = state.shareResponse.data.content;
                } else {
                  // Nếu là trang tiếp theo, thêm vào danh sách hiện tại
                  shareEntries.addAll(state.shareResponse.data.content);
                }
                currentPage = state.currentPage;
                totalPages = state.shareResponse.data.totalPages;
                isLoading = false;
                isInitialLoading = false;
              });
            } else if (state is ShareTicketActionSuccess) {
              SnackbarCore.success(state.message);
              _loadShareTickets();
            } else if (state is TicketProcessDetailError) {
              log('Error in TicketProcessDetail: ${state.message}');
              setState(() {
                isLoading = false;
                isInitialLoading = false;
                if (state.message.contains('user')) {
                  isUserListLoaded = true;
                }
              });

              SnackbarCore.error(state.message);
            }
          },
          builder: (context, state) {
            return Column(
              children: [
                Container(
                  width: double.infinity,
                  color: getColorSkin().white,
                  padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 4.w),
                        child: RichText(
                          text: TextSpan(
                            text: 'Chia sẻ với người dùng ',
                            style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                            children: <TextSpan>[
                              TextSpan(text: '*', style: getTypoSkin().regular12.copyWith(color: getColorSkin().red)),
                            ],
                          ),
                        ),
                      ),
                      isUserListLoaded
                          ? CustomDropdownMulti(
                              label: 'Nhập account người dùng ',
                              titleStyle: getTypoSkin().medium16.copyWith(
                                    color: getColorSkin().ink1,
                                  ),
                              enableSearch: true,
                              imgSearchPath: StringImage.ic_search,
                              showBottomSheetLabel: false,
                              bottomSheetTitleStyle: getTypoSkin().medium16.copyWith(
                                    color: getColorSkin().ink1,
                                  ),
                              options: _convertToSelectItems(userList),
                              dropdownHeight: 48.h,
                              showLabel: false,
                              showSelectAllOption: false,
                              onSelected: (selected) {
                                setState(() {
                                  selectedUsers = selected;
                                });
                              },
                            )
                          : Center(
                              child: SizedBox(
                                height: 48.h,
                                child: AppConstraint.buildShimmer(),
                              ),
                            ),
                    ],
                  ),
                ),

                SizedBox(height: 8.h),

                // Danh sách chia sẻ
                Expanded(
                  child: _buildShareList(),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildShareList() {
    if (isInitialLoading) {
      return Center(
        child: AppConstraint.buildShimmer(
          child: ListView.builder(
            itemBuilder: (_, index) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                child: Container(
                  height: 212.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              );
            },
            itemCount: 5,
          ),
        ),
      );
    }

    if (shareEntries.isEmpty) {
      return Center(
        child: Text(
          'Không có dữ liệu',
          style: getTypoSkin().medium16,
        ),
      );
    }

    final isTablet = DeviceUtils.isTablet;

    // Hiển thị bảng cho tablet
    if (isTablet) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            // Header của bảng
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: getColorSkin().grey4Background,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Hành động',
                      style: getTypoSkin().medium14.copyWith(
                            color: getColorSkin().ink1,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Thời gian',
                      style: getTypoSkin().medium14.copyWith(
                            color: getColorSkin().ink1,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Người thực hiện',
                      style: getTypoSkin().medium14.copyWith(
                            color: getColorSkin().ink1,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      'Người được chia sẻ/ xóa chia sẻ',
                      style: getTypoSkin().medium14.copyWith(
                            color: getColorSkin().ink1,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  SizedBox(width: 40.w),
                ],
              ),
            ),
            // Body của bảng
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                itemCount: shareEntries.length + (isLoading ? 1 : 0),
                itemBuilder: (_, index) {
                  if (index == shareEntries.length) {
                    return Center(child: AppConstraint.buildLoading(context));
                  }

                  final item = shareEntries[index];
                  final String actionText = item.type == 'SHARED' ? 'Chia sẻ' : 'Xóa chia sẻ';
                  final bool showDeleteButton = item.type == 'SHARED';

                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: getColorSkin().grey4Background,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            actionText,
                            style: getTypoSkin().bodyRegular14.copyWith(
                                  color: getColorSkin().ink1,
                                ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            item.createdDate,
                            style: getTypoSkin().bodyRegular14.copyWith(
                                  color: getColorSkin().ink1,
                                ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            item.createdUserInfo,
                            style: getTypoSkin().bodyRegular14.copyWith(
                                  color: getColorSkin().ink1,
                                ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            item.sharedUserInfo,
                            style: getTypoSkin().bodyRegular14.copyWith(
                                  color: getColorSkin().ink1,
                                ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(
                          width: 40.w,
                          child: showDeleteButton
                              ? IconButton(
                                  onPressed: () => _confirmAndDeleteShare(item.id),
                                  icon: SvgPicture.asset(
                                    StringImage.ic_red_trash,
                                    width: 20.w,
                                    height: 20.h,
                                  ),
                                  padding: EdgeInsets.zero,
                                  constraints: BoxConstraints(),
                                )
                              : null,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    } else {
      return ListView.builder(
        controller: _scrollController,
        itemCount: shareEntries.length + (isLoading ? 1 : 0),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemBuilder: (_, index) {
          if (index == shareEntries.length) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: Center(child: AppConstraint.buildLoading(context)),
            );
          }

          final item = shareEntries[index];
          final String actionText = item.type == 'SHARED' ? 'Chia sẻ' : 'Xóa chia sẻ';
          final bool showDeleteButton = item.type == 'SHARED';

          return Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: Card(
              color: getColorSkin().white,
              elevation: 1,
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Hành động: $actionText',
                          style: getTypoSkin().title6Regular.copyWith(
                                color: getColorSkin().ink1,
                              ),
                        ),
                        if (showDeleteButton)
                          IconButton(
                            onPressed: () => _confirmAndDeleteShare(item.id),
                            icon: SvgPicture.asset(
                              StringImage.ic_red_trash,
                              width: 20.w,
                              height: 20.h,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: BoxConstraints(),
                          ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Người thực hiện: ${item.createdUserInfo}',
                      style: getTypoSkin().title6Regular.copyWith(
                            color: getColorSkin().ink1,
                          ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Thời gian: ${item.createdDate}',
                      style: getTypoSkin().title6Regular.copyWith(
                            color: getColorSkin().ink1,
                          ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Người được chia sẻ: ${item.sharedUserInfo}',
                      style: getTypoSkin().title6Regular.copyWith(
                            color: getColorSkin().ink1,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }
  }
}
