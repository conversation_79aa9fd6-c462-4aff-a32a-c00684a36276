import 'dart:async';

import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'dart:io' show Platform;

class OtpField extends StatefulWidget {
  final String sessionState;
  final String? hash;
  final void Function(String)? onOtpComplete;

  const OtpField({
    super.key,
    required this.sessionState,
    this.hash,
    this.onOtpComplete,
  });

  @override
  State<OtpField> createState() => _OtpFieldState();
}

class _OtpFieldState extends State<OtpField> {
  final List<TextEditingController> _controllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());
  final FocusNode _keyboardFocusNode = FocusNode();
  // bool _isLongPress = false;
  Timer? _longPressTimer;
  int _currentIndex = 0;
  DateTime? _lastBackspaceTime;

  @override
  void initState() {
    super.initState();
    _setupFocusListeners();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_focusNodes.isNotEmpty) {
        _focusNodes[0].requestFocus();
      }
    });
  }

  void _setupFocusListeners() {
    for (int i = 0; i < 6; i++) {
      _focusNodes[i].addListener(() {
        if (_focusNodes[i].hasFocus) {
          _currentIndex = i;
        }
      });
    }
  }

  // void _handleResendOtp() {
  //   final stateToUse = widget.sessionState.isEmpty && widget.hash != null ? widget.hash! : widget.sessionState;

  //   context.read<AuthenticationBloc>().add(
  //         ResendOtpRequested(sessionState: stateToUse),
  //       );
  // }

  void _checkOtpComplete() {
    final otp = _controllers.map((controller) => controller.text).join();
    if (otp.length == 6) {
      widget.onOtpComplete?.call(otp);
    }
  }

  void _clearAllFields() {
    for (var controller in _controllers) {
      controller.clear();
    }
    if (_focusNodes.isNotEmpty) {
      _focusNodes[0].requestFocus();
    }
  }

  void _handleKeyEvent(KeyEvent event) {
    if (event.logicalKey == LogicalKeyboardKey.backspace) {
      if (event is KeyDownEvent) {
        final now = DateTime.now();

        if (_lastBackspaceTime == null) {
          _lastBackspaceTime = now;
        } else if (now.difference(_lastBackspaceTime!).inMilliseconds > 200) {
          _clearAllFields();
          _lastBackspaceTime = null;
          return;
        }

        if (_currentIndex >= 0 && _currentIndex < 6) {
          if (_controllers[_currentIndex].text.isEmpty && _currentIndex > 0) {
            _focusNodes[_currentIndex - 1].requestFocus();
            _controllers[_currentIndex - 1].clear();
          } else {
            _controllers[_currentIndex].clear();
          }
        }
      } else if (event is KeyUpEvent) {
        _lastBackspaceTime = null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: _keyboardFocusNode,
      onKeyEvent: _handleKeyEvent,
      autofocus: true,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              6,
              (index) => SizedBox(
                width: 45.w,
                child: GestureDetector(
                  onLongPress: () {
                    _clearAllFields();
                  },
                  child: TextField(
                    onTapOutside: (event) {
                      FocusScope.of(context).unfocus();
                    },
                    cursorColor: getColorSkin().title,
                    controller: _controllers[index],
                    focusNode: _focusNodes[index],
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    maxLength: 1,
                    style: getTypoSkin().title3Medium,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      if (Platform.isIOS)
                        _BackspaceFormatter(
                          index: index,
                          focusNodes: _focusNodes,
                          controllers: _controllers,
                          onLongBackspace: _clearAllFields,
                          lastBackspaceTimeProvider: () => _lastBackspaceTime,
                          setLastBackspaceTime: (time) =>
                              _lastBackspaceTime = time,
                        ),
                    ],
                    decoration: InputDecoration(
                      counterText: "",
                      filled: true,
                      fillColor: getColorSkin().white,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                        borderSide: BorderSide(
                          color: getColorSkin().grey3Background,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                        borderSide: BorderSide(
                          color: getColorSkin().primaryBlue,
                        ),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                    onChanged: (value) {
                      if (value.isNotEmpty && index < 5) {
                        _focusNodes[index + 1].requestFocus();
                      }
                      _checkOtpComplete();
                    },
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 16.h),
          TextButton(
            onPressed: () {},
            child: Text(
              'Gửi lại mã OTP',
              style: getTypoSkin().buttonText2Regular.copyWith(
                    color: getColorSkin().secondaryColor1,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _keyboardFocusNode.dispose();
    _longPressTimer?.cancel();
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }
}

class _BackspaceFormatter extends TextInputFormatter {
  final int index;
  final List<FocusNode> focusNodes;
  final List<TextEditingController> controllers;
  final VoidCallback onLongBackspace;
  final DateTime? Function() lastBackspaceTimeProvider;
  final Function(DateTime?) setLastBackspaceTime;

  _BackspaceFormatter({
    required this.index,
    required this.focusNodes,
    required this.controllers,
    required this.onLongBackspace,
    required this.lastBackspaceTimeProvider,
    required this.setLastBackspaceTime,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (oldValue.text.isNotEmpty && newValue.text.isEmpty) {
      final now = DateTime.now();
      final lastTime = lastBackspaceTimeProvider();

      // if (lastTime != null && now.difference(lastTime).inMilliseconds < 300) {
      //   WidgetsBinding.instance.addPostFrameCallback((_) {
      //     onLongBackspace();
      //   });
      // }
      if (index > 0) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          focusNodes[index - 1].requestFocus();
        });
      }

      setLastBackspaceTime(now);
    }

    return newValue;
  }
}
