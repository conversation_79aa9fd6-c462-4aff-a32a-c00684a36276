// import 'package:eapprove/common/form/form_builder.dart';
// import 'package:eapprove/models/form/form_model.dart';
// import 'package:eapprove/utils/form_parser.dart';
// import 'package:flutter/material.dart';

// class DynamicForm extends StatefulWidget {
//   final Map<String, dynamic> formData;

//   const DynamicForm({
//     Key? key,
//     required this.formData,
//   }) : super(key: key);

//   @override
//   State<DynamicForm> createState() => _DynamicFormState();
// }

// class _DynamicFormState extends State<DynamicForm> {
//   late FormModel _formModel;

//   @override
//   void initState() {
//     super.initState();
//     _formModel = FormParser.parseFormJson(widget.formData);
//   }

//   void _handleFieldChange(String fieldName, dynamic value) {
//     // Find the form item and update its value
//     final item = _formModel.formItems.firstWhere(
//           (item) => item.name == fieldName,
//       orElse: () => throw Exception('Field not found: $fieldName'),
//     );

//     setState(() {
//       item.value = value;
//     });
//   }

//   void _submitForm() {
//     if (_formModel.validate()) {
//       final values = _formModel.getValues();
//       print('Form submitted with values: $values');
//       // Send the values to your API
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     // var form = FormBuilder(
//     //   model: _formModel,
//     //   onFieldChange: (name, value) {},
//     //   context: context,
//     // );
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Dynamic Form'),
//       ),
//       body: form.build(),
//       bottomNavigationBar: BottomAppBar(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//             children: [
//               ElevatedButton(
//                 onPressed: _submitForm,
//                 child: const Text('Submit'),
//               ),
//               TextButton(
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                 },
//                 child: const Text('Cancel'),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }