import 'dart:developer';

import 'package:eapprove/screens/notification_screen/widgets/readmore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

class NotificationCard extends StatefulWidget {
  final String title;
  final String description;
  final String date;
  final String icon;
  final TextStyle? titleStyle;
  final Color? background;
  final bool isSeen;

  const NotificationCard(
      {super.key, required this.title,
      required this.description,
      required this.date,
      required this.icon,
      this.titleStyle,
      this.background,
      this.isSeen = true});

  @override
  _NotificationCardState createState() => _NotificationCardState();
}

class _NotificationCardState extends State<NotificationCard> {
  bool isExpanded = false;
  bool showReadMore = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkTextOverflow());
  }

  void _checkTextOverflow() {
    if (!mounted) return;
    final textPainter = TextPainter(
      text: TextSpan(text: widget.description, style: widget.titleStyle),
      maxLines: 3,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 100.w);

    if (textPainter.didExceedMaxLines) {
      setState(() {
        showReadMore = true;
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    return Card(
      color: widget.isSeen ? Colors.white : getColorSkin().blue2,
      margin: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.isSeen ? Colors.white : getColorSkin().blue2,
                border: Border.all(color: getColorSkin().grey4Background),
              ),
              alignment: Alignment.center,
              child: SvgPicture.asset(widget.icon, width: 32.w, height: 32.h),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    style: getTypoSkin().title5Medium.copyWith(
                          color: widget.isSeen ? getColorSkin().ink1 : getColorSkin().ink1,
                        ),
                  ),
                  SizedBox(height: 2.h),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          HtmlReadMore(
                            htmlContent: widget.description,
                            maxLines: 3,
                            textStyle: getTypoSkin().bodyRegular14.copyWith(
                                  color: widget.isSeen ? getColorSkin().ink2 : getColorSkin().ink1,
                                ),
                            expandText: "Xem thêm",
                            collapseText: "Thu gọn",
                            expandTextStyle: getTypoSkin().bodyRegular14.copyWith(
                                  color: getColorSkin().primaryBlue,
                                ),
                            collapseTextStyle: getTypoSkin().bodyRegular14.copyWith(
                                  color: getColorSkin().primaryBlue,
                                ),


                            htmlStyles: buildHtmlStyles(widget.isSeen),

                            onLinkTap: (url) {
                              if (url != null && Uri.parse(url).hasScheme) {
                                launchUrl(Uri.parse(url), mode: LaunchMode.inAppWebView);
                              }
                            },
                          )
                        ],
                      );
                    },
                  ),
                  SizedBox(height: 6.h),
                  Text(
                    widget.date,
                    style: getTypoSkin().regular12.copyWith(color: getColorSkin().ink3),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  Map<String, Style> buildHtmlStyles(bool isSeen) {
  final baseTextStyle = getTypoSkin().bodyRegular14.copyWith(
    color: isSeen ? getColorSkin().ink2 : getColorSkin().ink1,
    fontWeight: isSeen ? FontWeight.w400 : FontWeight.w700,
  );

  return {
    "html": Style.fromTextStyle(baseTextStyle),
    "body": Style.fromTextStyle(baseTextStyle),
    "p": Style.fromTextStyle(baseTextStyle).copyWith(
      margin: Margins.zero,
      padding: HtmlPaddings.zero,
    ),
    "span": Style.fromTextStyle(baseTextStyle),

    "strong": Style(fontWeight: FontWeight.w900),
    "b": Style(fontWeight: FontWeight.w900),

    "em": Style(
      fontStyle: FontStyle.italic,
      color: getColorSkin().primaryText,
      fontWeight: isSeen ? FontWeight.w400 : FontWeight.w500,
    ),
    "i": Style(
      fontStyle: FontStyle.italic,
      color: getColorSkin().primaryText,
      fontWeight: isSeen ? FontWeight.w400 : FontWeight.w500,
    ),

    "u": Style(textDecoration: TextDecoration.underline),
    "ins": Style(textDecoration: TextDecoration.underline),

    "del": Style(textDecoration: TextDecoration.lineThrough),
    "s": Style(textDecoration: TextDecoration.lineThrough),

    "a": Style(
      color: getColorSkin().primaryBlue,
      textDecoration: TextDecoration.underline,
    ),
    "a.wysiwyg-mention": Style(
      textDecoration: TextDecoration.none,
      color: getColorSkin().title,
      fontWeight: isSeen ? FontWeight.w500 : FontWeight.w700,
    ),
    "br": Style(
      margin: Margins.zero,
      padding: HtmlPaddings.zero,
      height: Height(8),
    ),
    "ul": Style(
      margin: Margins.zero,
      padding: HtmlPaddings.only(left: 20),
    ),
    "ol": Style(
      margin: Margins.zero,
      padding: HtmlPaddings.only(left: 20),
    ),
    "li": Style.fromTextStyle(baseTextStyle),
  };
}
}
