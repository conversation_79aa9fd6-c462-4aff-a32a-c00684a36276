import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/work_flow_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/work_flow_state.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:eapprove/models/ticket_other_action/work_flow_requestmodel.dart';

class WorkflowBloc extends Bloc<WorkflowEvent, WorkflowState> {
  final TicketOtherActionRepository repository;

  WorkflowBloc({required this.repository}) : super(const WorkflowInitial()) {
    on<LoadWorkflow>(_onLoadWorkflow);
  }

  Future<void> _onLoadWorkflow(LoadWorkflow event, Emitter<WorkflowState> emit) async {
    emit(const WorkflowLoading());
    try {
      final workflow = await repository.workFlow(
        requestBody: WorkFlowRequestmodel(
          procDefId: event.procDefId,
          procInstId: event.procInstId,
          fromNodeId: event.fromNodeId,
        ),
      );
      debugPrint('▶️ Workflow response: ${workflow.data?.length} group(s)');
      for (var g in workflow.data ?? []) {
        debugPrint('   • group has ${g.nodes?.length ?? 0} node(s)');
        for (var n in g.nodes ?? []) {
          debugPrint('     → node: id=${n.id}, name=${n.name}, type=${n.type}, position=${n.position}');
        }
      }
      emit(WorkflowLoaded(workflow));
    } catch (e) {
      emit(WorkflowError(e.toString()));
    }
  }
}
