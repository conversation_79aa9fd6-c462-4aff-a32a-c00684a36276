import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';

class PaymentScreen extends StatelessWidget {
  final String title;
  final String code;
  final bool isLoading;

  const PaymentScreen({
    Key? key,
    required this.title,
    required this.code,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        width: DeviceUtils.isTablet ? 900.w : 353.w,
        height: DeviceUtils.isTablet ? 700.h : 754.h,
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: AppConstraint.buildShimmer(
          child: SizedBox(
            height: 500.h,
            child: ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  width: double.infinity,
                  height: 100.h,
                  margin: EdgeInsets.symmetric(vertical: 8.h),
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                );
              },
            ),
          ),
        ),
      );
    }
    if (DeviceUtils.isTablet) {
      // Giao diện tablet (giống hình)
      return Container(
        width: 900.w,
        height: 700.h,
        decoration: BoxDecoration(
          color: getColorSkin().white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10.0,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Padding(
              padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: getTypoSkin().medium16.copyWith(
                          color: getColorSkin().ink1,
                        ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: getColorSkin().ink1),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // Content section
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Text(
                          'Lịch thanh toán 12501040008984',
                          style:
                              getTypoSkin().medium16.copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                        ),
                      ),
                      SizedBox(height: 9.h),
                      Text(
                        '1. Thông tin hợp đồng',
                        style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                      ),
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'Hợp đồng số: ',
                                      style: getTypoSkin().medium14.copyWith(
                                            color: getColorSkin().ink1,
                                          ),
                                    ),
                                    Text(
                                      'HD158.1',
                                      style: getTypoSkin()
                                          .medium14
                                          .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                                SizedBox(width: 20.w),
                                Row(
                                  children: [
                                    Text(
                                      'Ngày hợp đồng: ',
                                      style: getTypoSkin().medium14.copyWith(
                                            color: getColorSkin().ink1,
                                          ),
                                    ),
                                    Text(
                                      '15/08/2024',
                                      style: getTypoSkin()
                                          .medium14
                                          .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                    ),
                                    SizedBox(width: 20.w),
                                    Text(
                                      'Dự án: ',
                                      style: getTypoSkin().medium14.copyWith(
                                            color: getColorSkin().ink1,
                                          ),
                                    ),
                                    Text(
                                      'Du an Department',
                                      style: getTypoSkin()
                                          .medium14
                                          .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                                SizedBox(width: 20.w),
                              ],
                            ),
                            Row(
                              children: [
                                Text(
                                  'Tên hợp đồng: ',
                                  style: getTypoSkin().medium14.copyWith(
                                        color: getColorSkin().ink1,
                                      ),
                                ),
                                Text(
                                  'HD 15/8 (CT chung)',
                                  style: getTypoSkin()
                                      .medium14
                                      .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            SizedBox(width: 20.w),
                            Row(
                              children: [
                                Text(
                                  'Nhà thầu/NCC: ',
                                  style: getTypoSkin().medium14.copyWith(
                                        color: getColorSkin().ink1,
                                      ),
                                ),
                                Text(
                                  'Công Ty Cổ Phần Tập Đoàn Đất Xanh',
                                  style: getTypoSkin()
                                      .medium14
                                      .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            SizedBox(width: 20.w),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Tổng giá trị hợp đồng: ',
                                  style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                ),
                                Text(
                                  '22,352,000',
                                  style: getTypoSkin()
                                      .medium14
                                      .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                ),
                                SizedBox(width: 20.w),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          'Tổng giá trị đã thanh toán: ',
                                          style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                        ),
                                        Text(
                                          '10,000,000',
                                          style: getTypoSkin()
                                              .medium14
                                              .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(width: 20.w),
                                        Text(
                                          'Tổng giá trị còn phải thanh toán: ',
                                          style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                        ),
                                        Text(
                                          '22,352,000',
                                          style: getTypoSkin()
                                              .medium14
                                              .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          'Tỷ lệ % đã thanh toán: ',
                                          style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                        ),
                                        Text(
                                          '0%',
                                          style: getTypoSkin()
                                              .medium14
                                              .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(width: 20.w),
                                        Text(
                                          'Tổng giá trị đã xuất hóa đơn: ',
                                          style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                        ),
                                        Text(
                                          '0',
                                          style: getTypoSkin()
                                              .medium14
                                              .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Text(
                        '2. Chi tiết lịch thanh toán',
                        style: getTypoSkin().medium14.copyWith(
                              fontWeight: FontWeight.bold,
                              color: getColorSkin().ink1,
                            ),
                      ),
                      SizedBox(height: 10.h),
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: Color(0xFFB3C5E6),
                                  ),
                                  child: Column(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Text(
                                          'Thông tin hợp đồng',
                                          style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Theme(
                                        data: Theme.of(context).copyWith(
                                          dividerColor: Colors.red,
                                        ),
                                        child: DataTable(
                                          border: TableBorder.all(color: getColorSkin().grey2),
                                          dataRowColor:
                                              WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
                                            return Colors.white; // màu nền cho header
                                          }),
                                          columns: [
                                            DataColumn(
                                                label: Text('Mã đợt',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Diễn giải đợt thanh toán',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Ngày thanh\n toán dự kiến',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Giá trị TT',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                          ],
                                          rows: [
                                            DataRow(cells: [
                                              DataCell(Text('D001')),
                                              DataCell(Text('D001')),
                                              DataCell(Text('01/08/2024')),
                                              DataCell(Text('10,000,000')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('D002')),
                                              DataCell(Text('D002')),
                                              DataCell(Text('05/08/2024')),
                                              DataCell(Text('5,000,000')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('Tổng cộng')),
                                              DataCell(Text(
                                                '15,000,000',
                                                style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                              )),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('Tổng GT Hợp Đồng')),
                                              DataCell(Text(
                                                '15,000,000',
                                                style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                              )),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('Tổng GT đã xuất hóa đơn')),
                                              DataCell(Text(
                                                '0',
                                                style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                              )),
                                            ]),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: Color(0xFFF1AF84),
                                  ),
                                  child: Column(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Text(
                                          'Đã phê duyệt',
                                          style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Theme(
                                        data: Theme.of(context).copyWith(
                                          dividerColor: Colors.red,
                                        ),
                                        child: DataTable(
                                          border: TableBorder.all(color: getColorSkin().grey2),
                                          dataRowColor:
                                              WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
                                            return Colors.white; // màu nền cho header
                                          }),
                                          columns: [
                                            DataColumn(
                                                label: Text('Giá trị',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Chữ ký/ Ngày',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Số Phiếu',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                          ],
                                          rows: [
                                            DataRow(cells: [
                                              DataCell(Text('1,500,000')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('500,000')),
                                              DataCell(Text('')),
                                              DataCell(Text('DNTT-\n2412030006')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('1,000,000')),
                                              DataCell(Text('')),
                                              DataCell(Text('DNTT-\n25002250037')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text(
                                                '1,500,000',
                                                style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                              )),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: Color(0xFFA8CF8D),
                                  ),
                                  child: Column(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Text(
                                          'Giá trị thực chi từ kế toán',
                                          style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Theme(
                                        data: Theme.of(context).copyWith(
                                          dividerColor: Colors.red,
                                        ),
                                        child: DataTable(
                                          border: TableBorder.all(color: getColorSkin().grey2),
                                          dataRowColor:
                                              WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
                                            return Colors.white; // màu nền cho header
                                          }),
                                          columns: [
                                            DataColumn(
                                                label: Text('Giá trị',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Ngày chi',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                            DataColumn(
                                                label: Text('Số chứng từ',
                                                    style:
                                                        getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold))),
                                          ],
                                          rows: [
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text(
                                                '0',
                                                style: getTypoSkin().medium14.copyWith(fontWeight: FontWeight.bold),
                                              )),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                            DataRow(cells: [
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                              DataCell(Text('')),
                                            ]),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
            ),
            // Nút "Tải File PDF" đặt ở đáy Container
            Padding(
              padding: EdgeInsets.only(bottom: 16.h, right: 16.w),
              child: Align(
                alignment: Alignment.bottomRight,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: getColorSkin().primaryBlue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  onPressed: () {},
                  child: Text(
                    'Tải File PDF',
                    style: getTypoSkin().medium14.copyWith(
                          color: getColorSkin().white,
                        ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // Mobile: giữ nguyên giao diện cũ
      return Container(
        width: 353.w,
        height: 754.h,
        decoration: BoxDecoration(
          color: getColorSkin().white,
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10.0,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Padding(
              padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: getTypoSkin().medium16.copyWith(
                          color: getColorSkin().ink1,
                        ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: getColorSkin().ink1),
                    onPressed: () => Navigator.of(context).pop(), // Đóng popup
                  ),
                ],
              ),
            ),
            // Content section
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Text(
                          'Lịch thanh toán 12501040008984',
                          style:
                              getTypoSkin().medium16.copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                        ),
                      ),
                      SizedBox(height: 9.h),
                      Text(
                        '1. Thông tin hợp đồng',
                        style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                      ),
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'Hợp đồng số: ',
                                      style: getTypoSkin().medium14.copyWith(
                                            color: getColorSkin().ink1,
                                          ),
                                    ),
                                    Text(
                                      'HD158.1',
                                      style: getTypoSkin()
                                          .medium14
                                          .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                                SizedBox(width: 20.w),
                                Row(
                                  children: [
                                    Text(
                                      'Ngày hợp đồng: ',
                                      style: getTypoSkin().medium14.copyWith(
                                            color: getColorSkin().ink1,
                                          ),
                                    ),
                                    Text(
                                      '31/10/2023',
                                      style: getTypoSkin()
                                          .medium14
                                          .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                                SizedBox(width: 20.w),
                              ],
                            ),
                            Row(
                              children: [
                                Text(
                                  'Tên hợp đồng: ',
                                  style: getTypoSkin().medium14.copyWith(
                                        color: getColorSkin().ink1,
                                      ),
                                ),
                                Text(
                                  'HD 15/8 (CT chung)',
                                  style: getTypoSkin()
                                      .medium14
                                      .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            SizedBox(width: 20.w),
                            Row(
                              children: [
                                Text(
                                  'Nhà thầu/NCC: ',
                                  style: getTypoSkin().medium14.copyWith(
                                        color: getColorSkin().ink1,
                                      ),
                                ),
                                Text(
                                  'Công Ty C6 Phân Tạp Đoàn Đất Xanh',
                                  style: getTypoSkin()
                                      .medium14
                                      .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            SizedBox(width: 20.w),
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        'Tổng giá trị hợp đồng: ',
                                        style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                      ),
                                      Text(
                                        '22,352,000',
                                        style: getTypoSkin()
                                            .medium14
                                            .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 20.w),
                                  Row(
                                    children: [
                                      Text(
                                        'Tổng giá trị đã thanh toán: ',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                      Text(
                                        '20,200,000,000.00',
                                        style: getTypoSkin()
                                            .medium14
                                            .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 5.h),
                                  Text(
                                    'Tỷ lệ % đã thanh toán',
                                    style: getTypoSkin().medium14.copyWith(
                                          color: getColorSkin().ink1,
                                        ),
                                  ),
                                  SizedBox(width: 20.w),
                                  Row(
                                    children: [
                                      Text(
                                        'Tổng giá trị còn phải thanh toán: ',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                      Text(
                                        '79,800,000,000.00',
                                        style: getTypoSkin()
                                            .medium14
                                            .copyWith(color: getColorSkin().ink1, fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 10.w),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Text(
                        '2. Chi tiết lịch thanh toán',
                        style: getTypoSkin().medium14.copyWith(
                              fontWeight: FontWeight.bold,
                              color: getColorSkin().ink1,
                            ),
                      ),
                      SizedBox(height: 10.h),
                      SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: 800.w,
                            child: Table(
                              columnWidths: {
                                0: FixedColumnWidth(50.w), // Cột "A"
                                1: FixedColumnWidth(70.w), // "Mã đợt"
                                2: FixedColumnWidth(150.w), // "Diễn giải"
                                3: FixedColumnWidth(100.w), // "Ngày"
                                4: FixedColumnWidth(100.w), // "Trạng thái"
                                5: FixedColumnWidth(150.w), // "Ghi chú"
                              },
                              border: TableBorder.all(),
                              children: [
                                TableRow(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Mã đợt',
                                        style: getTypoSkin().medium14.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Diễn giải thanh toán',
                                        style: getTypoSkin().medium14.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Ngày',
                                        style: getTypoSkin().medium14.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Trạng thái',
                                        style: getTypoSkin().medium14.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Ghi chú',
                                        style: getTypoSkin().medium14.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                                TableRow(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.all(12.0.w),
                                      child: Text(
                                        'D001',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 20.h),
                                      child: Text(
                                        'Đợt 1',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        '15/05/2025',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Hoàn thành',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Đã thanh toán',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                                TableRow(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.all(12.0.w),
                                      child: Text(
                                        'D002',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 20.h),
                                      child: Text(
                                        'Đợt 2',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        '31/05/2025',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Đang xử lý',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Chờ duyệt',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                                TableRow(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.all(12.0.w),
                                      child: Text(
                                        'D003',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 20.h),
                                      child: Text(
                                        'Đợt 3',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        '30/06/2025',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Chưa bắt đầu',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(8.0.w),
                                      child: Text(
                                        'Chưa có',
                                        style: getTypoSkin().medium14.copyWith(
                                              color: getColorSkin().ink1,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h), // Đệm thêm không gian phía dưới nội dung
                    ],
                  ),
                ),
              ),
            ),
            // Nút "Tải File PDF" đặt ở đáy Container
            Padding(
              padding: EdgeInsets.only(bottom: 16.h, right: 16.w),
              child: Align(
                alignment: Alignment.bottomRight,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: getColorSkin().primaryBlue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  onPressed: () {},
                  child: Text(
                    'Tải File PDF',
                    style: getTypoSkin().medium14.copyWith(
                          color: getColorSkin().white,
                        ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
  }
}
