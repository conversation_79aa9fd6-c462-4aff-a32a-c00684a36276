import 'package:eapprove/common/form/column_widget.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class RowWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final Map<String, List<Widget>> columnContents;
  final Function(String)? onAddColumn;
  final Function(String)? onDeleteColumn;
  final Function(String)? onDuplicateColumn;
  final List<FormItemInfo> columns;

  const RowWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.columnContents,
    required this.columns,
    this.onAddColumn,
    this.onDeleteColumn,
    this.onDuplicateColumn,
  }) : super(key: key);

  @override
  State<RowWidget> createState() => _RowWidgetState();
}

class _RowWidgetState extends State<RowWidget> {
  @override
  Widget build(BuildContext context) {
    // Don't render if display is false
    if (widget.data.display == false) {
      return const SizedBox.shrink();
    }

    // Sort columns by sortOrder
    final sortedColumns = List<FormItemInfo>.from(widget.columns);
    sortedColumns.sort((a, b) {
      final aSortOrder = a.colSortOrder ?? 0;
      final bSortOrder = b.colSortOrder ?? 0;
      return aSortOrder.compareTo(bSortOrder);
    });

    return Container(
      margin: EdgeInsets.only(bottom: 24.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row header with actions
          _buildRowHeader(),

          // Row content with columns
          if (sortedColumns.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 12.h),
              child: _buildRowContent(sortedColumns),
            )
          else
            _buildEmptyRow(),
        ],
      ),
    );
  }

  Widget _buildRowHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.data.label ?? 'Row',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16.sp,
          ),
        ),
        if (widget.data.readonly != true && widget.onAddColumn != null)
          ElevatedButton.icon(
            onPressed: () => widget.onAddColumn!(widget.data.id ?? ''),
            icon: Icon(Icons.add, size: 16.sp),
            label: Text('Add Column'),
            style: ElevatedButton.styleFrom(
              backgroundColor: getColorSkin().primaryBlue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              textStyle: TextStyle(fontSize: 12.sp),
            ),
          ),
      ],
    );
  }

  Widget _buildRowContent(List<FormItemInfo> columns) {
    return Container(
      constraints: BoxConstraints(
        minHeight: 0,
        maxHeight: double.infinity,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: columns.map((column) {
          // Calculate flex based on number of columns (equal width for now)
          // Could be enhanced to support custom column widths
          final flex = 1;

          return Expanded(
            flex: flex,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: ColumnWidget(
                data: column,
                onChange: widget.onChange,
                columnContent: widget.columnContents[column.id] ?? [],
                onDelete: widget.onDeleteColumn != null
                    ? () => widget.onDeleteColumn!(column.id ?? '')
                    : null,
                onDuplicate: widget.onDuplicateColumn != null
                    ? () => widget.onDuplicateColumn!(column.id ?? '')
                    : null,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyRow() {
    return Container(
      height: 120.h,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      margin: EdgeInsets.only(top: 16.h),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.grid_view,
              size: 32.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 8.h),
            Text(
              'No columns added',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14.sp,
              ),
            ),
            if (widget.data.readonly != true && widget.onAddColumn != null)
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: ElevatedButton(
                  onPressed: () => widget.onAddColumn!(widget.data.id ?? ''),
                  child: Text('Add Column'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: getColorSkin().primaryBlue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}