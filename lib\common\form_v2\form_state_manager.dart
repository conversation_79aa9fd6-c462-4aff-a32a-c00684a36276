import 'dart:async';
import 'dart:convert';
import 'package:eapprove/utils/utils.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';

import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_event.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/helper/api_expression.dart';
import 'package:eapprove/models/form/call_service_request_model.dart';
import 'package:eapprove/models/form/individual_info_request_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import 'package:eapprove/models/form/form_item_info.dart';
import 'form_validator.dart';
import 'package:collection/collection.dart';

class FormStateManager extends ChangeNotifier {
  final Map<String, dynamic> _formState = {};
  final Map<String, dynamic> _saveFormState = {};
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String?> _errors = {};
  final Map<String, Map<String, dynamic>> _eventResults = {};
  final Map<String, Map<String, dynamic>> _widgetProperties = {};
  late List<Map<String, dynamic>> _uploadedFiles = [];
  late final FormValidator _validator;
  List<FormItemInfo> _widgets = [];
  late DropdownBloc _dropdownBloc;
  FormBloc? _formBloc;
  final Map<String, dynamic> _lastDropdownRequests = {};
  final Map<String, Timer> _fetchTimers = {};
  String _taskDefKey = 'start';
  final Map<String, FocusNode> _focusNodes = {};
  bool _isDisposed = false;
  bool _skipFetchDropdown = false;
  bool get skipFetchDropdown => _skipFetchDropdown;

  FormStateManager() {
    _validator = FormValidator(
      formState: _formState,
      controllers: _controllers,
    );
    _uploadedFiles = [];
  }

  void initBloc(DropdownBloc dropdownBloc, FormBloc formBloc) {
    _dropdownBloc = dropdownBloc;
    _formBloc = formBloc;
  }

  void setTaskDefKey(String key) {
    _taskDefKey = key;
    notifyListeners();
  }

  String get taskDefKey => _taskDefKey;

  void _debouncedFetch(String key, VoidCallback callback) {
    if (_isDisposed) return;
    _fetchTimers[key]?.cancel();
    _fetchTimers[key] = Timer(const Duration(milliseconds: 200), () {
      if (!_isDisposed) {
        callback();
      }
    });
  }

  // Evaluate all event expressions
  // void _evaluateAllEventExpressions() {
  //   bool hasChanges;
  //   do {
  //     hasChanges = false;
  //     for (final widget in _widgets) {
  //       if (widget.eventExpression?.isNotEmpty == true) {
  //         final results = _validator.evaluateEventExpressions(widget);
  //         final currentResults = _eventResults[widget.name.toString()];

  //         developer.log('results: ${widget.name} ${results}',
  //             name: 'FormStateManager');

  //         // Check if results are different from current
  //         bool isDifferent = false;
  //         if (currentResults == null) {
  //           isDifferent = true;
  //         } else {
  //           // Compare hien_thi specifically
  //           final currentHienThi = currentResults['hien_thi'];
  //           final newHienThi = results['hien_thi'];
  //           isDifferent = currentHienThi != newHienThi;

  //           // Compare other properties
  //           if (!isDifferent) {
  //             for (final key in results.keys) {
  //               if (key != 'hien_thi' && currentResults[key] != results[key]) {
  //                 isDifferent = true;
  //                 break;
  //               }
  //             }
  //           }
  //         }

  //         if (isDifferent) {
  //           hasChanges = true;
  //           _eventResults[widget.name.toString()] = Map.from(results);

  //           // Only update form state for non-special fields
  //           results.forEach((key, value) {
  //             if (![
  //               'ten_truong',
  //               'bat_buoc',
  //               'chi_duoc_doc',
  //               'huong_dan',
  //               'hien_thi'
  //             ].contains(key)) {
  //               if (_formState[key] != value) {
  //                 _formState[key] = value;
  //                 final controller = _controllers[key];
  //                 if (controller != null &&
  //                     controller.text != value.toString()) {
  //                   controller.text = value.toString();
  //                 }
  //               }
  //             }
  //           });
  //         }
  //       }
  //     }
  //   } while (hasChanges);
  // }

  // Set widgets and evaluate all event expressions
  void setWidgets(List<FormItemInfo> widgets) {
    if (_isDisposed) return;
    _widgets = widgets;
    evaluateEventExpressions(fromFunction: 'setWidgets');
    notifyListeners();
  }

  // Get field value by id
  dynamic getFieldValue(String id, {int? rowIndex}) {
    // debugPrint('getFieldValue by id: $id, rowIndex: $rowIndex');

    if (rowIndex != null) {
      final rowKey = 'row_$rowIndex';
      final rowData = _formState[rowKey];
      if (rowData is Map) {
        return rowData[id];
      }
    }
    return _formState[id];
  }

  /// Get field values by type
  /// Returns a list of field values that match the specified type
  /// [type] - The FormItemType to filter by
  /// [parentName] - Optional parent name for nested fields
  /// [rowIndex] - Optional row index for table fields
  List<Map<String, dynamic>> getFieldValueByType(FormItemType type, {String? parentName, int? rowIndex}) {
    // developer.log('🔍 Getting fields of type: $type',
    //     name: 'getFieldValueByType');

    List<Map<String, dynamic>> results = [];

    // Filter widgets by type
    final matchingWidgets = _widgets.where((widget) => widget.type == type).toList();

    for (final widget in matchingWidgets) {
      final value = getFieldValue(widget.name.toString(), rowIndex: rowIndex);
      if (value != null) {
        results.add({
          'name': widget.name,
          'label': widget.label,
          'value': value,
          'parentName': widget.parentName,
          'fileType': widget.fileType ?? "",
        });
      }
    }

    // developer.log('✅ Found ${results.length} fields of type $type',
    //     name: 'getFieldValueByType');
    return results;
  }

  // Get field value by name
  dynamic getFieldValueByName(String name, String? parentName, {int? rowIndex}) {
    // developer.log(
    //     '🔎 getFieldValueByName: name=$name, parent=$parentName, row=$rowIndex',
    //     name: 'getFieldValueByName');

    if (parentName?.isNotEmpty == true) {
      final rawParent = _formState[parentName];
      if (rawParent is Map) {
        final parentMap = Map<String, dynamic>.from(rawParent);

        if (rowIndex != null) {
          final rowData = parentMap['row_$rowIndex'];
          if (rowData is Map && rowData.containsKey(name)) {
            final value = rowData[name];
            // developer.log(
            //     "✅ Found: [$parentName][row_$rowIndex][$name] = $value",
            //     name: 'getFieldValueByName');
            return value;
          }
        }

        if (parentMap.containsKey(name)) {
          final value = parentMap[name];
          // developer.log("✅ Found: [$parentName][$name] = $value",
          //     name: 'getFieldValueByName');
          return value;
        }

        // developer.log("⚠️ Not found: [$parentName][$name]",
        //     name: 'getFieldValueByName');
      }
    }

    final widget = _widgets.firstWhere(
      (w) => w.name?.toLowerCase() == name.toLowerCase(),
      orElse: () => FormItemInfo(),
    );

    final value = _formState[widget.name];
    // developer.log("🔍 Found in root: ${widget.name} = $value",
    //     name: 'getFieldValueByName');
    return value;
  }

  dynamic getFieldLabel(String name) {
    // developer.log('getFieldLabel by name: $name', name: 'getFieldLabel');
    final widget = _widgets.firstWhere(
      (w) => w.name.toString().toLowerCase() == name.toString().toLowerCase(),
      orElse: () => FormItemInfo(),
    );
    return widget.label;
  }

  // Get event expression results
  Map<String, dynamic>? getEventExpressionResults(String name) {
    final results = _eventResults[name];
    return results;
  }

  Map<String, dynamic> _buildDropdownRequestModel(FormItemInfo item) {
    switch (item.optionType) {
      case 'masterData':
        final mdExpressions = item.optionConfig?['mdExpression'] as List<dynamic>? ?? [];
        final masterDataId = item.optionConfig?['masterDataId']?.toString() ?? '';
        return ApiExpression.convertMdExpressionToMasterDataFilter(mdExpressions, masterDataId, this, item.parentName)
            .toJson();

      case 'orgchart':
        final orgchartType = item.optionConfig?['orgchartType']?.toString() ?? 'user';
        final orgchartExpressions = item.optionConfig?['orgchartExpression'] as List<dynamic>? ?? [];
        return ApiExpression.convertOrgExpressionToInputDataFilter(
                orgchartExpressions, orgchartType, [], this, item.parentName)
            .toJson();

      case 'api_link':
        return ApiExpression.getApiLinkInput(
            item.optionConfig?['apiExpression'] as List<dynamic>? ?? [], this, item.parentName);

      default:
        return {}; // For normal/static dropdowns
    }
  }

  // Set field value with both formats
  void setFieldValue(
    String name,
    dynamic value, {
    String? parentName,
    int? rowIndex,
    List<dynamic>? optionSelect = const [],
    bool skipEvaluateEvent = false,
    String? fromFunction,
  }) {
    if (_isDisposed) return;
    // developer.log(
    //   '🔄 setFieldValue: $fromFunction $parentName $name = $value',
    // );

    // Reset error for this field
    setError(name, null);

    if (skipEvaluateEvent) {
      _skipFetchDropdown = true;
    }

    bool hasChanges = false;

    if (parentName == null || parentName.isEmpty) {
      // For non-table inputs
      if (_formState[name] != value) {
        _formState[name] = value;
        // Update controller if exists
        final controller = _controllers[name];
        if (controller != null && controller.text != value.toString()) {
          controller.text = value.toString();
        }

        // Update save form state
        _updateSaveFormState(
          name,
          value,
          rowIndex: rowIndex,
          optionSelect: optionSelect,
        );

        hasChanges = true;
      }
    } else {
      // For table inputs
      var tableData = _formState[parentName];

      // Initialize table data if needed
      if (tableData == null) {
        tableData = <Map<String, dynamic>>[];
        _formState[parentName] = tableData;
        hasChanges = true;
      }

      // Convert to List if it's not already
      if (tableData is! List) {
        if (tableData is Map) {
          // Convert from old format
          final newTableData = <Map<String, dynamic>>[];
          tableData.forEach((key, value) {
            if (key.toString().startsWith('row_')) {
              final rowIndex = int.tryParse(key.toString().substring(4)) ?? 0;
              while (newTableData.length <= rowIndex) {
                newTableData.add(<String, dynamic>{});
              }
              if (value is Map) {
                // Convert Map<dynamic, dynamic> to Map<String, dynamic>
                final convertedMap = <String, dynamic>{};
                value.forEach((k, v) {
                  convertedMap[k.toString()] = v;
                });
                newTableData[rowIndex] = convertedMap;
              }
            }
          });
          tableData = newTableData;
          _formState[parentName] = tableData;
          hasChanges = true;
        } else {
          // Initialize empty table
          tableData = <Map<String, dynamic>>[];
          _formState[parentName] = tableData;
          hasChanges = true;
        }
      }

      if (rowIndex != null) {
        // Ensure the list has enough rows
        while (tableData.length <= rowIndex) {
          tableData.add(<String, dynamic>{});
        }

        // Update the specific cell
        final row = tableData[rowIndex];
        if (row[name] != value) {
          // Convert value to String if it's not already
          final stringValue = value?.toString();
          row[name] = stringValue;

          // Update controller if exists
          final controllerKey = '${parentName}_row_${rowIndex}_$name';
          final controller = _controllers[controllerKey];
          if (controller != null && controller.text != stringValue) {
            controller.text = stringValue ?? '';
          }

          // Update save form state
          _updateSaveFormState(
            name,
            stringValue,
            rowIndex: rowIndex,
            optionSelect: optionSelect,
            parentName: parentName,
          );

          hasChanges = true;
        }
      }
    }
    // debugPrint("hasChanges:: ${hasChanges}");
    // developer.log('setFieldValueskipEvaluateEvent: $skipEvaluateEvent', name: 'setFieldValue');
    // if (hasChanges) {
    if (!skipEvaluateEvent) {
      // Evaluate event expressions
      // developer.log('setFieldValueevaluateEventExpressions', name: 'setFieldValue');
      evaluateEventExpressions(
          fieldNames: [name], fromFunction: 'setFieldValue', parentName: parentName);

      // Check and update dependent dropdowns
      final checkList = dropdownDependsOn(_widgets, name);
      // developer.log(
      //     '📋 Dependent dropdowns to update: ${checkList.map((e) => e.name).join(', ')}',
      //     name: 'setFieldValue');

      for (final item in checkList) {
        final requestModel = _buildDropdownRequestModel(item);
        final lastRequest = _lastDropdownRequests[item.name ?? ''];

        if (!mapEquals(requestModel, lastRequest)) {
          _lastDropdownRequests[item.name ?? ''] = requestModel;
          _debouncedFetch(item.name ?? '', () => fetchDropdownOptions(item));
        }
      }
    }

    // Batch the state update
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _skipFetchDropdown = false;
      notifyListeners();
    });
    // }
  }

  /// Updates the save form state with the given field value
  ///
  /// [name] - The name of the field
  /// [value] - The value to be saved
  /// [optionSelect] - Optional list of selected options for dropdown fields
  /// [rowIndex] - Optional index for table row data
  void _updateSaveFormState(
    String name,
    dynamic value, {
    List<dynamic>? optionSelect = const [],
    int? rowIndex,
    String? parentName,
  }) {
    // developer.log('_updateSaveFormState: $parentName: $name = $value', name: '_updateSaveFormState');
    if (parentName != null && parentName.isNotEmpty) {
      // Handle table/matrix fields
      final parentWidget = _widgets.firstWhere(
        (w) => w.name.toString() == parentName,
        orElse: () => FormItemInfo(),
      );
      final widget = _widgets.firstWhere(
        (w) => w.name.toString() == name && w.parentName == parentName,
        orElse: () => FormItemInfo(),
      );

      if (!_saveFormState.containsKey(parentName)) {
        _saveFormState[parentName] = {
          "value": "",
          "type": "Json",
          "valueInfo": {"data": []}
        };
      }

      final saveEntry = _saveFormState[parentName] as Map<dynamic, dynamic>;
      final valueInfo = saveEntry["valueInfo"] as Map<dynamic, dynamic>;

      // Ensure 'data' list
      if (!valueInfo.containsKey("data")) {
        valueInfo["data"] = [];
      }
      final dataList = valueInfo["data"] as List;

      // Ensure correct number of rows
      if (rowIndex != null) {
        while (dataList.length <= rowIndex) {
          dataList.add({});
        }

        final row = dataList[rowIndex] as Map<dynamic, dynamic>;

        // Handle special field types within table
        if (widget.type == FormItemType.datePicker) {
          DateTime? date;
          if (value is String) {
            try {
              date = DateFormat('dd/MM/yyyy').parse(value);
            } catch (e) {
              date = null;
            }
          } else if (value is DateTime) {
            date = value;
          }

          if (date != null) {
            row[name] = DateFormat('dd/MM/yyyy').format(date);
            row["${name}_iso"] = date.toIso8601String();
          } else {
            row[name] = value?.toString() ?? "";
          }
        } else if (widget.type == FormItemType.dropdown ||
            widget.type == FormItemType.radioButton ||
            widget.type == FormItemType.checkbox) {
          row[name] = value?.toString() ?? "";
          if (optionSelect != null && optionSelect.isNotEmpty) {
            row['${name}_opt'] = optionSelect;
            // Add text field for display value
            final selectedOption = optionSelect.firstWhere(
              (opt) => opt['value'] == value,
              orElse: () => {'label': value?.toString() ?? ""},
            );
            row['${name}_text'] = selectedOption['label'] ?? (value?.toString() ?? "");
          }
        } else {
          row[name] = _safeValue(value) ?? "";
        }
      }

      // Build table object
      final tableObject = {
        "customType": "Table",
        "data": {
          "tableName": parentWidget.label ?? "",
          "columns": parentWidget.columns?.map((e) => {"name": e.name ?? "", "label": e.label ?? ""}).toList() ?? [],
          "data": dataList
        }
      };
      final jsonString = jsonEncode(tableObject);
      saveEntry["value"] = jsonString;
      saveEntry["type"] = "Json";

      developer.log('saveEntry: ${saveEntry["value"].runtimeType}', name: 'saveEntry');
      developer.log('saveEntry: ${saveEntry["value"].toString()}', name: 'saveEntry');
      _saveFormState[parentName] = saveEntry;
    } else {
      // Handle non-table fields
      final widget = findWidgetByName(name);
      if (widget == null) return;
// developer.log('_updateSaveFormStateautoGenId: ${widget.autoGenId}' , name: '_updateSaveFormState');
      
      // Handle different field types
      switch (widget.type) {
        case FormItemType.datePicker:
          DateTime? date;
          if (value is String) {
            try {
              date = DateFormat('dd/MM/yyyy').parse(value);
            } catch (e) {
              date = null;
            }
          } else if (value is DateTime) {
            date = value;
          }

          if (date != null) {
            // Add ISO date field
            _saveFormState['${name}_iso'] = {
              "value": Utils.convertToUtcIso(date.toIso8601String()),
              "type": "String",
              "valueInfo": {}
            };

            // Add formatted date field
            _saveFormState[name] = {
              "value": DateFormat('dd/MM/yyyy').format(date).toString(),
              "type": "String",
              "valueInfo": {"type": "DATE", "value": date.millisecondsSinceEpoch}
            };
          } else {
            _saveFormState[name] = {
              "value": "",
              "type": "String",
              "valueInfo": {"type": "DATE", "value": null}
            };
          }
          break;
        case FormItemType.dropdown:
        case FormItemType.radioButton:
        case FormItemType.checkbox:
          if (optionSelect != null && optionSelect.isNotEmpty) {
            // Add main field
            _saveFormState[name] = {
              "value": value?.toString() ?? "",
              "type": "String",
              "valueInfo": {
                "data": {
                  "value": value?.toString() ?? "",
                  "label": optionSelect
                          .firstWhere(
                            (opt) => opt['value'] == value,
                            orElse: () => {'label': value?.toString() ?? ""},
                          )['label']
                          ?.toString() ??
                      (value?.toString() ?? ""),
                  "options": optionSelect
                }
              }
            };

            // Add text field
            final selectedOption = optionSelect.firstWhere(
              (opt) => opt['value'] == value,
              orElse: () => {'label': value?.toString() ?? ""},
            );
            _saveFormState['${name}_text'] = {
              "value": selectedOption['label'] ?? (value?.toString() ?? ""),
              "type": "String",
              "valueInfo": {}
            };
          } else {
            _saveFormState[name] = {
              "value": value?.toString() ?? "",
              "type": "String",
              "valueInfo": {
                "data": {"value": value?.toString() ?? "", "label": value?.toString() ?? "", "options": []}
              }
            };
          }
          break;
        default:
          // Default handling for other field types
          
          _saveFormState[name] = {
            "value": value?.toString() ?? "",
            "type": widget.autoGenId == true ? "AUTO" : "String",
            "valueInfo": {}
          };
          break;
      }

    }
  }

  /// Helper method to get table data in the correct format
  List<Map<String, dynamic>> _getTableData(String tableName) {
    final tableData = _formState[tableName];
    if (tableData is List) {
      return List<Map<String, dynamic>>.from(tableData);
    } else if (tableData is Map) {
      final result = <Map<String, dynamic>>[];
      tableData.forEach((key, value) {
        if (key.toString().startsWith('row_')) {
          final rowIndex = int.tryParse(key.toString().substring(4)) ?? 0;
          while (result.length <= rowIndex) {
            result.add({});
          }
          if (value is Map) {
            result[rowIndex] = Map<String, dynamic>.from(value);
          }
        }
      });
      return result;
    }
    return [];
  }

  /// Creates the base save value structure
  Map<String, dynamic> _createBaseSaveValue(dynamic value) {
    return {
      'value': value != null && value.toString().isNotEmpty ? value.toString() : "",
      'type': 'String',
      'valueInfo': {}
    };
  }

  /// Handles special field types and their specific save value structures
  void _handleSpecialFieldTypes(
    FormItemInfo widget,
    String name,
    dynamic value,
    Map<String, dynamic> saveValue,
    List<dynamic>? optionSelect,
    String? parentName,
  ) {
    // developer.log('handleSpecialFieldTypes: ${widget.type} $name $optionSelect',
    //     name: 'handleSpecialFieldTypes');
    // Reset valueInfo for special types
    saveValue['valueInfo'] = {};

    // Handle table row data first
    // if (parentName != null && parentName.isNotEmpty) {
    //   final parentWidget = _widgets.firstWhere(
    //     (w) => w.name.toString() == parentName,
    //     orElse: () => FormItemInfo(),
    //   );

    //   if (parentWidget.type == FormItemType.table) {
    //     _handleTableValue(value, saveValue, optionSelect, parentName, name);
    //     return;
    //   }
    // }
    // developer.log('handleSpecialFieldTypes: ${widget.type} $name $optionSelect',
    //     name: 'handleSpecialFieldTypes');
    // Handle other field types
    switch (widget.type) {
      case FormItemType.datePicker:
        _handleDatePickerValue(name, value, saveValue);
        break;
      case FormItemType.fileUpload:
        _handleFileUploadValue(name, value, saveValue);
        break;
      case FormItemType.dropdown:
        _handleDropdownValue(name, value, saveValue, optionSelect);
        break;
      case FormItemType.radioButton:
      case FormItemType.checkbox:
      case FormItemType.dropdown:
      case FormItemType.select:
        _handleRadioButtonValue(name);
        break;
      default:
        // For all other types, keep the default valueInfo
        break;
    }
  }

  /// Handles date picker field value
  void _handleDatePickerValue(String name, dynamic value, Map<String, dynamic> saveValue) {
    // saveValue['type'] = 'DATE';
    if (value == null) {
      saveValue['valueInfo'] = {'type': 'DATE', 'value': ""};

      _saveFormState['${name}_iso'] = {
        // "value": null,
        "type": "String",
        "valueInfo": {}
      };
      return;
    }

    final dateTime = value is String ? DateTime.parse(value) : value as DateTime;
    saveValue['valueInfo'] = {'type': 'DATE', 'value': dateTime.millisecondsSinceEpoch};
    _saveFormState['${name}_iso'] = {"value": dateTime.toIso8601String().toString(), "type": "String", "valueInfo": {}};
    _saveFormState[name] = saveValue;
  }

  /// Handles file upload field value
  void _handleFileUploadValue(
    String name,
    dynamic value,
    Map<String, dynamic> saveValue,
  ) {
    // if (value is! Map<String, dynamic>) return;

    final widget = _widgets.firstWhere(
      (w) => w.name.toString().toLowerCase() == name.toLowerCase(),
      orElse: () => FormItemInfo(),
    );
    final now = DateTime.now();
    final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(now);
    final Box authBox = Hive.box('authentication');
    final String? username = authBox.get('username');

    // if (widget.attachFileObj?.isNotEmpty == true) {
    //   final data = {
    //     "createdUser": username ?? "",
    //     "createdTime": now.toIso8601String(),
    //     "uploadTime": formattedDate,
    //     "filename": widget.attachFileObj?['fileName'] ?? "",
    //     "downloadUrl": widget.attachFileObj?['downloadUrl'] ??
    //         widget.attachFileObj?['orgFilePath'] ??
    //         "",
    //     "idHistory": widget.attachFileObj?['idHistory'] ?? "",
    //     "displayName": widget.attachFileObj?['fileName'] ?? "",
    //     "fileSize": widget.attachFileObj?['fileSize'] ?? 0,
    //     "isSave": true
    //   };

    //   saveValue['value'] = widget.attachFileObj?['fileName'] ?? '';
    //   saveValue['type'] = 'FILE';
    //   saveValue['valueInfo'] = {
    //     'filename': widget.attachFileObj?['fileName'] ?? '',
    //     'mimeType':
    //         getMimeTypeFromFileName(widget.attachFileObj?['fileName'] ?? ''),
    //     'data': data
    //   };

    //   _saveFormState[name] = saveValue;
    //   _saveFormState['${name}_multiName'] = {"value": "", "type": "String"};
    //   _saveFormState['${name}_history'] = {
    //     "value": data.toString(),
    //     "type": "String"
    //   };
    // }
    // if (widget.attachFileObjDefault?.isNotEmpty == true) {
    if (value.length == 1) {
      final data = {
        "createdUser": username ?? "",
        "createdTime": now.toIso8601String(),
        "uploadTime": formattedDate,
        "filename": value[0]['fileName'] ?? "",
        "downloadUrl": value[0]['path'] ?? "",
        "idHistory": value[0]?['idHistory'] ?? "",
        "displayName": value[0]?['fileName']?.split('/').last ?? "",
        "fileSize": value[0]?['fileSize'] ?? 0,
        "isSave": true
      };

      saveValue['value'] = value[0]?['path'] ?? value[0]?['fileName'] ?? '';
      saveValue['type'] = 'File';
      saveValue['valueInfo'] = {
        'filename': value[0]?['fileName'] ?? '',
        'mimeType': getMimeTypeFromFileName(value[0]?['fileName'] ?? ''),
        'data': data
      };

      _saveFormState[name] = saveValue;
      _saveFormState['${name}_multiName'] = {"value": "", "type": "String"};
      _saveFormState['${name}_history'] = {"value": data.toString(), "type": "String"};
    }
  }

  /// Handles dropdown field value
  void _handleDropdownValue(
    String name,
    dynamic value,
    Map<String, dynamic> saveValue,
    List<dynamic>? optionSelect,
  ) {
    // developer.log('handleDropdownValue: $value $optionSelect',
    //     name: 'handleDropdownValue');
    saveValue['valueInfo'] = {'data': optionSelect};
    _saveFormState[name] = saveValue;
  }

  /// Handles radio button field value
  void _handleRadioButtonValue(String name) {
    _saveFormState['${name}_text'] = {"type": "String", "valueInfo": {}};
  }

  dynamic _safeValue(dynamic value) {
    if (value is DateTime) {
      return value.toIso8601String(); // convert toàn bộ DateTime thành String
    }
    if (value is List) {
      return value.map(_safeValue).toList();
    }
    if (value is Map) {
      return value.map((key, val) => MapEntry(key, _safeValue(val)));
    }
    return value;
  }

  // Get save form state
  Map<String, dynamic> getSaveFormState() => Map.from(_saveFormState);

  // Clear save form state
  void clearSaveFormState() {
    _saveFormState.clear();
  }

  // Get controller
  TextEditingController getController(
    String name, {
    String? parentName,
    int? rowIndex,
  }) {
    if (_isDisposed) {
      throw StateError('FormStateManager has been disposed');
    }

    final controllerKey =
        parentName?.isNotEmpty == true && rowIndex != null ? '${parentName}_row_${rowIndex}_$name' : name;

    _controllers.putIfAbsent(controllerKey, () => TextEditingController());
    _focusNodes.putIfAbsent(controllerKey, () => FocusNode());

    return _controllers[controllerKey]!;
  }

  FocusNode? getFocusNode(
    String name, {
    String? parentName,
    int? rowIndex,
  }) {
    final controllerKey =
        parentName?.isNotEmpty == true && rowIndex != null ? '${parentName}_row_${rowIndex}_$name' : name;
    return _focusNodes[controllerKey];
  }

  void safeUpdateController(String key, dynamic value) {
    final controller = _controllers[key];
    final focusNode = _focusNodes[key];
    if (controller != null && controller.text != value.toString() && !(focusNode?.hasFocus ?? false)) {
      // 🔥 kiểm focusNode ở đây
      controller.text = value.toString();
    }
  }

  // Get/Set error
  String? getError(String name) => _errors[name];
  void setError(String name, String? error) {
    if (_errors[name] != error) {
      _errors[name] = error;
      notifyListeners();
    }
  }

  // Get/Set event results
  Map<String, dynamic> getEventResults(String name) => _eventResults[name] ?? {};
  void setEventResults(String name, Map<String, dynamic> results) {
    if (!mapEquals(_eventResults[name], results)) {
      _eventResults[name] = results;
      notifyListeners();
    }
  }

  // Validate field
  // String? validateField(FormItemInfo widget) {
  //   // Check if field is visible based on eventExpression
  //   final eventResults = _eventResults[widget.name.toString()];
  //   if (eventResults != null && eventResults['hien_thi'] == false) {
  //     return null; // Skip validation for hidden fields
  //   }

  //   final error = _validator.validateField(widget);

  //   if (error != null && error.isNotEmpty) {
  //     // developer.log('validateField: $widget $error', name: 'validateField');
  //   }
  //   setError(widget.name.toString(), error);
  //   return error;
  // }

  // Override clearForm to also clear save form state
  @override
  void clearForm() {
    _formState.clear();
    _saveFormState.clear();
    _uploadedFiles.clear();
    _controllers.values.forEach((controller) => controller.clear());
    notifyListeners();
  }

  // Get all form data
  Map<String, dynamic> getAllFormData() {
    final result = Map<String, dynamic>.from(_formState);

    // Process table fields to ensure they are in the correct format
    for (final widget in _widgets) {
      if (widget.type == FormItemType.table) {
        final tableName = widget.name.toString();
        final tableData = result[tableName];

        if (tableData is List) {
          // Table data is already in the correct format
          result[tableName] = tableData;
        } else if (tableData is Map) {
          // Convert old format to new format
          final newTableData = <Map<String, dynamic>>[];
          tableData.forEach((key, value) {
            if (key.toString().startsWith('row_')) {
              final rowIndex = int.tryParse(key.toString().substring(4)) ?? 0;
              while (newTableData.length <= rowIndex) {
                newTableData.add({});
              }
              if (value is Map) {
                newTableData[rowIndex] = Map<String, dynamic>.from(value);
              }
            }
          });
          result[tableName] = newTableData;
        } else {
          // Initialize empty table
          result[tableName] = <Map<String, dynamic>>[];
        }
      }
    }

    return result;
  }

  // Validate all fields
  bool validateAll() {
    bool isValid = true;
    for (final widget in _widgets) {
      final fieldName = widget.name.toString();

      // Check if field is visible based on eventExpression
      final eventResults = _eventResults[fieldName];
      if ((eventResults == null || eventResults['hien_thi'] == false) ||
          widget.display == false ||
          eventResults['bat_buoc'] == false) {
        continue; // Skip validation for hidden fields
      }

      final fieldValue = _formState[fieldName];
      final error = _validator.validateField(widget.copyWith(value: fieldValue));
      debugPrint('validateAll: $fieldName $fieldValue $error');
      setError(fieldName, error);
      if (error != null) isValid = false;
    }
    return isValid;
  }

  // Add new method to set multiple field values at once
  void setAllFields(Map<String, dynamic> values) {
    if (_isDisposed) return;
    bool hasChanges = false;

    // First pass: Update form state and controllers
    values.forEach((key, value) {
      if (_formState[key] != value) {
        _formState[key] = value;
        final controller = _controllers[key];
        if (controller != null && controller.text != value.toString()) {
          controller.text = value.toString();
        }

        // Update save form state for each field
        final widget = findWidgetByName(key);
        if (widget != null) {
          // Special handling for file type
          if (widget.type == FormItemType.fileUpload) {
            if (widget.attachFileObj?.isNotEmpty == true) {
              _updateSaveFormState(
                key,
                widget.attachFileObj?['fileName'] ?? '',
                parentName: widget.parentName,
              );
            } else if (widget.attachFileObjDefault?.isNotEmpty == true) {
              _updateSaveFormState(
                key,
                widget.attachFileObjDefault?['fileName'] ?? '',
                parentName: widget.parentName,
              );
            }
          } else {
            _updateSaveFormState(
              key,
              value,
              parentName: widget.parentName,
            );
          }
        }

        hasChanges = true;
      }
    });

    if (hasChanges) {
      // Second pass: Evaluate event expressions only for affected widgets
      for (final widget in _widgets) {
        // developer
        //     .log("evaluateEventExpressionswidgetwidgetwidget:: ${widget.name}");
        if (widget.type == FormItemType.table) {
          for (final column in widget.columns ?? []) {
            if (column.eventExpression?.isNotEmpty == true) {
              final results = _validator.evaluateEventExpressions(column, 'SetAllFieldsTAble');
              // developer.log(
              //     'evaluateEventExpressionsTableColumnResults: ${column.name} ${results}',
              //     name: 'evaluateEventExpressions');
            }
          }
        }
        final results = _validator.evaluateEventExpressions(widget, 'SetAllFields');
        if (!mapEquals(results, _eventResults[widget.name.toString()])) {
          _eventResults[widget.name.toString()] = Map.from(results);

          // Update dependent fields
          results.forEach((key, value) {
            if (!['ten_truong', 'bat_buoc', 'chi_duoc_doc', 'huong_dan', 'hien_thi'].contains(key)) {
              _formState[key] = value;
            }
          });
        }
      }

      notifyListeners();
    }
  }

  Future<void> prefetchDropdownData() async {
    if (_isDisposed) return;
    for (final item in _widgets) {
      if (_isDisposed) return;
        await fetchDropdownOptions(item);
      
      if (item.type == FormItemType.table) {
        for (final column in item.columns ?? []) {
          if (_isDisposed) return;
          await fetchDropdownOptions(column);
        }
      }
    }
  }

  Future<void> fetchDropdownOptions(FormItemInfo item) async {
    if (_isDisposed) return;
    debugPrint(
      "fetchDropdownOptions - item: ${item.name} ${item.optionType}",
    );
    switch (item.optionType) {
      case 'masterData':
        _fetchMasterData(item);
        break;
      case 'orgchart':
        _fetchOrgchartData(item);
        break;
      case 'api_link':
        _fetchApiLinkData(item);
        break;
      default:
        // Do nothing for normal/static
        break;
    }
  }

  void _fetchMasterData(FormItemInfo item) {
    final masterDataId = item.optionConfig?['masterDataId']?.toString() ?? '';
    if (masterDataId.isEmpty) return;

    final mdExpressions = item.optionConfig?['mdExpression'] as List<dynamic>?;
    // developer.log('mdExpressions: $mdExpressions', name: 'fetchMasterData');
    // developer.log('masterDataId: $masterDataId', name: 'fetchMasterData');
    final requestModel =
        ApiExpression.convertMdExpressionToMasterDataFilter(mdExpressions ?? [], masterDataId, this, item.parentName);
    // debugPrint("item.parentName: ${item.parentName}");
    final key = item.parentName?.isNotEmpty == true ? '${item.parentName}.${item.name}' : item.name ?? '';
    // debugPrint("requestModel $requestModel");
    // debugPrint("keykey: ${key}");
    _dropdownBloc.add(GetDropdownDataFromMasterData(
      requestModel: requestModel,
      requestKey: key,
      apiURL: '',
      forceRefresh: true,
    ));
  }

  void _fetchOrgchartData(FormItemInfo item) {
   String orgchartType = 'user';
    if(item.optionConfig?['orgchartType']?.toString() != ""){
      orgchartType =  item.optionConfig?['orgchartType']?.toString() ?? '';
    }

    // developer.log('_fetchOrgchartDataorgchartType: ${item.name} ${orgchartType}', name: '_fetchOrgchartData');
    final orgchartExpressions = item.optionConfig?['orgchartExpression'] as List<dynamic>? ?? [];

    if (orgchartType.isEmpty) return;
    // developer.log('_fetchOrgchartDataorgchartType: ${item.name} ${orgchartType}', name: '_fetchOrgchartData');
    final requestModel = ApiExpression.convertOrgExpressionToInputDataFilter(
        orgchartExpressions, orgchartType, [], this, item.parentName);
    final key = item.parentName?.isNotEmpty == true ? '${item.parentName}.${item.name}' : item.name;

    // debugPrint('keykeykeykeykeykeykeykey key: $key ${requestModel.toJson()}');
    Future.delayed(const Duration(milliseconds: 100), () {
      _dropdownBloc.add(GetDropdownDataRequested(
        requestModel: requestModel,
        requestKey: key ?? "",
        forceRefresh: true,
      ));
    });
  }

  void _fetchApiLinkData(FormItemInfo item) async {
    // Add null check for _formBloc
    if (_formBloc == null) {
      // developer.log('FormBloc not initialized', name: '_fetchApiLinkData');
      return;
    }

    // Chuẩn bị tham số request cho API API link
    final listBaseUrl = _formBloc!.state.listBaseUrlResponse;

    final apiURLBase = item.optionConfig?['apiURLBase']?.toString();
    // debugPrint("apiURLBaseapiURLBase ${item.name} ${apiURLBase} ${item.optionConfig?['apiURLBase']}");
    // debugPrint("listBaseUrllistBaseUrllistBaseUrl ${listBaseUrl}");

    // Đảm bảo xử lý đồng bộ trước khi tìm kiếm
    await Future.delayed(Duration.zero);
    // developer.log('listBaseUrl: $listBaseUrl', name: 'fetchApiLinkData');
    final baseURL = listBaseUrl?.firstWhere(
      (e) {
        // debugPrint(
        //     "e.id.toString() == apiURLBase.toString() ${e.id} ${apiURLBase} ${e.id.toString() == apiURLBase?.toString()}");
        return e.id.toString() == apiURLBase?.toString();
      },
      orElse: () => listBaseUrl.first,
    )?.url;

    if (baseURL == null || baseURL.isEmpty) {
      return;
    }

    final requestBody = ApiExpression.getApiLinkInput(
        item.optionConfig?['apiExpression'] as List<dynamic>? ?? [], this, item.parentName);
    // developer.log('requestBodygetApiLinkInput: ${item.name} - $requestBody',
    //     name: 'fetchApiLinkData');

    const checkURLBase = ['call-service-admin', 'call-service-ihrp', 'call-service-cons', 'call-service-sap'];
    bool found = checkURLBase.any((char) => baseURL?.contains(char) ?? false);
    final apiURL = item.optionConfig?['apiURL']?.toString() ?? '';
    // developer.log('baseURLfetchApiLinkData: $baseURL $apiURL ${item.name}',
    //     name: 'fetchApiLinkData');

    // debugPrint("baseURLbaseURL ${item.name} ${baseURL}");
    // debugPrint("foundfoundfoundfound ${item.name} ${found}");
    if (found) {
      _dropdownBloc.add(GetDropdownDataFromAPILink(
        requestModel: requestBody,
        requestKey: item.parentName?.isNotEmpty == true ? '${item.parentName}.${item.name}' : item.name ?? '',
        endpoint: apiURL.startsWith("/") ? apiURL : "/$apiURL",
        apiURL: baseURL,
        forceRefresh: true,
      ));
      return;
    } else {
      final input = {
        'url': Uri.parse('${baseURL}${item.optionConfig?['apiURL']}'),
        'method': item.optionConfig?['apiMethod']?.toString() ?? 'POST',
        'params': requestBody,
        'headers': item.optionConfig?['addHeaderTemplate'].isEmpty ? {} : item.optionConfig?['addHeaderTemplate'],
        'isSaveLog': item.optionConfig?['isSaveLog'] ?? false,
      };

      _dropdownBloc.add(GetDropdownDataFromAPILinkService(
        requestModel: CallServiceRequestModel.fromJson(input),
        requestKey: item.parentName?.isNotEmpty == true ? '${item.parentName}.${item.name}' : item.name ?? '',
        apiURL: baseURL.endsWith("/") ? baseURL : "$baseURL/",
        forceRefresh: true,
      ));
    }
  }

  List<FormItemInfo> dropdownDependsOn(List<FormItemInfo> widgets, String changedFieldName) {
    final fieldNameRef = '@$changedFieldName';
    List<FormItemInfo> arrChange = [];
    // developer.log('🔍 Checking dependencies for field: $changedFieldName',
    //     name: 'dropdownDependsOn');

    for (final widget in widgets) {
      // Check for regular widget dependencies
      final apiExpressions = widget.optionConfig?['apiExpression'] ?? [];
      final mdExpressions = widget.optionConfig?['mdExpression'] ?? [];
      final orgchartExpressions = widget.optionConfig?['orgchartExpression'] ?? [];

      final allExpressions = [...apiExpressions, ...mdExpressions, ...orgchartExpressions];

      // Check if any expression contains the field reference
      final check = allExpressions.any((expr) {
        final input = expr['input']?.toString() ?? '';
        // Check for exact match or if input contains the field reference
        final matches = input.toLowerCase() == fieldNameRef.toLowerCase() ||
            input.toLowerCase().contains(fieldNameRef.toLowerCase());
        if (matches) {
          // developer.log(
          //     '✅ Found dependency: ${widget.name} depends on $changedFieldName',
          //     name: 'dropdownDependsOn');
        }
        return matches;
      });

      if (check) {
        arrChange.add(widget);
      }

      // Check for table column dependencies
      if (widget.type == FormItemType.table) {
        for (final column in widget.columns ?? []) {
          // developer.log("🔍 Checking table column: ${column.name}",
          //     name: 'dropdownDependsOn');

          final columnApiExpressions = column.optionConfig?['apiExpression'] ?? [];
          final columnMdExpressions = column.optionConfig?['mdExpression'] ?? [];
          final columnOrgchartExpressions = column.optionConfig?['orgchartExpression'] ?? [];

          final columnAllExpressions = [...columnApiExpressions, ...columnMdExpressions, ...columnOrgchartExpressions];

          final columnCheck = columnAllExpressions.any((expr) {
            final input = expr['input']?.toString() ?? '';
            // Check for exact match or if input contains the field reference
            final matches = input.toLowerCase() == fieldNameRef.toLowerCase() ||
                input.toLowerCase().contains(fieldNameRef.toLowerCase());
            if (matches) {
              // developer.log(
              //     '✅ Found table column dependency: ${column.name} depends on $changedFieldName',
              //     name: 'dropdownDependsOn');
            }
            return matches;
          });

          if (columnCheck) {
            arrChange.add(column);
          }
        }
      }
    }

    // developer.log('📋 Total dependencies found: ${arrChange.length}',
    //     name: 'dropdownDependsOn');
    return arrChange;
  }

  /// Evaluate event expressions cho danh sách widget chỉ định.
  /// Nếu để trống => evaluate toàn bộ
  void evaluateEventExpressions({
    List<String>? fieldNames,
    String? fromFunction,
    String? parentName = '',
  }) {
    if (_isDisposed) return;
    bool hasChanges = false;

    // Helper function to check if an expression depends on any of the changed fields
    bool hasDependency(Map<String, dynamic> expr, List<String> fields) {
      final input = expr['input']?.toString() ?? '';
      final expression = expr['expression']?.toString() ?? '';

      // Helper function to check if a field reference matches any of the changed fields
      bool isFieldMatch(String fieldRef) {
        // Handle table field references (e.g. @tableName.fieldName)
        if (fieldRef.contains('.')) {
          final parts = fieldRef.split('.');
          if (parts.length == 2) {
            final tableName = parts[0].replaceAll('@', '');
            final fieldName = parts[1];
            return fields.any((field) =>
                field.toLowerCase() == tableName.toLowerCase() || field.toLowerCase() == fieldName.toLowerCase());
          }
        }
        // Handle regular field references
        return fields.any((field) => fieldRef.toLowerCase().contains('@${field.toLowerCase()}'));
      }

      // Check both input and expression for field references
      final allReferences =
          [...input.split(' '), ...expression.split(' ')].where((part) => part.contains('@')).toList();

      return allReferences.any(isFieldMatch);
    }

    // Always evaluate all widgets if fieldNames is provided
    final widgetsToEvaluate = fieldNames != null ? _widgets : _widgets;

    // debugPrint("Evaluating events for widgets: ${widgetsToEvaluate.map((w) => w.name).join(', ')}");

    for (final widget in widgetsToEvaluate) {
      if (widget.type == FormItemType.table) {
        // Handle table-level event expressions
        if (widget.eventExpression != null && widget.eventExpression!.isNotEmpty) {
          final tableResults = _validator.evaluateEventExpressions(
              widget, 'evaluateEventExpressionsFormStateManagerTable');
          final currentTableResults = _eventResults[widget.name.toString()];
          bool isTableDifferent = !mapEquals(tableResults, currentTableResults);

          if (isTableDifferent) {
            hasChanges = true;
            _eventResults[widget.name.toString()] = Map.from(tableResults);

            // Handle table-level switchField logic
            if (tableResults['switchField'] != null && widget.switchFieldConfig != null) {
              final switchFieldValue = tableResults['switchField'].toString();
              final config = widget.switchFieldConfig!.firstWhere(
                (c) => c['switchFieldName'] == switchFieldValue,
                orElse: () => <String, dynamic>{},
              );

              if (config.isNotEmpty) {
                final updatedTable = widget.copyWith(
                  optionConfig: config['optionConfig'] as Map<String, dynamic>?,
                  optionType: config['optionType']?.toString(),
                  typeOption: config['typeOption']?.toString(),
                  readonly: config['readonly'] as bool?,
                  validations: config['validations'] as Map<String, dynamic>?,
                  label: config['label']?.toString(),
                  displayName: config['displayName']?.toString(),
                  type: _parseFormItemType(config['switchFieldType']),
                );

                final index = _widgets.indexOf(widget);
                if (index != -1) {
                  _widgets[index] = updatedTable;
                }
              }
            }

            // Update form state for table-level properties
            tableResults.forEach((key, value) {
              if (!['ten_truong', 'bat_buoc', 'chi_duoc_doc', 'huong_dan', 'hien_thi'].contains(key)) {
                _formState[key] = value;
              }
            });
          }
        }

        // Then evaluate column-level event expressions
        for (final column in widget.columns ?? []) {
          if (column.eventExpression?.isNotEmpty == true) {
            final results = _validator.evaluateEventExpressions(column, 'evaluateEventExpressionsFormStateManager1111');
            final currentResults = _eventResults[column.name.toString()];
            bool isDifferent = !mapEquals(results, currentResults);

            if (isDifferent) {
              hasChanges = true;
              _eventResults[column.name.toString()] = Map.from(results);

              // Handle switchField logic for table columns
              if (results['switchField'] != null && column.switchFieldConfig != null) {
                final switchFieldValue = results['switchField'].toString();
                final config = column.switchFieldConfig!.firstWhere(
                  (c) => c['switchFieldName'] == switchFieldValue,
                  orElse: () => <String, dynamic>{},
                );

                if (config.isNotEmpty) {
                  final updatedColumn = column.copyWith(
                    optionConfig: config['optionConfig'] as Map<String, dynamic>?,
                    optionType: config['optionType']?.toString(),
                    typeOption: config['typeOption']?.toString(),
                    readonly: config['readonly'] as bool?,
                    validations: config['validations'] as Map<String, dynamic>?,
                    label: config['label']?.toString(),
                    displayName: config['displayName']?.toString(),
                    type: _parseFormItemType(config['switchFieldType']),
                  );

                  final columnIndex = widget.columns?.indexOf(column) ?? -1;
                  if (columnIndex != -1) {
                    widget.columns![columnIndex] = updatedColumn;
                  }
                }
              }

              // Update form state for column properties
              results.forEach((key, value) {
                if (!['ten_truong', 'bat_buoc', 'chi_duoc_doc', 'huong_dan', 'hien_thi'].contains(key)) {
                  _formState[key] = value;
                }
              });
            }
          }
        }
      } else {
        final results = _validator.evaluateEventExpressions(widget,
            'evaluateEventExpressionsFormStateManager2222${fromFunction}');
            developer.log("revaluateEventExpressionsesults: ${widget.name} $results", name: 'evaluateEventExpressionsFormStateManager2222');
        final currentResults = _eventResults[widget.name.toString()];

        bool isDifferent = !mapEquals(results, currentResults);

        if (isDifferent) {
          hasChanges = true;
          _eventResults[widget.name.toString()] = Map.from(results);

          // Handle switchField logic
          if (results['switchField'] != null && widget.switchFieldConfig != null) {
            final switchFieldValue = results['switchField'].toString();
            final config = widget.switchFieldConfig!.firstWhere(
              (c) => c['switchFieldName'] == switchFieldValue,
              orElse: () => {},
            );

            if (config.isNotEmpty) {
              final updatedWidget = widget.copyWith(
                optionConfig: config['optionConfig'] as Map<String, dynamic>?,
                optionType: config['optionType']?.toString(),
                typeOption: config['typeOption']?.toString(),
                readonly: config['readonly'] as bool?,
                validations: config['validations'] as Map<String, dynamic>?,
                label: config['label']?.toString(),
                displayName: config['displayName']?.toString(),
                type: _parseFormItemType(config['switchFieldType']),
              );

              final index = _widgets.indexOf(widget);
              if (index != -1) {
                _widgets[index] = updatedWidget;
              }

              // Fetch dropdown options if needed
              if (!skipFetchDropdown) {
                fetchDropdownOptions(updatedWidget);
              }
            }
          }

          // Update form state and call setFieldValue for values
          results.forEach((key, value) {
            if (!['ten_truong', 'bat_buoc', 'chi_duoc_doc', 'huong_dan', 'hien_thi'].contains(key)) {
              // Call setFieldValue if the value is not null and different from current value
              if (value != null && _formState[key] != value) {
              _formState[widget.name.toString()] = value;
                setFieldValue(widget.name.toString(), value, skipEvaluateEvent: true);
              }
            }
          });
        }
      }
    }

    if (hasChanges) {
      // Batch the state update
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  @override
  void dispose() {
    if (!_isDisposed) {
      _isDisposed = true;

      // Cancel all pending timers first
      _fetchTimers.values.forEach((timer) => timer.cancel());
      _fetchTimers.clear();

      // Clear all state before disposing controllers
      _formState.clear();
      _saveFormState.clear();
      _uploadedFiles.clear();
      _errors.clear();
      _eventResults.clear();
      _widgetProperties.clear();
      _lastDropdownRequests.clear();
      _widgets.clear();

      // Only reset dropdown data if the bloc is still valid
      try {
        _dropdownBloc.add(ResetDropdownData());
      } catch (e) {
        // developer.log('Error during dropdown reset: $e',
        //     name: 'FormStateManager');
      }

      // Clear and dispose all controllers
      _controllers.values.forEach((controller) {
        try {
          controller.clear();
          controller.dispose();
        } catch (e) {
          // developer.log('Error disposing controller: $e',
          //     name: 'FormStateManager');
        }
      });
      _controllers.clear();

      // Dispose all focus nodes
      _focusNodes.values.forEach((node) {
        try {
          node.dispose();
        } catch (e) {
          // developer.log('Error disposing focus node: $e',
          //     name: 'FormStateManager');
        }
      });
      _focusNodes.clear();

      super.dispose();
    }
  }

  /// Tìm kiếm widget theo tên
  /// [name] - Tên của widget cần tìm
  /// [parentName] - Tên của widget cha (nếu có)
  /// [caseSensitive] - Có phân biệt chữ hoa chữ thường không
  FormItemInfo? findWidgetByName(String name, {String? parentName, bool caseSensitive = false}) {
    final compareName = caseSensitive ? name : name.toLowerCase();

    return _widgets.firstWhere(
      (widget) {
        final widgetName = caseSensitive ? widget.name.toString() : widget.name.toString().toLowerCase();
        final matchesName = widgetName == compareName;
        final matchesParent = parentName == null || widget.parentName == parentName;
        return matchesName && matchesParent;
      },
      orElse: () => FormItemInfo(),
    );
  }

  // Lấy dữ liệu của dòng trong bảng theo parentName và rowIndex
  dynamic getTableRowDataByName(String tableName, String? parentName, {int? rowIndex}) {
    final tableData = _saveFormState[tableName]?['valueInfo']?['data'];
    if (tableData != null && rowIndex != null && rowIndex < tableData.length) {
      return tableData[rowIndex];
    }
    return null;
  }

  @override
  void notifyListeners() {
    if (_isDisposed) return;
    super.notifyListeners();
  }

  // Add new methods for handling uploaded files
  void setUploadedFiles(Map<String, dynamic> files, {int? rowIndex}) {
    final fileName = files['name']?.toString();
    // developer.log('setUploadedFiles: $files', name: 'setUploadedFiles');
    // developer.log('fileName: $fileName', name: 'setUploadedFiles');
    if (fileName != null) {
      // Find and remove existing file with same name
      _uploadedFiles.removeWhere((file) => file['name']?.toString() == fileName && file['rowIndex'] == rowIndex);
    }
    _uploadedFiles.add(files);
    notifyListeners();
  }

  List<Map<String, dynamic>> getUploadedFiles() {
    return _uploadedFiles.toList();
  }

  void clearUploadedFiles(String fieldName) {
    _uploadedFiles.remove(fieldName);
    notifyListeners();
  }

  void clearAllUploadedFiles() {
    _uploadedFiles.clear();
    notifyListeners();
  }

  String getMimeTypeFromFileName(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      default:
        return '';
    }
  }
}

FormItemType? _parseFormItemType(dynamic value) {
  if (value == null) return null;
  if (value is FormItemType) return value;
  if (value is String) {
    switch (value) {
      case 'text':
        return FormItemType.input;
      case 'textarea':
        return FormItemType.textArea;
      case 'checkbox':
        return FormItemType.checkbox;
      case 'radio':
        return FormItemType.radioButton;
      case 'select':
        return FormItemType.dropdown;
      case 'date':
        return FormItemType.datePicker;
      case 'splitter':
        return FormItemType.splitter;
      case 'row':
        return FormItemType.row;
      case 'column':
        return FormItemType.column;
      case 'step':
        return FormItemType.step;
      case 'tab':
        return FormItemType.tab;
      case 'file':
        return FormItemType.fileUpload;
      case 'table':
        return FormItemType.table;
      case 'matrix':
        return FormItemType.matrix;
      case 'divider':
        return FormItemType.divider;
      case 'label':
        return FormItemType.label;
      case 'url':
        return FormItemType.url;
      case 'number':
        return FormItemType.number;
      case 'currency':
        return FormItemType.currency;
      case 'formula':
        return FormItemType.formula;
      default:
        return null;
    }
  }
  return null;
}
