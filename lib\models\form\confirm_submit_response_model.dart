// api Nhấn nút Xác nhận đệ trình phiếu có mẫu trình ký hoặc nhấn nút Đệ trình
class ConfirmSubmitResponseModel {
  int? code;
  String? message;
  List<Data>? data;

  ConfirmSubmitResponseModel({this.code, this.message, this.data});

  ConfirmSubmitResponseModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  String? taskKey;
  String? taskName;
  String? formKey;
  int? printId;
  String? taskType;
  dynamic collection;
  String? assignee;
  dynamic candidateUsers;
  String? elementType;
  List<String>? listTaskAfter;
  List<String>? listTaskBefore;
  dynamic listVariable;
  bool? used;
  bool? multiInstance;

  Data(
      {this.taskKey,
        this.taskName,
        this.formKey,
        this.printId,
        this.taskType,
        this.collection,
        this.assignee,
        this.candidateUsers,
        this.elementType,
        this.listTaskAfter,
        this.listTaskBefore,
        this.listVariable,
        this.used,
        this.multiInstance});

  Data.fromJson(Map<String, dynamic> json) {
    taskKey = json['taskKey'];
    taskName = json['taskName'];
    formKey = json['formKey'];
    printId = json['printId'];
    taskType = json['taskType'];
    collection = json['collection'];
    assignee = json['assignee'];
    candidateUsers = json['candidateUsers'];
    elementType = json['elementType'];
    listTaskAfter = json['listTaskAfter']?.cast<String>();
    listTaskBefore = json['listTaskBefore']?.cast<String>();
    listVariable = json['listVariable'];
    used = json['used'];
    multiInstance = json['multiInstance'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['taskKey'] = this.taskKey;
    data['taskName'] = this.taskName;
    data['formKey'] = this.formKey;
    data['printId'] = this.printId;
    data['taskType'] = this.taskType;
    data['collection'] = this.collection;
    data['assignee'] = this.assignee;
    data['candidateUsers'] = this.candidateUsers;
    data['elementType'] = this.elementType;
    data['listTaskAfter'] = this.listTaskAfter;
    data['listTaskBefore'] = this.listTaskBefore;
    data['listVariable'] = this.listVariable;
    data['used'] = this.used;
    data['multiInstance'] = this.multiInstance;
    return data;
  }
}
