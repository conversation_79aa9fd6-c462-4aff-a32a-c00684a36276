class ConfigImg {
  static bool isHostApp = false; // Mặc định là chạy độc lập

  static String get urlSvg => !isHostApp ? 'lib/assets/svg' : 'packages/eapprove/lib/assets/svg';

  static String get urlImg => !isHostApp ? 'lib/assets/images' : 'packages/eapprove/lib/assets/images';

  static void setHostApp(bool value) {
    print('valueeee:: ${value}');
    isHostApp = value;
  }
}

class StringImage {
  static String ic_user = '${ConfigImg.urlSvg}/ic_user.svg';
  static String ic_arrow_left = '${ConfigImg.urlSvg}/ic_arrow_left.svg';
  static String ic_search = '${ConfigImg.urlSvg}/ic_search.svg';
  static String ic_filter = '${ConfigImg.urlSvg}/ic_filter.svg';
  static String ic_unfilter = '${ConfigImg.urlSvg}/ic_unfilter.svg';
  static String ic_calendar = '${ConfigImg.urlSvg}/ic_calendar.svg';
  static String ic_close = '${ConfigImg.urlSvg}/ic_close.svg';
  static String ic_upload = '${ConfigImg.urlSvg}/ic_upload.svg';
  static String ic_arrow_right = '${ConfigImg.urlSvg}/ic_arrow_right.svg';
  static String ic_camera = '${ConfigImg.urlSvg}/ic_camera.svg';
  static String ic_arrow_down = '${ConfigImg.urlSvg}/ic_arrow_down.svg';
  static String ic_arrow_up = '${ConfigImg.urlSvg}/ic_arrow_up.svg';
  static String ic_user_solid = '${ConfigImg.urlSvg}/ic_user_solid.svg';
  static String ic_red_trash = '${ConfigImg.urlSvg}/ic_red_trash.svg';
  static String ic_bag = '${ConfigImg.urlSvg}/ic_bag.svg';
  static String ic_pdf = '${ConfigImg.urlSvg}/ic_pdf.svg';
  static String ic_megaphone = '${ConfigImg.urlSvg}/ic_megaphone.svg';
  static String ic_message = '${ConfigImg.urlSvg}/ic_message.svg';
  static String ic_readAll = '${ConfigImg.urlSvg}/ic_readAll.svg';
  static String ic_filtered = '${ConfigImg.urlSvg}/ic_filtered.svg';
  static String ic_lock = '${ConfigImg.urlSvg}/lock.svg';
  static String ic_bell = '${ConfigImg.urlSvg}/bell.svg';
  static String ic_mail = '${ConfigImg.urlSvg}/mail.svg';
  static String ic_people = '${ConfigImg.urlSvg}/people.svg';
  static String ic_instructions = '${ConfigImg.urlSvg}/instructions.svg';
  static String ic_introduce = '${ConfigImg.urlSvg}/introduce.svg';
  static String ic_logout = '${ConfigImg.urlSvg}/ic_logout.svg';
  static String ic_renew = '${ConfigImg.urlSvg}/renew.svg';
  static String ic_exclamation = '${ConfigImg.urlSvg}/ic_exclamation.svg';
  static String ic_no_result = '${ConfigImg.urlSvg}/ic_no_result.svg';
  static String ic_arrow_left_2 = '${ConfigImg.urlSvg}/ic_arrow_left_2.svg';
  static String ic_black_close = '${ConfigImg.urlSvg}/ic_black_close.svg';
  static String service_icon = '${ConfigImg.urlSvg}/service_icon.svg';
  static String logout = '${ConfigImg.urlSvg}/logout.svg';

  static String user_profile = '${ConfigImg.urlSvg}/user-profile.svg';
  static String user = '${ConfigImg.urlSvg}/user.svg';

  static String home = '${ConfigImg.urlSvg}/home.svg';
  static String alarm_bell = '${ConfigImg.urlSvg}/alarm-bell.svg';
  static String ic_assign = '${ConfigImg.urlSvg}/ic_assign.svg';
  static String menu_fold = '${ConfigImg.urlSvg}/menu_fold.svg';
  static String avatar_default = '${ConfigImg.urlSvg}/avatar_default.svg';
  static String ic_home = '${ConfigImg.urlSvg}/ic_home.svg';
  static String ic_ticket_info = '${ConfigImg.urlSvg}/ic_ticket_info.svg';
  static String ic_ticket_comment = '${ConfigImg.urlSvg}/ic_ticket_comment.svg';
  static String ic_ticket_info_process = '${ConfigImg.urlSvg}/ic_ticket_info_process.svg';
  static String ic_ticket_input = '${ConfigImg.urlSvg}/ic_ticket_input.svg';
  static String ic_ticket_output = '${ConfigImg.urlSvg}/ic_ticket_output.svg';
  static String ic_ticket_message = '${ConfigImg.urlSvg}/ic_ticket_message.svg';
  static String ic_shared_user = '${ConfigImg.urlSvg}/ic_shared_user.svg';
  static String ic_file_edit = '${ConfigImg.urlSvg}/file_edit.svg';
  static String ic_share = '${ConfigImg.urlSvg}/ic_share.svg';
  static String ic_plus_circle = '${ConfigImg.urlSvg}/ic_plus_circle.svg';
  static String ic_start = '${ConfigImg.urlSvg}/ic_start.svg';
  static String ic_manager = '${ConfigImg.urlSvg}/ic_manager.svg';
  static String ic_destination = '${ConfigImg.urlSvg}/ic_destination.svg';
  static String ic_eye = '${ConfigImg.urlSvg}/ic_eye.svg';
  static String ic_download = '${ConfigImg.urlSvg}/ic_download.svg';
  static String ic_unlock = '${ConfigImg.urlSvg}/unlock-alt.svg';
  static String ic_globe = '${ConfigImg.urlSvg}/ic_globe.svg';
  static String ic_paper_clip = '${ConfigImg.urlSvg}/ic_paper_clip.svg';
  static String ic_delete = '${ConfigImg.urlSvg}/ic_delete.svg';
  static String ic_close_2 = '${ConfigImg.urlSvg}/close_button.svg';

  //png
  static String ic_ticket_group_EAP = '${ConfigImg.urlImg}/ic_ticket_group.png';
  static String ic_ticket_group_CRM = '${ConfigImg.urlImg}/ic_ticket_group_CRM.png';
  static String ic_ticket_group_iHRP = '${ConfigImg.urlImg}/ic_ticket_group_iHRP.png';
  static String ic_ticket_group_SAP = '${ConfigImg.urlImg}/ic_ticket_group_SAP.png';
  static String ic_ticket_group_CONS = '${ConfigImg.urlImg}/ic_ticket_group_CONS.png';
  static String ic_ticket_group_others = '${ConfigImg.urlImg}/ic_ticket_group_others.png';
  static String background = '${ConfigImg.urlImg}/background.png 14-28-28-520.png';
  static String dxg_bottom_bar = '${ConfigImg.urlImg}/dxg_bottom_bar.png';
  static String login_background = '${ConfigImg.urlImg}/login_background.png 14-28-46-600.png';
  static String logo_switch_account_1 = '${ConfigImg.urlImg}/logo_switchAccount1.png';
  static String logo_switch_account_2 = '${ConfigImg.urlImg}/logo_switchAccount2.png';
  static String logo_switch_account_3 = '${ConfigImg.urlImg}/logo_switchAccount3.png';
  static String logo = '${ConfigImg.urlImg}/logo.png 14-28-51-547.png';
  static String logo_tablet_small = '${ConfigImg.urlImg}/logo_tablet_small.png';
  static String logo_tablet_big = '${ConfigImg.urlImg}/logo_tablet_big.png';
  static String execution_icon = '${ConfigImg.urlImg}/execution_icon.png';
  static String signature_icon = '${ConfigImg.urlImg}/signature_icon.png';
  static String speech_bubble = '${ConfigImg.urlImg}/speech-bubble.png';
  static String ic_switchAccount = '${ConfigImg.urlImg}/ic_switchAccount.png';
  static String ic_setting = '${ConfigImg.urlImg}/ic_setting.png';
  static String ticket_info = '${ConfigImg.urlImg}/ticket_info.png';
  static String upload = '${ConfigImg.urlImg}/upload.png';
}
