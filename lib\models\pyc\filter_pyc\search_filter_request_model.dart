class RequestListService {
  final List<int>? serviceType;
  final int? limit;
  final int? size;
  final int? page;
  final String? sortBy;
  final String? sortType;
  final List<String>? sortCompanyCode;
  final bool? admin;

  RequestListService({
    this.serviceType,
    this.limit,
    this.size,
    this.page,
    this.sortBy,
    this.sortType,
    this.sortCompanyCode,
    this.admin,
  });
  RequestListService copyWith({
    List<int>? serviceType,
    int? limit,
    int? size,
    int? page,
    String? sortBy,
    String? sortType,
  }) {
    return RequestListService(
      serviceType: serviceType ?? this.serviceType,
      limit: limit ?? this.limit,
      size: size ?? this.size,
      page: page ?? this.page,
      sortBy: sortBy ?? this.sortBy,
      sortType: sortType ?? this.sortType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceType': serviceType,
      'limit': limit,
      'size': size,
      'page': page,
      'sortBy': sortBy,
      'sortType': sortType,
      'sortCompanyCode': sortCompanyCode,
      'admin': admin,
    };
  }

  static RequestListService fromJson(Map<String, dynamic> json) {
    return RequestListService(
      serviceType: json['serviceType'] != null ? List<int>.from(json['serviceType']) : null,
      limit: json['limit'],
      size: json['size'],
      page: json['page'],
      sortBy: json['sortBy'],
      sortType: json['sortType'],
      sortCompanyCode: json['sortCompanyCode'] != null ? List<String>.from(json['sortCompanyCode']) : null,
      admin: json['admin'],
    );
  }
}

class ListAssigneeRequestModel {
  final bool isLoadAll;
  ListAssigneeRequestModel({required this.isLoadAll});

  Map<String, dynamic> toJson() {
    return {
      'isLoadAll': isLoadAll,
    };
  }
}

class ChartFilterRequestModel {
  final List<dynamic>? orgchart;
  final String? infoType;
  final List<dynamic>? condition;

  ChartFilterRequestModel({
    this.orgchart,
    this.infoType,
    this.condition,
  });

  factory ChartFilterRequestModel.fromJson(Map<String, dynamic> json) {
    return ChartFilterRequestModel(
      orgchart: json['orgchart'] as List<dynamic>?,
      infoType: json['infoType'] as String?,
      condition: json['condition'] as List<dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orgchart': orgchart,
      'infoType': infoType,
      'condition': condition,
    };
  }
}

class ListTaskDefKeyRequestModel {
  final List<int>? listServiceId;

  ListTaskDefKeyRequestModel(this.listServiceId);

  Map<String, dynamic> toJson() {
    return {
      'listServiceId': listServiceId,
    };
  }
}

class ListCreatedUserRequestModel {
  final bool isLoadAll;
  final List<int>? orgchart;

  ListCreatedUserRequestModel({required this.isLoadAll, this.orgchart});
  factory ListCreatedUserRequestModel.fromJson(Map<String, dynamic> json) {
    return ListCreatedUserRequestModel(
      isLoadAll: json['isLoadAll'],
      orgchart: json['orgchart'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'isLoadAll': isLoadAll,
      'orgchart': orgchart,
    };
  }
}

class ListPriorityRequestModel {
  final String? search;
  final String sortBy;
  final String sortType;
  final int? limit;
  final int? size;
  final int? page;

  ListPriorityRequestModel(
      {this.search, required this.sortBy, required this.sortType, this.limit, this.size, this.page});

  Map<String, dynamic> toJson() {
    return {
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'limit': limit,
      'size': size,
      'page': page,
    };
  }
}

class SearchFilterRequestModel {
  String? search;
  int? page;
  int? limit;
  String? sortBy;
  String? sortType;
  String? type;
  List<String>? listAssignee;
  String? ticketTitle;
  List<String>? listApprovalByStatus;
  List<String>? listAssignedStatus;
  List<String>? listTaskStatus;
  List<String>? listAdditionalStatus;
  List<String>? listCreatedUser;
  List<String>? listTaskDefKey;
  String? dateType;
  List<String>? listAssignStatus;
  bool? hasDiscussion;
  List<String>? listTicketStatus;
  bool? isAssistant;
  String? toDate;
  List<int>? listService;
  bool? isNotification;
  String? fromDate;
  List<int>? listTaskPriority;
  bool? isDiscussion;
  List<int>? listChartNodeId;
  List<int>? listChartId;
  String? shareStatus;

  SearchFilterRequestModel({
    this.search,
    this.page,
    this.limit,
    this.sortBy,
    this.sortType,
    this.type,
    this.listAssignee,
    this.ticketTitle,
    this.listApprovalByStatus,
    this.listAssignedStatus,
    this.listTaskStatus,
    this.listAdditionalStatus,
    this.listCreatedUser,
    this.listTaskDefKey,
    this.dateType,
    this.listAssignStatus,
    this.hasDiscussion,
    this.listTicketStatus,
    this.isAssistant,
    this.toDate,
    this.listService,
    this.isNotification,
    this.fromDate,
    this.listTaskPriority,
    this.isDiscussion,
    this.listChartNodeId,
    this.listChartId,
    this.shareStatus,
  });
  factory SearchFilterRequestModel.fromJson(Map<String, dynamic> json) {
    return SearchFilterRequestModel(
      search: json['search'] as String?,
      page: json['page'] as int?,
      limit: json['limit'] as int?,
      sortBy: json['sortBy'] as String?,
      sortType: json['sortType'] as String?,
      type: json['type'] as String?,
      listAssignee: (json['listAssignee'] as List?)?.map((item) => item as String).toList(),
      ticketTitle: json['ticketTitle'] as String?,
      listApprovalByStatus: (json['listApprovalByStatus'] as List?)?.map((item) => item as String).toList(),
      listAssignedStatus: (json['listAssignedStatus'] as List?)?.map((item) => item as String).toList(),
      listTaskStatus: (json['listTaskStatus'] as List?)?.map((item) => item as String).toList(),
      listAdditionalStatus: (json['listAdditionalStatus'] as List?)?.map((item) => item as String).toList(),
      listCreatedUser: (json['listCreatedUser'] as List?)?.map((item) => item as String).toList(),
      listTaskDefKey: (json['listTaskDefKey'] as List?)?.map((item) => item as String).toList(),
      dateType: json['dateType'] as String?,
      listAssignStatus: (json['listAssignStatus'] as List?)?.map((item) => item as String).toList(),
      hasDiscussion: json['hasDiscussion'] as bool?,
      listTicketStatus: (json['listTicketStatus'] as List?)?.map((item) => item as String).toList(),
      isAssistant: json['isAssistant'] as bool?,
      toDate: json['toDate'] as String?,
      listService: (json['listService'] as List?)?.map((item) => item as int).toList(),
      isNotification: json['isNotification'] as bool?,
      fromDate: json['fromDate'] as String?,
      listTaskPriority: (json['listTaskPriority'] as List?)?.map((item) => item as int).toList(),
      isDiscussion: json['isDiscussion'] as bool?,
      listChartNodeId: (json['listChartNodeId'] as List?)?.map((item) => item as int).toList(),
      listChartId: (json['listChartId'] as List?)?.map((item) => item as int).toList(),
      shareStatus: json['shareStatus'] as String?,
    );
  }
  factory SearchFilterRequestModel.fromMap(Map<String, dynamic> map) {
    return SearchFilterRequestModel(
      search: "",
      page: 1,
      limit: 10,
      sortBy: "id",
      sortType: "DESC",
      ticketTitle: map['ticketTitle'],
      listTicketStatus: (map['listTicketStatus'] as List?)?.cast<String>(),
      listService: map['listService'] is List
          ? (map['listService'] as List).map((e) => int.tryParse(e.toString())).whereType<int>().toList()
          : null,
      listTaskStatus: (map['listTaskStatus'] as List?)?.cast<String>(),
      listAssignee:
          map['listAssignee'] is List ? (map['listAssignee'] as List).map((e) => e.toString()).toList() : null,
      shareStatus: map['shareStatus'],
      listChartId: map['listChartId'] is List
          ? (map['listChartId'] as List).map((e) => int.tryParse(e.toString())).whereType<int>().toList()
          : null,
      listChartNodeId: map['listChartNodeId'] is List
          ? (map['listChartNodeId'] as List).map((e) => int.tryParse(e.toString())).whereType<int>().toList()
          : null,
      listCreatedUser:
          map['listCreatedUser'] is List ? (map['listCreatedUser'] as List).map((e) => e.toString()).toList() : null,
      listTaskPriority: map['listTaskPriority'] is List
          ? (map['listTaskPriority'] as List).map((e) => int.tryParse(e.toString())).whereType<int>().toList()
          : null,
      dateType: map['dateType'],
      fromDate: map['fromDate'] as String?,
      toDate: map['toDate'] as String?,
      hasDiscussion: map['hasDiscussion'] == true,
      isDiscussion: map['isDiscussion'] == true,
      isAssistant: map['isAssistant'] == true,
      isNotification: map['isNotification'] == true,
      listTaskDefKey:
          map['listTaskDefKey'] is List ? (map['listTaskDefKey'] as List).map((e) => e.toString()).toList() : null,
      listAssignStatus:
          map['listAssignStatus'] is List ? (map['listAssignStatus'] as List).map((e) => e.toString()).toList() : null,
      listAssignedStatus: map['listAssignedStatus'] is List
          ? (map['listAssignedStatus'] as List).map((e) => e.toString()).toList()
          : null,
      listApprovalByStatus: map['listApprovalByStatus'] != null ? [map['listApprovalByStatus']] : null,
      listAdditionalStatus: map['listAdditionalStatus'] is List
          ? (map['listAdditionalStatus'] as List).map((e) => e.toString()).toList()
          : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'search': search,
      'page': page,
      'limit': limit,
      'sortBy': sortBy,
      'sortType': sortType,
      'type': type,
      'listAssignee': listAssignee,
      'ticketTitle': ticketTitle,
      'listApprovalByStatus': listApprovalByStatus,
      'listAssignedStatus': listAssignedStatus,
      'listTaskStatus': listTaskStatus,
      'listAdditionalStatus': listAdditionalStatus,
      'listCreatedUser': listCreatedUser,
      'listTaskDefKey': listTaskDefKey,
      'dateType': dateType,
      'listAssignStatus': listAssignStatus,
      'hasDiscussion': hasDiscussion,
      'listTicketStatus': listTicketStatus,
      'isAssistant': isAssistant,
      'toDate': toDate,
      'listService': listService,
      'isNotification': isNotification,
      'fromDate': fromDate,
      'listTaskPriority': listTaskPriority,
      'isDiscussion': isDiscussion,
      'listChartNodeId': listChartNodeId,
      'listChartId': listChartId,
      'shareStatus': shareStatus,
    };
  }

  void removeNull() {
    if (listAssignee?.isEmpty ?? true) listAssignee = null;
    if (listApprovalByStatus?.isEmpty ?? true) listApprovalByStatus = null;
    if (listAssignedStatus?.isEmpty ?? true) listAssignedStatus = null;
    if (listTaskStatus?.isEmpty ?? true) listTaskStatus = null;
    if (listAdditionalStatus?.isEmpty ?? true) listAdditionalStatus = null;
    if (listCreatedUser?.isEmpty ?? true) listCreatedUser = null;
    if (listTaskDefKey?.isEmpty ?? true) listTaskDefKey = null;
    if (listAssignStatus?.isEmpty ?? true) listAssignStatus = null;
    if (listTicketStatus?.isEmpty ?? true) listTicketStatus = null;
    if (listService?.isEmpty ?? true) listService = null;
    if (listTaskPriority?.isEmpty ?? true) listTaskPriority = null;
    if (listChartNodeId?.isEmpty ?? true) listChartNodeId = null;
    if (listChartId?.isEmpty ?? true) listChartId = null;

    if (ticketTitle?.isEmpty ?? true) ticketTitle = null;
    if (dateType?.isEmpty ?? true) dateType = null;
    if (toDate?.isEmpty ?? true) toDate = null;
    if (fromDate?.isEmpty ?? true) fromDate = null;
    if (shareStatus?.isEmpty ?? true) shareStatus = null;
  }
}
