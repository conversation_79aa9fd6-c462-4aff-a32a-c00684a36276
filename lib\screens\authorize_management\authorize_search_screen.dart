import 'dart:async';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_bloc.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_event.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_state.dart';
import 'package:eapprove/common/search_history_manager.dart';
import 'package:eapprove/models/authorize_management/authorize_management_request_model.dart';
import 'package:eapprove/screens/authorize_management/widgets/authorize_card.dart';
import 'package:eapprove/screens/common/search_bar.dart';
import 'package:eapprove/screens/common/search_history.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';

class UyQuyenSearchScreen extends StatefulWidget {
  const UyQuyenSearchScreen({super.key});

  @override
  State<UyQuyenSearchScreen> createState() => _UyQuyenSearchScreenState();
}

class _UyQuyenSearchScreenState extends State<UyQuyenSearchScreen> {
  String searchQuery = '';
  List<String> searchHistory = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = false;
  Timer? _debounce; // Biến Timer để debounce
  Box box = Hive.box('authentication');

  @override
  void initState() {
    super.initState();

    // Đặt màu StatusBar thành đen và đè lên cài đặt trước đó
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // Nền StatusBar trong suốt
        statusBarIconBrightness: Brightness.dark, // Icon StatusBar màu đen
        statusBarBrightness: Brightness.light, // Dành cho iOS
      ));
    });

    _loadSearchHistory();
  }

  @override
  void dispose() {
    _debounce?.cancel(); // Hủy debounce khi dispose
    // context.read<AuthorizeManagementBloc>().add(ClearAuthorizeListFilter());
    // Khôi phục trạng thái mặc định của StatusBar
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // Icon StatusBar màu trắng
        statusBarBrightness: Brightness.dark, // Dành cho iOS
      ),
    );
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSearchHistory() async {
    final history = await SearchHistoryManager.getSearchHistory();
    debugPrint('Search history: $history');
    setState(() {
      searchHistory = history;
    });
  }

  void _onSearch(String query) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel(); // Hủy debounce trước đó
    }

    _debounce = Timer(const Duration(milliseconds: 500), () {
      // Thực hiện tìm kiếm sau 500ms
      setState(() {
        searchQuery = query.toLowerCase();
      });
      final processedQuery =
          query.contains('-') ? query.split('-')[1].trim() : query.trim();
      debugPrint('Search query: $processedQuery');
      final username = box.get('username', defaultValue: '');

      if (query.isEmpty) {
        context.read<AuthorizeManagementBloc>().add(FetchAuthorizeListFilter(
            requestModel: AuthorizeManagementRequestModel(
                search: "",
                sortBy: "createdDate",
                sortType: "DESC",
                status: [0, -1, -2, -3, -4],
                limit: 100,
                page: 100,
                userLogin: username)));
      } else {
        context.read<AuthorizeManagementBloc>().add(FetchAuthorizeListFilter(
            requestModel: AuthorizeManagementRequestModel(
                search: processedQuery,
                sortBy: "createdDate",
                sortType: "DESC",
                status: [0, -1, -2, -3, -4],
                limit: 50,
                page: 1,
                userLogin: username)));
      }
    });
  }

  Future<void> _onSearchSubmitted(String query) async {
    if (query.trim().isNotEmpty) {
      setState(() {
        _isLoading = true;
      });
      final username = box.get('username', defaultValue: '');

      try {
        final processedQuery =
            query.contains('-') ? query.split('-')[1].trim() : query.trim();
        debugPrint('Processed query: $processedQuery');
        final requestModel = AuthorizeManagementRequestModel(
            search: processedQuery,
            sortBy: "createdDate",
            sortType: "DESC",
            status: [0, -1, -2, -3, -4],
            limit: 50,
            page: 1,
            userLogin: username);

        context
            .read<AuthorizeManagementBloc>()
            .add(FetchAuthorizeListFilter(requestModel: requestModel));

        SearchHistoryManager.addSearchTerm(processedQuery).then((_) {
          _loadSearchHistory();
          setState(() {
            searchQuery = processedQuery.toLowerCase();
          });
        });
      } catch (e) {
        debugPrint('Error searching: $e');
        // Show error message to user if needed
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onSearchHistorySelected(String term) {
    _searchController.text = term;
    setState(() {
      searchQuery = term.toLowerCase();
    });
    _onSearchSubmitted(term);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthorizeManagementBloc, AuthorizeManagementState>(
      listener: (context, state) {
        if (state.status == AuthorizeManagementStatus.loading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state.status == AuthorizeManagementStatus.loaded) {
          setState(() {
            _isLoading = false;
          });
        } else if (state.status == AuthorizeManagementStatus.error) {
          setState(() {
            _isLoading = false;
          });
          // Show error message if needed
          debugPrint('Error: ${state.errorMessage}');
        }
      },
      child: SafeArea(
        child: Scaffold(
          backgroundColor: getColorSkin().white,
          body: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                child: Row(
                  children: [
                    IconButton(
                      icon: SvgPicture.asset(StringImage.ic_arrow_left,
                          width: 9.w, height: 18.h),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          searchQuery = '';
                        });
                        Navigator.pop(context);
                      },
                    ),
                    Expanded(
                      child: CustomSearchBar(
                        controller: _searchController,
                        defaultText: "Mã tờ trình, tên ủy quyền",
                        onSubmitted: _onSearchSubmitted,
                        onChanged: _onSearch, // Gọi debounce khi nhập
                      ),
                    ),
                  ],
                ),
              ),
              _isLoading
                  ? Center(
                      child: AppConstraint.buildLoading(context),
                    )
                  : Expanded(
                      child: BlocBuilder<AuthorizeManagementBloc,
                          AuthorizeManagementState>(
                        builder: (context, state) {
                          if (searchQuery.isEmpty) {
                            return SearchHistory(
                              searchHistory: searchHistory,
                              onSearchSelected: _onSearchHistorySelected,
                            );
                          }

                          if (state.status ==
                              AuthorizeManagementStatus.initial) {
                            return const Center(
                              child: Text('Nhập từ khóa để tìm kiếm'),
                            );
                          }

                          if (state.status ==
                              AuthorizeManagementStatus.loading) {
                            return Center(
                              child: AppConstraint.buildLoading(context),
                            );
                          }

                          if (state.status == AuthorizeManagementStatus.error) {
                            return Center(
                              child:
                                  Text('Có lỗi xảy ra: ${state.errorMessage}'),
                            );
                          }

                          final items = state.authorizeManagementListFilter;
                          if (items.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    StringImage.ic_no_result,
                                    width: 120.w,
                                    height: 120.h,
                                  ),
                                  SizedBox(height: 16.h),
                                  Text(
                                    "Không có phiếu nào",
                                    style: getTypoSkin().medium16.copyWith(
                                          color: getColorSkin().ink1,
                                        ),
                                  ),
                                ],
                              ),
                            );
                          }

                          return Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: ListView.separated(
                              padding: EdgeInsets.symmetric(vertical: 0.h),
                              itemCount: items.length,
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: 8.h),
                              itemBuilder: (context, index) => Container(
                                decoration: BoxDecoration(
                                  color: getColorSkin().white,
                                  borderRadius: BorderRadius.circular(8.r),
                                  border: Border.all(
                                    color: getColorSkin().ink5,
                                    width: 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      offset: const Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: PaymentRequestCard(
                                  request: items[index],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
