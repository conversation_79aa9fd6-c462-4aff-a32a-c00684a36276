import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:eapprove/screens/common/error_dialog.dart';
import 'package:eapprove/screens/common/network_error.dart';
import 'package:eapprove/main.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class ErrorHandler {
  static Future<bool> checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// Handles all types of errors and shows appropriate UI feedback
  static Future<void> handleError(
    BuildContext context, 
    dynamic error, 
    VoidCallback onRetry,
  ) async {
    await Future.delayed(Duration.zero);
    if (!context.mounted) return;

    // Handle network errors
    if (error is NoInternetException || 
        error is SocketException || 
        error is HttpException || 
        error is WebSocketException) {
      showNetworkErrorDialog(
        context: context,
        onRetry: onRetry,
      );
      return;
    }

    // Handle other errors
    showErrorDialog(
      context: context,
      onRetry: onRetry,
    );
  }

  /// Shows a snackbar with error message
  static void showErrorSnackBar(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: getColorSkin().red,
      ),
    );
  }

  /// Shows a success snackbar
  static void showSuccessSnackBar(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: getColorSkin().primaryBlue,
      ),
    );
  }

  /// Wraps a widget with error handling UI
  static Widget wrapWithErrorHandling({
    required Widget child,
    required bool hasError,
    required VoidCallback onRetry,
    String? title,
    String? message,
  }) {
    if (hasError) {
      return ErrorWrapper(
        hasError: true,
        onRetry: onRetry,
        title: title,
        message: message,
        child: child,
      );
    }
    return child;
  }

  /// Wraps a widget with network error handling UI
  static Widget wrapWithNetworkErrorHandling({
    required Widget child,
    required bool isNetworkError,
    required VoidCallback onRetry,
    String? title,
    String? message,
  }) {
    if (isNetworkError) {
      return NetworkErrorWrapper(
        isNetworkError: true,
        onRetry: onRetry,
        title: title,
        message: message,
        child: child,
      );
    }
    return child;
  }

  /// Handles API response errors
  static void handleApiError(int statusCode, BuildContext context) {
    final Map<int, String> errorMessages = {
      400: 'Yêu cầu không hợp lệ',
      401: 'Phiên đăng nhập đã hết hạn, vui lòng đăng nhập lại',
      403: 'Bạn không có quyền thực hiện hành động này',
      404: 'Không tìm thấy tài nguyên',
      500: 'Lỗi hệ thống, vui lòng thử lại sau',
      20007014: 'Tài khoản không được liên kết!',
      20007026: 'Tài khoản chưa đủ thông tin!',
    };

    final message = errorMessages[statusCode] ?? 'Đã xảy ra lỗi, vui lòng thử lại sau';
    showErrorSnackBar(context, message);
  }

  /// Gets the appropriate error message for different error types
  static String getErrorMessage(dynamic error) {
    if (error is NoInternetException || 
        error is SocketException || 
        error is HttpException || 
        error is WebSocketException) {
      return 'Vui lòng kiểm tra kết nối Internet';
    }

    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    }

    return 'Đã xảy ra lỗi, vui lòng thử lại sau';
  }
} 