import 'package:eapprove/screens/payment_step/payment_main_screen.dart';
import 'package:eapprove/screens/payment_step/widgets/payment_step_2.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/screens/payment_proposal/widgets/approver_card.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class PaymentStep1Screen extends BasePaymentStepScreen {
  const PaymentStep1Screen({super.key}) : super(
    currentStep: 1,
    totalSteps: 4,
    stepTitle: 'Bước 1',
    stepDescription: 'Nhập thông tin đề nghị thanh toán',
  );

  @override
  State<PaymentStep1Screen> createState() => _PaymentStep1ScreenState();
}

class _PaymentStep1ScreenState extends BasePaymentStepState<PaymentStep1Screen> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget buildStepContent() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // const InformationSubmitterCard(),
          SizedBox(height: 16.h),
          // const PaymentProposalInput(),
          SizedBox(height: 16.h),
          const ApproverCard(),
        ],
      ),
    );
  }

  @override
  void handleNextStep() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const PaymentStep2Screen(),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vui lòng kiểm tra lại thông tin')),
      );
    }
  }
}
