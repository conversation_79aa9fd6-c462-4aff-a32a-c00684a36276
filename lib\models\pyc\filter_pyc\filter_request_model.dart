class FilterRequestModel {
  final String? search;
  final String? type;

  FilterRequestModel({this.search, this.type});
  
  Map<String, dynamic> toJson() {
    return {
      'search': search,
      'type': type,
    };
  }
}
class FilterTicketRequestModel {
  final String? search;
  final int? page;
  final int? limit;
  final String? sortBy;
  final String? sortType;
  final int? filterId;
  final String? type;

  FilterTicketRequestModel({
    this.search,
    this.page,
    this.limit,
    this.sortBy,
    this.sortType,
    this.filterId,
    this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'search': search,
      'page': page,
      'limit': limit,
      'sortBy': sortBy,
      'sortType': sortType,
      'filterId': filterId,
      'type': type,
    };
  }

  factory FilterTicketRequestModel.fromJson(Map<String, dynamic> json) {
    return FilterTicketRequestModel(
      search: json['search'],
      page: json['page'],
      limit: json['limit'],
      sortBy: json['sortBy'],
      sortType: json['sortType'],
      filterId: json['filterId'],
      type: json['type'],
    );
  }
}