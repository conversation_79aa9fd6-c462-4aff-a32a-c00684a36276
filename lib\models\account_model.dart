class AccountModel {
  final String image;
  final String name;
  final String id;
  final String email;
  final String phone;
  final String company;
  final String position;
  final String role;

  AccountModel({
    required this.image,
    required this.name,
    required this.id,
    required this.email,
    required this.phone,
    required this.company,
    required this.position,
    required this.role,
  });

  factory AccountModel.fromMap(Map<String, String> map) {
    return AccountModel(
      image: map['image'] ?? '',
      name: map['name'] ?? '',
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      company: map['company'] ?? '',
      position: map['position'] ?? '',
      role: map['role'] ?? '',
    );
  }
}
