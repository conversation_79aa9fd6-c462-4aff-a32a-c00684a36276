import 'package:equatable/equatable.dart';

abstract class ConsultationOpinionEvent extends Equatable {
  const ConsultationOpinionEvent();

  @override
  List<Object?> get props => [];
}

class GetConsultationOpinions extends ConsultationOpinionEvent {
  final String procInstId;
  final int ticketId;
  final bool isAdditionalRequest;

  const GetConsultationOpinions({
    required this.procInstId,
    required this.ticketId,
    this.isAdditionalRequest = false,
  });

  @override
  List<Object?> get props => [procInstId, ticketId, isAdditionalRequest];
}

class AddConsultationOpinion extends ConsultationOpinionEvent {
  final String procInstId;
  final int ticketId;
  final String content;
  final bool isPrivate;
  final List<Map<String, dynamic>>? files;
  final bool isAdditionalRequest;
  final bool isCreateUserAdditionalRequest;
  final int? discusId;
  final List<int>? idFilesDelete;
  final int? groupId;

  const AddConsultationOpinion({
    required this.procInstId,
    required this.ticketId,
    required this.content,
    required this.isPrivate,
    this.files,
    this.isAdditionalRequest = false,
    this.isCreateUserAdditionalRequest = false,
    this.discusId,
    this.idFilesDelete,
    this.groupId,
  });

  @override
  List<Object?> get props => [procInstId, ticketId, content, isPrivate, files, isAdditionalRequest, isCreateUserAdditionalRequest, discusId, idFilesDelete, groupId];
}

class DeleteConsultationOpinion extends ConsultationOpinionEvent {
  final int discusId;
  final String procInstId;
  final int ticketId;

  const DeleteConsultationOpinion({
    required this.discusId,
    required this.procInstId,
    required this.ticketId,
  });

  @override
  List<Object?> get props => [discusId, procInstId, ticketId];
}

class AddConsultationOpinionWithFile extends ConsultationOpinionEvent {
  final String procInstId;
  final String ticketId;
  final int intTicketId;
  final int ticketIntId;
  final String content;
  final bool isPrivate;
  final String filePath;
  final bool isAdditionalRequest;
  final bool isCreateUserAdditionalRequest;

  const AddConsultationOpinionWithFile({
    required this.procInstId,
    required this.ticketId,
    required this.intTicketId,
    required this.ticketIntId,
    required this.content,
    required this.isPrivate,
    required this.filePath,
    this.isAdditionalRequest = false,
    this.isCreateUserAdditionalRequest = false,
  });

  @override
  List<Object?> get props => [
        procInstId,
        ticketId,
        intTicketId,
        ticketIntId,
        content,
        isPrivate,
        filePath,
        isAdditionalRequest,
        isCreateUserAdditionalRequest,
      ];
}

class AddConsultationOpinionWithMultipleFiles extends ConsultationOpinionEvent {
  final String procInstId;
  final String ticketId;
  final int intTicketId;
  final int ticketIntId;
  final String content;
  final bool isPrivate;
  final List<String> filePaths;
  final bool isAdditionalRequest;
  final bool isCreateUserAdditionalRequest;

  const AddConsultationOpinionWithMultipleFiles({
    required this.procInstId,
    required this.ticketId,
    required this.intTicketId,
    required this.ticketIntId,
    required this.content,
    required this.isPrivate,
    required this.filePaths,
    this.isAdditionalRequest = false,
    this.isCreateUserAdditionalRequest = false,
  });

  @override
  List<Object?> get props => [
        procInstId,
        ticketId,
        intTicketId,
        ticketIntId,
        content,
        isPrivate,
        filePaths,
        isAdditionalRequest,
        isCreateUserAdditionalRequest,
      ];
}
