class ChartModel {
  final int? id;
  final String? code;
  final String? companyCode;
  final String? name;
  final String? shortName;
  final String? type;
  final String? description;
  final String? status;
  final int? userTotal;
  final int? linkTotal;
  final String? version;
  final String? createdDate;
  final String? createdUser;
  final String? modifiedDate;
  final String? modifiedUser;
  final List<ChartSharedUserDto>? chartSharedUserDtoList;
  final List<dynamic>? chartNodeDtoList;
  final String? userInfoName;
  final String? chartLinkName;
  final String? errorMessage;
  final String? applyFor;

  ChartModel({
    this.id,
    this.code,
    this.companyCode,
    this.name,
    this.shortName,
    this.type,
    this.description,
    this.status,
    this.userTotal,
    this.linkTotal,
    this.version,
    this.createdDate,
    this.createdUser,
    this.modifiedDate,
    this.modifiedUser,
    this.chartSharedUserDtoList,
    this.chartNodeDtoList,
    this.userInfoName,
    this.chartLinkName,
    this.errorMessage,
    this.applyFor,
  });

  factory ChartModel.fromJson(Map<String, dynamic> json) {
    return ChartModel(
      id: json['id'],
      code: json['code'],
      companyCode: json['companyCode'],
      name: json['name'],
      shortName: json['shortName'],
      type: json['type'],
      description: json['description'],
      status: json['status'],
      userTotal: json['userTotal'],
      linkTotal: json['linkTotal'],
      version: json['version'],
      createdDate: json['createdDate'],
      createdUser: json['createdUser'],
      modifiedDate: json['modifiedDate'],
      modifiedUser: json['modifiedUser'],
      chartSharedUserDtoList: (json['chartSharedUserDtoList'] as List?)
          ?.map((e) => ChartSharedUserDto.fromJson(e))
          .toList(),
      chartNodeDtoList: json['chartNodeDtoList'],
      userInfoName: json['userInfoName'],
      chartLinkName: json['chartLinkName'],
      errorMessage: json['errorMessage'],
      applyFor: json['applyFor'],
    );
  }
}

class ChartSharedUserDto {
  final int? id;
  final int? chartId;
  final String? chartName;
  final String? name;
  final String? userEmail;

  ChartSharedUserDto({
    this.id,
    this.chartId,
    this.chartName,
    this.name,
    this.userEmail,
  });

  factory ChartSharedUserDto.fromJson(Map<String, dynamic> json) {
    return ChartSharedUserDto(
      id: json['id'],
      chartId: json['chartId'],
      chartName: json['chartName'],
      name: json['name'],
      userEmail: json['userEmail'],
    );
  }
}

class ChartResponse {
  final int code;
  final String message;
  final List<ChartModel> data;

  ChartResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory ChartResponse.fromJson(Map<String, dynamic> json) {
    return ChartResponse(
      code: json['code'],
      message: json['message'],
      data: (json['data'] as List)
          .map((e) => ChartModel.fromJson(e))
          .toList(),
    );
  }
} 