import 'dart:developer';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_chung_response_model.dart';
import 'package:eapprove/models/handle_ticket/thong_tin_dau_vao_response_model.dart';
import 'package:eapprove/screens/pyc/overview_ticket_board_screen.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'dart:developer' as developer;

import 'package:eapprove/screens/payment_step/widgets/bottom_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'handle_modal.dart';

class PdfViewerPage extends StatefulWidget {
  final ThongTinDauVaoResponseModel? thongTinDauVao;
  final String pdfName;
  final String? pdfUrl;
  final String? taskIdValue;
  final int initialPage;
  final String source;
  final String? ticketStatus;
  final String? title;
  final dynamic ticket;
  final ThongTinChungModel? data;
  final int? processType;
  final String? nodeType;
  final String? assignee;
  final Function(String nodeId, String nodeType)? onWorkflowNodeSelected;

  const PdfViewerPage(
      {super.key,
      required this.pdfName,
      this.thongTinDauVao,
      this.pdfUrl,
      this.taskIdValue,
      this.initialPage = 0,
      this.source = 'create',
      this.nodeType,
      this.ticketStatus,
      this.ticket,
      this.title,
      this.data,
      this.processType,
      this.assignee,
      this.onWorkflowNodeSelected});

  @override
  State<PdfViewerPage> createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> with TickerProviderStateMixin {
  String? localPdfPath;
  bool isLoading = true;
  int? totalPages;
  int currentPage = 0;
  String errorMessage = '';
  PDFViewController? pdfController;
  late AnimationController _arrowController;
  bool _showArrowButton = true;
  bool _showModal = false;
  bool _canShowHandleModal = false;
  int _currentModalTab = 0;
  List<String> _modalTabNames = [];
  VoidCallback? _modalSaveDraftAction;
  VoidCallback? _modalPrimaryAction;
  bool _showModalSaveDraft = false;
  bool _isProcessing = false;

  @override
  void initState() {
    _arrowController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 400),
    );

    final procInstId = TicketUtils.getCorrectTicketId(widget.ticket);
    final taskDefKey = TicketUtils.getTaskDefKey(widget.ticket);

    // DEBUG: Log callback
    log('🎯 [PDFView] initState - onWorkflowNodeSelected: ${widget.onWorkflowNodeSelected != null ? 'NOT NULL' : 'NULL'}');

    if (procInstId != null && taskDefKey != null) {
      context.read<TicketProcessDetailBloc>().add(
            LoadTicketProcessDetail(
              procInstId: procInstId,
              taskDefKey: taskDefKey,
              user: '',
              status: '',
            ),
          );
    }

    super.initState();
    loadPdf();
  }

  @override
  void dispose() {
    _arrowController.dispose();
    super.dispose();
  }

  void _onTabletModalCallback({
    required int selectedTab,
    required List<String> tabNames,
    required VoidCallback? onSaveDraft,
    required VoidCallback? onPrimaryAction,
    required bool showSaveDraft,
    bool isProcessing = false,
  }) {
    setState(() {
      _currentModalTab = selectedTab;
      _modalTabNames = tabNames;
      _modalSaveDraftAction = onSaveDraft;
      _modalPrimaryAction = onPrimaryAction;
      _showModalSaveDraft = showSaveDraft;
      _isProcessing = isProcessing;
    });
  }

  Widget _buildHandleModalForTablet() {
    return BlocProvider.value(
      value: context.read<TicketDialogActionBloc>(),
      child: HandleModal(
        initialTab: 0,
        taskIdValue: widget.taskIdValue,
        data: widget.data ?? ThongTinChungModel(),
        ticket: widget.ticket,
        ticketStatus: widget.ticketStatus ?? '',
        dataDauVao: widget.thongTinDauVao,
        hideHeader: true,
        onTabletCallback: _onTabletModalCallback,
      ),
    );
  }

  Future<void> loadPdf() async {
    setState(() {
      isLoading = true;
      errorMessage = '';
    });

    try {
      if (widget.pdfUrl != null) {
        developer.log(' URLllllllllll: ${widget.pdfUrl}');
        final response = await http.get(Uri.parse(widget.pdfUrl!));
        log('response33333333333: ${response}');

        final dir = await getTemporaryDirectory();
        final file = File('${dir.path}/${widget.pdfName}');

        await file.writeAsBytes(response.bodyBytes);
        localPdfPath = file.path;
      } else {
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/${widget.pdfName}');

        try {
          try {
            final ByteData data = await rootBundle.load('assets/${widget.pdfName}');
            final bytes = data.buffer.asUint8List();
            await tempFile.writeAsBytes(bytes);
            localPdfPath = tempFile.path;
            debugPrint('Successfully loaded from assets/${widget.pdfName}');
          } catch (e) {
            debugPrint('Failed with assets/${widget.pdfName}: $e');

            try {
              final ByteData data = await rootBundle.load('assets/test.pdf');
              final bytes = data.buffer.asUint8List();
              await tempFile.writeAsBytes(bytes);
              localPdfPath = tempFile.path;
              debugPrint('Successfully loaded from assets/test.pdf');
            } catch (e) {
              debugPrint('Failed with assets/test.pdf: $e');

              try {
                final ByteData data = await rootBundle.load('lib/assets/${widget.pdfName}');
                final bytes = data.buffer.asUint8List();
                await tempFile.writeAsBytes(bytes);
                localPdfPath = tempFile.path;
                debugPrint('Successfully loaded from lib/assets/${widget.pdfName}');
              } catch (e) {
                debugPrint('Failed with lib/assets/${widget.pdfName}: $e');

                final String directPath = 'lib/assets/${widget.pdfName}';
                final File directFile = File(directPath);

                if (await directFile.exists()) {
                  localPdfPath = directPath;
                  debugPrint('Found PDF with direct access: $directPath');
                } else {
                  throw Exception('PDF not found in any location');
                }
              }
            }
          }
        } catch (e) {
          errorMessage = 'Could not load PDF: $e';
          debugPrint(errorMessage);
          rethrow;
        }
      }

      if (localPdfPath != null) {
        final fileCheck = File(localPdfPath!);
        if (await fileCheck.exists()) {
          debugPrint('PDF verified at: ${fileCheck.path}');
          setState(() {
            isLoading = false;
          });
        } else {
          throw Exception('File path exists but file does not: $localPdfPath');
        }
      } else {
        throw Exception('PDF path is null after all loading attempts');
      }
    } catch (e) {
      debugPrint('Error in loadPdf: $e');
      if (mounted) {
        setState(() {
          localPdfPath = null;
          isLoading = false;
          errorMessage =
              'Failed to load PDF. Please check if the file exists and is correctly configured in pubspec.yaml.';
        });
      }
    }
  }

  void _popToHandleTicket() {
    if (widget.onWorkflowNodeSelected != null) {
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    } else {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return BlocConsumer<TicketProcessDetailBloc, TicketProcessDetailState>(
      listenWhen: (previous, current) {
        return current is TicketProcessDetailLoaded;
      },
      listener: (context, state) {
        if (state is TicketProcessDetailLoaded) {
          setState(() {
            _canShowHandleModal = state.ticketProcessDetail.data?.startPermission == 2 ||
                state.ticketProcessDetail.data?.startPermission == 3;
            log('canShowHandleModal: $_canShowHandleModal');
          });
        }
      },
      buildWhen: (previous, current) {
        if (previous is TicketProcessDetailLoaded && current is TicketProcessDetailLoaded) {
          return true;
        }
        if (previous is TicketProcessDetailLoading && current is! TicketProcessDetailLoading) {
          return true;
        }
        return false;
      },
      builder: (context, state) {
        return WillPopScope(
          onWillPop: () async {
            final isNestedNavigation = ModalRoute.of(context)?.settings.name != '/handle-ticket';
            if (isNestedNavigation) {
              _popToHandleTicket();
              return false; // Prevent default pop
            }
            return true; // Allow default pop
          },
          child: Material(
            color: Colors.white,
            child: SizedBox(
              height: MediaQuery.of(context).size.height,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: GradientBackground(
                      child: Scaffold(
                        backgroundColor: getColorSkin().transparent,
                        appBar: isTablet
                            ? null
                            : CustomAppBar(
                                automaticallyImplyLeading: true,
                                centerTitle: true,
                                title: widget.title ?? 'Tờ trình đề nghị thanh toán',
                                titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
                                textColor: getColorSkin().black,
                                actionsPadding: EdgeInsets.only(right: 18.w),
                                height: 40.h,
                                leading: IconButton(
                                  icon: Icon(Icons.arrow_back, color: getColorSkin().ink1),
                                  onPressed: () {
                                    if (Navigator.canPop(context)) {
                                      final isNestedNavigation =
                                          ModalRoute.of(context)?.settings.name != '/handle-ticket';
                                      if (isNestedNavigation) {
                                        _popToHandleTicket();
                                      } else {
                                        Navigator.pop(context);
                                      }
                                    }
                                  },
                                ),
                              ),
                        body: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Stack(
                              children: [
                                Positioned.fill(
                                  child: isLoading
                                      ? AppConstraint.buildShimmer(
                                          child: Container(
                                            width: double.infinity,
                                            height: double.infinity,
                                            margin: EdgeInsets.all(8.w),
                                            decoration: BoxDecoration(
                                              color: getColorSkin().white,
                                              borderRadius: BorderRadius.circular(8.r),
                                            ),
                                          ),
                                        )
                                      : localPdfPath == null
                                          ? Center(
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    'Không thể tải được tài liệu.',
                                                    style:
                                                        getTypoSkin().title3Medium.copyWith(color: getColorSkin().ink1),
                                                  ),
                                                  const SizedBox(height: 16),
                                                  FFilledButton(
                                                    size: FButtonSize.size24,
                                                    onPressed: loadPdf,
                                                    child: const Text('Thử lại'),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : PDFView(
                                              filePath: localPdfPath!,
                                              enableSwipe: true,
                                              swipeHorizontal: false,
                                              autoSpacing: true,
                                              pageFling: true,
                                              pageSnap: true,
                                              defaultPage: widget.initialPage,
                                              onRender: (pages) {
                                                setState(() {
                                                  totalPages = pages;
                                                });

                                                if (widget.initialPage > 0 && pdfController != null) {
                                                  pdfController!.setPage(widget.initialPage);
                                                }
                                              },
                                              onViewCreated: (PDFViewController controller) {
                                                pdfController = controller;

                                                if (widget.initialPage > 0) {
                                                  Future.delayed(const Duration(milliseconds: 300), () {
                                                    controller.setPage(widget.initialPage);
                                                  });
                                                }
                                              },
                                              onPageChanged: (page, total) {
                                                setState(() {
                                                  currentPage = page!;
                                                });
                                              },
                                              onError: (error) {
                                                debugPrint('Error rendering PDF: $error');
                                              },
                                            ),
                                ),
                                if (_showModal && isTablet)
                                  Positioned.fill(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() => _showModal = false);
                                        _arrowController.animateTo(0.0);
                                      },
                                      child: AnimatedContainer(
                                        duration: Duration(milliseconds: 300),
                                        color: Colors.black.withOpacity(0.3),
                                      ),
                                    ),
                                  ),
                              ],
                            ),

                            // Page indicator
                            if (!isLoading && localPdfPath != null)
                              Positioned(
                                top: 16.h,
                                left: 0,
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 14.w),
                                  decoration: BoxDecoration(
                                    color: getColorSkin().grey4Background.withAlpha(75),
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(8.r),
                                      bottomRight: Radius.circular(8.r),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        CupertinoIcons.sidebar_left,
                                        color: getColorSkin().ink1.withAlpha(98),
                                        size: 18.sp,
                                      ),
                                      SizedBox(width: 8.w),
                                      Text(
                                        '${currentPage + 1} / ${totalPages ?? "?"}',
                                        style: getTypoSkin().bodyRegular14Bold.copyWith(
                                              color: getColorSkin().ink1.withAlpha(98),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                            // Arrow button
                            if ((isTablet && !_showModal) ||
                                (!isTablet &&
                                    !_showModal &&
                                    _canShowHandleModal &&
                                    widget.data?.ticketStatus != 'COMPLETED' &&
                                    widget.data?.ticketStatus != 'CLOSED' &&
                                    widget.data?.ticketStatus != 'COMPLETE'))
                              Positioned(
                                top: 0,
                                right: _showModal && isTablet ? 450.w : 0,
                                child: Container(
                                  decoration: BoxDecoration(
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 4,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: InkWell(
                                    onTap: () async {
                                      if (isTablet) {
                                        setState(() => _showModal = true);
                                        _arrowController.forward();
                                      } else {
                                        _arrowController.forward();
                                        await Navigator.of(context)
                                            .push(
                                          PageRouteBuilder(
                                            opaque: false,
                                            barrierColor: Colors.black.withAlpha(20),
                                            transitionDuration: Duration(milliseconds: 400),
                                            pageBuilder: (context, animation, secondaryAnimation) => BlocProvider.value(
                                              value: context.read<TicketDialogActionBloc>(),
                                              child: HandleModal(
                                                initialTab: 0,
                                                taskIdValue: widget.taskIdValue,
                                                data: widget.data ?? ThongTinChungModel(),
                                                ticket: widget.ticket,
                                                ticketStatus: widget.ticketStatus ?? '',
                                                dataDauVao: widget.thongTinDauVao,
                                                onSuccess: () {
                                                  SchedulerBinding.instance.addPostFrameCallback((_) {
                                                    Navigator.of(context).pushAndRemoveUntil(
                                                      MaterialPageRoute(
                                                        builder: (context) => const OverviewTicketScreen(),
                                                        settings: const RouteSettings(name: '/overview'),
                                                      ),
                                                      (route) => false,
                                                    );
                                                  });
                                                },
                                              ),
                                            ),
                                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                              animation.addStatusListener((status) {
                                                if (status == AnimationStatus.forward) {
                                                  if (_showArrowButton) {
                                                    setState(() => _showArrowButton = false);
                                                  }
                                                }
                                                if (status == AnimationStatus.reverse) {
                                                  _arrowController.animateTo(0.5);
                                                }
                                              });

                                              final offsetAnimation = Tween<Offset>(
                                                begin: Offset(1, 0),
                                                end: Offset(0, 0),
                                              ).animate(CurvedAnimation(parent: animation, curve: Curves.ease));

                                              return SlideTransition(
                                                position: offsetAnimation,
                                                child: child,
                                              );
                                            },
                                          ),
                                        )
                                            .then((_) {
                                          setState(() => _showArrowButton = true);
                                          _arrowController.animateTo(0.0);
                                        });
                                      }
                                    },
                                    child: Container(
                                      width: 40.w,
                                      height: 40.w,
                                      color: getColorSkin().primaryBlue,
                                      alignment: Alignment.center,
                                      child: RotationTransition(
                                        turns: Tween(begin: 0.0, end: 1.0).animate(_arrowController),
                                        child: Icon(
                                          Icons.chevron_left_rounded,
                                          color: Colors.white,
                                          size: 28.sp,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                            // Side Modal cho tablet
                            if (isTablet)
                              AnimatedPositioned(
                                duration: Duration(milliseconds: 400),
                                curve: Curves.easeInOut,
                                top: 0,
                                right: _showModal ? 0 : -450.w,
                                bottom: 0,
                                child: Material(
                                  elevation: 16,
                                  child: Container(
                                    width: 450.w,
                                    height: double.infinity,
                                    color: getColorSkin().white,
                                    child: _showModal ? _buildHandleModalForTablet() : SizedBox.shrink(),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        bottomNavigationBar: !isLoading && localPdfPath != null
                            ? widget.source == 'handle_ticket'
                                ? buildHandleTicketBottomBar(
                                    context,
                                    widget.data ?? ThongTinChungModel(),
                                    widget.processType ?? 0,
                                    ticketStatus: widget.ticketStatus ?? '',
                                    ticket: widget.ticket,
                                    isHandleTicket: false,
                                    assignee: widget.assignee,
                                    isTabletModalActive: isTablet && _showModal,
                                    currentModalTab: _currentModalTab,
                                    modalTabNames: _modalTabNames,
                                    modalSaveDraftAction: _modalSaveDraftAction,
                                    modalPrimaryAction: _modalPrimaryAction,
                                    showModalSaveDraft: _showModalSaveDraft,
                                    isProcessing: _isProcessing,
                                    nodeType: widget.nodeType ?? '',
                                    onWorkflowNodeSelected: (nodeId, nodeType) {
                                      log('🚀 [PDFView] Received workflow callback: nodeId=$nodeId, nodeType=$nodeType');
                                      // Gọi callback từ parent nếu có
                                      if (widget.onWorkflowNodeSelected != null) {
                                        widget.onWorkflowNodeSelected!(nodeId, nodeType);
                                      }
                                      // Pop về HandleTicketScreen để update với node được chọn
                                      _popToHandleTicket();
                                    },
                                  )
                                : buildBottomBar(context)
                            : null,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
