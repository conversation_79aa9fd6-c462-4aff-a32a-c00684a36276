import 'dart:developer';
import 'package:eapprove/common/form/form_label.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class RadioButtonWidget extends StatefulWidget {
  final FormItemInfo data;
  final FormStateManager stateManager;
  final int? rowIndex;
  final bool? isShowLabel;

  const RadioButtonWidget({
    super.key,
    required this.data,
    required this.stateManager,
    this.rowIndex,
    this.isShowLabel = true,
  });

  @override
  State<RadioButtonWidget> createState() => _RadioButtonWidgetState();
}

class _RadioButtonWidgetState extends State<RadioButtonWidget> {
  List<Map<String, dynamic>> _extractOptions() {
    final List<Map<String, dynamic>> result = [];

    try {
      final optionList = widget.data.option;
      if (optionList != null && optionList is List) {
        for (var item in optionList) {
          if (item is Map) {
            final Map<String, dynamic> optionItem =
                Map<String, dynamic>.from(item);
            if (optionItem.containsKey('label') &&
                optionItem.containsKey('value')) {
              result.add(optionItem);
            }
          }
        }
      }
    } catch (e) {
      log('RadioButtonWidget - Error extracting options: $e');
    }

    return result;
  }

  void _handleOptionChange(String value) {
    log('handleOptionChange - value: $value', name: 'RadioButtonWidget');
    if (widget.data.readonly == true) return;

    setState(() {
      widget.data.value = value;
    });

    // Call onChange directly without Future.microtask
    widget.stateManager.setFieldValue(widget.data.name ?? '', value, rowIndex: widget.rowIndex);
  }

  @override
  Widget build(BuildContext context) {
    final options = _extractOptions();
    final theme = Theme.of(context);
    final isReadOnly = widget.data.readonly == true;

    final grValue = widget.stateManager
        .getFieldValueByName(widget.data.name ?? '', widget.data.parentName);
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if(widget.isShowLabel == true)
          FormLabel(
            displayName: widget.data.displayName,
            label: widget.data.label,
            suggestText: widget.data.suggestText,
            isRequired: widget.data.validations?['required'] == true,
          ),
          ...options.isEmpty
              ? []
              : options.map((option) => _buildRadioOption(
                    option['value']?.toString() ?? '',
                    option['label']?.toString() ?? '',
                    isReadOnly,
                    grValue,
                  )),
          if (widget.data.error?.isNotEmpty == true)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                widget.data.error ?? '',
                style: TextStyle(
                  color: theme.colorScheme.error,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRadioOption(
      String value, String label, bool isReadOnly, String? groupValue) {
    return Container(
      width: double.infinity,
      // margin: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Radio<String>(
            activeColor: getColorSkin().primaryBlue,
            value: value,
            groupValue: groupValue,
            onChanged: isReadOnly
                ? null
                : (newValue) => _handleOptionChange(newValue!),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: GestureDetector(
              onTap: isReadOnly ? null : () => _handleOptionChange(value),
              child: Text(
                label,
                style: const TextStyle(fontSize: 14),
                softWrap: true,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
