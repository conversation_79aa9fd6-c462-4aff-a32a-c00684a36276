class PriorityManagementRequestModel {
  final String? search;
  final String sortBy;
  final String sortType;
  final int limit;
  final int page;
  final int size;
  final List<int> listActiveStatus;

  PriorityManagementRequestModel({
    this.search,
    required this.sortBy,
    required this.sortType,
    required this.limit,
    required this.page,
    required this.size,
    required this.listActiveStatus,
  });

  factory PriorityManagementRequestModel.fromJson(Map<String, dynamic> json) {
    return PriorityManagementRequestModel(
      search: json['search'] as String?,
      sortBy: json['sortBy'] as String,
      sortType: json['sortType'] as String,
      limit: json['limit'] as int,
      page: json['page'] as int,
      size: json['size'] as int,
      listActiveStatus: (json['listActiveStatus'] as List).map((e) => e as int).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'search': search,
      'sortBy': sortBy,
      'sortType': sortType,
      'limit': limit,
      'page': page,
      'size': size,
      'listActiveStatus': listActiveStatus,
    };
  }
} 