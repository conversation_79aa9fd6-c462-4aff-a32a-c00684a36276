import 'package:eapprove/screens/payment_step/widgets/pdf_view.dart';
import 'package:eapprove/screens/payment_step/widgets/step_2_card.dart';
import 'package:flutter/material.dart';
import 'package:eapprove/screens/payment_step/payment_main_screen.dart';

class PaymentStep2Screen extends BasePaymentStepScreen {
  const PaymentStep2Screen({Key? key})
      : super(
    key: key,
    currentStep: 2,
    totalSteps: 4,
    stepTitle: 'Bước 2',
    stepDescription: 'Nhập thông tin đề nghị thanh toán',
  );

  @override
  State<PaymentStep2Screen> createState() => _PaymentStep2ScreenState();
}

class _PaymentStep2ScreenState extends BasePaymentStepState<PaymentStep2Screen> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget buildStepContent() {
    return Form(
      key: _formKey,
      child: const EmployeeNavigationCard(),
    );
  }

  @override
  void handleNextStep() {
    Navigator.push(context,
        MaterialPageRoute(builder: (context) =>
        const PdfViewerPage(pdfName: 'test.pdf')));
  }
}