import 'package:eapprove/models/pyc/ticket_task_model.dart';
import 'package:flutter/rendering.dart';

class FilterServiceData {
  final int id;
  final int parentId;
  final String serviceName;
  final String? color;
  final String? icon;
  final int serviceType;
  final int processId;
  final String? processName;
  final String? url;
  final String? description;
  final String? note;
  final bool notShowingWebsite;
  final bool notShowingMobile;
  final bool hideAllUser;
  final bool visibleAllUser;
  final bool specialFlow;
  final bool specialApplyFor;
  final String companyCode;
  final String companyName;
  final String status;
  final String createdDate;
  final String modifiedDate;

  FilterServiceData({
    required this.id,
    required this.parentId,
    required this.serviceName,
    this.color,
    this.icon,
    required this.serviceType,
    required this.processId,
    this.processName,
    this.url,
    this.description,
    this.note,
    required this.notShowingWebsite,
    required this.notShowingMobile,
    required this.hideAllUser,
    required this.visibleAllUser,
    required this.specialFlow,
    required this.specialApplyFor,
    required this.companyCode,
    required this.companyName,
    required this.status,
    required this.createdDate,
    required this.modifiedDate,
  });

  factory FilterServiceData.fromJson(Map<String, dynamic> json) {
    try {
      return FilterServiceData(
        id: _parseInt(json['id']),
        parentId: _parseInt(json['parentId']),
        serviceName: json['serviceName'] ?? '',
        color: json['color'],
        icon: json['icon'],
        serviceType: _parseInt(json['serviceType']),
        processId: _parseInt(json['processId']),
        processName: json['processName'],
        url: json['url'],
        description: json['description'],
        note: json['note'],
        notShowingWebsite: _parseBool(json['notShowingWebsite']),
        notShowingMobile: _parseBool(json['notShowingMobile']),
        hideAllUser: _parseBool(json['hideAllUser']),
        visibleAllUser: _parseBool(json['visibleAllUser']),
        specialFlow: _parseBool(json['specialFlow']),
        specialApplyFor: _parseBool(json['specialApplyFor']),
        companyCode: json['companyCode'] ?? '',
        companyName: json['companyName'] ?? '',
        status: json['status'] ?? '',
        createdDate: json['createdDate'] ?? '',
        modifiedDate: json['modifiedDate'] ?? '',
      );
    } catch (e) {
      debugPrint('Error parsing FilterServiceData: $e, json: $json');
      // Trả về giá trị mặc định để tránh lỗi
      return FilterServiceData(
        id: 0,
        parentId: 0,
        serviceName: json['serviceName'] ?? 'Unknown',
        serviceType: 0,
        processId: 0,
        notShowingWebsite: false,
        notShowingMobile: false,
        hideAllUser: false,
        visibleAllUser: false,
        specialFlow: false,
        specialApplyFor: false,
        companyCode: '',
        companyName: '',
        status: '',
        createdDate: '',
        modifiedDate: '',
      );
    }
  }

  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      try {
        return int.parse(value);
      } catch (_) {
        return 0;
      }
    }
    if (value is double) return value.toInt();
    return 0;
  }

  static bool _parseBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is String) {
      try {
        return bool.parse(value);
      } catch (_) {
        return false;
      }
    }
    if (value is int) return value != 0;
    return false;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'serviceName': serviceName,
      'color': color,
      'icon': icon,
      'serviceType': serviceType,
      'processId': processId,
      'processName': processName,
      'url': url,
      'description': description,
      'note': note,
      'notShowingWebsite': notShowingWebsite,
      'notShowingMobile': notShowingMobile,
      'hideAllUser': hideAllUser,
      'visibleAllUser': visibleAllUser,
      'specialFlow': specialFlow,
      'specialApplyFor': specialApplyFor,
      'companyCode': companyCode,
      'companyName': companyName,
      'status': status,
      'createdDate': createdDate,
      'modifiedDate': modifiedDate,
    };
  }
}

class ListUserInfoDataModel {
  final List<ListUserInfoModel> data;

  ListUserInfoDataModel({
    required this.data,
  });

  factory ListUserInfoDataModel.fromJson(Map<String, dynamic> json) {
    var dataList = json['data'] as List;
    List<ListUserInfoModel> employees =
        dataList.map((item) => ListUserInfoModel.fromJson(item as Map<String, dynamic>)).toList();
    return ListUserInfoDataModel(data: employees);
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((employee) => employee.toJson()).toList(),
    };
  }
}

class ListUserInfoModel {
  final int? id;
  final String? fullName;
  final String? title;
  final String? username;

  ListUserInfoModel({this.id, this.fullName, this.title, this.username});
  factory ListUserInfoModel.fromJson(Map<String, dynamic> json) {
    return ListUserInfoModel(
      id: json['id'],
      fullName: json['fullName'],
      title: json['title'],
      username: json['username'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'title': title,
      'username': username,
    };
  }
}

class ListChartIdDataModel {
  final int? id;
  final String? code;
  final String? name;
  final String? shortName;
  final String? type;
  final String? status;
  final int? userTotal;
  final int? linkTotal;
  final DateTime? createdDate;
  final String? createdUser;
  final DateTime? modifiedDate;
  final String? modifiedUser;
  final List<ChartSharedUserDto>? chartSharedUserDtoList;

  ListChartIdDataModel({
    this.id,
    this.code,
    this.name,
    this.shortName,
    this.type,
    this.status,
    this.userTotal,
    this.linkTotal,
    this.createdDate,
    this.createdUser,
    this.modifiedDate,
    this.modifiedUser,
    this.chartSharedUserDtoList,
  });

  factory ListChartIdDataModel.fromJson(Map<String, dynamic> json) => ListChartIdDataModel(
        id: json["id"],
        code: json["code"],
        name: json["name"],
        shortName: json["shortName"],
        type: json["type"],
        status: json["status"],
        userTotal: json["userTotal"],
        linkTotal: json["linkTotal"],
        createdDate: json["createdDate"] == null ? null : DateTime.parse(json["createdDate"]),
        createdUser: json["createdUser"],
        modifiedDate: json["modifiedDate"] == null ? null : DateTime.parse(json["modifiedDate"]),
        modifiedUser: json["modifiedUser"],
        chartSharedUserDtoList: json["chartSharedUserDtoList"] == null
            ? null
            : List<ChartSharedUserDto>.from(json["chartSharedUserDtoList"].map((x) => ChartSharedUserDto.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "code": code,
        "name": name,
        "shortName": shortName,
        "type": type,
        "status": status,
        "userTotal": userTotal,
        "linkTotal": linkTotal,
        "createdDate": createdDate?.toIso8601String(),
        "createdUser": createdUser,
        "modifiedDate": modifiedDate?.toIso8601String(),
        "modifiedUser": modifiedUser,
        "chartSharedUserDtoList":
            chartSharedUserDtoList == null ? null : List<dynamic>.from(chartSharedUserDtoList!.map((x) => x.toJson())),
      };
}

class ChartSharedUserDto {
  final int? id;
  final String? userEmail;

  ChartSharedUserDto({
    this.id,
    this.userEmail,
  });

  factory ChartSharedUserDto.fromJson(Map<String, dynamic> json) => ChartSharedUserDto(
        id: json["id"],
        userEmail: json["userEmail"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "userEmail": userEmail,
      };
}

class OrgData {
  String? code;
  String? username;
  String? fullname;
  int? userId;
  String? position;
  List<OrgChild>? children;
  String? shortNameChart;
  int? id;
  String? title;
  int? value;
  String? shortNameChartNode;
  int? chartId;
  int? chartNodeId;
  String? name;
  OrgData({
    this.code,
    this.username,
    this.fullname,
    this.userId,
    this.position,
    this.children,
    this.shortNameChart,
    this.id,
    this.title,
    this.value,
    this.shortNameChartNode,
    this.chartId,
    this.chartNodeId,
    this.name,
  });

  factory OrgData.fromJson(Map<String, dynamic> json) => OrgData(
        code: json['code'],
        username: json['username'],
        fullname: json['fullname'],
        userId: json['userId'],
        position: json['position'],
        children: (json['children'] as List<dynamic>?)
            ?.map((e) => OrgChild.fromJson(e))
            .toList(),
        shortNameChart: json['shortNameChart'],
        id: json['id'],
        title: json['title'],
        value: json['value'],
        shortNameChartNode: json['shortNameChartNode'],
        chartId: json['chartId'],
        chartNodeId: json['chartNodeId'],
        name: json['name'],
      );


  Map<String, dynamic> toJson() => {
        'code': code,
        'children': children?.map((e) => e.toJson()).toList(),
        'shortNameChart': shortNameChart,
        'id': id,
        'title': title,
        'value': value,
        'shortNameChartNode': shortNameChartNode,
      };
}

class OrgChild {
  String? title;
  int? value;
  String? code;
  String? shortName;
  List<OrgChild>? children;
  String? shortNameChart;

  OrgChild({
    this.title,
    this.value,
    this.code,
    this.shortName,
    this.children,
    this.shortNameChart,
  });

  factory OrgChild.fromJson(Map<String, dynamic> json) => OrgChild(
        title: json['title'],
        value: json['value'],
        code: json['code'],
        shortName: json['shortName'],
        children: (json['children'] as List<dynamic>?)?.map((e) => OrgChild.fromJson(e)).toList(),
        shortNameChart: json['shortNameChart'],
      );

  Map<String, dynamic> toJson() => {
        'title': title,
        'value': value,
        'code': code,
        'shortName': shortName,
        'children': children?.map((e) => e.toJson()).toList(),
        'shortNameChart': shortNameChart,
      };
}

class ListTaskDefKeyDataModel {
  final String? taskDefKey;
  final String? taskName;

  ListTaskDefKeyDataModel({this.taskDefKey, this.taskName});

  factory ListTaskDefKeyDataModel.fromJson(Map<String, dynamic> json) {
    return ListTaskDefKeyDataModel(
      taskDefKey: json['taskDefKey'],
      taskName: json['taskName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskDefKey': taskDefKey,
      'taskName': taskName,
    };
  }
}

class ListPriorityDataModel {
  final int? id;
  final String? code;
  final String? name;
  final String? color;
  final String? alertTimeComplete;
  final List<String>? shareWith;
  final int? status;
  final double? slaValue;
  final String? createdUser;
  final String? createdDate;
  final String? modifiedUser;
  final String? modifiedDate;
  final String? description;
  final int? reminderBeingTime;
  final int? reminderTime;
  final int? reminderBeingValue;
  final int? reminderValue;
  final String? reminderBeingType;
  final String? reminderType;
  final bool? configReminder;
  final List<String>? applyFor;
  final String? companyName;
  final String? companyCode;
  final int? activeStatus;

  ListPriorityDataModel(
      {this.id,
      this.code,
      this.name,
      this.color,
      this.alertTimeComplete,
      this.shareWith,
      this.status,
      this.slaValue,
      this.createdUser,
      this.createdDate,
      this.modifiedUser,
      this.modifiedDate,
      this.description,
      this.reminderBeingTime,
      this.reminderTime,
      this.reminderBeingValue,
      this.reminderValue,
      this.reminderBeingType,
      this.reminderType,
      this.configReminder,
      this.applyFor,
      this.companyName,
      this.companyCode,
      this.activeStatus});

  factory ListPriorityDataModel.fromJson(Map<String, dynamic> json) {
    var shareWithList = json['shareWith'] != null ? List<String>.from(json['shareWith']) : null;
    var applyForList = List<String>.from(json['applyFor'] ?? []);

    return ListPriorityDataModel(
      id: json['id'],
      code: json['code'],
      name: json['name'],
      color: json['color'],
      alertTimeComplete: json['alertTimeComplete'],
      shareWith: shareWithList,
      status: json['status'],
      slaValue: json['slaValue'].toDouble(),
      createdUser: json['createdUser'],
      createdDate: json['createdDate'],
      modifiedUser: json['modifiedUser'],
      modifiedDate: json['modifiedDate'],
      description: json['description'],
      reminderBeingTime: json['reminderBeingTime'],
      reminderTime: json['reminderTime'],
      reminderBeingValue: json['reminderBeingValue'],
      reminderValue: json['reminderValue'],
      reminderBeingType: json['reminderBeingType'],
      reminderType: json['reminderType'],
      configReminder: json['configReminder'],
      applyFor: applyForList,
      companyName: json['companyName'],
      companyCode: json['companyCode'],
      activeStatus: json['activeStatus'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'color': color,
      'alertTimeComplete': alertTimeComplete,
      'shareWith': shareWith,
      'status': status,
      'slaValue': slaValue,
      'createdUser': createdUser,
      'createdDate': createdDate,
      'modifiedUser': modifiedUser,
      'modifiedDate': modifiedDate,
      'description': description,
      'reminderBeingTime': reminderBeingTime,
      'reminderTime': reminderTime,
      'reminderBeingValue': reminderBeingValue,
      'reminderValue': reminderValue,
      'reminderBeingType': reminderBeingType,
      'reminderType': reminderType,
      'configReminder': configReminder,
      'applyFor': applyFor,
      'companyName': companyName,
      'companyCode': companyCode,
      'activeStatus': activeStatus,
    };
  }
}

class SearchFilterContentModel {
  final String? companyCode;
  final String? ticketTitle;
  final String? startKey;
  final int? ticketCreatedTime;
  final String? procInstId;
  final String? serviceName;
  final String? procDefId;
  final List<TicketTask>? ticketTaskDtoList;
  final int? chartId;
  final String? ticketPriority;
  final String? chartNodeName;
  final String? ticketStatus;
  final String? cancelUser;
  final String? requestCode;
  final int? ticketEditTime;
  final int? id;
  final int? serviceId;
  final String? createdUser;

  SearchFilterContentModel({
    this.companyCode,
    this.ticketTitle,
    this.startKey,
    this.ticketCreatedTime,
    this.procInstId,
    this.serviceName,
    this.procDefId,
    this.ticketTaskDtoList,
    this.chartId,
    this.ticketPriority,
    this.chartNodeName,
    this.ticketStatus,
    this.cancelUser,
    this.requestCode,
    this.ticketEditTime,
    this.id,
    this.serviceId,
    this.createdUser,
  });

  factory SearchFilterContentModel.fromJson(Map<String, dynamic> json) {
    return SearchFilterContentModel(
      companyCode: json['companyCode'],
      ticketTitle: json['ticketTitle'],
      startKey: json['startKey'],
      ticketCreatedTime: json['ticketCreatedTime'],
      procInstId: json['procInstId'],
      serviceName: json['serviceName'],
      procDefId: json['procDefId'],
      ticketTaskDtoList: (json['ticketTaskDtoList'] as List<dynamic>?)?.map((e) => TicketTask.fromJson(e)).toList(),
      chartId: json['chartId'],
      ticketPriority: json['ticketPriority'],
      chartNodeName: json['chartNodeName'],
      ticketStatus: json['ticketStatus'],
      cancelUser: json['cancelUser'],
      requestCode: json['requestCode'],
      ticketEditTime: json['ticketEditTime'],
      id: json['id'],
      serviceId: json['serviceId'],
      createdUser: json['createdUser'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'companyCode': companyCode,
      'ticketTitle': ticketTitle,
      'startKey': startKey,
      'ticketCreatedTime': ticketCreatedTime,
      'procInstId': procInstId,
      'serviceName': serviceName,
      'procDefId': procDefId,
      'ticketTaskDtoList': ticketTaskDtoList?.map((e) => e.toJson()).toList(),
      'chartId': chartId,
      'ticketPriority': ticketPriority,
      'chartNodeName': chartNodeName,
      'ticketStatus': ticketStatus,
      'cancelUser': cancelUser,
      'requestCode': requestCode,
      'ticketEditTime': ticketEditTime,
      'id': id,
      'serviceId': serviceId,
      'createdUser': createdUser,
    };
  }
}

class MentionUserModel {
  final int? userId;
  final String username;
  final String fullname;
  final String title;
  final String? chartId;
  final String? chartName;
  final String? shortNameChart;
  final String? chartNodeId;
  final String? name;
  final String? shortNameChartNode;

  MentionUserModel({
    this.userId,
    required this.username,
    required this.fullname,
    required this.title,
    this.chartId,
    this.chartName,
    this.shortNameChart,
    this.chartNodeId,
    this.name,
    this.shortNameChartNode,
  });

  factory MentionUserModel.fromJson(Map<String, dynamic> json) {
    return MentionUserModel(
      userId: json['userId'],
      username: json['username'] ?? '',
      fullname: json['fullname'] ?? '',
      title: json['title'] ?? '',
      chartId: json['chartId']?.toString(),
      chartName: json['chartName'],
      shortNameChart: json['shortNameChart'],
      chartNodeId: json['chartNodeId']?.toString(),
      name: json['name'],
      shortNameChartNode: json['shortNameChartNode'],
    );
  }

  String getDisplayText() {
    return '$username - $fullname - $title';
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'username': username,
      'fullname': fullname,
      'title': title,
      'chartId': chartId,
      'chartName': chartName,
      'shortNameChart': shortNameChart,
      'chartNodeId': chartNodeId,
      'name': name,
      'shortNameChartNode': shortNameChartNode,
    };
  }
}
