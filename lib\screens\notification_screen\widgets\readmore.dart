import 'package:eapprove/screens/notification_screen/widgets/html_style_parser.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:html/parser.dart' as html_parser;

class HtmlReadMore extends StatefulWidget {
  final String htmlContent;
  final int maxLines;
  final TextStyle? textStyle;
  final TextStyle? expandTextStyle;
  final TextStyle? collapseTextStyle;
  final String expandText;
  final String collapseText;
  final bool initiallyExpanded;
  final Function(bool)? onExpandChanged;
  final Map<String, Style> htmlStyles;
  final Function(String?)? onLinkTap;

  const HtmlReadMore({
    Key? key,
    required this.htmlContent,
    this.maxLines = 3,
    this.textStyle,
    this.expandTextStyle,
    this.collapseTextStyle,
    this.expandText = 'Xem thêm',
    this.collapseText = 'Thu gọn',
    this.initiallyExpanded = false,
    this.onExpandChanged,
    this.htmlStyles = const {},
    this.onLinkTap,
  }) : super(key: key);

  @override
  State<HtmlReadMore> createState() => _HtmlReadMoreState();
}

class _HtmlReadMoreState extends State<HtmlReadMore> {
  late final String _plainText;
  late final String _processedHtml;
  bool _isExpanded = false;
  late final bool _needsReadMore;
  final TapGestureRecognizer _tapGestureRecognizer = TapGestureRecognizer();
  late Map<String, Style> _dynamicHtmlStyles;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _processedHtml = _processHtml(widget.htmlContent);
    _plainText = html_parser.parse(_processedHtml).body?.text.trim() ?? '';
    const avgCharsPerLine = 40; // Average characters per line for truncation
    _needsReadMore = _plainText.length > widget.maxLines * avgCharsPerLine;
    _tapGestureRecognizer.onTap = _toggleExpand;
  }
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _dynamicHtmlStyles = _buildDynamicStyles();
  }
  @override
  void dispose() {
    _tapGestureRecognizer.dispose();
    super.dispose();
  }
  String _processHtml(String html) {
    var document = html_parser.parse(html);

    var spans = document.querySelectorAll('span[style]');
    for (var span in spans) {
      final styleAttr = span.attributes['style'];
      if (styleAttr != null) {
        final styles = StyleParser.parseInlineStyle(styleAttr);

        var classes = <String>[];
        if (styles['color'] != null) {
      
          classes.add('custom-color');
        }
        if (styles['fontSize'] != null) {
          span.attributes['data-font-size'] = styles['fontSize'].toString();
          classes.add('custom-font-size');
        }
        if (styles['fontFamily'] != null) {
          span.attributes['data-font-family'] = styles['fontFamily'];
          classes.add('custom-font-family');
        }
        if (styles['fontWeight'] != null) {
          final fontWeight = styles['fontWeight'] as FontWeight;
          span.attributes['data-font-weight'] = fontWeight.value.toString();
          classes.add('custom-font-weight');
        }

        if (classes.isNotEmpty) {
          span.classes.addAll(classes);
        }
      }
    }

    return document.body?.outerHtml ?? html;
  }
  Map<String, Style> _buildDynamicStyles() {
    final baseStyle = widget.textStyle ?? DefaultTextStyle.of(context).style;

    final Map<String, Style> dynamicStyles = {
      "html": Style.fromTextStyle(baseStyle),
      "body": Style.fromTextStyle(baseStyle),
      "p": Style.fromTextStyle(baseStyle).copyWith(
        margin: Margins.zero,
        padding: HtmlPaddings.zero,
      ),
      "span": Style.fromTextStyle(baseStyle),
      "div": Style.fromTextStyle(baseStyle),

      // Bold styles
      "strong": Style(fontWeight: FontWeight.w900),
      "b": Style(fontWeight: FontWeight.w900),

      // Italic styles
      "em": Style(
        fontStyle: FontStyle.italic,
        fontWeight: FontWeight.w400,
      ),
      "i": Style(
        fontStyle: FontStyle.italic,
        fontWeight: FontWeight.w400,
      ),

      // Underline
      "u": Style(textDecoration: TextDecoration.underline),
      "ins": Style(textDecoration: TextDecoration.underline),

      // Strikethrough
      "del": Style(textDecoration: TextDecoration.lineThrough),
      "s": Style(textDecoration: TextDecoration.lineThrough),

      // Links
      "a": Style.fromTextStyle(baseStyle).copyWith(
        color: getColorSkin().secondaryColor1,
        textDecoration: TextDecoration.underline,
      ),
      "a.wysiwyg-mention": Style(
        textDecoration: TextDecoration.none,
        color: getColorSkin().title,
        fontWeight: FontWeight.w700, 
      ),
      "a.wysiwyg-mention span": Style(
        color: getColorSkin().title,
        fontWeight: FontWeight.w700,
      ),

      ...widget.htmlStyles,
    };

    return dynamicStyles;
  }
 

  

  String _truncateWithSuffix(
      String text, double maxWidth, TextStyle style, TextStyle suffixStyle, int maxLines, String suffix) {
    final suffixPainter = TextPainter(
      text: TextSpan(text: suffix, style: suffixStyle),
      textDirection: TextDirection.ltr,
    )..layout();
    final suffixWidth = suffixPainter.width;

    final availableWidth = maxWidth - suffixWidth - 10;

    int low = 0, high = text.length, best = 0;
    while (low <= high) {
      final mid = (low + high) >> 1;
      final candidate = text.substring(0, mid);

      final textPainter = TextPainter(
        text: TextSpan(text: candidate, style: style),
        maxLines: maxLines,
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: availableWidth);

      if (textPainter.didExceedMaxLines || textPainter.width > availableWidth) {
        high = mid - 1;
      } else {
        best = mid;
        low = mid + 1;
      }
    }

    String truncated = text.substring(0, best);
    if (best < text.length && truncated.isNotEmpty) {
      final lastSpaceIndex = truncated.lastIndexOf(' ');
      if (lastSpaceIndex != -1 && lastSpaceIndex > truncated.length / 2) {
        truncated = truncated.substring(0, lastSpaceIndex);
      }
    }

    return truncated;
  }

  void _toggleExpand() {
    setState(() => _isExpanded = !_isExpanded);
    widget.onExpandChanged?.call(_isExpanded);
  }
  Widget _buildHtmlWithInlineStyles(String htmlData) {
    final processedData = _processHtmlWithInlineStyles(htmlData);
    return Html(
      data: processedData.html,
      style: processedData.styles,
      onLinkTap: (url, attrs, elem) {
        if (url != null) widget.onLinkTap?.call(url);
      },
    );
  }
  
  ({String html, Map<String, Style> styles}) _processHtmlWithInlineStyles(String htmlData) {
    final baseStyle = widget.textStyle ?? DefaultTextStyle.of(context).style;

    final document = html_parser.parse(htmlData);
    final Map<String, Style> dynamicStyles = Map.from(_dynamicHtmlStyles);

    final elementsWithStyle = document.querySelectorAll('*[style]');

    for (var element in elementsWithStyle) {
      final styleAttr = element.attributes['style'];
      if (styleAttr != null && styleAttr.isNotEmpty) {
        final styles = StyleParser.parseInlineStyle(styleAttr);

        final uniqueClass = 'inline_${element.hashCode.abs()}';
        element.classes.add(uniqueClass);

        Style customStyle = Style.fromTextStyle(baseStyle);

        if (styles['color'] != null) {
          customStyle = customStyle.copyWith(color: styles['color'] as Color);
        }

        var parent = element.parent;
        bool isInMention = false;
        while (parent != null) {
          if (parent.classes.contains('wysiwyg-mention')) {
            isInMention = true;
            break;
          }
          parent = parent.parent;
        }

        if (styles['fontSize'] != null) {
          final fontSize = styles['fontSize'] as double;
          customStyle = customStyle.copyWith(
            fontSize: FontSize(fontSize),
            fontWeight: isInMention ? FontWeight.w700 : FontWeight.w400,
          );
        }

        if (styles['fontFamily'] != null) {
          customStyle = customStyle.copyWith(fontFamily: styles['fontFamily'] as String);
        }

        if (styles['fontWeight'] != null) {
          customStyle = customStyle.copyWith(fontWeight: styles['fontWeight'] as FontWeight);
        }

        if (isInMention) {
          customStyle = customStyle.copyWith(
            fontWeight: FontWeight.w700,
            color: getColorSkin().title, 
          );
        }

        if (styles['textDecoration'] != null) {
          customStyle = customStyle.copyWith(textDecoration: styles['textDecoration'] as TextDecoration);
        }

        dynamicStyles['${element.localName}.$uniqueClass'] = customStyle;
      }
    }

    return (html: document.body?.outerHtml ?? htmlData, styles: dynamicStyles);
  }

  @override
  Widget build(BuildContext context) {
    final baseStyle = widget.textStyle ?? DefaultTextStyle.of(context).style;

    final expandStyle = widget.expandTextStyle ??
        baseStyle.copyWith(
          color: getColorSkin().secondaryColor1,
          fontWeight: FontWeight.w700,
        );

    final collapseStyle = widget.collapseTextStyle ??
        baseStyle.copyWith(
          color: getColorSkin().secondaryColor1,
          fontWeight: FontWeight.w700,
        );

    final textStyle = baseStyle.copyWith(
      fontSize: baseStyle.fontSize,
      fontFamily: baseStyle.fontFamily,
      fontWeight: baseStyle.fontWeight,
      height: baseStyle.height,
      letterSpacing: baseStyle.letterSpacing,
    );

    if (!_needsReadMore) {
      return _buildHtmlWithInlineStyles(_processedHtml);
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // ===== COLLAPSED STATE =====
        if (!_isExpanded) {
          final truncatedText = _truncateWithSuffix(
            _plainText,
            constraints.maxWidth,
            textStyle,
            expandStyle,
            widget.maxLines,
            "... ${widget.expandText}",
          );

          return GestureDetector(
            onTap: _toggleExpand,
            behavior: HitTestBehavior.translucent,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 4.0),
              child: RichText(
                maxLines: widget.maxLines,
                overflow: TextOverflow.clip,
                text: TextSpan(
                  style: textStyle,
                  children: [
                    TextSpan(
                      text: truncatedText,
                      style: textStyle,
                    ),
                    TextSpan(text: "... "),
                    TextSpan(
                      text: widget.expandText,
                      style: expandStyle,
                      recognizer: _tapGestureRecognizer,
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        // ===== EXPANDED STATE =====
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHtmlWithInlineStyles(_processedHtml),
            GestureDetector(
              onTap: _toggleExpand,
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: RichText(
                  text: TextSpan(
                    text: widget.collapseText,
                    style: collapseStyle,
                    recognizer: _tapGestureRecognizer,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
