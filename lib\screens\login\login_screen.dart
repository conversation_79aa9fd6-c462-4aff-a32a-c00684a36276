import 'package:eapprove/screens/login/widget/login_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';

class LoginScreen extends StatelessWidget {
  static const routeName = "/login";

  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => const LoginScreen());
  }

  const LoginScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      // backgroundColor: Colors.transparent,
      body: Padding(
        padding: EdgeInsets.only(top: 80),
        child: LoginForm(),
      ),
    );
  }
}
