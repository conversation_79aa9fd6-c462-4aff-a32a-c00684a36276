import 'dart:developer';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/screens/ticket_other_action/widget/comment_model.dart';
import 'package:eapprove/screens/ticket_other_action/widget/comment_utils.dart';
import 'package:eapprove/screens/ticket_other_action/widget/consultation/comment_input_widget.dart';
import 'package:eapprove/screens/ticket_other_action/widget/consultation/comment_widget.dart';
import 'package:eapprove/screens/ticket_other_action/widget/file_attachment_widgets.dart';
import 'package:eapprove/screens/ticket_other_action/widget/mention_model.dart';
import 'package:eapprove/screens/ticket_other_action/widget/mention_suggestion_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_sdk/widgets/custom_divider.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;

import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_event.dart';
import 'package:eapprove/blocs/pyc/pyc_state.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/consultation_opinion_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/consultation_opinion_state.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/models/common/document_model.dart' as document_model;
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/screens/common/file_viewer.dart';

class ConsultationOpinionScreen extends StatefulWidget {
  const ConsultationOpinionScreen({
    super.key,
    required this.ticketId,
    required this.ticketProcId,
    this.isAdditionalContent = false,
  });

  final dynamic ticketId;
  final dynamic ticketProcId;
  final bool isAdditionalContent;

  @override
  State<ConsultationOpinionScreen> createState() => _ConsultationOpinionScreenState();
}

class _ConsultationOpinionScreenState extends State<ConsultationOpinionScreen> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  String creator = '';
  bool _isPrivate = false;
  bool _showMentionList = false;
  String _currentMentionQuery = '';
  List<PlatformFile> _selectedFiles = [];
  int? _editingCommentId;
  bool _isEditing = false;
  List<Map<String, dynamic>>? _existingFiles = [];
  List<int> _removedFileIds = [];

  int? _replyingToCommentId;

  final TextEditingController _replyController = TextEditingController();
  final FocusNode _replyFocusNode = FocusNode();

  final Map<String, String> _usernameCache = {};
  bool _isReplyPrivate = false;
  List<PlatformFile> _replySelectedFiles = [];
  List<MentionRange> _mentions = [];
  final List<MentionRange> _replyMentions = [];
  bool _isInitialTextSet = false;
  bool get _canUseMention => !widget.isAdditionalContent;
  bool get _canDeleteComment => !widget.isAdditionalContent;
  bool get _canEditComment => !widget.isAdditionalContent;
  final List<String> _supportedExtensions = [
    'png',
    'jpg',
    'jpeg',
    'mp4',
    'avi',
    'mov',
    'wmv',
    'xls',
    'xlsx',
    'doc',
    'docx',
    'pdf',
    'ppt',
    'pptx',
    'jfif',
    'rar',
    'zip',
    'msg',
    'txt'
  ];

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
    _controller.addListener(_onTextChanged);
    _replyFocusNode.addListener(_onReplyFocusChange);
    _replyController.addListener(_onReplyTextChanged);
    _isPrivate = false;
    _isReplyPrivate = false;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    context.read<ConsultationOpinionBloc>().add(
          GetConsultationOpinions(
            procInstId: widget.ticketProcId?.toString() ?? '',
            ticketId: widget.ticketId is int ? widget.ticketId : int.tryParse(widget.ticketId?.toString() ?? '0') ?? 0,
            isAdditionalRequest: widget.isAdditionalContent,
          ),
        );

      context.read<TicketProcessDetailBloc>().add(
      LoadUserTaskInfo(ticketId: widget.ticketId?.toString() ?? '0'),
    );
    context.read<PycBloc>().add(
          FetchOrgChartList(
            requestModel: ChartFilterRequestModel(orgchart: [], infoType: "user", condition: []),
          ),
        );
  }

  void _onTextChanged() {
    setState(() {});
    if (!_canUseMention) return;
    final text = _controller.text;

    if (_isInitialTextSet) return;
    setState(() {});

    // Cập nhật lại danh sách mention khi text thay đổi
    _mentions.removeWhere((mention) {
      if (mention.start >= text.length) return true;
      if (mention.start + mention.length > text.length) return true;
      return text.substring(mention.start, mention.start + mention.length) != mention.data;
    });

    // Kiểm tra xem có ký tự @ không
    if (!text.contains('@')) {
      setState(() {
        _showMentionList = false;
        _currentMentionQuery = '';
      });
      return;
    }

    final lastAtIndex = text.lastIndexOf('@');
    final cursorPosition = _controller.selection.baseOffset;

    if (lastAtIndex >= 0 && cursorPosition > lastAtIndex) {
      bool isPartOfExistingMention = false;
      for (var mention in _mentions) {
        if (lastAtIndex >= mention.start && lastAtIndex < mention.start + mention.length) {
          isPartOfExistingMention = true;
          break;
        }
      }

      if (!isPartOfExistingMention) {
        final query = lastAtIndex == text.length - 1 ? '' : text.substring(lastAtIndex + 1, cursorPosition);
        final pycState = context.read<PycBloc>().state;

        if (pycState.orgChartListStatus == PycStatus.loaded) {
          final hasMatches = pycState.orgChartList.any((user) =>
              (user.name?.toLowerCase() ?? '').contains(query.toLowerCase()) ||
              (user.username?.toLowerCase() ?? '').contains(query.toLowerCase()));

          setState(() {
            _showMentionList = hasMatches;
            _currentMentionQuery = query;
          });
        } else {
          setState(() {
            _showMentionList = true;
            _currentMentionQuery = query;
          });
        }
        ;

        log('Show mention list: $_showMentionList, query: "$_currentMentionQuery"');

        if (pycState.orgChartListStatus != PycStatus.loaded || pycState.orgChartList.isEmpty) {
          log('Fetching orgchart data when typing @');
          context.read<PycBloc>().add(FetchOrgChartList(
                requestModel: ChartFilterRequestModel(orgchart: [], infoType: "user", condition: []),
              ));
        }
      }
    } else {
      setState(() {
        _showMentionList = false;
      });
    }
  }

  void _onReplyFocusChange() {
    setState(() {});
  }

  void _onReplyTextChanged() {
    final text = _replyController.text;
    setState(() {});

    if (!_canUseMention) {
      setState(() {
        _showMentionList = false;
      });
      return;
    }

    if (text.isNotEmpty && text.contains('@')) {
      final lastAtIndex = text.lastIndexOf('@');
      final cursorPosition = _replyController.selection.baseOffset;

      if (lastAtIndex >= 0 && cursorPosition > lastAtIndex) {
        bool isPartOfExistingMention = false;
        for (var mention in _replyMentions) {
          if (lastAtIndex >= mention.start && lastAtIndex < mention.start + mention.length) {
            isPartOfExistingMention = true;
            break;
          }
        }

        if (!isPartOfExistingMention) {
          setState(() {
            _showMentionList = true;

            if (lastAtIndex == text.length - 1) {
              _currentMentionQuery = '';
            } else {
              final query = text.substring(lastAtIndex + 1, cursorPosition);
              _currentMentionQuery = query;
            }
          });

          final pycState = context.read<PycBloc>().state;
          if (pycState.orgChartListStatus != PycStatus.loaded || pycState.orgChartList.isEmpty) {
            context.read<PycBloc>().add(FetchOrgChartList(
                  requestModel: ChartFilterRequestModel(orgchart: [], infoType: "user", condition: []),
                ));
          }
        }
      }
    } else {
      setState(() {
        _showMentionList = false;
      });
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _controller.removeListener(_onTextChanged);
    _replyFocusNode.removeListener(_onReplyFocusChange);
    _replyController.removeListener(_onReplyTextChanged);
    _focusNode.dispose();
    _controller.dispose();
    _replyController.dispose();
    _replyFocusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {});
  }

  void _sendMessage() {
    final text = _controller.text.trim();
    if (text.isNotEmpty || _selectedFiles.isNotEmpty || (_isEditing && _existingFiles!.isNotEmpty)) {
      String processedContent =
          widget.isAdditionalContent ? text : CommentUtils.convertTextToHtml(text, mentionList: _mentions);

      List<Map<String, dynamic>> fileAttachments = [];
      if (_selectedFiles.isNotEmpty) {
        for (var file in _selectedFiles) {
          if (file.path != null) {
            fileAttachments.add({
              'fileName': file.name,
              'fileSize': file.size,
              'filePath': file.path!,
            });
          }
        }
      }

      if (_isEditing && _existingFiles != null && _existingFiles!.isNotEmpty) {
        for (var file in _existingFiles!) {
          if (file['id'] != null) {
            fileAttachments.add({
              'fileId': file['id'],
              'fileName': file['fileName'] ?? '',
              'fileSize': file['fileSize'] ?? 0,
            });
          }
        }
      }

      context.read<ConsultationOpinionBloc>().add(
            AddConsultationOpinion(
              procInstId: widget.ticketProcId?.toString() ?? '',
              ticketId:
                  widget.ticketId is int ? widget.ticketId : int.tryParse(widget.ticketId?.toString() ?? '0') ?? 0,
              content: processedContent,
              isPrivate: _isPrivate,
              files: fileAttachments.isEmpty ? null : fileAttachments,
              isAdditionalRequest: widget.isAdditionalContent,
              isCreateUserAdditionalRequest: false,
              discusId: _editingCommentId,
              idFilesDelete: _removedFileIds.isEmpty ? null : _removedFileIds,
            ),
          );

      setState(() {
        _isEditing = false;
        _editingCommentId = null;
        _existingFiles = [];
        _selectedFiles = [];
        _removedFileIds = [];
      });

      _controller.clear();
      FocusScope.of(context).unfocus();
    }
  }

  void _insertMention(MentionUserModel user) {
    if (!_canUseMention) return;

    final controller = _replyFocusNode.hasFocus ? _replyController : _controller;
    final mentionsList = _replyFocusNode.hasFocus ? _replyMentions : _mentions;

    final text = controller.text;
    final cursorPosition = controller.selection.baseOffset;

    int atIndex = -1;
    for (int i = cursorPosition - 1; i >= 0; i--) {
      if (text[i] == '@') {
        bool isPartOfExistingMention = false;
        for (var mention in mentionsList) {
          if (i >= mention.start && i < mention.start + mention.length) {
            isPartOfExistingMention = true;
            break;
          }
        }

        if (!isPartOfExistingMention) {
          atIndex = i;
          break;
        }
      }
    }

    if (atIndex == -1) return;

    final before = text.substring(0, atIndex);
    final mentionText = '@${user.username} - ${user.fullname} - ${user.title}';
    final after = text.substring(cursorPosition);

    final newText = '$before$mentionText $after';
    controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: before.length + mentionText.length + 1),
    );

    mentionsList.add(MentionRange(before.length, mentionText.length, mentionText));

    setState(() {
      _showMentionList = false;
    });
  }

  Future<void> _pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: _supportedExtensions,
        allowMultiple: true,
      );

      if (result != null) {
        setState(() {
          final validFiles = result.files.where((file) {
            final ext = path.extension(file.name).toLowerCase().replaceAll('.', '');
            return _supportedExtensions.contains(ext);
          }).toList();

          _selectedFiles.addAll(validFiles);
        });
      }
    } catch (e) {
      log('Error picking files: $e');
    }
  }

  void _removeSelectedFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
    });
  }

  void _removeExistingFile(int fileId) {
    setState(() {
      _removedFileIds.add(fileId);
      _existingFiles!.removeWhere((file) => file['id'] == fileId);
    });
  }

  void _showDeleteConfirmDialog(int? commentId) {
    if (commentId == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomDialog(
          backgroundColor: getColorSkin().white,
          title: 'Xác nhận xóa',
          content: 'Bạn có chắc chắn muốn xóa bình luận này không?',
          cancelButtonText: 'Không',
          confirmButtonText: 'Xóa',
          cancelButtonColor: getColorSkin().ink3,
          confirmButtonColor: getColorSkin().red,
          cancelButtonTextStyle: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().white),
          confirmButtonTextStyle: getTypoSkin().bodyRegular14.copyWith(color: getColorSkin().white),
          onCancel: () {
            Navigator.of(context).pop();
          },
          onConfirm: () {
            Navigator.of(context).pop();
            context.read<ConsultationOpinionBloc>().add(
                  DeleteConsultationOpinion(
                    discusId: commentId,
                    procInstId: widget.ticketProcId?.toString() ?? '',
                    ticketId: widget.ticketId is int
                        ? widget.ticketId
                        : int.tryParse(widget.ticketId?.toString() ?? '0') ?? 0,
                  ),
                );
          },
        );
      },
    );
  }

  void _startEditing(int commentId, String htmlContent, bool isPrivate, List? files) {
    if (!_canEditComment) return;

    _isInitialTextSet = true;

    final textContent = CommentUtils.convertHtmlToText(htmlContent);
    _mentions = CommentUtils.extractMentionsFromHtml(htmlContent);

    setState(() {
      _isEditing = true;
      _editingCommentId = commentId;
      _isPrivate = isPrivate;
      _controller.text = textContent;
      _existingFiles = files != null ? List<Map<String, dynamic>>.from(files) : [];
      _removedFileIds = [];
      _selectedFiles = [];
    });

    Future.delayed(Duration(milliseconds: 100), () {
      setState(() {
        _isInitialTextSet = false;
      });
    });

    FocusScope.of(context).requestFocus(_focusNode);
  }

  void _startReplying(int commentId, String username) {
    setState(() {
      _replyingToCommentId = commentId;
      _replyMentions.clear();

      if (_canUseMention) {
        String fullUserInfo = username;
        if (!username.contains('-')) {
          final state = context.read<TicketProcessDetailBloc>().state;
          if (state is UserTaskInfoLoaded) {
            for (final task in state.userTaskInfo.data ?? []) {
              for (final assignee in task.lstAssigneeInfo) {
                if (assignee.username == username) {
                  fullUserInfo = '${assignee.username} - ${assignee.fullName} - ${assignee.title}';
                  break;
                }
              }
            }
          }
        }

        final mentionText = '@$fullUserInfo';
        _replyController.text = '$mentionText ';

        _replyMentions.add(MentionRange(0, mentionText.length, mentionText));
      } else {
        _replyController.text = '';
      }

      _replySelectedFiles = [];
      _isReplyPrivate = _isPrivate;
    });

    Future.delayed(const Duration(milliseconds: 100), () {
      _replyFocusNode.requestFocus();
    });
  }

  Future<void> _pickReplyFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: _supportedExtensions,
        allowMultiple: true,
      );

      if (result != null) {
        setState(() {
          final validFiles = result.files.where((file) {
            final ext = path.extension(file.name).toLowerCase().replaceAll('.', '');
            return _supportedExtensions.contains(ext);
          }).toList();

          _replySelectedFiles.addAll(validFiles);
        });
      }
    } catch (e) {
      log('Error picking files for reply: $e');
    }
  }

  void _sendReply() {
    final text = _replyController.text.trim();
    if (text.isNotEmpty || _replySelectedFiles.isNotEmpty) {
      String processedContent = CommentUtils.convertTextToHtml(text, mentionList: _replyMentions);

      List<Map<String, dynamic>>? fileAttachments;
      if (_replySelectedFiles.isNotEmpty) {
        fileAttachments = [];
        for (var file in _replySelectedFiles) {
          if (file.path != null) {
            fileAttachments.add({
              'fileName': file.name,
              'fileSize': file.size,
              'filePath': file.path!,
            });
          }
        }
      }

      context.read<ConsultationOpinionBloc>().add(
            AddConsultationOpinion(
              procInstId: widget.ticketProcId?.toString() ?? '',
              ticketId:
                  widget.ticketId is int ? widget.ticketId : int.tryParse(widget.ticketId?.toString() ?? '0') ?? 0,
              content: processedContent,
              isPrivate: _isReplyPrivate,
              files: fileAttachments,
              isAdditionalRequest: widget.isAdditionalContent,
              isCreateUserAdditionalRequest: false,
              groupId: _replyingToCommentId,
            ),
          );

      setState(() {
        _replyingToCommentId = null;
        _replyController.clear();
        _replySelectedFiles = [];
        _replyMentions.clear();
      });
    }
  }

  void _showFileOptions(Map<String, dynamic> file) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return FileOptionsDialog(
          file: file,
          onView: () {
            Navigator.pop(context);
            _viewFile(file);
          },
          onDownload: () {
            Navigator.pop(context);
            _downloadFile(file);
          },
        );
      },
    );
  }

  void _viewFile(Map<String, dynamic> file) async {
    final fileName = file['fileName'] ?? '';
    final downloadUrl = file['downloadUrls'] ?? '';
    final extension = fileName.split('.').last.toLowerCase();

    final document = document_model.DocumentModel(
      id: file['id']?.toString() ?? '0',
      name: fileName,
      url: downloadUrl,
      type: _getFileTypeFromExtension(extension),
      size: file['fileSize']?.toString() ?? '0',
      extension: extension,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FileViewer(
          item: document,
        ),
      ),
    );
  }

  document_model.FileType _getFileTypeFromExtension(String extension) {
    if (['jpg', 'jpeg', 'png', 'jfif'].contains(extension)) {
      return document_model.FileType.png;
    } else if (['mp4', 'avi', 'mov', 'wmv'].contains(extension)) {
      return document_model.FileType.defaulted;
    } else if (['doc', 'docx'].contains(extension)) {
      return document_model.FileType.doc;
    } else if (['xls', 'xlsx'].contains(extension)) {
      return document_model.FileType.xls;
    } else if (extension == 'pdf') {
      return document_model.FileType.pdf;
    } else if (['ppt', 'pptx'].contains(extension)) {
      return document_model.FileType.defaulted;
    } else {
      return document_model.FileType.defaulted;
    }
  }

  void _downloadFile(Map<String, dynamic> file) async {
    final fileName = file['fileName'] ?? '';
    final downloadUrl = file['downloadUrls'] ?? '';
    final extension = fileName.split('.').last.toLowerCase();

    final document = document_model.DocumentModel(
      id: file['id']?.toString() ?? '0',
      name: fileName,
      url: downloadUrl,
      type: _getFileTypeFromExtension(extension),
      size: file['fileSize']?.toString() ?? '0',
      extension: extension,
    );

    // Download file
    await document.downloadFile();
  }

  Widget _buildReplyInput({String? username}) {
    // Create a properly structured fullUserInfo list
    List<Map<String, dynamic>> fullUserInfoList = [];

    if (username != null && username.isNotEmpty) {
      // Parse the username if it's in the format "username - fullname - title"
      List<String> parts = username.split(' - ');
      String parsedUsername = parts.isNotEmpty ? parts[0].replaceAll('@', '') : username.replaceAll('@', '');
      String fullname = parts.length > 1 ? parts[1] : '';
      String title = parts.length > 2 ? parts[2] : '';

      // Add the user to the fullUserInfo list
      fullUserInfoList.add(
          {'id': parsedUsername, 'name': fullname, 'username': parsedUsername, 'title': title, 'fullname': fullname});
    }

    return CommentInputWidget(
      controller: _replyController,
      focusNode: _replyFocusNode,
      mentions: _replyMentions,
      isPrivate: _isReplyPrivate,
      attachments: _replySelectedFiles,
      fullUserInfo: fullUserInfoList, // Pass the properly structured list
      onPrivacyToggle: (value) {
        setState(() {
          _isReplyPrivate = value;
        });
      },
      onPickFiles: _pickReplyFiles,
      onSend: _sendReply,
      onRemoveAttachment: (index) {
        setState(() {
          _replySelectedFiles.removeAt(index);
        });
      },
      onRemoveExistingFile: (_) {}, // Not used for replies
    );
  }

  Widget _buildCommentShimmerList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: 5,
      itemBuilder: (context, index) {
        Widget? divider;
        if (index > 0) {
          divider = Padding(
            padding: EdgeInsets.only(bottom: 16.h, left: 40.w),
            child: CustomDivider(
              color: getColorSkin().grey4Background,
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (divider != null) divider,
            _buildCommentShimmer(),
          ],
        );
      },
    );
  }

  Widget _buildCommentShimmer() {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32.w,
            height: 32.h,
            decoration: BoxDecoration(
              color: getColorSkin().white,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 16.h,
                        decoration: BoxDecoration(
                          color: getColorSkin().white,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Container(
                      width: 80.w,
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: getColorSkin().white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Container(
                  height: 12.h,
                  margin: EdgeInsets.only(bottom: 4.h),
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                Container(
                  height: 12.h,
                  margin: EdgeInsets.only(bottom: 4.h),
                  width: MediaQuery.of(context).size.width * 0.7,
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                Container(
                  height: 12.h,
                  width: MediaQuery.of(context).size.width * 0.4,
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Container(
                      width: 40.w,
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: getColorSkin().white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Container(
                      width: 40.w,
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: getColorSkin().white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Box box = Hive.box('authentication');
    final username = box.get('username', defaultValue: '');

    final pycState = context.watch<PycBloc>().state;

    if (_showMentionList && _canUseMention) {
      MentionSuggestionList(
        pycState: context.read<PycBloc>().state,
        currentQuery: _currentMentionQuery,
        onSelectUser: _insertMention,
      );
    }

    if (_showMentionList &&
        (pycState.orgChartListStatus == PycStatus.initial ||
            (pycState.orgChartListStatus == PycStatus.loaded && pycState.orgChartList.isEmpty))) {
      log('Calling API in build due to state: ${pycState.orgChartListStatus}');
      context.read<PycBloc>().add(FetchOrgChartList(
            requestModel: ChartFilterRequestModel(orgchart: [], infoType: "user", condition: []),
          ));
    }

    final isTablet = DeviceUtils.isTablet;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        automaticallyImplyLeading: isTablet ? false : true,
        centerTitle: true,
        title: widget.isAdditionalContent ? 'Nội dung bổ sung' : 'Ý kiến tham vấn',
        backgroundColor: getColorSkin().lightBlue,
        titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
        textColor: getColorSkin().black,
        actionsPadding: EdgeInsets.only(right: 18.w),
        actions: [
          if (isTablet)
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: SvgPicture.asset(
                StringImage.ic_close,
                width: 24.w,
                height: 24.h,
                colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
              ),
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(),
            )
        ],
      ),
      body: RefreshIndicator(
        backgroundColor: getColorSkin().white,
        color: getColorSkin().primaryBlue,
        onRefresh: _loadInitialData,
        child: Column(
          children: [
            BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
              builder: (context, state) {
                if (state is UserTaskInfoLoaded) {
                   for (final task in state.userTaskInfo.data ?? []) {
                    for (final assignee in task.lstAssigneeInfo) {
                      log('[👤 UserTask] username: ${assignee.username}, fullName: ${assignee.fullName}, title: ${assignee.title}');
                    }
                  }
                }
                return const SizedBox.shrink();
              },
            ),
            Expanded(
              child: BlocBuilder<ConsultationOpinionBloc, ConsultationOpinionState>(
                builder: (context, state) {
                  if (state.status == ServiceStatus.loading) {
                    return Center(
                        child: AppConstraint.buildShimmer(
                      child: _buildCommentShimmerList(),
                    ));
                  }

                  if (state.status == ServiceStatus.failure) {
                    return Center(child: Text("Lỗi: ${state.errorMessage}"));
                  }

                  final discussions = state.opinions;

                  if (discussions.isEmpty) {
                    return Center(
                        child: Text(
                            widget.isAdditionalContent ? "Không có nội dung bổ sung" : "Không có dữ liệu tham vấn"));
                  }

                  final visibleDiscussions = state.opinionsResponse.getVisibleContent(username);

                  if (visibleDiscussions.isEmpty) {
                    return Center(
                        child: Text(
                            widget.isAdditionalContent ? "Không có nội dung bổ sung" : "Không có dữ liệu tham vấn"));
                  }

                  return BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
                    builder: (context, detailState) {
                      return ListView.builder(
                        padding: EdgeInsets.all(16.w),
                        itemCount: visibleDiscussions.length,
                        itemBuilder: (context, index) {
                          final item = visibleDiscussions[index];
                          final typeDiscussion = item['typeDiscussion'] ?? 1;
                          final createdUser = item['createdUser'] ?? '';
                          final commentId = item['id'];

                          // Find username details
                         String displayUsername = createdUser;
                          if (_usernameCache.containsKey(createdUser)) {
                            displayUsername = _usernameCache[createdUser]!;
                          } else if (detailState is UserTaskInfoLoaded) {
                            final matchedAssignee = detailState.userTaskInfo.data
                                ?.expand((task) => task.lstAssigneeInfo ?? [])
                                .firstWhere(
                                  (a) => a.username.trim() == createdUser.trim(),
                                  orElse: () => null,
                                );

                            if (matchedAssignee != null) {
                              final fullInfo = '${matchedAssignee.username} - ${matchedAssignee.fullName} - ${matchedAssignee.title}';
                              _usernameCache[createdUser] = fullInfo;
                              displayUsername = fullInfo;
                            }
                          }
                          log('[🧪 displayUsername] createdUser: $createdUser => displayUsername: $displayUsername');

                          Widget? divider;
                          if (index > 0) {
                            divider = Padding(
                              padding: EdgeInsets.only(bottom: 16.h, left: 40.w),
                              child: CustomDivider(
                                color: getColorSkin().grey4Background,
                              ),
                            );
                          }

                          // Process replies
                          List<Widget>? replyWidgets;
                          if (item['discussionReplys'] != null) {
                            replyWidgets = (item['discussionReplys'] as List).map((reply) {
                              final replyCreatedUser = reply['createdUser'] ?? '';

                              // Find username details for reply
                              String replyDisplayUsername = replyCreatedUser;
                              if (detailState is UserTaskInfoLoaded) {
                                for (final task in detailState.userTaskInfo.data ?? []) {
                                  for (final assignee in task.lstAssigneeInfo) {
                                    if (assignee.username == replyCreatedUser) {
                                      replyDisplayUsername =
                                          '${assignee.username} - ${assignee.fullName} - ${assignee.title}';
                                      break;
                                    }
                                  }
                                }
                              }

                              // Create comment model for reply
                              final replyComment = CommentModel(
                                id: reply['id'] ?? 0,
                                username: replyDisplayUsername,
                                createdUser: replyCreatedUser,
                                content: reply['content'] ?? '',
                                createdDate: reply['createdDate'],
                                typeDiscussion: typeDiscussion,
                                files: reply['fileResponse'],
                                formattedDate: CommentUtils.formatDate(reply['createdDate']),
                              );

                              return CommentWidget(
                                comment: replyComment,
                                currentUsername: username,
                                isReply: true,
                                onReply: (_) {}, // No reply to replies
                                onEdit: (id, content, isPrivate, files) {
                                  _startEditing(id, content, isPrivate, files);
                                },
                                onDelete: (id) {
                                  if (_canDeleteComment) {
                                    _showDeleteConfirmDialog(id);
                                  }
                                },
                                showDeleteButton: _canDeleteComment,
                                showEditButton: _canEditComment,
                              );
                            }).toList();
                          }

                          // Create comment model for main comment
                          final comment = CommentModel(
                            id: commentId ?? 0,
                            username: displayUsername,
                            createdUser: createdUser,
                            content: item['content'] ?? '',
                            createdDate: item['createdDate'],
                            typeDiscussion: typeDiscussion,
                            files: item['fileResponse'],
                            formattedDate: CommentUtils.formatDate(item['createdDate']),
                          );

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (divider != null) divider,
                              CommentWidget(
                                comment: comment,
                                currentUsername: username,
                                replies: replyWidgets,
                                showReplyInput: _replyingToCommentId == commentId,
                                replyInputWidget: _replyingToCommentId == commentId
                                    ? _buildReplyInput(username: displayUsername)
                                    : null,
                                onReply: (id) {
                                  _startReplying(id, createdUser);
                                },
                                onEdit: (id, content, isPrivate, files) {
                                  _startEditing(id, content, isPrivate, files);
                                },
                                onDelete: (id) {
                                  if (_canDeleteComment) {
                                    _showDeleteConfirmDialog(id);
                                  }
                                },
                                showDeleteButton: _canDeleteComment,
                                showEditButton: _canEditComment,
                              ),
                            ],
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
            if (_replyingToCommentId == null)
              SafeArea(
                child: CommentInputWidget(
                  controller: _controller,
                  focusNode: _focusNode,
                  mentions: _mentions,
                  isPrivate: _isPrivate,
                  attachments: _selectedFiles,
                  isEditing: _isEditing,
                  existingFiles: _existingFiles,
                  showExistingAttachments: _isEditing && _existingFiles != null && _existingFiles!.isNotEmpty,
                  onPrivacyToggle: (value) {
                    setState(() {
                      _isPrivate = value;
                    });
                  },
                  onPickFiles: _pickFiles,
                  onSend: _sendMessage,
                  onRemoveAttachment: _removeSelectedFile,
                  onRemoveExistingFile: _removeExistingFile,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
