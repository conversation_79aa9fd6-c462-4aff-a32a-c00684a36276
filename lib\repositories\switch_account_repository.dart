import 'dart:convert';
import 'package:eapprove/models/switch_account/switch_account_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/utils/logger.dart';

class SwitchAccountRepository {
  final ApiService apiService;
  final logger = LoggerConfig.logger;

  SwitchAccountRepository({required this.apiService});

  Future<SwitchAccountResponse?> switchAccount(String username) async {
    try {
      logger.d('Switching to account: $username');

      await Future.delayed(const Duration(milliseconds: 500));

      final jsonBody = {"username": username};

      final response = await apiService.post(
          '/v1/auth/users/switchAccount', jsonBody,
          customBaseUrl: 'https://uat-api-sso.datxanh.com.vn/');

      logger.d('Switch account API status code: ${response.statusCode}');

      if (response.statusCode >= 400) {
        logger.e(
            'Server error: ${response.statusCode} - ${response.reasonPhrase}');
      }

      String responseBody = utf8.decode(response.bodyBytes);
      logger.d('Switch account response length: ${responseBody.length}');

      if (responseBody.length > 200) {
        logger.d(
            'Switch account response preview: ${responseBody.substring(0, 200)}...');
      } else {
        logger.d('Switch account response: $responseBody');
      }

      final jsonData = jsonDecode(responseBody);

      if (jsonData is! Map<String, dynamic>) {
        throw Exception('Invalid JSON format');
      }

      final switchAccountResponse = SwitchAccountResponse.fromJson(jsonData);

      return switchAccountResponse;
    } catch (e, stackTrace) {
      logger.e('Error switching account: ${e.toString()}',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }
}
