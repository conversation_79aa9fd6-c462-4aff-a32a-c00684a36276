import 'package:flutter/material.dart';

class CustomTextField extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final String? hintText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;

  const CustomTextField(
      {super.key,
      required this.label,
      required this.controller,
      this.hintText,
      this.prefixIcon,
      this.suffixIcon});

  @override
  // ignore: library_private_types_in_public_api
  _CustomTextFieldState createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  final FocusNode _focusNode = FocusNode();
  bool isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        isFocused = _focusNode.hasFocus || widget.controller.text.isNotEmpty;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 56, // Fix height của TextField
      child: Stack(
        children: [
          TextField(
            controller: widget.controller,
            focusNode: _focusNode,
            style: const TextStyle(fontSize: 16),
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(
                  left: widget.prefixIcon != null ? 0 : 16,
                  top: 35,
                  bottom: 5,
                  right: widget.suffixIcon != null ? 0 : 16),
              prefixIcon: widget.prefixIcon != null
                  ? Icon(widget.prefixIcon, color: Colors.grey)
                  : null,
              suffixIcon: widget.suffixIcon != null
                  ? Icon(widget.suffixIcon, color: Colors.grey)
                  : null,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue, width: 2),
              ),
            ),
          ),
          Positioned(
            left: widget.prefixIcon != null ? 50 : 16,
            top: isFocused
                ? 5
                : (56 - 16) / 2, // Điều chỉnh vị trí để giữ label đẹp
            child: InkWell(
              onTap: () {
                _focusNode
                    .requestFocus(); // Khi nhấn vào label -> focus vào TextField
              },
              child: AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 100),
                style: TextStyle(
                  fontSize: isFocused ? 12 : 16,
                  color: isFocused ? Colors.blue : Colors.black,
                ),
                child: Text(widget.label),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    widget.controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
