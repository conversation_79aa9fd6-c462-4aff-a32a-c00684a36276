import 'dart:convert';
import 'package:eapprove/services/token.dart';
import 'package:http/http.dart' as http;
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:developer' as developer;

class ApiFormService {
  final String baseUrl = 'https://uat-api-eapp.datxanh.com.vn';


   Future<dynamic> getDefaultSignature(String username) async {
    final url = '$baseUrl/customer/v1/signature/getDefaultSignature/$username';
    final token = await TokenManager().getFormattedToken();
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Authorization': token.toString(),
        'Content-Type': 'application/json',
      },
    );
    developer.log('response: ${response.body}', name: 'ApiFormService');
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    return null;
  }

   Future<dynamic> getByUsername(String username) async {
    final token = await TokenManager().getFormattedToken();
    final response = await http.get(
      Uri.parse('$baseUrl/customer/userInfo/getByUsername?username=$username'),
      headers: {
        'Authorization': token.toString(),
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    return null;
  }

   Future<dynamic> getTitleByUsername(String username) async {
    final token = await TokenManager().getFormattedToken();
    final response = await http.post(
      Uri.parse('$baseUrl/customer/userTitle/getFinalTitleByListUser'),
      body: jsonEncode([username]),
      headers: {
        'Authorization': token.toString(),
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    return null;
  }

  Future<dynamic> getLoadTemplate(dynamic request) async {
    final token = await TokenManager().getFormattedToken();
    final response = await http.post(
      Uri.parse('$baseUrl/md-service/masterData/getLoadTemplate'),
      body: jsonEncode(request),
      headers: {
        'Authorization': token.toString(),
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return jsonResponse;
    }
    return null;
  }
} 