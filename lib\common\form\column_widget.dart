import 'package:eapprove/models/form/form_item_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class ColumnWidget extends StatefulWidget {
  final FormItemInfo data;
  final Function(String, dynamic)? onChange;
  final List<Widget> columnContent;
  final VoidCallback? onDelete;
  final VoidCallback? onDuplicate;

  const ColumnWidget({
    Key? key,
    required this.data,
    this.onChange,
    required this.columnContent,
    this.onDelete,
    this.onDuplicate,
  }) : super(key: key);

  @override
  State<ColumnWidget> createState() => _ColumnWidgetState();
}

class _ColumnWidgetState extends State<ColumnWidget> {
  @override
  Widget build(BuildContext context) {
    // Don't render if display is false
    if (widget.data.display == false) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(4.r),
      ),
      margin: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Column header with actions
          _buildColumnHeader(),

          // Column content
          Padding(
            padding: EdgeInsets.all(12.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widget.columnContent,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(4.r),
          topRight: Radius.circular(4.r),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.data.label ?? 'Column',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14.sp,
            ),
          ),
          if (widget.data.readonly != true)
            Row(
              children: [
                // Duplicate column button
                if (widget.onDuplicate != null)
                  IconButton(
                    icon: Icon(Icons.copy, size: 18.sp),
                    onPressed: widget.onDuplicate,
                    tooltip: 'Duplicate Column',
                    constraints: BoxConstraints(
                      minWidth: 32.w,
                      minHeight: 32.h,
                    ),
                  ),

                // Delete column button
                if (widget.onDelete != null)
                  IconButton(
                    icon: Icon(Icons.delete, size: 18.sp, color: Colors.red),
                    onPressed: widget.onDelete,
                    tooltip: 'Delete Column',
                    constraints: BoxConstraints(
                      minWidth: 32.w,
                      minHeight: 32.h,
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }
}