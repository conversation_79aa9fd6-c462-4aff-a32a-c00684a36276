import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MoreOptionsDropdown extends StatefulWidget {
  const MoreOptionsDropdown({super.key});

  @override
  State<MoreOptionsDropdown> createState() => _MoreOptionsDropdownState();
}

class _MoreOptionsDropdownState extends State<MoreOptionsDropdown> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;

  @override
  void dispose() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    super.dispose();
  }

  void _showDropdown() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isDropdownOpen = false;
    });
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _hideDropdown();
    } else {
      _showDropdown();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    final double xPosition = offset.dx + 10.w;
    final double yPosition = offset.dy - 105.h;

    return OverlayEntry(
      builder: (context) => Positioned(
        left: xPosition,
        top: yPosition,
        width: 200,
        child: Material(
          color: Colors.white,
          elevation: 2,
          shadowColor: getColorSkin().grey3Background,
          borderRadius: BorderRadius.circular(8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('Khác'),
                onTap: () {
                  _hideDropdown();
                },
              ),
              // No divider
              ListTile(
                title: const Text('Lịch thanh toán'),
                onTap: () {
                  // Handle option selection
                  _hideDropdown();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleDropdown,
        child: Container(
          decoration: BoxDecoration(
            color: getColorSkin().green2,
            borderRadius: BorderRadius.circular(8.r),
          ),
          padding: EdgeInsets.all(10),
          child: Icon(
            Icons.more_horiz,
            color: Colors.green,
            size: 24.sp,
          ),
        ),
      ),
    );
  }
}
