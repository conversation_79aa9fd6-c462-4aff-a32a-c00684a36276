import 'package:flutter/material.dart';

class CustomColor extends ThemeExtension<CustomColor> {
  final Color? primary;
  final Color? secondary;
  final Color? background;
  final Color? surface;
  final Color? error;
  final Color? onPrimary;
  final Color? onSecondary;
  final Color? onBackground;
  final Color? onSurface;
  final Color? onError;

  const CustomColor({
    this.primary,
    this.secondary,
    this.background,
    this.surface,
    this.error,
    this.onPrimary,
    this.onSecondary,
    this.onBackground,
    this.onSurface,
    this.onError,
  });

  static CustomColor defaultInstance() {
    return const CustomColor(
      primary: Color(0xFF2196F3),
      secondary: Color(0xFF03DAC6),
      background: Color(0xFFFFFFFF),
      surface: Color(0xFFFFFFFF),
      error: Color(0xFFB00020),
      onPrimary: Color(0xFFFFFFFF),
      onSecondary: Color(0xFF000000),
      onBackground: Color(0xFF000000),
      onSurface: Color(0xFF000000),
      onError: Color(0xFFFFFFFF),
    );
  }

  @override
  ThemeExtension<CustomColor> copyWith({
    Color? primary,
    Color? secondary,
    Color? background,
    Color? surface,
    Color? error,
    Color? onPrimary,
    Color? onSecondary,
    Color? onBackground,
    Color? onSurface,
    Color? onError,
  }) {
    return CustomColor(
      primary: primary ?? this.primary,
      secondary: secondary ?? this.secondary,
      background: background ?? this.background,
      surface: surface ?? this.surface,
      error: error ?? this.error,
      onPrimary: onPrimary ?? this.onPrimary,
      onSecondary: onSecondary ?? this.onSecondary,
      onBackground: onBackground ?? this.onBackground,
      onSurface: onSurface ?? this.onSurface,
      onError: onError ?? this.onError,
    );
  }

  @override
  ThemeExtension<CustomColor> lerp(
    ThemeExtension<CustomColor>? other,
    double t,
  ) {
    if (other is! CustomColor) {
      return this;
    }
    return CustomColor(
      primary: Color.lerp(primary, other.primary, t),
      secondary: Color.lerp(secondary, other.secondary, t),
      background: Color.lerp(background, other.background, t),
      surface: Color.lerp(surface, other.surface, t),
      error: Color.lerp(error, other.error, t),
      onPrimary: Color.lerp(onPrimary, other.onPrimary, t),
      onSecondary: Color.lerp(onSecondary, other.onSecondary, t),
      onBackground: Color.lerp(onBackground, other.onBackground, t),
      onSurface: Color.lerp(onSurface, other.onSurface, t),
      onError: Color.lerp(onError, other.onError, t),
    );
  }
} 