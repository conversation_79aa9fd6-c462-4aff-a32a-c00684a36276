// Create a new file named global_navigation.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';

enum NavigationEvent {
  logout,
  backToSuperApp
}

class GlobalNavigation {
  static final GlobalNavigation _instance = GlobalNavigation._internal();
  
  factory GlobalNavigation() {
    return _instance;
  }
  
  GlobalNavigation._internal();
  
  final StreamController<NavigationEvent> _eventController = 
      StreamController<NavigationEvent>.broadcast();
  
  Stream<NavigationEvent> get eventStream => _eventController.stream;

  // Function to handle back navigation
  Future<void> handleBackNavigation(BuildContext context) async {
      if (DeviceUtils.isTablet) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        return;
      }
      Navigator.of(context, rootNavigator: true).pop();
  }

  // Function to handle logout
  Future<bool?> handleLogout(BuildContext context) async {
    final confirmed = await showCustomDialog<bool>(
      context: context,
      title: "Đăng xuất",
      content: "Bạn có chắc chắn muốn đăng xuất?",
      onCancel: () => Navigator.of(context, rootNavigator: true).pop(false),
      onConfirm: () => Navigator.of(context, rootNavigator: true).pop(true),
    );
    if (confirmed == true) {
      if (DeviceUtils.isTablet) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        return true;
      }
      return true;
    }
    return false;
  }
  
  void navigate(NavigationEvent event) {
    _eventController.add(event);
  }
  
  void dispose() {
    _eventController.close();
  }
}