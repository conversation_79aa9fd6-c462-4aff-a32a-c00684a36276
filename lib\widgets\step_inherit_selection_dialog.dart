import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_event.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_state.dart';
import 'package:eapprove/models/handle_ticket/inheritable_tasks_model.dart';
import 'package:eapprove/utils/ticket_utils.dart';
import 'package:eapprove/widgets/custom_dropdown_multi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_sdk/widgets/custom_dropdown.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';

class StepSelectionDialog extends StatefulWidget {
  final dynamic ticket;

  const StepSelectionDialog({
    super.key,
    required this.ticket,
  });

  @override
  State<StepSelectionDialog> createState() => _StepSelectionDialogState();
}

class _StepSelectionDialogState extends State<StepSelectionDialog> {
  List<InheritableTaskData> inheritableTasks = [];
  SelectItem? selectedStepToInherit;
  List<SelectItem> selectedInheritFields = [];

  // Danh sách cứng các trường kế thừa
  final List<SelectItem> inheritFieldOptions = [
    SelectItem(value: 'attached_documents', label: 'Chứng từ đính kèm (Nếu có)'),
    SelectItem(value: 'note', label: 'Ghi chú'),
    SelectItem(value: 'related_documents', label: 'Danh sách tờ trình liên quan'),
    SelectItem(value: 'assistant', label: 'Trợ lý'),
  ];

  @override
  void initState() {
    super.initState();
    _loadInheritableTasks();
  }

  void _loadInheritableTasks() {
    final procDefId = TicketUtils.getProcDefId(widget.ticket);
    final ticketId = TicketUtils.getCorrectTicketId(widget.ticket);

    if (procDefId != null && ticketId != null) {
      context.read<TicketProcessDetailBloc>().add(
            LoadInheritableTasksList(
              procDefId: procDefId.toString(),
              ticketId: ticketId.toString(),
            ),
          );
    }
  }

  List<SelectItem> get stepOptions {
    return inheritableTasks
        .where((task) => task.taskDefKey != null)
        .map((task) => SelectItem(
              value: task.taskDefKey!,
              label: task.taskDefKey!,
            ))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: getColorSkin().white,
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: getColorSkin().grey4Background,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Chọn bước kế thừa',
                    style: getTypoSkin().medium20,
                  ),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Icon(
                      Icons.close,
                      color: getColorSkin().black,
                      size: 24.sp,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: BlocListener<TicketProcessDetailBloc, TicketProcessDetailState>(
                listener: (context, state) {
                  if (state is InheritableTasksListLoaded) {
                    setState(() {
                      inheritableTasks = state.inheritableTasksResponse.data;
                      // Set default value for step dropdown
                      if (stepOptions.isNotEmpty) {
                        selectedStepToInherit = stepOptions.first;
                      }
                      // Không chọn sẵn trường nào, để user tự chọn
                      selectedInheritFields = [];
                    });
                  } else if (state is TicketProcessDetailError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: getColorSkin().red,
                      ),
                    );
                  }
                },
                child: BlocBuilder<TicketProcessDetailBloc, TicketProcessDetailState>(
                  builder: (context, state) {
                    if (state is TicketProcessDetailLoading) {
                      return Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.h),
                          child: CircularProgressIndicator(
                            color: getColorSkin().primaryBlue,
                          ),
                        ),
                      );
                    } else if (state is TicketProcessDetailError) {
                      return Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.h),
                          child: Column(
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48.sp,
                                color: getColorSkin().red,
                              ),
                              SizedBox(height: 8.h),
                              Text(
                                state.message,
                                style: getTypoSkin().bodyRegular14.copyWith(
                                      color: getColorSkin().red,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 16.h),
                              FFilledButton(
                                backgroundColor: getColorSkin().primaryBlue,
                                onPressed: _loadInheritableTasks,
                                size: FButtonSize.size40,
                                child: Text(
                                  'Thử lại',
                                  style: getTypoSkin().medium16.copyWith(
                                        color: getColorSkin().white,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return SingleChildScrollView(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'Bước kế thừa',
                                  style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                ),
                                TextSpan(
                                  text: ' *',
                                  style: getTypoSkin().medium14.copyWith(color: getColorSkin().red),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 8.h),
                          if (stepOptions.isNotEmpty)
                            CustomDropdownMenu(
                              label: 'Chọn bước kế thừa',
                              options: stepOptions,
                              showLabel: false,
                              onSelected: (item) {
                                setState(() {
                                  selectedStepToInherit = item;
                                });
                              },
                              defaultValue: selectedStepToInherit,
                            )
                          else
                            Container(
                              height: 56.h,
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              decoration: BoxDecoration(
                                border: Border.all(color: getColorSkin().ink5),
                                borderRadius: BorderRadius.circular(8.r),
                                color: getColorSkin().whiteSmoke,
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    'Không có bước nào để kế thừa',
                                    style: getTypoSkin().bodyRegular14.copyWith(
                                          color: getColorSkin().ink3,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          SizedBox(height: 24.h),

                          // Trường kế thừa multi-select
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'Trường kế thừa',
                                  style: getTypoSkin().medium14.copyWith(color: getColorSkin().ink1),
                                ),
                                TextSpan(
                                  text: ' *',
                                  style: getTypoSkin().medium14.copyWith(color: getColorSkin().red),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 8.h),
                          CustomDropdownMulti(
                            label: '',
                            placeholder: 'Chọn trường kế thừa',
                            options: inheritFieldOptions,
                            showLabel: false,
                            onSelected: (items) {
                              setState(() {
                                selectedInheritFields = items;
                              });
                            },
                            defaultValues: selectedInheritFields,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),

            // Footer buttons
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: getColorSkin().grey4Background,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: FFilledButton(
                      backgroundColor: getColorSkin().ink3,
                      onPressed: () => Navigator.of(context).pop(),
                      size: FButtonSize.size40,
                      child: Text(
                        'Hủy',
                        style: getTypoSkin().medium16.copyWith(
                              color: getColorSkin().white,
                            ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: FFilledButton(
                      backgroundColor: getColorSkin().primaryBlue,
                      onPressed: _handleNext,
                      size: FButtonSize.size40,
                      child: Text(
                        'Tiếp',
                        style: getTypoSkin().medium16.copyWith(
                              color: getColorSkin().white,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleNext() {
    if (selectedStepToInherit == null) {
      SnackbarCore.error('Vui lòng chọn bước kế thừa');
      return;
    }

    if (selectedInheritFields.isEmpty) {
      SnackbarCore.error('Vui lòng chọn ít nhất trường kế thừa');
      return;
    }
  }
}
