import 'dart:async';
import 'dart:convert';
import 'package:eapprove/models/authentication/token_model.dart';
import 'package:eapprove/utils/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

enum TokenStatus {
  unknown,
  valid,
  expired,
  invalid,
}

class TokenManager {
  static const String _tokenDataKey = 'secure_token_data';
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final Logger _logger = LoggerConfig.logger;

  final _tokenStatusController = StreamController<TokenStatus>.broadcast();
  Stream<TokenStatus> get tokenStatus => _tokenStatusController.stream;

  String? _cachedFormattedToken;
  Map<String, dynamic>? _cachedTokenData;
  bool _isCacheValid = false;

  TokenManager() {
    checkTokenValidity();
  }

  Future<void> saveTokenData(TokenData tokenData) async {
    try {
      final expiryTime =
          DateTime.now().millisecondsSinceEpoch + (tokenData.expiresIn * 1000);
      final refreshExpiryTime = DateTime.now().millisecondsSinceEpoch +
          (tokenData.refreshExpiresIn * 1000);

      final tokenMap = {
        'accessToken': tokenData.accessToken,
        'expiresIn': tokenData.expiresIn,
        'refreshExpiresIn': tokenData.refreshExpiresIn,
        'refreshToken': tokenData.refreshToken,
        'tokenType': tokenData.tokenType,
        'notBeforePolicy': tokenData.notBeforePolicy,
        'sessionState': tokenData.sessionState,
        'scope': tokenData.scope,
        'expiryTime': expiryTime,
        'refreshExpiryTime': refreshExpiryTime,
      };

      _cachedTokenData = tokenMap;
      _cachedFormattedToken = '${tokenData.tokenType} ${tokenData.accessToken}';
      _isCacheValid = true;

      _logger.i('Token cached in memory');

      final jsonString = jsonEncode(tokenMap);
      await _secureStorage.write(key: _tokenDataKey, value: jsonString);

      await Future.delayed(const Duration(milliseconds: 300));

      _logger.i('Token data saved to secure storage');
      _tokenStatusController.add(TokenStatus.valid);
    } catch (e, stackTrace) {
      _isCacheValid = false;
      _logger.e(
        'Error saving token data to secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      _tokenStatusController.add(TokenStatus.invalid);
    }
  }


  Future<Map<String, dynamic>?> getTokenData() async {
    try {
      // Return cached token data if available
      if (_isCacheValid && _cachedTokenData != null) {
        _logger.d('Using cached token data');
        return _cachedTokenData;
      }

      _logger.d('Reading token data from secure storage');
      final jsonString = await _secureStorage.read(key: _tokenDataKey);
      if (jsonString == null || jsonString.isEmpty) return null;

      // Update the cache with data from storage
      _cachedTokenData = jsonDecode(jsonString) as Map<String, dynamic>;
      _isCacheValid = true;
      return _cachedTokenData;
    } catch (e, stackTrace) {
      _isCacheValid = false;
      _logger.e(
        'Error reading token data from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<void> checkTokenValidity() async {
    try {
      final tokenData = await getTokenData();
      if (tokenData == null) {
        _isCacheValid = false;
        _tokenStatusController.add(TokenStatus.invalid);
        return;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final tokenExpiry = tokenData['expiryTime'] as int;

      if (now > tokenExpiry) {
        _isCacheValid = false;
        _cachedFormattedToken = null;
        _tokenStatusController.add(TokenStatus.expired);
      } else {
        if (_cachedFormattedToken == null) {
          final tokenType = tokenData['tokenType'] as String;
          final accessToken = tokenData['accessToken'] as String;
          _cachedFormattedToken = '$tokenType $accessToken';
        }
        _tokenStatusController.add(TokenStatus.valid);
      }
    } catch (e, stackTrace) {
      _isCacheValid = false;
      _logger.e(
        'Error checking token validity: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      _tokenStatusController.add(TokenStatus.invalid);
    }
  }

  Future<String?> getFormattedToken() async {
    try {
      if (_isCacheValid && _cachedFormattedToken != null) {
        _logger.d('Using cached formatted token');
        return _cachedFormattedToken;
      }

      _logger.d('Generating formatted token from storage data');
      final tokenData = await getTokenData();
      if (tokenData == null) {
        _isCacheValid = false;
        _tokenStatusController.add(TokenStatus.invalid);
        return null;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final tokenExpiry = tokenData['expiryTime'] as int;

      if (now > tokenExpiry) {
        _isCacheValid = false;
        _cachedFormattedToken = null;
        _tokenStatusController.add(TokenStatus.expired);
        return null;
      }

      final tokenType = tokenData['tokenType'] as String;
      final accessToken = tokenData['accessToken'] as String;
      _cachedFormattedToken = '$tokenType $accessToken';
      _isCacheValid = true;
      _tokenStatusController.add(TokenStatus.valid);
      return _cachedFormattedToken;
    } catch (e, stackTrace) {
      _isCacheValid = false;
      _cachedFormattedToken = null;
      _logger.e(
        'Error getting formatted token: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
      _tokenStatusController.add(TokenStatus.invalid);
      return null;
    }
  }

  Future<void> clearTokenData() async {
    try {
      _cachedFormattedToken = null;
      _cachedTokenData = null;
      _isCacheValid = false;

      await _secureStorage.delete(key: _tokenDataKey);
      _logger.i('Token data cleared from memory cache and secure storage');
      _tokenStatusController.add(TokenStatus.invalid);
    } catch (e, stackTrace) {
      _logger.e(
        'Error clearing token data from secure storage: ${e.toString()}',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  void refreshCachedToken() {
    _isCacheValid = false;
    getFormattedToken();
  }

  void dispose() {
    _tokenStatusController.close();
  }
}
