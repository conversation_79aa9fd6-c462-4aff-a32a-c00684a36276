import 'dart:convert';
import 'dart:developer' as developer;

import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/models/form/individual_info_request_model.dart';
import 'package:eapprove/models/form/md_service_request_model.dart';

class ApiExpression {
  // Item với id fieldItem_375f04d0-8055-4ef5-b153-e56565f88ddb
  static Map<String, dynamic> fieldItem = {
    "id": "fieldItem_375f04d0-8055-4ef5-b153-e56565f88ddb",
    "name": "slt_idChiNhanh",
    "apiExpression": [
      {
        "input":
            "{ \"search\": \"\", \"page\": \"1\", \"limit\": \"9999999\", \"listType\": [ \"import\" ], \"sortBy\": \"id\", \"sortType\": \"ASC\"}",
        "expression": "=",
        "condition": "and",
        "fieldType": "",
        "type": "formData",
        "defaultValue": "",
        "objectInput": [
          {
            "type": "paragraph",
            "children": [
              {"text": "{"}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"search\": \"\","}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"page\": \"1\","}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"limit\": \"9999999\","}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"listType\": ["}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"import\""}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " ],"}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"sortBy\": \"id\","}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": " \"sortType\": \"ASC\""}
            ]
          },
          {
            "type": "paragraph",
            "children": [
              {"text": "}"}
            ]
          }
        ]
      }
    ]
  };

  // Điều kiện tìm kiếm theo username
  static Map<String, dynamic> getUserByUsername = {
    "orgchart": [],
    "infoType": "user",
    "condition": [
      {
        "expression": "and",
        "filterType": "user",
        "filterField": "username",
        "operator": "=",
        "value": ""
      }
    ]
  };

  // Điều kiện tìm kiếm theo chartId
  static Map<String, dynamic> getUserByChartId = {
    "orgchart": [],
    "infoType": "user",
    "condition": [
      {
        "expression": "and",
        "filterType": "user",
        "filterField": "chartId",
        "operator": "=",
        "value": "1632"
      }
    ]
  };

  /// Hàm xử lý apiExpression và trả về input cho API
  ///
  /// Trả về Map chứa thông tin input cho API
  static Map<String, dynamic> getFieldItemInput() {
    Map<String, dynamic> result = {};

    var apiExpression = fieldItem['apiExpression'][0];
    String input = apiExpression['input'] as String;
    String expression = apiExpression['expression'] as String;
    String condition = apiExpression['condition'] as String;
    String fieldType = apiExpression['fieldType'] as String;

    // Parse input string to JSON
    Map<String, dynamic> inputData = {
      'search': '',
      'page': '1',
      'limit': '9999999',
      'listType': ['import'],
      'sortBy': 'id',
      'sortType': 'ASC'
    };

    result = {
      'input': inputData,
      'expression': expression,
      'condition': condition,
      'type': fieldType
    };

    return result;
  }

  /// Hàm xử lý điều kiện tìm kiếm theo username
  ///
  /// [username] là username cần tìm kiếm
  ///
  /// Trả về Map chứa thông tin điều kiện tìm kiếm
  static Map<String, dynamic> getUserByUsernameInput(String username) {
    Map<String, dynamic> result = Map.from(getUserByUsername);
    result['condition'][0]['value'] = username;
    return result;
  }

  /// Hàm xử lý điều kiện tìm kiếm theo chartId
  ///
  /// [chartId] là chartId cần tìm kiếm
  ///
  /// Trả về Map chứa thông tin điều kiện tìm kiếm
  static Map<String, dynamic> getUserByChartIdInput(String chartId) {
    Map<String, dynamic> result = Map.from(getUserByChartId);
    result['condition'][0]['value'] = chartId;
    return result;
  }

  /// Hàm xử lý apiExpression cho optionType = "api_link"
  ///
  /// [apiExpression] là apiExpression từ form
  ///
  /// Trả về Map chứa thông tin input cho API
  static Map<String, dynamic> getApiLinkInput(List<dynamic> apiExpression,
      FormStateManager stateManager, String? parentName) {
    // Tìm item có type là formData
    developer.log('apiExpression: $apiExpression', name: 'getApiLinkInput');
    var formDataItem = apiExpression.firstWhere(
      (item) => item['type'] == 'formData',
      orElse: () => Map<String, Object>.from({}),
    );

    // Nếu có formData thì xử lý input của formData
    if (formDataItem.isNotEmpty) {
      developer.log('formDataItem: $formDataItem', name: 'getApiLinkInput');
      String input = formDataItem["input"] as String? ?? "";
      String defaultValue = formDataItem["defaultValue"] as String? ?? "";

      if (input.isNotEmpty) {
        try {
          // Parse JSON input và đảm bảo kiểu Map<String, dynamic>
          Map<String, dynamic> inputData = Map<String, dynamic>.from(
              json.decode(input) as Map<dynamic, dynamic>);
          developer.log('inputData: $inputData', name: 'getApiLinkInput');

          // Hàm đệ quy để xử lý các trường tham chiếu (@)
          dynamic processFieldReferences(dynamic value) {
            developer.log('processFieldReferences: $value',
                name: 'getApiLinkInput');
            if (value is String) {
              // Xử lý chuỗi có thể chứa nhiều tham chiếu
              if (value.contains('@')) {
                // Tìm tất cả các tham chiếu trong chuỗi
                final regex = RegExp(r'@([^"\s]+)');
                String result = value;

                regex.allMatches(value).forEach((match) {
                  final fieldName = match.group(1)!;
                  final fieldValue = stateManager
                          .getFieldValueByName(fieldName, parentName)
                          ?.toString() ??
                      "";
                  result = result.replaceAll('@$fieldName', fieldValue);
                });
                developer.log('resultresultresult: $result',
                    name: 'getApiLinkInput');
                return result;
              }
              return value;
            } else if (value is Map) {
              // Đảm bảo kiểu Map<String, dynamic>
              return Map<String, dynamic>.from(value.map((key, val) =>
                  MapEntry(key.toString(), processFieldReferences(val))));
            } else if (value is List) {
              return value.map((item) => processFieldReferences(item)).toList();
            }
            return value;
          }

          // Xử lý tất cả các trường tham chiếu trong inputData
          final processedData = processFieldReferences(inputData);

          // Nếu sau khi xử lý mà không có giá trị nào được thay thế, sử dụng defaultValue
          if (processedData.toString() == inputData.toString() &&
              defaultValue.isNotEmpty) {
            try {
              return Map<String, dynamic>.from(
                  json.decode(defaultValue) as Map<dynamic, dynamic>);
            } catch (e) {
              developer.log('Error parsing defaultValue: $e',
                  name: 'getApiLinkInput');
            }
          }

          // Đảm bảo kiểu trả về là Map<String, dynamic>
          return Map<String, dynamic>.from(processedData);
        } catch (e) {
          developer.log('Error parsing JSON input: $e',
              name: 'getApiLinkInput');
          // Nếu không parse được input, thử parse defaultValue
          if (defaultValue.isNotEmpty) {
            try {
              return Map<String, dynamic>.from(
                  json.decode(defaultValue) as Map<dynamic, dynamic>);
            } catch (e) {
              developer.log('Error parsing defaultValue: $e',
                  name: 'getApiLinkInput');
            }
          }
          return {'input': input};
        }
      }
    }

    // Nếu không có formData hoặc input rỗng, xử lý các item khác
    Map<String, dynamic> result = {};
    for (var item in apiExpression) {
      if (item['type']?.toString() != 'formData') {
        String input = item["input"] as String? ?? "";
        String defaultValue = item["defaultValue"] as String? ?? "";

        if (input.startsWith('@')) {
          final fieldName = input.substring(1);
          developer.log('fieldName: $fieldName $parentName',
              name: 'getApiLinkInput');

          developer.log('defaultValuedefaultValue: ${defaultValue}',
              name: 'getApiLinkInput');


          input = stateManager
                  .getFieldValueByName(fieldName, parentName)
                  ?.toString() ?? "";

          developer.log('inputinputinput: $input', name: 'getApiLinkInput');
        }

        result[item['type']?.toString() ?? ''] = input.isNotEmpty
            ? input
            : defaultValue.isNotEmpty
                ? defaultValue
                : "";
      }
    }
    return result;
  }

  /// Hàm xử lý apiExpression cho optionType = "orgChart"
  ///
  /// [apiExpression] là apiExpression từ form
  ///
  /// Trả về Map chứa thông tin input cho API
  static Map<String, dynamic> getOrgChartInput(List<dynamic> apiExpression) {
    Map<String, dynamic> result = {
      "orgchart": [],
      "infoType": "user",
      "condition": []
    };

    for (var expression in apiExpression) {
      String input = expression['input'] as String;
      String expressionType = expression['expression'] as String;
      String condition = expression['condition'] as String;
      String fieldType = expression['fieldType'] as String;

      // Thêm điều kiện vào mảng condition
      result['condition'].add({
        "expression": condition,
        "filterType": "user",
        "filterField": fieldType,
        "operator": expressionType,
        "value": input
      });
    }

    return result;
  }

  /// Hàm xử lý apiExpression cho optionType = "masterData"
  ///
  /// [apiExpression] là apiExpression từ form
  ///
  /// Trả về Map chứa thông tin input cho API
  static Map<String, dynamic> getMasterDataInput(List<dynamic> apiExpression) {
    Map<String, dynamic> result = {
      "masterData": {
        "type": "",
        "name": "",
        "id": "",
        "value": "",
        "expression": []
      }
    };

    for (var expression in apiExpression) {
      String input = expression['input'] as String;
      String expressionType = expression['expression'] as String;
      String condition = expression['condition'] as String;
      String fieldType = expression['fieldType'] as String;

      // Thêm biểu thức vào mảng expression
      result['masterData']['expression'].add({
        "input": input,
        "expression": expressionType,
        "condition": condition,
        "fieldType": fieldType
      });
    }

    return result;
  }

  /// Hàm xử lý apiExpression dựa trên optionType
  ///
  /// [apiExpression] là apiExpression từ form
  /// [optionType] là loại option ("api_link", "orgChart", "masterData")
  ///
  /// Trả về Map chứa thông tin input cho API
  // static Map<String, dynamic> getInputByOptionType(
  //   List<dynamic> apiExpression,
  //   String optionType,
  // ) {
  //   switch (optionType) {
  //     case "api_link":
  //       return getApiLinkInput(apiExpression);
  //     case "orgChart":
  //       return getOrgChartInput(apiExpression);
  //     case "masterData":
  //       return getMasterDataInput(apiExpression);
  //     default:
  //       return {};
  //   }
  // }

  /// Convert mdExpression to masterData filter format
  ///
  /// Example input:
  /// ```dart
  /// mdExpression: [{
  ///   "input": "123",
  ///   "expression": "not like",
  ///   "condition": "and",
  ///   "fieldType": "",
  ///   "type": "name",
  ///   "id": "RUqSH"
  /// }]
  /// masterDataId: "-1500611407147894268"
  /// ```
  ///
  /// Returns:
  /// ```dart
  /// {
  ///   "masterDataId": "-1500611407147894268",
  ///   "conditionFilter": [
  ///     {
  ///       "condition": "and",
  ///       "begin": "name",
  ///       "operator": "not like",
  ///       "end": "123"
  ///     }
  ///   ]
  /// }
  /// ```
  static MdServiceRequestBody convertMdExpressionToMasterDataFilter(
    List<dynamic> mdExpression,
    String masterDataId,
    FormStateManager stateManager,
    String? parentName,
  ) {
    final List<dynamic> conditionFilters = [];

    for (final expression in mdExpression) {
      String input = expression["input"] as String? ?? "";
      developer.log('input: $input',
          name: 'convertMdExpressionToMasterDataFilter');
      // Kiểm tra và format giá trị nếu bắt đầu bằng @
      if (input.startsWith('@')) {
        final fieldName = input.substring(1); // Bỏ ký tự @
        // Lấy giá trị từ stateManager
        input = stateManager
                .getFieldValueByName(fieldName, parentName)
                ?.toString() ??
            "";
        developer.log('Resolved field reference @$fieldName to value: $input',
            name: 'convertMdExpressionToMasterDataFilter');
      }
      final conditionFilter = {
        "condition": expression["condition"],
        "begin": expression["type"],
        "operator": expression["expression"],
        "end": input
      };
      conditionFilters.add(conditionFilter);
    }

    return MdServiceRequestBody(
        masterDataId: masterDataId,
        conditionFilter: conditionFilters
            .map((filter) => ConditionFilter.fromJson(filter))
            .toList());
  }

  static IndividualInfoRequestModel convertOrgExpressionToInputDataFilter(
    List<dynamic> orgExpression,
    String infoType,
    List<dynamic> orgchart,
    FormStateManager stateManager,
    String? parentName,
  ) {
    final List<Condition> conditions = [];

    for (final expression in orgExpression) {
      String input = expression["input"] as String? ?? "";

      // Kiểm tra và format giá trị nếu bắt đầu bằng @
      if (input.startsWith('@')) {
        final fieldName = input.substring(1); // Bỏ ký tự @

        if ([
          'system_chartNodeCustomId',
          'system_chartCustomId',
          'system_chartId_service',
          'system_chartNodeId_ticket'
        ].contains(fieldName)) {
          final data = stateManager.getAllFormData();

          // developer.log('fieldName: $fieldName $data', name: 'convertOrgExpressionToInputDataFilter');
          input = data[fieldName]?.toString() ?? "";
        } else {
          input = stateManager
                  .getFieldValueByName(fieldName, parentName)
                  ?.toString() ??
              "";
        }
        // Lấy giá trị từ stateManager
        developer.log(
            'Resolved field reference @$fieldName to value: $input',
            name: 'convertOrgExpressionToInputDataFilter');
      }

      final condition = Condition(
        expression: expression["condition"] as String? ?? "and",
        filterType: expression["type"] as String? ?? "user",
        filterField: expression["fieldType"] as String? ?? "username",
        operator: expression["expression"] as String? ?? "=",
        value: input,
      );
      conditions.add(condition);
    }

    return IndividualInfoRequestModel(
      orgchart: orgchart,
      infoType: infoType,
      condition: conditions,
    );
  }
}
