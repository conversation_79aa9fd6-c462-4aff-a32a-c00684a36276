import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/authentication/authentication_state.dart';
import 'package:eapprove/blocs/authorize_management/authorize_management_bloc.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/feature/feature_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/notification/notification_bloc.dart';
import 'package:eapprove/blocs/overlay/overlay_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/ticket_dialog_action/ticket_dialog_action_bloc.dart';
import 'package:eapprove/blocs/ticket_input/ticket_input_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/assistant_opinion_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/attachment_list_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/cancel_ticket_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/recover_request_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/ticket_history_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/work_flow_bloc.dart';

import 'package:eapprove/models/authentication/token_model.dart';
import 'package:eapprove/repositories/authentication_repository.dart';
import 'package:eapprove/repositories/authorize_management_repository.dart';
import 'package:eapprove/repositories/chart_repository.dart';
import 'package:eapprove/repositories/feature_repository.dart';
import 'package:eapprove/repositories/form_repository.dart';
import 'package:eapprove/repositories/notification_repository.dart';
import 'package:eapprove/repositories/pyc_repository.dart';
import 'package:eapprove/repositories/service_repository.dart';
import 'package:eapprove/repositories/sign_print_repository.dart';
import 'package:eapprove/repositories/filter_data_authorize_repository.dart';
import 'package:eapprove/repositories/ticket_dialog_action_repository.dart';
import 'package:eapprove/repositories/ticket_input_repository.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:eapprove/screens/authorize_management/authorize_management_screen.dart';
import 'package:eapprove/screens/handle_ticket/handle_ticket_screen.dart';
import 'package:eapprove/services/filter_data_authorize_service.dart';
import 'package:eapprove/blocs/authorize_management/data_filter/filter_data_authorize_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/services/list_user_info_service.dart';
import 'package:eapprove/repositories/list_user_info_repository.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:eapprove/services/network_manager.dart';
import 'package:eapprove/services/token.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart' as nav;
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'blocs/chart/chart_bloc.dart';
import 'blocs/dropdown/dropdown_bloc.dart';
import 'blocs/service/service_bloc.dart';
import 'blocs/sign_print/sign_print_bloc.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/repositories/account_repository.dart';
import 'package:eapprove/repositories/switch_account_repository.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'dart:developer' as developer;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:eapprove/repositories/ticket_process_detail_repository.dart';
import 'package:eapprove/blocs/ticket_process_detail/ticket_process_detail_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:eapprove/screens/setting_screen.dart';
// import 'package:flutter_sdk/widgets/router_delegate.dart';
// import 'package:flutter_sdk/widgets/root_back_button_dispatcher.dart';
// import 'package:provider/provider.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/repositories/user_repository.dart';
import 'package:eapprove/repositories/business_process_repository.dart';
import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/blocs/theme/app_theme_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/theme/custom_text_style.dart';
import 'package:eapprove/theme/custom_color.dart';
import 'package:eapprove/widgets/overlay_widget.dart';
import 'package:provider/provider.dart' as provider_pkg;

GlobalKey<NavigatorState> navigatorKey = GlobalKey();

NavigatorState get navigator => navigatorKey.currentState!;

class EApproveApp extends StatefulWidget {
  final String username;
  final String token;
  final String buildNumber;
  final String buildVersion;
  final List<LocalizationsDelegate>? localizationsDelegates;
  final List<Locale>? supportedLocales;
  final String logoCode;
  const EApproveApp({
    super.key,
    required this.token,
    required this.username,
    required this.buildNumber,
    required this.buildVersion,
    this.localizationsDelegates,
    this.supportedLocales,
    required this.logoCode,
  });

  @override
  State<EApproveApp> createState() => ModuleApp();
}

class AuthorizationInterceptor implements InterceptorContract {
  final TokenManager tokenManager;
  final GlobalKey<NavigatorState> navigatorKey;
  final String initialToken;

  AuthorizationInterceptor({
    required this.tokenManager,
    required this.navigatorKey,
    required this.initialToken,
  });

  @override
  Future<BaseRequest> interceptRequest({required BaseRequest request}) async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none && navigatorKey.currentContext != null) {
      SnackbarCore.error('Vui Lòng Kiểm Tra Kết Nối Internet');
    }

    final token = await tokenManager.getFormattedToken() ?? initialToken;
    if (token.isNotEmpty) {
      request.headers['Authorization'] = token;
      developer.log('URL: ${request.url}', name: 'API');
      developer.log('METHOD: ${request.method}', name: 'API');
      developer.log('HEADERS: ${request.headers}', name: 'API');
    } else {
      developer.log('WARNING: No valid token available for request: ${request.url}', name: 'API');
    }

    return request;
  }

  @override
  Future<BaseResponse> interceptResponse({required BaseResponse response}) async {
    developer.log('\nRESPONSE CHECK:\n-URL: ${response.request?.url}\n-Status: ${response.statusCode}');

    if (response.statusCode == 401 && navigatorKey.currentContext != null) {
      try {
        final authBloc = BlocProvider.of<AuthenticationBloc>(navigatorKey.currentContext!);
        authBloc.add(const RefreshTokenRequested());
        return response;
      } catch (e) {
        await tokenManager.clearTokenData();
        SnackbarCore.error('Phiên đăng nhập đã hết hạn, vui lòng đăng nhập lại');
      }
    }

    return response;
  }

  @override
  Future<bool> shouldInterceptRequest() async => true;

  @override
  Future<bool> shouldInterceptResponse() async => true;
}

class ModuleNavigationHandler {
  static void navigateToScreen(BuildContext context, String route) {
    final router = GoRouter.of(context);
    if (route.startsWith('/eapprove/')) {
      // Handle module-specific navigation
      router.push(route.replaceFirst('/eapprove', ''));
    } else {
      // Handle super app navigation
      Navigator.of(context).pushNamed(route);
    }
  }
}

class ModuleApp extends State<EApproveApp> {
  late final TokenManager tokenManager;
  late final ApiService apiService;
  late final AccountRepository accountRepository;
  bool _isInitialized = false;

  void _handleHomeScreenBack(bool? isLogout) async {
    if (isLogout == true) {
      final confirmed = await showCustomDialog<bool>(
        context: context,
        title: "Đăng xuất",
        content: "Bạn có chắc chắn muốn đăng xuất?",
        onCancel: () => Navigator.of(context).pop(false),
        onConfirm: () => Navigator.of(context).pop(true),
      );
      if (confirmed == true) {
        if (DeviceUtils.isTablet) {
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        }
        Navigator.of(context).pop('logout');
      }
    } else {
      if (DeviceUtils.isTablet) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      }
      Navigator.of(context).pop();
    }
  }

  @override
  void initState() {
    super.initState();
    tokenManager = TokenManager();
    if (DeviceUtils.isTablet) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.top]);

      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ));
    }

    TokenData tokenData = TokenData(
      accessToken: widget.token,
      tokenType: '',
      refreshToken: '',
      expiresIn: 108000,
      refreshExpiresIn: 108000,
      notBeforePolicy: 0,
      sessionState: '',
      scope: '',
    );

    NetworkManager().initialize(navigatorKey: navigatorKey);
    tokenManager.saveTokenData(tokenData);

    final interceptedClient = InterceptedClient.build(
      interceptors: [
        AuthorizationInterceptor(
          tokenManager: tokenManager,
          navigatorKey: navigatorKey,
          initialToken: widget.token,
        ),
      ],
    );

    apiService = ApiService(
      httpClient: interceptedClient,
      tokenManager: tokenManager,
    );

    accountRepository = AccountRepository(apiService: apiService);
    Hive.openBox('userBox1');
    _initializeModuleWithToken();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndNavigateFromNotification();
    });
  }

  Future<void> _checkAndNavigateFromNotification() async {
    try {
      final eapproveTicketBox = await Hive.openBox('eapproveTicketBox');
      final fromNotification = eapproveTicketBox.get('fromNotification') == true;
      if (!fromNotification) return;

      final procDefId = eapproveTicketBox.get('procDefId');
      final ticketId = eapproveTicketBox.get('ticketId');
      final taskDefKey = eapproveTicketBox.get('taskDefKey');
      final maPhieu = eapproveTicketBox.get('maPhieu');
      final requestSubject = eapproveTicketBox.get('requestSubject');

      if (procDefId == null || ticketId == null || taskDefKey == null) return;

      final ticket = {
        'id': ticketId,
        'ticketId': ticketId,
        'procDefId': procDefId,
        'ticketProcDefId': procDefId,
        'title': requestSubject ?? 'Chi tiết phiếu',
        'name': requestSubject ?? 'Chi tiết phiếu',
        'ticketTitle': requestSubject ?? 'Chi tiết phiếu',
        'requestCode': maPhieu ?? ticketId,
        'maPhieu': maPhieu ?? ticketId,
        'fromNotification': true,
        'ticketTaskDtoList': [
          {'taskDefKey': taskDefKey, 'taskId': ticketId.toString()}
        ],
      };

      await Future.delayed(const Duration(milliseconds: 500));

      if (navigatorKey.currentContext != null) {
        Navigator.of(navigatorKey.currentContext!)
            .push(
          MaterialPageRoute(
            builder: (context) => HandleTicketScreen(ticket: ticket),
          ),
        )
            .then((_) async {
          await eapproveTicketBox.clear();
        });
      }
    } catch (e) {
      developer.log('Error navigating from notification: $e');
    }
  }

  Future<void> _initializeModuleWithToken() async {
    if (mounted) {
      try {
        await Hive.initFlutter();
        await Hive.openBox("authentication");
        await Hive.openBox("setting");
        await Hive.openBox("user_box");

        Box box = Hive.box('authentication');
        Box boxSetting = Hive.box('setting');
        Box boxUser = Hive.box('user_box');
        await box.put('username', widget.username);
        await boxSetting.put('buildNumber', widget.buildNumber);
        await boxSetting.put('buildVersion', widget.buildVersion);
        await boxUser.put('logoCode', widget.logoCode);
        setState(() {
          _isInitialized = true;
        });
      } catch (e) {
        developer.log('💥 Critical error during app initialization: $e', name: 'APP_INIT');
        setState(() {
          _isInitialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Material(
        child: Center(
          child: SizedBox(),
        ),
      );
    }

    return LayoutBuilder(builder: (_, constraints) {
      final designSize = constraints.maxWidth >= 550
          ? const Size(1133, 744) // tablet
          : const Size(375, 764); // điện thoại
      return ScreenUtilInit(
        designSize: designSize,
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          BotToastInit();

          final interceptedClient = InterceptedClient.build(
            interceptors: [
              AuthorizationInterceptor(
                tokenManager: tokenManager,
                navigatorKey: navigatorKey,
                initialToken: widget.token,
              ),
            ],
          );

          final apiService = ApiService(
            httpClient: interceptedClient,
            tokenManager: tokenManager,
          );

          // Initialize repositories
          final authenticationRepository = AuthenticationRepository(apiService: apiService);
          final formRepository = FormRepository(apiService: apiService);
          final accountRepository = AccountRepository(apiService: apiService);
          final serviceRepository = ServiceRepository(apiService: apiService);
          final switchAccountRepository = SwitchAccountRepository(apiService: apiService);
          final pycRepository = PycRepository(apiService: apiService);
          final filterDataAuthorizeRepository = FilterDataAuthorizeRepository(apiService: apiService);
          final userInfoRepository = UserInfoRepository(apiService);
          final ticketProcessDetailRepository = TicketProcessDetailRepository(apiService: apiService);
          final notiRepository = NotiRepository(apiService: apiService);
          final userRepository = UserRepository(apiService: apiService);
          final chartRepository = ChartRepository(apiService);
          final ticketOtherActionRepository = TicketOtherActionRepository(apiService: apiService);
          final businessProcessRepository = BusinessProcessRepository(apiService: apiService);
          final featureRepository = FeatureRepository(apiService: apiService);
          final authorizeManagementRepository = AuthorizeManagementRepository(apiService: apiService);
          final ticketInputRepository = TicketInputRepository(apiService: apiService);          final signPrintRepository = SignPrintRepository(apiService: apiService);

          // Initialize BLoCs
          final authenticationBloc = AuthenticationBloc(
            authenticationRepository: authenticationRepository,
            tokenManager: tokenManager,
          );
          final featureBloc = FeatureBloc(repository: featureRepository);
          final ticketDialogActionRepository = TicketDialogActionRepository(apiService: apiService);
          return MultiBlocProvider(
            providers: [
              BlocProvider<AppThemeBloc>(
                create: (_) => AppThemeBloc(),
              ),
              BlocProvider<BottomNavBloc>(
                create: (_) => BottomNavBloc(),
              ),
              BlocProvider<AuthenticationBloc>.value(
                value: authenticationBloc,
              ),
              BlocProvider<ServiceBloc>(
                create: (_) => ServiceBloc(serviceRepository, accountRepository),
              ),
              BlocProvider<FormBloc>(
                create: (_) => FormBloc(
                  formRepository: formRepository,
                  accountRepository: accountRepository,
                ),
              ),
              BlocProvider<UserListBloc>(
                create: (_) => UserListBloc(accountRepository: accountRepository),
              ),
              BlocProvider<PycBloc>(
                create: (_) => PycBloc(pycRepository),
              ),
              BlocProvider<FilterDataAuthorizeBloc>(
                create: (_) => FilterDataAuthorizeBloc(filterDataAuthorizeRepository),
              ),
              BlocProvider<UserInfoBloc>(
                create: (_) => UserInfoBloc(userInfoRepository),
              ),
              BlocProvider<AuthorizeManagementBloc>(
                create: (_) => AuthorizeManagementBloc(authorizeManagementRepository),
              ),
              BlocProvider<UserBloc>(
                create: (_) => UserBloc(userRepository: userRepository),
              ),
              BlocProvider<TicketProcessDetailBloc>(
                create: (_) => TicketProcessDetailBloc(ticketProcessDetailRepository, formRepository),
              ),
              BlocProvider<ChartBloc>(
                create: (_) => ChartBloc(chartRepository: chartRepository),
              ),
              BlocProvider<NotiBloc>(
                create: (_) => NotiBloc(notiRepository: notiRepository),
              ),
              BlocProvider<DropdownBloc>(
                create: (_) => DropdownBloc(formRepository: formRepository),
              ),
              BlocProvider<TicketHistoryBloc>(
                create: (_) => TicketHistoryBloc(ticketOtherActionRepository: ticketOtherActionRepository),
              ),
              BlocProvider<CheckTypeBloc>(
                create: (_) => CheckTypeBloc(repository: businessProcessRepository),
              ),
              BlocProvider<PrintFileTemplateBloc>(
                create: (_) => PrintFileTemplateBloc(repository: businessProcessRepository),
              ),
              BlocProvider<AttachmentListBloc>(
                create: (_) => AttachmentListBloc(ticketOtherActionRepository: ticketOtherActionRepository),
              ),
              BlocProvider<ConsultationOpinionBloc>(
                create: (_) => ConsultationOpinionBloc(repository: ticketOtherActionRepository),
              ),
              BlocProvider<BpmProcInstBloc>(
                create: (_) => BpmProcInstBloc(repository: formRepository),
              ),
              BlocProvider<FeatureBloc>.value(
                value: featureBloc,
              ),
              BlocProvider<CancelTicketBloc>(
                create: (_) => CancelTicketBloc(repository: ticketOtherActionRepository),
              ),
              BlocProvider<RecoverRequestBloc>(
                create: (_) => RecoverRequestBloc(repository: ticketOtherActionRepository),
              ),
              BlocProvider<AssistantOpinionBloc>(
                create: (_) => AssistantOpinionBloc(repository: ticketOtherActionRepository),
              ),
              // BlocProvider(
              //   create: (_) {
              //     debugPrint('Initializing OverlayBloc');
              //     return OverlayBloc();
              //   },
              // ),
              BlocProvider<TicketInputBloc>(
                create: (_) => TicketInputBloc(repository: ticketInputRepository),
              ),
              BlocProvider<TicketDialogActionBloc>(
                create: (_) => TicketDialogActionBloc(repository: ticketDialogActionRepository),
              ),
              provider_pkg.Provider<SwitchAccountRepository>(
                create: (_) => switchAccountRepository,
              ),
              BlocProvider<WorkflowBloc>(
                create: (_) => WorkflowBloc(repository: ticketOtherActionRepository),
              ),
              BlocProvider<SignPrintBloc>(
                create: (_) => SignPrintBloc(repository: signPrintRepository),
              ),
            ],
            child: BlocBuilder<AppThemeBloc, AppThemeState>(
              builder: (context, themeState) {
                return MaterialApp(
                  debugShowCheckedModeBanner: false,
                  navigatorKey: navigatorKey,
                  localizationsDelegates: widget.localizationsDelegates,
                  supportedLocales: widget.supportedLocales ?? const [Locale('en')],
                  home: Scaffold(
                    body: nav.BottomNavScreen(
                      isEmbedded: true,
                    ),
                  ),
                );
              },
            ),
          );
        },
      );
    });
  }
}
