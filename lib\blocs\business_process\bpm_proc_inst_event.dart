part of 'bpm_proc_inst_bloc.dart';

abstract class BpmProcInstEvent extends Equatable {
  const BpmProcInstEvent();

  @override
  List<Object> get props => [];
}

class CreateInstRequested extends BpmProcInstEvent {
  final String procDefId;
  final int ticketId;
  final BpmProcInstCreateRequestModel requestBody;

  const CreateInstRequested({
    required this.procDefId,
    required this.ticketId,
    required this.requestBody,
  });

  @override
  List<Object> get props => [procDefId, ticketId, requestBody];
} 