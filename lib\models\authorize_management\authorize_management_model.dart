class DateFilterList {
  final String? fromDate;
  final String? toDate;
  final String? type;

  DateFilterList(this.fromDate, this.toDate, this.type);
}

class ServiceRangeItem {
  final int id;
  final int parentId;
  final String serviceName;
  final String color;
  final String icon;
  final int serviceType;
  final int processId;
  final String description;
  final String? note;
  final String url;
  final int positionPackage;
  final int idOrgChart;
  final bool notShowingWebsite;
  final bool notShowingMobile;
  final String? hideName;
  final String? visibleName;
  final bool deleted;
  final int masterParentId;
  final List<dynamic> lsChild;

  ServiceRangeItem({
    required this.id,
    required this.parentId,
    required this.serviceName,
    required this.color,
    required this.icon,
    required this.serviceType,
    required this.processId,
    required this.description,
    this.note,
    required this.url,
    required this.positionPackage,
    required this.idOrgChart,
    required this.notShowingWebsite,
    required this.notShowingMobile,
    this.hideName,
    this.visibleName,
    required this.deleted,
    required this.masterParentId,
    required this.lsChild,
  });

  factory ServiceRangeItem.fromJson(Map<String, dynamic> json) {
    return ServiceRangeItem(
      id: json['id'],
      parentId: json['parentId'],
      serviceName: json['serviceName'],
      color: json['color'],
      icon: json['icon'],
      serviceType: json['serviceType'],
      processId: json['processId'],
      description: json['description'],
      note: json['note'],
      url: json['url'],
      positionPackage: json['positionPackage'],
      idOrgChart: json['idOrgChart'],
      notShowingWebsite: json['notShowingWebsite'],
      notShowingMobile: json['notShowingMoblie'], // Note: Typo in JSON key
      hideName: json['hide_name'],
      visibleName: json['visible_name'],
      deleted: json['deleted'],
      masterParentId: json['masterParentId'],
      lsChild: json['lsChild'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'serviceName': serviceName,
      'color': color,
      'icon': icon,
      'serviceType': serviceType,
      'processId': processId,
      'description': description,
      'note': note,
      'url': url,
      'positionPackage': positionPackage,
      'idOrgChart': idOrgChart,
      'notShowingWebsite': notShowingWebsite,
      'notShowingMoblie': notShowingMobile, // Note: Typo in JSON key
      'hide_name': hideName,
      'visible_name': visibleName,
      'deleted': deleted,
      'masterParentId': masterParentId,
      'lsChild': lsChild,
    };
  }
}

class AuthorizeItemData {
  final int id;
  final String? assignName;
  final String? assignUser;
  final String? assignedUser;
  final int ticketId;
  final List<ServiceRangeItem> serviceRange;
  final String? startDate;
  final String? endDate;
  final int status;
  final int effect;
  final String? description;
  final String? createdDate;
  final String? createdUser;
  final String? updatedDate;
  final String? updatedUser;
  final String fileName;
  final String? base64;
  final dynamic newTicket;
  final String? authorityConditions;
  final int serviceId;
  final String? requestCode;
  final dynamic newRequestCode;
  final String? assignTitle;
  final String? titleName;
  final dynamic historyStatus;
  final String? companyCode;
  final String? companyName;

  AuthorizeItemData({
    required this.id,
    required this.assignName,
    required this.assignUser,
    required this.assignedUser,
    required this.ticketId,
    required this.serviceRange,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.effect,
    required this.description,
    required this.createdDate,
    required this.createdUser,
    this.updatedDate,
    this.updatedUser,
    required this.fileName,
    this.base64,
    this.newTicket,
    required this.authorityConditions,
    required this.serviceId,
    required this.requestCode,
    this.newRequestCode,
    this.assignTitle,
    this.titleName,
    this.historyStatus,
    required this.companyCode,
    required this.companyName,
  });

  factory AuthorizeItemData.fromJson(Map<String, dynamic> json) {
    return AuthorizeItemData(
      id: json['id'],
      assignName: json['assignName'],
      assignUser: json['assignUser'],
      assignedUser: json['assignedUser'],
      ticketId: json['ticketId'],
      serviceRange: (json['serviceRange'] as List)
          .map((item) => ServiceRangeItem.fromJson(item))
          .toList(),
      startDate: json['startDate'],
      endDate: json['endDate'],
      status: json['status'],
      effect: json['effect'],
      description: json['description'],
      createdDate: json['createdDate'],
      createdUser: json['createdUser'],
      updatedDate: json['updatedDate'],
      updatedUser: json['updatedUser'],
      fileName: json['fileName'],
      base64: json['base64'],
      newTicket: json['newTicket'],
      authorityConditions: json['authorityConditions'],
      serviceId: json['serviceId'],
      requestCode: json['requestCode'],
      newRequestCode: json['newRequestCode'],
      assignTitle: json['assignTitle'],
      titleName: json['titleName'],
      historyStatus: json['historyStatus'],
      companyCode: json['companyCode'],
      companyName: json['companyName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'assignName': assignName,
      'assignUser': assignUser,
      'assignedUser': assignedUser,
      'ticketId': ticketId,
      'serviceRange': serviceRange.map((item) => item.toJson()).toList(),
      'startDate': startDate,
      'endDate': endDate,
      'status': status,
      'effect': effect,
      'description': description,
      'createdDate': createdDate,
      'createdUser': createdUser,
      'updatedDate': updatedDate,
      'updatedUser': updatedUser,
      'fileName': fileName,
      'base64': base64,
      'newTicket': newTicket,
      'authorityConditions': authorityConditions,
      'serviceId': serviceId,
      'requestCode': requestCode,
      'newRequestCode': newRequestCode,
      'assignTitle': assignTitle,
      'titleName': titleName,
      'historyStatus': historyStatus,
      'companyCode': companyCode,
      'companyName': companyName,
    };
  }
}
