import 'package:flutter_sdk/utils/icon_data.dart';

class RequestStatusCardModel {
  final String title;
  final int count;
  final String icon;

  const RequestStatusCardModel({
    required this.title,
    required this.count,
    required this.icon,
  });

  /// Factory từ raw json
  factory RequestStatusCardModel.fromJson(Map<String, dynamic> json) {
    return RequestStatusCardModel(
      title: json['title'],
      count: json['count'],
      icon: json['icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'count': count,
      'icon': icon,
    };
  }

  static RequestStatusCardModel fromStatusKey(String key, int count) {
    switch (key.toUpperCase()) {
      case 'ONGOING':
        return RequestStatusCardModel(
            title: "Đang phê duyệt", count: count, icon: FIconData.icWaiting);
      case 'PROCESSING':
        return RequestStatusCardModel(
            title: "<PERSON><PERSON> lý", count: count, icon: FIconData.icWaiting);
      case 'COMPLETED':
      case 'COMPLETE':
        return RequestStatusCardModel(
            title: "Hoàn thành", count: count, icon: FIconData.icGreentick);
      case 'RECALLED':
        return RequestStatusCardModel(
            title: "Trả về/Thu hồi", count: count, icon: FIconData.icRefresh);
      case 'CANCEL':
        return RequestStatusCardModel(
            title: "Hủy", count: count, icon: FIconData.icRedCancel);
      case 'DRAFT':
        return RequestStatusCardModel(
            title: "Bản nháp đã lưu", count: count, icon: FIconData.icDraft);
      case 'SHARED':
        return RequestStatusCardModel(
            title: "Được chia sẻ/theo dõi",
            count: count,
            icon: FIconData.icOrangeShare);
      case 'SHARE':
        return RequestStatusCardModel(
            title: "Đã chia sẻ", count: count, icon: FIconData.icOrangeShare);
      case 'FOLLOWED':
        return RequestStatusCardModel(
            title: "Theo dõi", count: count, icon: FIconData.icOrangeShare);
      case 'APPROVAL':
        return RequestStatusCardModel(
            title: "Đang chờ phê duyệt",
            count: count,
            icon: FIconData.icWaiting);
      case 'APPROVED':
        return RequestStatusCardModel(
            title: "Đã phê duyệt", count: count, icon: FIconData.icGreentick);
      default:
        return RequestStatusCardModel(
            title: key, count: count, icon: FIconData.icWaiting);
    }
  }
}
