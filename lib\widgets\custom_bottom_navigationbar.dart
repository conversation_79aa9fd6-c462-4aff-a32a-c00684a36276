import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:eapprove/blocs/account/user_list_state.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/feature/feature_bloc.dart';
import 'package:eapprove/blocs/feature/feature_event.dart';
import 'package:eapprove/blocs/feature/feature_state.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/notification/notification_bloc.dart';
import 'package:eapprove/blocs/notification/notification_event.dart';
import 'package:eapprove/blocs/notification/notification_state.dart';
import 'package:eapprove/blocs/overlay/overlay_bloc.dart';
import 'package:eapprove/blocs/pyc/pyc_bloc.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/models/feature_respone_model.dart';
import 'package:eapprove/screens/notification_screen/notification_screen.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/tablet/pyc/ticket_screen_with_counts.dart';
import 'package:eapprove/screens/tablet/setting_screen_tablet.dart';
import 'package:eapprove/screens/tablet/tiket_service/create_ticket_screen_tablet.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/icon_data.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_dialog.dart';
import 'package:flutter_sdk/widgets/custom_icon.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:eapprove/widgets/overlay_widget.dart';
import 'dart:developer' as developer;

class CustomBottomNavigationBar extends StatefulWidget {
  final int selectedIndex;
  final Function(int) onTap;
  final Color selectedBackgroundColor;
  final Color unselectedBackgroundColor;

  const CustomBottomNavigationBar({
    super.key,
    required this.selectedIndex,
    required this.onTap,
    this.selectedBackgroundColor = const Color(0xFFE6F0FF),
    this.unselectedBackgroundColor = Colors.transparent,
  });

  @override
  State<CustomBottomNavigationBar> createState() => _CustomBottomNavigationBarState();
}

class _CustomBottomNavigationBarState extends State<CustomBottomNavigationBar> {
  bool _showOverlay = false;
  Widget? _overlayContent;

  @override
  void initState() {
    context.read<NotiBloc>().add(FetchUnreadAmount());
    super.initState();
  }

  void showOverlay(Widget content) {
    setState(() {
      _showOverlay = true;
      _overlayContent = content;
    });
  }

  void hideOverlay() {
    setState(() {
      _showOverlay = false;
      _overlayContent = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final textScaler = MediaQuery.of(context).textScaler;
    final effectiveTextScale = textScaler.scale(1.0);

    final heightScale = effectiveTextScale > 1.3 ? 1.3 : effectiveTextScale;
    debugPrint('ConfigImg.isHostAppConfigImg.isHostApp: ${ConfigImg.isHostApp}');
    return Stack(
      children: [
        BlocListener<NotiBloc, NotiState>(
          listener: (context, state) {
            if (state is UnreadLoaded) {}
          },
          child: DeviceUtils.isTablet ? const TabletLayout() : _buildMobileLayout(),
        ),
        const OverlayWidget(),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Theme(
      data: Theme.of(context).copyWith(splashColor: Colors.transparent, highlightColor: Colors.transparent),
      child: Container(
        // height: (kBottomNavigationBarHeight * heightScale),
        decoration: BoxDecoration(
          color: getColorSkin().white,
          boxShadow: [BoxShadow(color: Colors.black.withAlpha(26), blurRadius: 4, offset: const Offset(0, -1))],
        ),
        child: SafeArea(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Logo item with flex 2
              Expanded(
                flex: 2,
                child: _buildCustomNavItem(
                  context: context,
                  icon: FIconData.logo,
                  label: '',
                  width: 110.w,
                  isImage: true,
                  index: 0,
                ),
              ),
              Expanded(
                child: _buildCustomNavItem(
                  context: context,
                  icon: FIconData.icAddCircle,
                  label: 'Tạo tờ trình',
                  width: 22.w,
                  height: 22.h,
                  isImage: false,
                  index: 1,
                ),
              ),

              Expanded(
                child: BlocBuilder<NotiBloc, NotiState>(
                  builder: (ctx, state) {
                    final count = state is UnreadLoaded ? state.unreadAmount : 0;
                    return _buildCustomNavItem(
                      context: ctx,
                      icon: FIconData.icAlarmBell,
                      label: 'Thông báo',
                      width: 22.w,
                      height: 22.h,
                      isImage: false,
                      index: 2,
                      badgeCount: count,
                    );
                  },
                ),
              ),

              Expanded(
                child: _buildCustomNavItem(
                  context: context,
                  icon: FIconData.icLogOut,
                  label: 'Đăng xuất',
                  width: 22.w,
                  height: 22.h,
                  isImage: false,
                  index: 3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomNavItem({
    required BuildContext context,
    required dynamic icon,
    required String label,
    required double width,
    double? height,
    required bool isImage,
    required int index,
    int badgeCount = 0,
  }) {
    final isSelected = index == widget.selectedIndex;
    final textScaler = MediaQuery.of(context).textScaler;
    final baseStyle = getTypoSkin().labelRegular20;
    final adjustedStyle = baseStyle.copyWith(
      color: index == 3 ? getColorSkin().red : getColorSkin().black,
      fontSize: (baseStyle.fontSize ?? 12) / (textScaler.scale(1.0) > 1.3 ? 1.3 : 1.0),
    );

    // Lấy logoCompany từ Hive nếu index == 0
    Box box = Hive.box('user_box');
    final String? logoCode = box.get('logoCode', defaultValue: '1000');
    final String logoUrl = 'https://dxg.s3-sgn09.fptcloud.com/dxg/$logoCode.png';
    // 1) Tạo iconWidget (logo hoặc FIcon)
    Widget iconWidget;
    if (index == 0 && isImage && logoCode != null && logoCode.isNotEmpty) {
      iconWidget = Image.network(
        logoUrl,
        width: width,
        height: height,
        fit: BoxFit.contain,
        errorBuilder: (ctx, err, st) => FIcon(
          icon: FIconData.logo,
          width: width,
          height: height,
          isImage: true,
          color: getColorSkin().primaryBlue,
        ),
      );
    } else {
      iconWidget = FIcon(
        icon: icon,
        width: width,
        height: height,
        isImage: isImage,
        color: index == 3 ? getColorSkin().red : getColorSkin().primaryBlue,
      );
    }

    // 2) Nếu là tab Thông báo (index == 2) và badgeCount>0, chèn red dot
    if (index == 2 && badgeCount > 0) {
      iconWidget = Stack(
        clipBehavior: Clip.none,
        children: [
          iconWidget,
          Positioned(
            right: -6,
            top: -6,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
              child: Text(
                badgeCount > 9 ? '9+' : '$badgeCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }

    return InkWell(
      onTap: () {
        if (index == 2) {
          // Reset badge count and navigate
          context.read<NotiBloc>().add(ResetUnreadAmount());
          widget.onTap(index);
        } else {
          widget.onTap(index);
        }
      },
      child: Container(
        height: 72.h,
        decoration: BoxDecoration(
          color: isSelected ? widget.selectedBackgroundColor : widget.unselectedBackgroundColor,
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              iconWidget,
              if (label.isNotEmpty) ...[
                SizedBox(height: 4.h),
                Text(
                  label,
                  style: adjustedStyle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class TabletLayout extends StatefulWidget {
  const TabletLayout({super.key});

  @override
  State<TabletLayout> createState() => _TabletLayoutState();
}

class _TabletLayoutState extends State<TabletLayout> with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  bool _showNotificationPanel = false;
  bool _isNotificationSelected = false;
  bool _isExpanded = true;
  late AnimationController _animationController;
  late Animation<double> _widthAnimation;

  bool _hasCheckedRoles = false;
  bool _isAdmin = false;

//form
  bool _showTicketForm = false;
  String? _procDefId;
  String _ticketTitle = '';
  int? _processId;
  int? _ticketId;

  void showTicketForm(String procDefId, String title, int? processId, int? ticketId) {
    setState(() {
      _showTicketForm = true;
      _procDefId = procDefId;
      _ticketTitle = title;
      _processId = processId;
      _ticketId = ticketId;
    });
  }

  final Map<int, GlobalKey> _screenKeys = {
    0: GlobalKey(),
    1: GlobalKey(),
    2: GlobalKey(),
    3: GlobalKey(),
    4: GlobalKey(),
    5: GlobalKey(),
    6: GlobalKey(),
    7: GlobalKey(),
  };
  final Map<int, bool> _tabVisibility = {
    1: true, // My Ticket
    2: true, // Execution
    3: true, // Approval
    4: true, // Assistant
  };
  int get _effectiveSelectedIndex {
    // When showing ticket form, return -1 (no selection)
    return _showTicketForm ? -1 : _selectedIndex;
  }

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );

    _widthAnimation = Tween<double>(begin: 56, end: 300).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (_isExpanded) {
      _animationController.value = 1.0;
    } else {
      _animationController.value = 0.0;
    }

    context.read<FeatureBloc>().add(const LoadFeatures());
    context.read<NotiBloc>().add(ResetUnreadAmount());
    context.read<UserBloc>().add(FetchUserInfo());
    _fetchUserRoleAndData();

    context.read<UserListBloc>().stream.listen((state) {
      if (state is UserListLoaded) {
        log("UserListBloc state changed - isAdmin: ${state.isAdmin}, has roles: ${state.roles}");
        setState(() {
          _isAdmin = state.isAdmin;
        });
        _updateFeatureVisibility();
      }
    });
  }

  void _fetchUserRoleAndData() {
    if (context.read<UserListBloc>().state is UserListLoading) {
      return;
    }

    final userListState = context.read<UserListBloc>().state;
    if (userListState is UserListLoaded && userListState.roles != null) {
      setState(() {
        _isAdmin = userListState.isAdmin;
        _hasCheckedRoles = true;
      });
      _updateFeatureVisibility();
      return;
    }

    _hasCheckedRoles = true;
    context.read<UserListBloc>().add(CheckUserRoles());
  }

  void _updateFeatureVisibility() {
    final pycState = context.read<PycBloc>().state;

    final myTicketCount = pycState.myPycCountResponseModel?.data;
    final executionCount = pycState.procCountResponseModel?.data;
    final approvalCount = pycState.appCountResponseModel?.data;
    final assistCount = pycState.assisPycCountResponseModel?.data;

    final filterType1 = _getFilterType(1); // 'ticket'
    final filterType2 = _getFilterType(2); // 'execution'
    final filterType3 = _getFilterType(3); // 'approval'
    final filterType4 = _getFilterType(4); // 'assistant'

    final filterCounts = {
      1: pycState.filterList.where((item) => item.type == filterType1 && item.status == "pin").length,
      2: pycState.filterList.where((item) => item.type == filterType2 && item.status == "pin").length,
      3: pycState.filterList.where((item) => item.type == filterType3 && item.status == "pin").length,
      4: pycState.filterList.where((item) => item.type == filterType4 && item.status == "pin").length,
    };

    context.read<FeatureBloc>().add(UpdateFeatureVisibility(
          isAdmin: _isAdmin,
          pycCounts: {
            'myTicketCount': myTicketCount,
            'executionCount': executionCount,
            'approvalCount': approvalCount,
            'assistCount': assistCount,
            'filterCounts': filterCounts,
          },
        ));
  }

  String? _getFilterType(int index) {
    switch (index) {
      case 1:
        return 'ticket';
      case 2:
        return 'execution';
      case 3:
        return 'approval';
      case 4:
        return 'assistant';
      default:
        return null;
    }
  }

  @override
  void dispose() {
    if (DeviceUtils.isTablet) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    }
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _onTabSelected(int index) {
    if (_selectedIndex != index) {
      setState(() {
        _selectedIndex = index;
      });

      _reloadSelectedTab(index);
    }
  }

  void _reloadSelectedTab(int index) {
    switch (index) {
      case 0: // Dịch vụ
        CreateTicketScreenTablet.resetLoadState();
        break;
      case 1: // My Ticket
        break;
      case 2: // Execution
        break;
      case 3: // Approval
        break;
      case 4: // Assistant
        break;
    }
  }

  Widget _getScreenForIndex(int index) {
    developer.log('getScreenForIndex: $index', name: 'getScreenForIndex');
    if (_showTicketForm) {
      return MultiBlocProvider(
        providers: [
          BlocProvider.value(value: context.read<FormBloc>()),
          BlocProvider.value(value: context.read<DropdownBloc>()),
          BlocProvider.value(value: context.read<CheckTypeBloc>()),
          BlocProvider.value(value: context.read<PrintFileTemplateBloc>()),
          BlocProvider.value(value: context.read<BpmProcInstBloc>()),
          BlocProvider.value(value: context.read<ChartBloc>()),
          BlocProvider.value(value: context.read<UserListBloc>()),
        ],
        child: TicketFormScreen(
          key: _screenKeys[7],
          procDefId: _procDefId,
          title: _ticketTitle,
          processId: _processId,
          ticketId: _ticketId,
          onClose: closeTicketForm,
        ),
      );
    }
    switch (index) {
      case 0:
        return CreateTicketScreenTablet(
          key: _screenKeys[0],
          title: 'Trang dịch vụ',
          onServiceSelected: (procDefId, title, processId, ticketId) {
            showTicketForm(procDefId, title, processId, ticketId);
          },
        );
      case 1:
        return TicketViewTablet(key: _screenKeys[1], filterIndex: 0); // My Ticket
      case 2:
        return TicketViewTablet(key: _screenKeys[2], filterIndex: 1); // Execution
      case 3:
        return TicketViewTablet(key: _screenKeys[3], filterIndex: 2); // Approval
      case 4:
        return TicketViewTablet(key: _screenKeys[4], filterIndex: 3); // Assistant
      case 100:
        return NotificationScreen(key: _screenKeys[6]);
      case 101:
        return SettingScreenTablet(key: _screenKeys[5]);
      default:
        return Center(
          child: Text('Nội dung cho Index $index', style: const TextStyle(fontSize: 24)),
        );
    }
  }

  void closeTicketForm() {
    setState(() {
      _showTicketForm = false;
    });
    _getScreenForIndex(0);
  }

  Future<void> _handleAction(BuildContext context, bool isLogout) async {
    if (isLogout) {
      final confirmed = await showCustomDialog<bool>(
        context: context,
        title: "Đăng xuất",
        content: "Bạn có chắc chắn muốn đăng xuất?",
        onCancel: () {
          Navigator.of(context, rootNavigator: true).pop(false);
        },
        onConfirm: () {
          Navigator.of(context, rootNavigator: true).pop(true);
        },
      );

      if (confirmed == true) {
        if (DeviceUtils.isTablet) {
          await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        }
        Navigator.of(context, rootNavigator: true).pop('logout');
      }
    } else {
      // Back to Super App
      if (DeviceUtils.isTablet) {
        await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      }
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  void _toggleNotificationPanel() {
    setState(() {
      _showNotificationPanel = !_showNotificationPanel;
      _isNotificationSelected = _showNotificationPanel;
      if (_showNotificationPanel) {
        context.read<NotiBloc>().add(FetchUnreadAmount());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Platform.isAndroid
        ? SafeArea(
            child: Stack(
              children: [
                Scaffold(
                  extendBodyBehindAppBar: false,
                  backgroundColor: Colors.grey[100],
                  body: Material(
                    color: Colors.transparent,
                    child: MultiBlocListener(
                      listeners: [
                        BlocListener<FeatureBloc, FeatureState>(
                          listener: (context, state) {
                            if (state is FeatureLoaded) {
                              if (_selectedIndex >= 1 && _selectedIndex <= 4 && !state.tabVisibility[_selectedIndex]!) {
                                int newIndex = 0;
                                for (int i = 1; i <= 4; i++) {
                                  if (state.tabVisibility[i]!) {
                                    newIndex = i;
                                    break;
                                  }
                                }
                                setState(() {
                                  _selectedIndex = newIndex;
                                  _showTicketForm = false;
                                });
                              }
                            }
                          },
                        ),
                        BlocListener<BottomNavBloc, BottomNavState>(
                          listener: (context, state) {
                            if (state.navigateToTabIndex != null && DeviceUtils.isTablet) {
                              developer.log('Tablet navigation triggered to tab: ${state.navigateToTabIndex}');
                              setState(() {
                                _selectedIndex = state.navigateToTabIndex!;
                                _showTicketForm = false;
                                _showNotificationPanel = false;
                                _isNotificationSelected = false;
                              });
                              _onTabSelected(state.navigateToTabIndex!);
                            }
                          },
                        ),
                      ],
                      child: BlocBuilder<FeatureBloc, FeatureState>(
                        builder: (context, featureState) {
                          return AnimatedBuilder(
                            animation: _animationController,
                            builder: (context, child) {
                              return LayoutBuilder(
                                builder: (context, constraints) {
                                  final Widget contentWidget = _getScreenForIndex(_selectedIndex);

                                  Map<int, bool> tabVisibility = {
                                    1: true,
                                    3: true,
                                    2: false,
                                    4: false,
                                  };

                                  Feature? featureData;
                                  if (featureState is FeatureLoaded) {
                                    tabVisibility = featureState.tabVisibility;
                                    featureData = featureState.featureData;
                                  }

                                  final navigationRail = CustomNavigationRail(
                                    isExpanded: _isExpanded,
                                    selectedIndex: _effectiveSelectedIndex,
                                    onDestinationSelected: (index) {
                                      if (index == 100) {
                                        _toggleNotificationPanel();
                                      } else if (index == 102) {
                                        // Back to Super App action
                                        _handleAction(context, false);
                                      } else if (index == 103) {
                                        // Logout action
                                        _handleAction(context, true);
                                      } else {
                                        setState(() {
                                          _showNotificationPanel = false;
                                          _isNotificationSelected = false;
                                          _selectedIndex = index;
                                          _showTicketForm = false; // Reset ticket form when changing tabs
                                        });
                                        _onTabSelected(index);
                                      }
                                    },
                                    onExpandToggle: _toggleExpanded,
                                    widthAnimation: _widthAnimation,
                                    tabVisibility: tabVisibility,
                                    featureData: featureData,
                                    isNotificationSelected: _isNotificationSelected,
                                  );

                                  return GradientBackground(
                                    showBottomImage: false,
                                    showUpperImage: false,
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          left: _animationController.value > 0.1 ? 0 : 56,
                                          right: 0,
                                          top: 0,
                                          bottom: 0,
                                          child: GestureDetector(
                                            onTap: _isExpanded
                                                ? () {
                                                    _toggleExpanded();
                                                  }
                                                : null,
                                            behavior: HitTestBehavior.translucent,
                                            child: contentWidget,
                                          ),
                                        ),
                                        AnimatedOpacity(
                                          opacity: _showNotificationPanel ? 1.0 : 0.0,
                                          duration: const Duration(milliseconds: 250),
                                          child: _showNotificationPanel
                                              ? GestureDetector(
                                                  onTap: _toggleNotificationPanel,
                                                  child: Container(
                                                    color: Colors.black.withOpacity(0.5),
                                                  ),
                                                )
                                              : const SizedBox.shrink(),
                                        ),
                                        // Navigation rail
                                        Positioned(
                                          left: 0,
                                          top: 20.h,
                                          bottom: 0,
                                          child: Material(
                                            elevation: 4,
                                            color: Colors.white,
                                            borderRadius: const BorderRadius.only(
                                                topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
                                            child: navigationRail,
                                          ),
                                        ),
                                        AnimatedPositioned(
                                          duration: const Duration(milliseconds: 250),
                                          left: _widthAnimation.value + 8.w,
                                          curve: Curves.easeInOut,
                                          top: 20.h,
                                          bottom: 168.h,
                                          width: _showNotificationPanel ? 414.w : 0,
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8.r),
                                              bottomLeft: Radius.circular(8.r),
                                              topRight: Radius.circular(8.r),
                                              bottomRight: Radius.circular(8.r),
                                            ),
                                            child: Material(
                                              elevation: 4,
                                              color: getColorSkin().white,
                                              child: _showNotificationPanel
                                                  ? SizedBox(
                                                      width: 414.w,
                                                      child: BlocProvider<NotiBloc>.value(
                                                        value: context.read<NotiBloc>(),
                                                        child: NotificationScreen(
                                                          key: _screenKeys[6],
                                                          onClose: _toggleNotificationPanel,
                                                        ),
                                                      ),
                                                    )
                                                  : const SizedBox.shrink(),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        : Stack(
            children: [
              Scaffold(
                extendBodyBehindAppBar: false,
                backgroundColor: Colors.grey[100],
                body: Material(
                  color: Colors.transparent,
                  child: MultiBlocListener(
                    listeners: [
                      BlocListener<FeatureBloc, FeatureState>(
                        listener: (context, state) {
                          if (state is FeatureLoaded) {
                            if (_selectedIndex >= 1 && _selectedIndex <= 4 && !state.tabVisibility[_selectedIndex]!) {
                              int newIndex = 0;
                              for (int i = 1; i <= 4; i++) {
                                if (state.tabVisibility[i]!) {
                                  newIndex = i;
                                  break;
                                }
                              }
                              setState(() {
                                _selectedIndex = newIndex;
                                _showTicketForm = false; // Reset ticket form when changing tabs
                              });
                            }
                          }
                        },
                      ),
                      BlocListener<BottomNavBloc, BottomNavState>(
                        listener: (context, state) {
                          if (state.navigateToTabIndex != null && DeviceUtils.isTablet) {
                            developer.log('Tablet navigation triggered to tab: ${state.navigateToTabIndex}');
                            setState(() {
                              _selectedIndex = state.navigateToTabIndex!;
                              _showTicketForm = false;
                              _showNotificationPanel = false;
                              _isNotificationSelected = false;
                            });
                            _onTabSelected(state.navigateToTabIndex!);
                          }
                        },
                      ),
                    ],
                    child: BlocBuilder<FeatureBloc, FeatureState>(
                      builder: (context, featureState) {
                        return AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            return LayoutBuilder(
                              builder: (context, constraints) {
                                final Widget contentWidget = _getScreenForIndex(_selectedIndex);

                                Map<int, bool> tabVisibility = {
                                  1: true,
                                  3: true,
                                  2: false,
                                  4: false,
                                };

                                Feature? featureData;
                                if (featureState is FeatureLoaded) {
                                  tabVisibility = featureState.tabVisibility;
                                  featureData = featureState.featureData;
                                }

                                final navigationRail = CustomNavigationRail(
                                  isExpanded: _isExpanded,
                                  selectedIndex: _effectiveSelectedIndex,
                                  onDestinationSelected: (index) {
                                    if (index == 100) {
                                      _toggleNotificationPanel();
                                    } else if (index == 102) {
                                      // Back to Super App action
                                      _handleAction(context, false);
                                    } else if (index == 103) {
                                      // Logout action
                                      _handleAction(context, true);
                                    } else {
                                      setState(() {
                                        _showNotificationPanel = false;
                                        _isNotificationSelected = false;
                                        _selectedIndex = index;
                                        _showTicketForm = false; // Reset ticket form when changing tabs
                                      });
                                      _onTabSelected(index);
                                    }
                                  },
                                  onExpandToggle: _toggleExpanded,
                                  widthAnimation: _widthAnimation,
                                  tabVisibility: tabVisibility,
                                  featureData: featureData,
                                  isNotificationSelected: _isNotificationSelected,
                                );

                                return GradientBackground(
                                  showBottomImage: false,
                                  showUpperImage: false,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: _animationController.value > 0.1 ? 0 : 56,
                                        right: 0,
                                        top: 0,
                                        bottom: 0,
                                        child: GestureDetector(
                                          onTap: _isExpanded
                                              ? () {
                                                  _toggleExpanded();
                                                }
                                              : null,
                                          behavior: HitTestBehavior.translucent,
                                          child: contentWidget,
                                        ),
                                      ),
                                      AnimatedOpacity(
                                        opacity: _showNotificationPanel ? 1.0 : 0.0,
                                        duration: const Duration(milliseconds: 250),
                                        child: _showNotificationPanel
                                            ? GestureDetector(
                                                onTap: _toggleNotificationPanel,
                                                child: Container(
                                                  color: Colors.black.withOpacity(0.5),
                                                ),
                                              )
                                            : const SizedBox.shrink(),
                                      ),
                                      // Navigation rail
                                      Positioned(
                                        left: 0,
                                        top: 20.h,
                                        bottom: 0,
                                        child: Material(
                                          elevation: 4,
                                          color: Colors.white,
                                          borderRadius: const BorderRadius.only(
                                              topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
                                          child: navigationRail,
                                        ),
                                      ),
                                      AnimatedPositioned(
                                        duration: const Duration(milliseconds: 250),
                                        left: _widthAnimation.value + 8.w,
                                        curve: Curves.easeInOut,
                                        top: 20.h,
                                        bottom: 168.h,
                                        width: _showNotificationPanel ? 414.w : 0,
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(8.r),
                                            bottomLeft: Radius.circular(8.r),
                                            topRight: Radius.circular(8.r),
                                            bottomRight: Radius.circular(8.r),
                                          ),
                                          child: Material(
                                            elevation: 4,
                                            color: getColorSkin().white,
                                            child: _showNotificationPanel
                                                ? SizedBox(
                                                    width: 414.w,
                                                    child: BlocProvider<NotiBloc>.value(
                                                      value: context.read<NotiBloc>(),
                                                      child: NotificationScreen(
                                                        key: _screenKeys[6],
                                                        onClose: _toggleNotificationPanel,
                                                      ),
                                                    ),
                                                  )
                                                : const SizedBox.shrink(),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
  }
}

class CustomNavigationRail extends StatelessWidget {
  final bool isExpanded;
  final int selectedIndex;
  final ValueChanged<int> onDestinationSelected;
  final VoidCallback onExpandToggle;
  final Animation<double> widthAnimation;
  final Map<int, bool> tabVisibility;
  final Feature? featureData;
  final bool isNotificationSelected;
  const CustomNavigationRail({
    super.key,
    required this.isExpanded,
    required this.selectedIndex,
    required this.onDestinationSelected,
    required this.onExpandToggle,
    required this.widthAnimation,
    required this.tabVisibility,
    this.featureData,
    required this.isNotificationSelected,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widthAnimation,
      builder: (context, child) {
        return Container(
          width: widthAnimation.value,
          height: double.infinity,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Color(0x0000000F),
                offset: Offset(0, 4),
                blurRadius: 15,
              ),
            ],
          ),
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: SingleChildScrollView(
                  child: _buildNavigationItems(context),
                ),
              ),
              _buildFooter(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    Box box = Hive.box('user_box');
    final String? logoCode = box.get('logoCode', defaultValue: '1000');
    final String logoUrl = 'https://dxg.s3-sgn09.fptcloud.com/dxg/$logoCode.png';
    if (isExpanded) {
      // Expanded mode
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.only(left: 16.w, bottom: 10.h),
                child: Image.network(
                  logoUrl,
                  fit: BoxFit.contain,
                  alignment: Alignment.centerLeft,
                  width: 138.w,
                  height: 56.h,
                ),
              ),
              SizedBox(
                width: 44.w,
                height: 44.h,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(18.r),
                    onTap: onExpandToggle,
                    child: Center(
                      child: SvgPicture.asset(
                        StringImage.ic_arrow_left,
                        width: 24.w,
                        height: 24.h,
                        colorFilter: ColorFilter.mode(getColorSkin().secondaryColor1, BlendMode.srcIn),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          Divider(height: 1, thickness: 0.5, color: Colors.grey.withOpacity(0.3)),
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 100,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  alignment: Alignment.center,
                  child: Image.network(
                    logoUrl,
                    height: 42.h,
                    fit: BoxFit.contain,
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 10),
                  width: 44.w,
                  height: 44.h,
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(22.r),
                      onTap: onExpandToggle,
                      child: Center(
                        child: SvgPicture.asset(
                          StringImage.ic_arrow_right,
                          width: 24.w,
                          height: 24.h,
                          color: getColorSkin().secondaryColor1,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }
  }

  Widget _buildNavigationItems(BuildContext context) {
    final List<MutableNavItem> allItems = [
      MutableNavItem(
        icon: SvgPicture.asset(
          StringImage.service_icon,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Dịch vụ',
      ),
      MutableNavItem(
        icon: SvgPicture.asset(
          StringImage.user_profile,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Phiếu yêu cầu của tôi',
      ),
      MutableNavItem(
        icon: Image.asset(
          StringImage.execution_icon,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Phiếu yêu cầu cần thực hiện',
        isImage: true,
      ),
      MutableNavItem(
        icon: Image.asset(
          StringImage.signature_icon,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Phiếu yêu cầu cần phê duyệt',
        isImage: true,
      ),
      MutableNavItem(
        icon: Image.asset(
          StringImage.speech_bubble,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Phiếu yêu cầu trợ lý ',
        isImage: true,
      ),
    ];

    if (featureData?.data?.features != null) {
      for (var feature in featureData!.data!.features!) {
        if (feature.id == null) continue;

        int navIndex = -1;
        if (feature.id! >= 2 && feature.id! <= 6) {
          navIndex = feature.id! - 2;
        }

        if (navIndex >= 0 && navIndex < allItems.length) {
          if (feature.name != null && feature.name!.isNotEmpty) {
            allItems[navIndex].label = feature.name!;
          }

          // if (feature.icon != null && feature.icon!.isNotEmpty) {
          //   try {
          //     // Try to decode as base64
          //     final Uint8List bytes = base64Decode(feature.icon!);

          //     // Check if it's an SVG by looking at the first few bytes
          //     String svgHeader = String.fromCharCodes(bytes.sublist(0, bytes.length > 30 ? 30 : bytes.length));
          //     bool isSvg = svgHeader.trim().toLowerCase().startsWith('<svg');

          //     if (isSvg) {
          //       // Handle SVG data
          //       log('Detected SVG data');
          //       allItems[navIndex].icon = SvgPicture.memory(
          //         bytes,
          //         width: 20.w,
          //         height: 20.h,
          //         fit: BoxFit.contain,
          //       );
          //       allItems[navIndex].isImage = true;
          //     } else {
          //       // Handle bitmap image data
          //       log('Detected bitmap image data');
          //       allItems[navIndex].icon = Image.memory(
          //         bytes,
          //         width: 20.w,
          //         height: 20.h,
          //         fit: BoxFit.contain,
          //         errorBuilder: (context, error, stackTrace) {
          //           log('Error rendering image: $error');
          //           return Icon(
          //             Icons.image_not_supported,
          //             size: 20.w,
          //             color: getColorSkin().primaryBlue,
          //           );
          //         },
          //       );
          //       allItems[navIndex].isImage = true;
          //     }
          //   } catch (e) {
          //     log('Error processing icon data: $e');
          //     // Keep default icon if decode fails
          //   }
          // }
        }
      }
    }

    List<MutableNavItem> visibleItems = [];
    visibleItems.add(allItems[0]);

    if (tabVisibility[1] == true) visibleItems.add(allItems[1]); // My Ticket
    if (tabVisibility[2] == true) visibleItems.add(allItems[2]); // Execution
    if (tabVisibility[3] == true) visibleItems.add(allItems[3]); // Approval
    if (tabVisibility[4] == true) visibleItems.add(allItems[4]); // Assistant

    final List<Widget> columnChildren = [];

    for (int i = 0; i < visibleItems.length; i++) {
      final item = visibleItems[i];
      final mappedIndex = i == 0
          ? 0
          : allItems.indexOf(item) == 1
              ? 1
              : allItems.indexOf(item) == 2
                  ? 2
                  : allItems.indexOf(item) == 3
                      ? 3
                      : allItems.indexOf(item) == 4
                          ? 4
                          : 0;

      final bool isSelected = mappedIndex == selectedIndex;

      columnChildren.add(
        _buildNavItem(
          index: mappedIndex,
          context: context,
          isSelected: isSelected,
          icon: item.icon,
          label: item.label,
          color: item.color,
        ),
      );

      if (isExpanded) {
        columnChildren.add(
          Divider(height: 1, thickness: 0.5, color: getColorSkin().ink5),
        );
      }
    }

    return Column(children: columnChildren);
  }

  Widget _buildNavItem({
    required int index,
    required BuildContext context,
    required bool isSelected,
    required Widget icon,
    required String label,
    Color? color,
    int badgeCount = 0,
  }) {
    final selectedColor = getColorSkin().primaryBlue;
    final unselectedColor = getColorSkin().ink1;

    final isItemSelected = selectedIndex != -1 && (index == 100 ? isNotificationSelected : index == selectedIndex);

    Widget iconWidget = icon;
    if (index == 100 && badgeCount > 0) {
      iconWidget = Stack(
        clipBehavior: Clip.none,
        children: [
          icon,
          Positioned(
            right: -6.w,
            top: -6.h,
            child: Container(
              padding: EdgeInsets.all(2.w),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              constraints: BoxConstraints(minWidth: 16.w, minHeight: 16.h),
              child: Text(
                badgeCount > 9 ? '9+' : '$badgeCount',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }

    return InkWell(
      onTap: () {
        if (index == 100) {
          context.read<NotiBloc>().add(ResetUnreadAmount());
        }
        onDestinationSelected(index);
      },
      child: Container(
        height: 52.h,
        width: widthAnimation.value < 100 ? 56 : double.infinity,
        decoration: BoxDecoration(
          color: isItemSelected ? getColorSkin().blue2 : Colors.transparent,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: widthAnimation.value < 100 ? MainAxisAlignment.center : MainAxisAlignment.start,
          children: [
            iconWidget,
            if (widthAnimation.value > 100) ...[
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  label,
                  style: getTypoSkin().label4Regular.copyWith(
                        color: isItemSelected ? selectedColor : unselectedColor,
                      ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    final List<MutableNavItem> footerItems = [
      MutableNavItem(
        icon: SvgPicture.asset(
          StringImage.alarm_bell,
          width: 20.w,
          height: 20.h,
          colorFilter: ColorFilter.mode(getColorSkin().ink1, BlendMode.srcIn),
        ),
        label: 'Thông báo',
      ),
      MutableNavItem(
        icon: SvgPicture.asset(
          StringImage.user,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Tài khoản',
      ),
      MutableNavItem(
        icon: SvgPicture.asset(
          StringImage.home,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Quay về Super app',
      ),
      MutableNavItem(
        icon: SvgPicture.asset(
          StringImage.ic_logout,
          width: 20.w,
          height: 20.h,
        ),
        label: 'Đăng xuất',
      ),
    ];

    return Column(
      children: [
        Divider(height: 1, thickness: 0.5, color: getColorSkin().ink5),
        BlocBuilder<NotiBloc, NotiState>(
          buildWhen: (_, state) => state is UnreadLoaded,
          builder: (ctx, state) {
            final count = state is UnreadLoaded ? state.unreadAmount : 0;
            return _buildNavItem(
              index: 100,
              context: ctx,
              isSelected: isNotificationSelected,
              icon: footerItems[0].icon,
              label: footerItems[0].label,
              color: footerItems[0].color,
              badgeCount: count,
            );
          },
        ),
        Divider(height: 1, thickness: 0.5, color: getColorSkin().ink5),
        _buildNavItem(
          index: 101,
          context: context,
          isSelected: false,
          icon: footerItems[1].icon,
          label: footerItems[1].label,
          color: footerItems[1].color,
        ),
        Divider(height: 1, thickness: 0.5, color: getColorSkin().ink5),
        _buildNavItem(
          index: 102,
          context: context,
          isSelected: false,
          icon: footerItems[2].icon,
          label: footerItems[2].label,
          color: footerItems[2].color,
        ),
        Divider(height: 1, thickness: 0.5, color: getColorSkin().ink5),
        isExpanded
            ? Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0.h, horizontal: 10.w),
                child: Container(
                  width: 266.w,
                  height: 54.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: getColorSkin().red2,
                  ),
                  child: InkWell(
                    onTap: () => onDestinationSelected(103),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        footerItems[3].icon,
                        const SizedBox(width: 8),
                        Text(
                          footerItems[3].label,
                          style: getTypoSkin().label4Regular.copyWith(
                                color: getColorSkin().red,
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : _buildNavItem(
                index: 103,
                context: context,
                isSelected: false,
                icon: footerItems[3].icon,
                label: footerItems[3].label,
                color: footerItems[3].color,
              ),
        const SizedBox(height: 12),
      ],
    );
  }
}

class NavItem {
  final Widget icon;
  final String label;
  final Color? color;
  final bool isNewGroup;
  final bool isImage;

  NavItem({
    required this.icon,
    required this.label,
    this.color,
    this.isNewGroup = false,
    this.isImage = false,
  });
}

class MutableNavItem {
  Widget icon;
  String label;
  Color? color;
  bool isNewGroup;
  bool isImage;

  MutableNavItem({
    required this.icon,
    required this.label,
    this.color,
    this.isNewGroup = false,
    this.isImage = false,
  });
}
