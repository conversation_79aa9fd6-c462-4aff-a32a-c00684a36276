import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/overlay/overlay_bloc.dart' as custom_overlay;

class OverlayWidget extends StatelessWidget {
  const OverlayWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<custom_overlay.OverlayBloc, custom_overlay.OverlayState>(
      builder: (context, state) {
        debugPrint('OverlayBloc state: show=${state.show}, content=${state.content}');
        debugPrint('OverlayBloc state type: ${state.runtimeType}');
        if (state.show && state.content != null) {
          debugPrint('Rendering overlay content');
          return Material(
            color: Colors.transparent,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Container(
                    color: Colors.black.withOpacity(0.5),
                  ),
                ),
                Center(
                  child: state.content!,
                ),
              ],
            ),
          );
        }
        debugPrint('No overlay content to show');
        return const SizedBox.shrink();
      },
    );
  }
} 