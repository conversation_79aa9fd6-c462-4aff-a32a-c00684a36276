import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/event/request_relation_ticket_history_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/request_relation_ticket_history_state.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequestRelationTicketHistoryBloc extends Bloc<
    RequestRelationTicketHistoryEvent, RequestRelationTicketHistoryState> {
  final TicketOtherActionRepository _ticketOtherActionRepository;

  RequestRelationTicketHistoryBloc(
      {required TicketOtherActionRepository ticketOtherActionRepository})
      : _ticketOtherActionRepository = ticketOtherActionRepository,
        super(const RequestRelationTicketHistoryState()) {
    on<GetRequestRelationTicketHistory>(_onGetRequestRelationTicketHistory);
  }

  Future<void> _onGetRequestRelationTicketHistory(
      GetRequestRelationTicketHistory event,
      Emitter<RequestRelationTicketHistoryState> emit) async {
    try {
      emit(state.copyWith(
          status: ServiceStatus.loading,
          response: null,
          responseFrom: null,
          errorMessage: ''));
      final response =
          await _ticketOtherActionRepository.getTicketLink(event.ticketId);
      final responseFrom =
          await _ticketOtherActionRepository.getTicketFrom(event.ticketId);
      emit(state.copyWith(
          status: ServiceStatus.success,
          response: response,
          responseFrom: responseFrom));
    } catch (e) {
      emit(state.copyWith(
          status: ServiceStatus.failure,
          errorMessage: e.toString(),
          response: null,
          responseFrom: null));
    }
  }
}
