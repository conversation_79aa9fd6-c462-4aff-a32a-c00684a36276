import 'dart:convert';
import 'dart:math';

import 'package:eapprove/models/generic_respone_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_content_model.dart';
import 'package:eapprove/models/pyc/approve_pyc/approve_request_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_content_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_count_model.dart';
import 'package:eapprove/models/pyc/assistant_pyc/assistant_request_model.dart';
import 'package:eapprove/models/pyc/delete_draft_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/filter_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_response_model.dart';
import 'package:eapprove/models/pyc/generic_data_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_content_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_count_model.dart';
import 'package:eapprove/models/pyc/my_pyc/my_pyc_request_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:eapprove/models/pyc/process_pyc/proc_request_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_data_model.dart';
import 'package:eapprove/models/pyc/filter_pyc/search_filter_request_model.dart';
import 'package:eapprove/models/pyc/pyc_count_model.dart';
import 'package:eapprove/models/pyc/status_ticket_model.dart';
import 'package:eapprove/services/api_services.dart';
import 'package:flutter/material.dart';
import 'dart:developer' as developer;

class PycRepository {
  final ApiService _apiService;
  PycRepository({required ApiService apiService}) : _apiService = apiService;

  Future<GenericResponseModel<List<ListTaskDefKeyDataModel>>> getListTaskDefKey({
    required ListTaskDefKeyRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/bpmProcInst/getListTaskDefKeyByServiceId',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('getListTaskDefKey response: ${response.body}');
        return GenericResponseModel<List<ListTaskDefKeyDataModel>>.fromJson(
          jsonData,
          (data) => (data as List).map((item) => ListTaskDefKeyDataModel.fromJson(item)).toList(),
        );
      } else {
        throw Exception("Không thể tải danh sách taskDefKey: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Lỗi getListTaskDefKey: $e\n$stackTrace');
      throw Exception("Lỗi khi lấy danh sách taskDefKey: $e");
    }
  }

  Future<GenericResponseModel<List<ListChartIdDataModel>>> getListChartId() async {
    try {
      final response = await _apiService.get('customer/chart/getAllChartActive');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('[Repository] getListChartId response: ${response.body}');
        return GenericResponseModel<List<ListChartIdDataModel>>.fromJson(
          jsonData,
          (data) => (data as List).map((item) => ListChartIdDataModel.fromJson(item)).toList(),
        );
      } else {
        throw Exception("Không thể tải danh sách biểu đồ: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Lỗi getListChartId: $e\n$stackTrace');
      throw Exception("Lỗi khi lấy danh sách biểu đồ: $e");
    }
  }

  Future<GenericResponseModel<List<OrgData>>> getOrgChart({
    required ChartFilterRequestModel requestModel,
  }) async {
    try {
      final response = await _apiService.post(
        'customer/chart-node/getLoadTemplate',
        requestModel.toJson(),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        final result = GenericResponseModel<List<OrgData>>.fromJson(
          jsonData,
          (data) => (data as List).map((e) => OrgData.fromJson(e)).toList(),
        );

        return result;
      } else {
        throw Exception("Lỗi tải sơ đồ tổ chức: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Lỗi khi gọi getOrgChart: $e');
      throw Exception("Lỗi khi tải dữ liệu sơ đồ tổ chức: $e");
    }
  }

  Future<GenericResponseModel<List<FilterServiceData>>> getServiceList({
    required RequestListService requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/service-pack/getFilterServiceOption',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('Service list response: ${response.body}');
        return GenericResponseModel<List<FilterServiceData>>.fromJson(
          jsonData,
          (data) => (data as List).map((item) => FilterServiceData.fromJson(item)).toList(),
        );
      } else {
        throw Exception("Không thể tải danh sách dịch vụ: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Lỗi khi lấy danh sách dịch vụ: $e, stackTrace: $stackTrace');
      throw Exception("Lỗi khi lấy danh sách dịch vụ: $e");
    }
  }

  Future<List<ListUserInfoDataModel>> getListCreatedUser({
    required ListCreatedUserRequestModel requestBody,
    required int chartId,
  }) async {
    try {
      final response = await _apiService.post(
        'customer/v1/userInfo/getUserInfoFilter',
        requestBody.toJson(),
      );
      debugPrint('getListCreatedUser response: ${requestBody}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final SearchFilterResponseModel parsed = SearchFilterResponseModel.fromJson(jsonData);
        debugPrint('Response getListCreatedUser: ${response.body}');
        return parsed.data;
      } else {
        throw Exception("Không thể tải danh sách người trình: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getListCreatedUser: $e\n$stackTrace');
      rethrow;
    }
  }

  Future<SearchFilterResponseModel> getAssigneeList({
    required ListAssigneeRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'customer/v1/userInfo/getUserInfoFilter',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final SearchFilterResponseModel serviceResponseModel = SearchFilterResponseModel.fromJson(jsonData);

        debugPrint('[ServiceRepository]: serviceResponseModel: $serviceResponseModel');
        return serviceResponseModel;
      } else {
        throw Exception("Không thể tải danh sách người thực hiện: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getAssigneeList: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching AssigneeList: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<ProcContent>>> getProcPyc({required ProcRequestModel requestBody}) async {
    try {
      final response = await _apiService.post('business-process/task/myTask', requestBody.toJson());
      debugPrint('ProcPyc full response: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<ProcContent>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<ProcContent>.fromJson(
            json,
            (itemJson) => ProcContent.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getProcPyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching MyPyc: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<MyPycContent>>> getMyPyc({required MyPycRequestModel requestBody}) async {
    try {
      final response = await _apiService.post('business-process/bpmProcInst/search', requestBody.toJson());
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        final result = GenericResponseModel<GenericDataPyc<MyPycContent>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<MyPycContent>.fromJson(
            json,
            (itemJson) => MyPycContent.fromJson(itemJson),
          ),
        );
        return result;
      } else {
        debugPrint('getMyPyc - Error status code: ${response.statusCode}, Body: ${response.body}');
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getMyPyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching MyPyc: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<ApproveContent>>> getApprovePyc(
      {required ApproveRequestModel requestBody}) async {
    try {
      final response = await _apiService.post('business-process/task/myTask', requestBody.toJson());

      debugPrint('ApprovePyc full response: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<ApproveContent>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<ApproveContent>.fromJson(
            json,
            (itemJson) => ApproveContent.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getApprovePyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching ApprovePyc: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<AssisContent>>> getAssisPyc(
      {required AssisRequestModel requestBody}) async {
    try {
      final response = await _apiService.post("business-process/assistant/ticket-search", requestBody.toJson());
      debugPrint('AssistantPyc full response: ${response.body}');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<AssisContent>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<AssisContent>.fromJson(
            json,
            (itemJson) => AssisContent.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getAssisPyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching AssisPyc: $e");
    }
  }

  Future<GenericResponseModel<List<FilterData>>> getFilterPyc({
    required FilterRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/bpm-ticket-filter/search-filter',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        final List<dynamic> rawList = jsonData['data'] ?? [];

        final List<FilterData> filters = rawList.map((item) => FilterData.fromJson(item)).toList();
        debugPrint('FilterPyc full response: ${response.body}');
        return GenericResponseModel(
          code: jsonData['code'],
          message: jsonData['message'],
          data: filters,
        );
      } else {
        throw Exception('Failed with status: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in getFilterPyc: $e, stackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<GenericResponseModel<GenericDataPyc<TicketContentModel>>> getTicketFilterPyc(
      {required FilterTicketRequestModel requestBody}) async {
    try {
      final response =
          await _apiService.post("business-process/bpm-ticket-filter/search-filter-ticket", requestBody.toJson());
      debugPrint('FilterTicketPyc full response: ${response.body}');
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<TicketContentModel>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<TicketContentModel>.fromJson(
            json,
            (itemJson) => TicketContentModel.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in TicketFilterPyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching AssisPyc: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<ListPriorityDataModel>>> getListPriority({
    required ListPriorityRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/priority-management/search',
        requestBody.toJson(),
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<ListPriorityDataModel>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<ListPriorityDataModel>.fromJson(
            json,
            (itemJson) => ListPriorityDataModel.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getListPriority: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching ListPriority: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<SearchFilterContentModel>>> getSearchFilterPyc({
    required SearchFilterRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/bpm-ticket-filter/search-filter-ticket-mobile',
        requestBody.toJson(),
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<SearchFilterContentModel>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<SearchFilterContentModel>.fromJson(
            json,
            (itemJson) => SearchFilterContentModel.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load data, status code: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getSearchFilterPyc: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching SearchFilterPyc: $e");
    }
  }

  Future<GenericResponseModel<MyPycCountDataModel>> getMyPycCount({
    required MyPycCountRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/ticket/count',
        requestBody.toJson(),
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<MyPycCountDataModel>.fromJson(
          jsonData,
          (json) => MyPycCountDataModel.fromJson(json),
        );
      } else {
        throw Exception("Error fetching MyPycCount: $e");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getMyPycCount: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching MyPycCount: $e");
    }
  }

  Future<GenericResponseModel<ProcAppCountDataModel>> getProcAppCount({
    required ProcAppCountRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/task/count2',
        requestBody.toJson(),
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<ProcAppCountDataModel>.fromJson(
          jsonData,
          (json) => ProcAppCountDataModel.fromJson(json),
        );
      } else {
        throw Exception("Error fetching ProcAppCount: $e");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getProcAppCount: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching ProcAppCount: $e");
    }
  }

  Future<GenericResponseModel<List<AssisPycCountModel>>> getAssisPycCount({
    required AssisPycCountRequestModel requestBody,
  }) async {
    try {
      final response = await _apiService.post(
        'business-process/assistant/ticket-count',
        requestBody.toJson(),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return GenericResponseModel<List<AssisPycCountModel>>.fromJson(
          jsonData,
          (data) => (data as List<dynamic>).map((e) => AssisPycCountModel.fromJson(e)).toList(),
        );
      } else {
        throw Exception("Error fetching AssisPycCount");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getAssisPycCount: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching AssisPycCount: $e");
    }
  }

  Future<GenericResponseModel<GenericDataPyc<StatusContentModel>>> getStatusTicket(
      {required StatusTicketRequest requestBody}) async {
    try {
      final response = await _apiService.post(
        'customer/systemConfig/searchSystemConfig',
        requestBody.toJson(),
      );

      debugPrint('StatusTicketList response: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return GenericResponseModel<GenericDataPyc<StatusContentModel>>.fromJson(
          jsonData,
          (json) => GenericDataPyc<StatusContentModel>.fromJson(
            json,
            (itemJson) => StatusContentModel.fromJson(itemJson),
          ),
        );
      } else {
        throw Exception("Failed to load StatusTicketList: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in getStatusTicketList: $e, stackTrace: $stackTrace');
      throw Exception("Error fetching StatusTicketList: $e");
    }
  }

  Future<DeleteDraftResponseModel> deleteDraftTicket(List<int> ticketId) async {
    try {
      final response = await _apiService.post('business-process/delete/draft', ticketId);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return DeleteDraftResponseModel.fromJson(jsonData);
      } else {
        throw Exception("Lỗi xóa nháp: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Lỗi xóa nháp: $e, stackTrace: $stackTrace');
      throw Exception("Lỗi xóa nháp: $e");
    }
  }
}
