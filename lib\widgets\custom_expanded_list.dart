import 'package:eapprove/models/pyc/process_pyc/proc_content_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/button_size.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/button.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:convert';

import 'package:hive_flutter/hive_flutter.dart';

class CustomExpandedList<T> extends StatefulWidget {
  static const double _defaultIconContainerSize = 40.0;
  static const double _defaultIconSize = 32.0;
  static const double _defaultChevronSize = 24.0;

  final String title;
  final String? iconPath;
  final String? svgIconPath;
  final String? imageNetwork;
  final bool? isExpanded;
  final List<T>? children;
  final Function(T)? onChildTap;
  final String Function(T)? getTitle;
  final bool Function(T)? hasChevron;
  final Widget? child;

  final String? expandedSvgIconPath;
  final String? collapsedSvgIconPath;
  final String? assignee;
  final Icon? expandedIcon;
  final Icon? collapsedIcon;

  final Color trailingIconColor;
  final TextStyle? titleStyle;
  final EdgeInsets? childPadding;
  final EdgeInsets? childrenPadding;
  final EdgeInsets? paddingContainer;
  final double? cardElevation;
  final ShapeBorder? shape;
  final Color? cardColor;
  final EdgeInsets? cardMargin;
  final String? defaultImage;
  final bool? isBuildLeading;
  final double? iconWidth;
  final double? iconHeight;
  final double? imageWidth;
  final double? imageHeight;
  final dynamic ticket;
  final bool? isCommonInfo;
  final bool? isProcessDetail;
  final bool? isInputBlock;

  final VoidCallback? onHistoryPressed;
  final VoidCallback? onAdditionalContentPressed;
  final VoidCallback? onPaymentSchedulePressed;
  final VoidCallback? onAttachmentListPressed;
  final VoidCallback? onResubmitPressed;
  final VoidCallback? onAdditionalRequestPressed;
  final VoidCallback? onReturnPressed;
  final VoidCallback? onApprovePressed;

  const CustomExpandedList({
    super.key,
    required this.title,
    this.iconPath,
    this.svgIconPath,
    this.imageNetwork,
    this.isExpanded = false,
    this.children,
    this.onChildTap,
    this.getTitle,
    this.hasChevron,
    this.child,
    this.expandedSvgIconPath,
    this.collapsedSvgIconPath,
    this.expandedIcon,
    this.collapsedIcon,
    this.trailingIconColor = Colors.blue,
    this.titleStyle,
    this.childPadding,
    this.childrenPadding,
    this.paddingContainer = EdgeInsets.zero,
    this.cardElevation = 0,
    this.cardColor = Colors.white,
    this.shape = const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(0)),
    ),
    this.cardMargin = const EdgeInsets.symmetric(vertical: 0.5),
    this.defaultImage,
    this.isBuildLeading = true,
    this.iconWidth,
    this.iconHeight,
    this.imageWidth,
    this.imageHeight,
    this.ticket,
    this.isCommonInfo = false,
    this.isProcessDetail = false,
    this.isInputBlock = false,
    this.onHistoryPressed,
    this.onAdditionalContentPressed,
    this.onPaymentSchedulePressed,
    this.onAttachmentListPressed,
    this.onResubmitPressed,
    this.onAdditionalRequestPressed,
    this.onReturnPressed,
    this.onApprovePressed,
    this.assignee,
  });

  @override
  State<CustomExpandedList<T>> createState() => _CustomExpandedListState<T>();
}

class _CustomExpandedListState<T> extends State<CustomExpandedList<T>> {
  late bool _isExpanded;
  final isTablet = DeviceUtils.isTablet;
  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isExpanded ?? false;
  }

  List<Widget> _buildChildren() {
    if (widget.child != null) {
      return [
        Padding(
          padding: widget.childPadding ?? EdgeInsets.only(left: 0.w),
          child: widget.child!,
        ),
      ];
    }

    if (widget.children != null && widget.getTitle != null) {
      return widget.children!.map(_buildChildItem).toList();
    }

    return [];
  }

  Widget _buildChildItem(T child) {
    return ListTile(
      title: Text(
        widget.getTitle!(child),
        style: getTypoSkin().title6Regular.copyWith(color: getColorSkin().ink1),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
      trailing: _buildChildTrailingIcon(child),
      onTap: () => widget.onChildTap?.call(child),
    );
  }

  Widget? _buildChildTrailingIcon(T child) {
    if (widget.hasChevron != null && widget.hasChevron!(child)) {
      if (widget.collapsedSvgIconPath != null) {
        return SvgPicture.asset(
          widget.collapsedSvgIconPath!,
          width: CustomExpandedList._defaultChevronSize.w,
          height: CustomExpandedList._defaultChevronSize.h,
          colorFilter: ColorFilter.mode(widget.trailingIconColor, BlendMode.srcIn),
        );
      }
      return Icon(
        Icons.chevron_right,
        color: widget.trailingIconColor,
        size: CustomExpandedList._defaultChevronSize.w,
      );
    }
    return null;
  }

  Widget _buildLeadingIcon() {
    final String? iconData = widget.imageNetwork;
    final bool isBase64 = iconData != null && iconData.isNotEmpty && RegExp(r'^[A-Za-z0-9+/=]+$').hasMatch(iconData);

    if (isBase64) {
      return _buildBase64Icon(iconData!);
    } else if (widget.svgIconPath != null) {
      return _buildSvgIcon();
    } else if (widget.iconPath != null) {
      return _buildAssetIcon();
    }
    return _buildEmptyIcon();
  }

  Widget _buildBase64Icon(String iconData) {
    try {
      return _buildIconContainer(
        child: Image.memory(
          base64Decode(iconData),
          width: widget.imageWidth ?? CustomExpandedList._defaultIconSize.w,
          height: widget.imageHeight ?? CustomExpandedList._defaultIconSize.h,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Error loading base64 image: $error');
            return _buildDefaultImage();
          },
        ),
      );
    } catch (e) {
      return _buildIconContainer(child: _buildDefaultImage());
    }
  }

  Widget _buildSvgIcon() {
    return _buildIconContainer(
      child: SvgPicture.asset(
        widget.svgIconPath!,
        width: widget.imageWidth ?? CustomExpandedList._defaultIconSize.w,
        height: widget.imageHeight ?? CustomExpandedList._defaultIconSize.h,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildAssetIcon() {
    return _buildIconContainer(
      child: Image.asset(
        widget.iconPath!,
        width: widget.imageWidth ?? CustomExpandedList._defaultIconSize.w,
        height: widget.imageHeight ?? CustomExpandedList._defaultIconSize.h,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildEmptyIcon() {
    return _buildIconContainer();
  }

  Widget _buildIconContainer({Widget? child}) {
    return Container(
      child: SizedBox(
        width: widget.iconWidth ?? CustomExpandedList._defaultIconContainerSize.w,
        height: widget.iconHeight ?? CustomExpandedList._defaultIconContainerSize.h,
        child: child != null ? Center(child: child) : null,
      ),
    );
  }

  Widget _buildDefaultImage() {
    return Image.asset(
      widget.defaultImage ?? '',
      fit: BoxFit.contain,
    );
  }

  Widget _buildTrailingIcon() {
    if (_isExpanded) {
      return widget.expandedSvgIconPath != null
          ? SvgPicture.asset(widget.expandedSvgIconPath!)
          : widget.expandedIcon ??
              Icon(
                Icons.keyboard_arrow_down,
                color: widget.trailingIconColor,
              );
    }
    return widget.collapsedSvgIconPath != null
        ? SvgPicture.asset(widget.collapsedSvgIconPath!)
        : widget.collapsedIcon ??
            Icon(
              Icons.chevron_right,
              color: widget.trailingIconColor,
            );
  }

  Widget _buildActionButton(
      bool isCommonInfo, bool isProcessDetail, bool isInputBlock, dynamic ticket, String? assignee) {
    final isResubmit = ticket?.ticketStatus == 'DELETED_BY_RU' || ticket?.ticketStatus == 'RECALLED';
    final isSuccess =
        ticket?.ticketStatus == 'COMPLETED' || ticket?.ticketStatus == 'COMPLETE' || ticket?.ticketStatus == 'CLOSED';
    final isInPerformPhase = ticket is ProcContent;
    Box box = Hive.box('authentication');
    String? username = box.get('username');
    final isAssignee = assignee == username;
    List<Widget> buttons = [];
    final isCreator = ticket?.ticketStartUserId == username;
    if (isInputBlock) {
      buttons.addAll([
        if (isCreator)
          FFilledButton(
            onPressed: widget.onAdditionalRequestPressed,
            size: FButtonSize.size24,
            backgroundColor: getColorSkin().white,
            borderColor: getColorSkin().primaryBlue,
            child: Text('Yêu cầu bổ sung',
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue, height: 1.0)),
          ),
        FFilledButton(
          onPressed: widget.onReturnPressed,
          size: FButtonSize.size24,
          backgroundColor: getColorSkin().white,
          borderColor: getColorSkin().primaryBlue,
          child:
              Text('Trả về', style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue, height: 1.0)),
        ),
        if (isSuccess)
          FFilledButton(
            onPressed: widget.onApprovePressed,
            size: FButtonSize.size24,
            backgroundColor: getColorSkin().primaryBlue,
            child: Text(isInPerformPhase ? 'Thực hiện' : 'Phê duyệt',
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().white, height: 1.0)),
          ),
      ]);
    } else if (isCommonInfo) {
      buttons.addAll([
        FFilledButton(
          onPressed: widget.onHistoryPressed,
          size: FButtonSize.size24,
          backgroundColor: getColorSkin().white,
          borderColor: getColorSkin().primaryBlue,
          child: Text('Lịch sử phiếu',
              style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue, height: 1.0)),
        ),
        if (isAssignee)
          FFilledButton(
            onPressed: widget.onAdditionalContentPressed,
            size: FButtonSize.size24,
            backgroundColor: getColorSkin().white,
            borderColor: getColorSkin().primaryBlue,
            child: Text('Nội dung bổ sung',
                style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue, height: 1.0)),
          ),
        FFilledButton(
          onPressed: widget.onPaymentSchedulePressed,
          size: FButtonSize.size24,
          backgroundColor: getColorSkin().white,
          borderColor: getColorSkin().primaryBlue,
          child: Text('Xem lịch thanh toán',
              style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue, height: 1.0)),
        ),
        FFilledButton(
          onPressed: widget.onAttachmentListPressed,
          size: FButtonSize.size24,
          backgroundColor: getColorSkin().white,
          borderColor: getColorSkin().primaryBlue,
          child: Text('Danh sách tài liệu đính kèm',
              style: getTypoSkin().regular12.copyWith(color: getColorSkin().primaryBlue, height: 1.0)),
        ),
      ]);
    } else if (isProcessDetail && isResubmit) {
      buttons.add(
        FFilledButton(
          onPressed: widget.onResubmitPressed,
          size: FButtonSize.size24,
          backgroundColor: getColorSkin().primaryBlue,
          child:
              Text('Đệ trình lại', style: getTypoSkin().regular12.copyWith(color: getColorSkin().white, height: 1.0)),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          constraints: BoxConstraints(maxWidth: constraints.maxWidth * 0.7),
          child: Wrap(
            spacing: 8.w,
            runSpacing: 4.h,
            alignment: WrapAlignment.end,
            children: buttons,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return Card(
      margin: widget.cardMargin,
      color: widget.cardColor,
      elevation: widget.cardElevation ?? 0,
      shape: widget.shape,
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          childrenPadding: widget.childrenPadding ?? EdgeInsets.only(left: 0.w),
          leading: widget.isBuildLeading == true
              ? isTablet
                  ? _buildTrailingIcon()
                  : _buildLeadingIcon()
              : null,
          title: Text(
            widget.title,
            style: widget.titleStyle ?? getTypoSkin().title6Regular.copyWith(color: getColorSkin().ink1),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          initiallyExpanded: widget.isExpanded ?? false,
          trailing: isTablet
              ? _buildActionButton(widget.isCommonInfo ?? false, widget.isProcessDetail ?? false,
                  widget.isInputBlock ?? false, widget.ticket, widget.assignee ?? '')
              : _buildTrailingIcon(),
          onExpansionChanged: (expanded) {
            setState(() {
              _isExpanded = expanded;
            });
          },
          children: _buildChildren(),
        ),
      ),
    );
  }
}

class ServiceChildItem {
  final String title;
  final bool hasChevron;

  const ServiceChildItem({
    required this.title,
    this.hasChevron = false,
  });
}
