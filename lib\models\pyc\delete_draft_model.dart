class DeleteDraftResponseModel {
  final int? code;
  final String? message;
  final bool? data;

  DeleteDraftResponseModel({
    this.code,
    this.message,
    this.data,
  });

  factory DeleteDraftResponseModel.fromJson(Map<String, dynamic> json) {
    return DeleteDraftResponseModel(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: json['data'] ?? false,
    );
  }
}
