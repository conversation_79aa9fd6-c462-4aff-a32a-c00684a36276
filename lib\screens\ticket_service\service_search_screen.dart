import 'dart:async';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/service/service_bloc.dart';
import 'package:eapprove/blocs/service/service_event.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/common/search_history_manager.dart';
import 'package:eapprove/models/service/service_data_model.dart';
import 'package:eapprove/screens/common/search_bar.dart';
import 'package:eapprove/screens/common/search_history.dart';
import 'package:eapprove/screens/ticket_service/widget/pdf_download_dialog.dart';
import 'package:eapprove/screens/ticket_service/widget/service_bottomsheet.dart';
import 'package:eapprove/screens/ticket_service/widget/service_search_result.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ServiceSearchScreen extends StatefulWidget {
  const ServiceSearchScreen({super.key});

  static const String routeName = '/service_search_screen';

  static Route route() {
    return MaterialPageRoute<void>(
      builder: (context) => const ServiceSearchScreen(),
    );
  }

  @override
  State<ServiceSearchScreen> createState() => _ServiceSearchScreenState();
}

class _ServiceSearchScreenState extends State<ServiceSearchScreen> {
  static const String historyKey = 'service_history_search';
  String searchQuery = '';
  List<String> searchHistory = [];
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = context.read<ServiceBloc>().state;
      if (state.serviceModels?.data == null ||
          state.serviceModels!.data.isEmpty) {
        context.read<ServiceBloc>().add(const ServiceFetchRoot());
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _loadSearchHistory() async {
    final history =
        await SearchHistoryManager.getSearchHistory(historyKey: historyKey);
    setState(() {
      searchHistory = history;
    });
  }

  void _onSearch(String query) {
    if (query.trim().isEmpty) {
      setState(() {
        searchQuery = '';
      });
      return;
    }

    setState(() {
      searchQuery = query.toLowerCase();
    });

    context.read<ServiceBloc>().add(SearchService(query));

    SearchHistoryManager.addSearchTerm(query.trim(), historyKey: historyKey)
        .then((_) => _loadSearchHistory());
  }

  void _onSearchHistorySelected(String term) {
    _searchController.text = term;
    _onSearch(term);
  }

  void showServiceBottomSheet(BuildContext context, ServiceData serviceData) {
    final allServices =
        context.read<ServiceBloc>().state.serviceModels?.data ?? [];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (bottomSheetContext) => BlocProvider.value(
        value: context.read<ServiceBloc>(),
        child: ServiceBottomSheet(
          initialServiceData: serviceData,
          allServices: allServices,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.read<ServiceBloc>().add(const ServiceFetchRoot());
        return true;
      },
      child: BlocBuilder<ServiceBloc, ServiceState>(
        buildWhen: (previous, current) {
          return previous.pdfDownloadStatus != current.pdfDownloadStatus ||
              previous.pdfFilePath != current.pdfFilePath ||
              previous.errorMessage != current.errorMessage;
        },
        builder: (context, state) {
          if (state.pdfDownloadStatus == PdfDownloadStatus.success &&
              state.pdfFilePath != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              SnackbarCore.success('Tải PDF thành công');
              context.read<ServiceBloc>().add(const ClearPdfStatus());
            });
          } else if (state.pdfDownloadStatus == PdfDownloadStatus.failure &&
              state.errorMessage != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              SnackbarCore.error('Tải PDF thất bại');
              context.read<ServiceBloc>().add(const ClearPdfStatus());
            });
          }

          return Stack(
            children: [
              Scaffold(
                backgroundColor: getColorSkin().white,
                appBar: PreferredSize(
                  preferredSize: const Size.fromHeight(0),
                  child: AppBar(
                    backgroundColor: getColorSkin().white,
                    elevation: 0,
                  ),
                ),
                body: Column(
                  children: [
                    const SizedBox(height: 8),
                    SafeArea(
                      child: Padding(
                        padding:
                            EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
                        child: Row(
                          children: [
                            IconButton(
                              icon: SvgPicture.asset(StringImage.ic_arrow_left,
                                  width: 9.w, height: 18.h),
                              onPressed: () {
                                context
                                    .read<ServiceBloc>()
                                    .add(const ServiceFetchRoot());
                                Navigator.pop(context);
                              },
                            ),
                            Expanded(
                              child: CustomSearchBar(
                                controller: _searchController,
                                defaultText: "Tìm kiếm dịch vụ",
                                onSubmitted: _onSearch,
                                textInputAction: TextInputAction.search,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: BlocBuilder<ServiceBloc, ServiceState>(
                        buildWhen: (previous, current) {
                          return previous.status != current.status ||
                              previous.serviceModels != current.serviceModels ||
                              previous.searchKey != current.searchKey;
                        },
                        builder: (context, state) {
                          if (state.status == ServiceStatus.loading) {
                            return Center(
                                child: AppConstraint.buildLoading(context));
                          }

                          if (searchQuery.isEmpty) {
                            if (searchHistory.isEmpty) {
                              return Center(
                                child: Text(
                                  "Không có lịch sử tìm kiếm",
                                  style: getTypoSkin()
                                      .medium16
                                      .copyWith(color: getColorSkin().ink2),
                                ),
                              );
                            }

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: SearchHistory(
                                    searchHistory: searchHistory,
                                    onSearchSelected: _onSearchHistorySelected,
                                    onClearAll: () {
                                      SearchHistoryManager.clearSearchHistory(
                                              historyKey: historyKey)
                                          .then((_) => _loadSearchHistory());
                                    },
                                  ),
                                ),
                              ],
                            );
                          }

                          final allData = state.serviceModels?.data ?? [];

                          if (allData.isEmpty) {
                            return Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Center(
                                  child: Column(
                                    children: [
                                      SvgPicture.asset(
                                        StringImage.ic_no_result,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(left: 16.w),
                                        child: Text(
                                          "Không tìm thấy dịch vụ",
                                          style:
                                              getTypoSkin().medium16.copyWith(
                                                    color: getColorSkin().ink1,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          }

                          final matchingServices = allData.where((service) {
                            if (service.parentId == null) {
                              return false;
                            }

                            if (service.serviceName == null ||
                                service.notShowingMoblie == true ||
                                !service.serviceName!.toLowerCase().contains(searchQuery.toLowerCase())) {
                              return false;
                            }

                            ServiceData? currentService = service;
                            while (currentService != null && currentService.parentId != null) {
                              final parentService = allData.firstWhere(
                                    (s) => s.id == currentService!.parentId,
                                orElse: () => ServiceData(),
                              );

                              if (parentService.id == null || parentService.notShowingMoblie == true) {
                                return false;
                              }

                              currentService = parentService.id != null ? parentService : null;
                            }

                            return true;
                          }).toList();

                          if (matchingServices.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    StringImage.ic_no_result,
                                  ),
                                  SizedBox(height: 16.h),
                                  Text(
                                    "Không có dịch vụ nào",
                                    style: getTypoSkin().medium16.copyWith(
                                      color: getColorSkin().ink1,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(left: 16.w, top: 10.h),
                                child: Text(
                                  "Kết quả tìm kiếm",
                                  style: getTypoSkin().medium16.copyWith(
                                        color: getColorSkin().ink1,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: ServiceSearchResults(
                                    allServices: allData,
                                    matchingServices: matchingServices,
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              if (state.pdfDownloadStatus == PdfDownloadStatus.downloading)
                GestureDetector(
                  onTap: () {
                    context.read<ServiceBloc>().add(const CancelDownload());
                    SnackbarCore.error('Tải PDF đã bị hủy');
                  },
                  child: Container(
                    color: Colors.black.withOpacity(0.1),
                    child: Center(
                      child: GestureDetector(
                        onTap: () {},
                        child: const PdfDownloadDialog(
                          message: 'Đang tải PDF...',
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
