import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'dart:convert';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/account/user_list_event.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_bloc.dart';
import 'package:eapprove/blocs/authorize_management/list_user/list_user_info_event.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/chart/chart_event.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/form/form_event.dart';
import 'package:eapprove/blocs/form/form_state.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/overlay/overlay_bloc.dart';
import 'package:eapprove/enum/enum.dart';
import 'package:eapprove/models/form/bpmProcInst_create_request_model.dart';
import 'package:eapprove/models/form/form_model.dart';
import 'package:eapprove/models/form/form_xml_response_model.dart';
import 'package:eapprove/models/form/tao_to_trinh_request_body.dart';
import 'package:eapprove/models/form/upload_file_response_model.dart';
import 'package:eapprove/screens/payment_proposal/widgets/advanced_bottom_bar.dart';
import 'package:eapprove/screens/payment_proposal/widgets/inform_for_widget.dart';
import 'package:eapprove/screens/payment_proposal/widgets/information_submitter_card.dart';
import 'package:eapprove/screens/payment_step/widgets/payment_step_1.dart';
import 'package:eapprove/screens/pyc/overview_ticket_board_screen.dart';
import 'package:eapprove/utils/utils.dart';
import 'package:eapprove/widgets/header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/device_utils.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:collection/collection.dart';
import 'package:eapprove/common/form_v2/form_builder_v2.dart';
import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/services/form_submit_service.dart';
import 'package:eapprove/services/api_form_service.dart';
import 'package:eapprove/services/navigation_service.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

class UploadFileWithField {
  final UploadSingleFileResponseModel response;
  final String fieldName;

  UploadFileWithField(this.response, this.fieldName);
}

class PDFViewWrapper extends StatefulWidget {
  final String filePath;

  const PDFViewWrapper({Key? key, required this.filePath}) : super(key: key);

  @override
  State<PDFViewWrapper> createState() => _PDFViewWrapperState();
}

class _PDFViewWrapperState extends State<PDFViewWrapper> {
  bool _isDisposed = false;

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposed) return const SizedBox.shrink();

    return PDFView(
      key: ValueKey(widget.filePath),
      filePath: widget.filePath,
      autoSpacing: true,
      fitPolicy: FitPolicy.WIDTH,
      enableSwipe: true,
      swipeHorizontal: false,
      pageSnap: true,
      pageFling: true,
      preventLinkNavigation: true,
      onError: (error) {
        print('PDF Error: $error');
      },
      onPageError: (page, error) {
        print('PDF Page Error: $error');
      },
    );
  }
}

class TicketFormScreen extends StatefulWidget {
  final String? procDefId;
  final String title;
  final int? processId;
  final int? ticketId;
  final VoidCallback? onClose;

  const TicketFormScreen({
    super.key,
    this.procDefId,
    this.title = 'Tờ trình đề nghị thanh toán',
    this.processId,
    this.ticketId,
    this.onClose,
  });

  @override
  State<TicketFormScreen> createState() => _TicketFormScreenState();
}

class _TicketFormScreenState extends State<TicketFormScreen> {
  late final FormStateManager stateManager;
  FormModel? _formModel;
  final _informForKey = GlobalKey();
  bool _hasNavigated = false;
  late final FormBloc _formBloc;
  late final CheckTypeBloc _checkTypeBloc;
  late final PrintFileTemplateBloc _printFileTemplateBloc;
  bool _isLoading = false;
  final requestSubjectController = TextEditingController();
  bool _isFormInitialized = false;
  bool _isLoadingInFile = false;
  late List<Map<String, dynamic>> _zones;
  late List<Map<String, dynamic>> _elements;
  late StreamSubscription _subscription;
  List<String> assistantEmail = [];
  InformationSubmitterState _stateInfoBase = InformationSubmitterState();

  @override
  void initState() {
    super.initState();
    stateManager = FormStateManager();
    _zones = [];
    _elements = [];
    // Initialize _subscription with a dummy subscription
    _subscription = StreamController().stream.listen((_) {});
    // Store bloc references
    _formBloc = context.read<FormBloc>();
    _checkTypeBloc = context.read<CheckTypeBloc>();
    _printFileTemplateBloc = context.read<PrintFileTemplateBloc>();

    // Initialize the blocs
    stateManager.initBloc(
      context.read<DropdownBloc>(),
      _formBloc,
    );

    if (widget.procDefId != null) {
      _formBloc.add(LoadAllFormDataRequested(
        procDefId: widget.procDefId!,
        processId: widget.processId,
      ));

      _checkTypeBloc.add(
        CheckTypeRequested(
          serviceId: widget.ticketId ?? 0,
          procDefId: widget.procDefId ?? '',
        ),
      );

      _formBloc.add(
        GetApprovalVinaphacoRequested(
          procDefId: widget.procDefId!,
        ),
      );

      context.read<FormBloc>().add(GetListBaseUrl());
      context.read<ChartBloc>().add(FetchChartsByUser());
      context.read<UserListBloc>().add(FetchUserInfoByToken());
    }
  }

  @override
  void dispose() {
    stateManager.dispose();
    stateManager.clearForm();
    _hasNavigated = false;
    // Clear all bloc states using stored references
    _formBloc.add(FormReset());
    _checkTypeBloc.add(ClearCheckTypeState());
    _printFileTemplateBloc.add(ClearPrintFileState());

    _subscription.cancel();

    super.dispose();
  }

  void initData() async {}

  void _checkType() async {
    final requestSubject = _stateInfoBase.requestSubject;
    if (requestSubject == "" || requestSubject == null) {
      SnackbarCore.error('Vui lòng nhập tên phiếu yêu cầu');
      return;
    }

    final validate = stateManager.validateAll();

    if (!validate) {
      SnackbarCore.error('Vui lòng kiểm tra lại thông tin');
      return;
    }
    final data = stateManager.getAllFormData();
    developer.log('getAllFormDatagetAllFormData: $data', name: 'Submit form');

    final checkTypeState = _checkTypeBloc.state;
    final checkTypeResponse =
        checkTypeState is CheckTypeSuccess ? checkTypeState.response : null;
    // int processType = checkTypeResponse?.processType ?? 0;
    final isHasFileSignature =
        checkTypeResponse?.listSignForm.isNotEmpty == true;
    debugPrint('isHasFileSignature: $isHasFileSignature');

    _showLoadingDialog();
    if (isHasFileSignature) {
      // Case 3: Need to generate signature file from template, possible attachments
      _printFile();
      return;
    }
    if (checkTypeResponse?.listSignForm.isEmpty == true) {
      // Case 2: Has signature file uploaded, possible attachments
      _handleUploadAndSubmit();
      return;
    }
  }

  void _printFile() async {
    final requestSubject = _stateInfoBase.requestSubject;
    final prefixedData = _transformFormValuesToVariables();
    developer.log('Prefixed form data: $prefixedData', name: 'Submit form');
    // setState(() {
    //   _isLoadingInFile = true;
    // });

    // return;
    final checkTypeState = _checkTypeBloc.state;
    final checkTypeResponse =
        checkTypeState is CheckTypeSuccess ? checkTypeState.response : null;

    final requestBody = {
      'variables': prefixedData,
      "uploadWordsChange":
          checkTypeResponse?.listSignForm.firstOrNull?.uploadWordsChange ?? "",
      "printType": "pdf",
      "createVariables": {"txt_tenPhieu": requestSubject}
    };

    developer.log('\n\nRequest body: ${jsonEncode(requestBody)}\n\n',
        name: 'Submit form');
    // return;
    _printFileTemplateBloc.add(
      PrintFileTemplateRequested(
        requestBody: requestBody,
      ),
    );
  }

  /// Transform form values into the BPM variables format with special handling for "slt" fields
  Map<String, dynamic> _transformFormValuesToVariables() {
    final saveData = stateManager.getSaveFormState();
    developer.log('_transformFormValuesToVariablessaveData: $saveData',
        name: 'Submit form');
    // Add 'start_' prefix to all field names except requestSubject and _iso fields
    final Map<String, dynamic> prefixedData = {};
    saveData.forEach((key, value) {
      final widget = stateManager.findWidgetByName(key);
      final eventRessult = stateManager.getEventExpressionResults(key);
      developer.log('eventRessult: $eventRessult', name: 'Submit form');
      developer.log('widget: $key ${widget?.autoGenId}', name: 'Submit form');
      if (key != 'requestSubject' && !key.endsWith('_iso')) {
        // Format the variable according to BPM format
        final variable = {
          "value": value['value'] ?? "",
          "type":
              widget?.autoGenId == true ? "AUTO" : value['type'] ?? "String",
          "valueInfo": value['valueInfo'] ?? {}
        };

        prefixedData['${stateManager.taskDefKey}_$key'] = variable;

        if (key == 'slt_logo') {
          final widget = stateManager.findWidgetByName(key);
          if (key == 'slt_logo') {
            prefixedData['${key}_text'] = {
              "value": widget?.option?.firstOrNull?['label']?.toString() ?? "",
              "type": "String",
              "valueInfo": {}
            };
            if (widget?.optionType == "normal") {
              prefixedData['${stateManager.taskDefKey}_$key'] = {
                "value": value['value']?.toString() ?? "",
                "type": value['type'] ?? "String",
                "valueInfo": {"data": widget?.option}
              };
            }
          }
        }
      } else {
        prefixedData[key] = value;
        if (key == "requestSubject") {
          final requestSubject = _stateInfoBase.requestSubject;
          prefixedData[key] = {
            "value": requestSubject,
            "type": "String",
            "valueInfo": {}
          };
        }
      }
    });
    return prefixedData;
  }

  Widget _buildFormShimmer() {
    return SafeArea(
        child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 30.h),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  AppConstraint.buildShimmer(
                    child: Container(
                      width: double.infinity,
                      height: 200.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  for (int i = 0; i < 4; i++) ...[
                    AppConstraint.buildShimmer(
                      child: Container(
                        width: double.infinity,
                        height: 80.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          color: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(height: 12.h),
                  ],
                ],
              ),
            )));
  }

  void _showLoadingDialog() {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext context) {
        return AppConstraint.buildLoading(context);
      },
    );
  }

  void _hideLoadingDialog() {
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      if (Navigator.of(context, rootNavigator: true).canPop() &&
          ConfigImg.isHostApp) {
        Navigator.of(context, rootNavigator: true).pop();
      } else {
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
      }
    }
  }

  void _resetNavigation() {
    if (!ConfigImg.isHostApp) {
      Navigator.of(context, rootNavigator: true).pop();
      return;
    }

    if (!DeviceUtils.isTablet) {
      developer.log("widget.ticketId: ${widget.ticketId}",
          name: "widget.ticketId");
      developer.log("widget.procDefId: ${widget.procDefId}",
          name: "widget.procDefId");
      context.read<BottomNavBloc>().add(const SwitchTab(0));
      context.read<BottomNavBloc>().add(const SetBottomNavVisibility(true));
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
            builder: (context) => BottomNavScreen(
                  key: bottomNavKey,
                  isEmbedded: ConfigImg.isHostApp,
                )),
        (route) => false,
      );
    } else {
      // For tablet, we need to:
      // 1. Reset system UI mode
      // 2. Close current form
      // 3. Return to main tablet layout
      // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      // Navigator.of(context, rootNavigator: true).pop();
      widget.onClose?.call();
    }
  }

  void _initializeForm(FormInfoState formState) {
    if (_isFormInitialized) return;

    final formResponse = formState.formResponse!;
    final widgets = formResponse.data.template.form;
    final chartData = formState.chartData;
    final chartNodeCustomData = formState.chartNodeCustomData;
    final chartNodeData = formState.chartNodeData;
    developer.log('chartNodeData: $chartNodeData');
    final initialValues = <String, dynamic>{
      'system_chartId_service': chartData?.data?.firstOrNull?.id,
      'system_chartNodeCustomId':
          chartNodeCustomData?.data?.data?.firstOrNull?.id,
      'system_chartCustomId':
          chartNodeCustomData?.data?.data?.firstOrNull?.chartId,
      'system_chartNodeId_ticket':
          chartNodeData?.data?.firstOrNull?.chartNodeId,
    };

    for (var widget in widgets) {
      if (widget.name?.contains('spl') == true) {
        continue;
      }
      if (widget.name == 'dtm_ngayLapPhieu') {
        initialValues[widget.name.toString()] =
            Utils.convertToUtcIso(DateTime.now().toIso8601String());
      } else if (widget.name != 'slt_logo') {
        initialValues[widget.name.toString()] = widget.value;
      }
      if (widget.autoGenId == true) {
        initialValues[widget.name.toString()] = widget.autoGenIdValue != null
            ? double.parse(widget.autoGenIdValue.toString()).toInt()
            : null;
      }
    }

    developer.log('initialValues: ${widgets}', name: 'initialValues');
    stateManager.setAllFields(initialValues);
    stateManager.setWidgets(widgets);
    _isFormInitialized = true;
  }

  static void clearUploadState(BuildContext context) {
    context.read<FormBloc>().add(FormReset());
    context.read<CheckTypeBloc>().add(ClearCheckTypeState());
    context.read<PrintFileTemplateBloc>().add(ClearPrintFileState());
  }

  void _updateStateInfoBase(InformationSubmitterState value) {
    _stateInfoBase = value;
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils.isTablet;
    return GradientBackground(
      child: Scaffold(
        backgroundColor: getColorSkin().transparent,
        body: MultiBlocListener(
          listeners: [
            BlocListener<FormBloc, FormInfoState>(
              listenWhen: (prev, curr) =>
                  prev.confirmSubmitResponseModel !=
                      curr.confirmSubmitResponseModel ||
                  prev.bpmProcInstCreateResponse !=
                      curr.bpmProcInstCreateResponse ||
                  prev.uploadFileResponse != curr.uploadFileResponse ||
                  prev.errorMessage != curr.errorMessage,
              listener: (context, formState) {
                // Handle approval response
                if (formState.confirmSubmitResponseModel != null) {
                  final taskDefKey = formState
                      .confirmSubmitResponseModel?.data?.firstOrNull?.taskKey;
                  stateManager.setTaskDefKey(taskDefKey ?? '');
                }
                // Handle error state
                if (formState.errorMessage != null) {
                  developer.log('Form error222: ${formState.errorMessage}',
                      name: 'formState.bpmProcInstCreateResponse');

                  // Reset all loading states
                  setState(() {
                    _isLoading = false;
                    _isLoadingInFile = false;
                  });
                  // Hide all possible loading dialogs
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  }
                  // Hide overlay if it's showing
                  context.read<OverlayBloc>().add(HideOverlay());

                  SnackbarCore.error(
                      'Có lỗi xảy ra: ${formState.errorMessage}');
                  return;
                }

                // Handle form submission response
                if (formState.hasBpmProcInstCreateResponse) {
                  developer.log(
                      'formState.bpmProcInstCreateResponse: ${formState.bpmProcInstCreateResponse}',
                      name: 'formState.bpmProcInstCreateResponse');
                  // Reset all loading states
                  setState(() {
                    _isLoading = false;
                    _isLoadingInFile = false;
                  });
                  // Hide all possible loading dialogs
                  _hideLoadingDialog();

                  // Hide overlay
                  context.read<OverlayBloc>().add(HideOverlay());

                  if (formState.bpmProcInstCreateResponse?.code == 1) {
                    final isError = formState.bpmProcInstCreateResponse?.data
                            ?.actionApiResults?.isNotEmpty ==
                        true;
                    if (isError) {
                      SnackbarCore.error(formState.bpmProcInstCreateResponse
                              ?.data?.actionApiResults?.firstOrNull?.message ??
                          'Xảy ra lỗi trong quá trình đệ trình phiếu');
                    } else {
                      SnackbarCore.success(formState.bpmProcInstCreateResponse
                              ?.data?.actionApiResults?.firstOrNull?.message ??
                          'Đệ trình thành công, vui lòng chờ duyệt');
                      clearUploadState(context);
                      _resetNavigation();
                    }
                    return;
                  } else {
                    SnackbarCore.error(
                        'Đệ trình thất bại, vui lòng kiểm tra lại thông tin');
                    // Clear all states on failure
                    // clearUploadState(context);
                  }
                  return;
                }
              },
            ),
            BlocListener<PrintFileTemplateBloc, PrintFileTemplateState>(
              listener: (context, state) async {
                if (state is PrintFileTemplateSuccess) {
                  final dir = await getTemporaryDirectory();
                  final file = File('${dir.path}/temp.pdf');
                  developer.log('state.response: ${state.response}',
                      name: 'state.response');
                  await file.writeAsBytes(state.response);
                  _showSignatureAndWaitForConfirm(
                    localPath: file.path,
                    isPrintFile: true,
                  );
                } else if (state is PrintFileTemplateFailure) {
                  SnackbarCore.error('Không thể tạo file PDF: ${state.error}');
                  setState(() {
                    _isLoading = false;
                  });
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
          child: BlocBuilder<FormBloc, FormInfoState>(
            buildWhen: (prev, curr) =>
                prev.formResponse != curr.formResponse ||
                prev.isFormLoading != curr.isFormLoading ||
                prev.uploadFileResponse != curr.uploadFileResponse,
            builder: (context, formState) {
              if (formState?.isFormLoading == true) {
                return _buildFormShimmer();
              }

              if (formState.formResponse?.code == 1 &&
                  formState.formResponse?.data != null &&
                  !_isFormInitialized) {
                _initializeForm(formState);
              }

              if (formState.formResponse?.code == 1 &&
                  formState.formResponse?.data != null) {
                return Column(
                  children: [
                    if (isTablet)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Container(
                          width: double.infinity,
                          height: 40,
                          margin: EdgeInsets.only(top: 20),
                          padding: EdgeInsets.symmetric(
                              vertical: 8.h, horizontal: 10.w),
                          decoration: BoxDecoration(
                            color: getColorSkin().white,
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            widget.title,
                            style: getTypoSkin().regular12.copyWith(
                                color: getColorSkin().black,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                    if (!isTablet)
                      Header(
                        title: widget.title,
                      ),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(
                          horizontal: 14.w,
                          vertical: 12.h,
                        ),
                        child: Column(
                          children: [
                            InformationSubmitterCard(
                              title: widget.title,
                              processId: widget.processId,
                              chartData: formState.chartData,
                              chartNodeData: formState.chartNodeData,
                              submitterInfoResponse:
                                  formState.submitterInfoResponse,
                              stateManager: stateManager,
                              allSubmissionType: formState.allSubmissionType,
                              onChanged: (value) {
                                developer.log(
                                    'InformationSubmitterCardvalue: ${value?.requestSubject}',
                                    name: 'value');
                                _updateStateInfoBase(
                                    value as InformationSubmitterState);
                                stateManager.setFieldValue(
                                    'requestSubject', value.requestSubject);
                              },
                            ),
                            FormBuilderV2(
                              widgets:
                                  formState.formResponse!.data.template.form,
                              stateManager: stateManager,
                              row: formState.formResponse!.data.template.row,
                              step: formState.formResponse!.data.template.step,
                              col: formState.formResponse!.data.template.column,
                              tab: formState.formResponse!.data.template.tab,
                            ),
                            InformForCard(
                              key: _informForKey,
                              chartData: formState.chartData?.data ?? [],
                              onSelected: (selectedItems) {
                                final selectedItemsString =
                                    selectedItems.map((e) => e.value).toList();
                                developer
                                    .log('selectedItems: $selectedItemsString');
                                assistantEmail = selectedItemsString;
                                // stateManager.setFieldValue(
                                //     'informFor', selectedItems);
                              },
                            ),
                            if (_isLoading)
                              Container(
                                color: Colors.black.withOpacity(0.3),
                                child:
                                    Center(child: CircularProgressIndicator()),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              }
              return Container();
            },
          ),
        ),
        bottomNavigationBar: BlocBuilder<FormBloc, FormInfoState>(
          builder: (context, formState) {
            final VoidCallback? buttonCallback =
                formState.hasData ? _checkType : null;
            return buildBottomBar(
              context,
              buttonText: 'Đệ trình',
              onButtonPressed: buttonCallback,
              nextScreen: const PaymentStep1Screen(),
            );
          },
        ),
      ),
    );
  }

  Future<UploadSingleFileResponseModel?> _uploadSingleFile(Map fileData) async {
    final completer = Completer<UploadSingleFileResponseModel?>();
    late final StreamSubscription sub;

    // Add validation for files
    if (fileData['files'] == null || (fileData['files'] as List).isEmpty) {
      completer.complete(null);
      return completer.future;
    }

    sub = _formBloc.stream.listen((state) {
      if (state.uploadFileResponse != null && state.name == fileData['name']) {
        completer.complete(state.uploadFileResponse);
        sub.cancel();
      }
    });
    _formBloc.add(UploadFileRequested(
      fileData: fileData['files'].first,
      name: fileData['name'],
      fileType: fileData['fileType'],
    ));
    return completer.future;
  }

  Future<List<UploadFileWithField>> _uploadAttachments(
      List<Map> attachmentFiles) async {
    List<UploadFileWithField> responses = [];
    for (final fileData in attachmentFiles) {
      final resp = await _uploadSingleFile(fileData);
      if (resp != null) {
        responses.add(UploadFileWithField(resp, fileData['name']));
      }
    }
    return responses;
  }

  Future<void> _handleUploadAndSubmit() async {
    final uploadedFiles = stateManager
        .getUploadedFiles()
        .where((e) => e['isDefault'] != true)
        .toList();
    final signatureFile =
        uploadedFiles.firstWhereOrNull((e) => e['fileType'] == 'signature');
    final attachmentFiles =
        uploadedFiles.where((e) => e['fileType'] != 'signature').toList();

    final isHasFileSignature = stateManager
        .getFieldValueByType(FormItemType.fileUpload)
        ?.where((e) => e['fileType'] == 'signature')
        .toList();

    UploadSingleFileResponseModel? signatureResponse;
    List<UploadFileWithField> attachmentResponses = [];

    // 1. Upload signature file and wait for response
    if (signatureFile != null) {
      try {
        signatureResponse = await _uploadSingleFile(signatureFile);
      } catch (e) {
        SnackbarCore.error('Có lỗi xảy ra khi upload file ký: $e');
        return;
      }
    }

    // 2. Upload attachments in background (do not wait for user)
    Future<List<UploadFileWithField>> attachmentFuture =
        _uploadAttachments(attachmentFiles);

    // 3. If signature file exists, show PDF and wait for user confirmation
    if (signatureResponse != null) {
      final pdfUrl = signatureResponse.data?.downloadUrl ?? '';
      if (pdfUrl.isEmpty) {
        SnackbarCore.error('Không thể lấy URL của file PDF');
        return;
      }

      try {
        // Download PDF first
        final localPath = await downloadAndSavePdf(pdfUrl);

        await _showSignatureAndWaitForConfirm(
          localPath: localPath,
          signatureResponse: signatureResponse,
          signatureFileName: signatureFile?['name'] ?? "",
        );
      } catch (e) {
        SnackbarCore.error('Không thể tải file PDF: $e');
      }
      return; // Return here since submission will be handled in overlay
    } else if (isHasFileSignature != null && isHasFileSignature.isNotEmpty) {
      developer.log('isHasFileSignatureisHasFileSignature: $isHasFileSignature',
          name: 'isHasFileSignature');
      // Case: Has signature field but no file uploaded (optional)
      await _showSignatureAndWaitForConfirm(
          signatureResponse: signatureResponse,
          signatureFileName: signatureFile?['name'] ?? "");
      return;
    }

    // 4. Wait for all attachments to finish uploading
    attachmentResponses = await attachmentFuture;

    // 5. Build formData from all upload responses
    final uploadData = <String, dynamic>{};
    for (final wrapper in attachmentResponses) {
      uploadData.addAll(transformUploadResponseToFormData(
        stateManager.taskDefKey,
        wrapper.fieldName,
        wrapper.response,
        stateManager,
      ));
    }

    // 6. Submit form with all upload data
    _submitFormWithUploadData(uploadData);
  }

  Future<void> _showSignatureAndWaitForConfirm(
      {String? localPath,
      UploadSingleFileResponseModel? signatureResponse,
      String? signatureFileName,
      bool? isPrintFile = false}) async {
    debugPrint('signatureResponse: $signatureResponse');
    debugPrint('localPath: $localPath');

    final completer = Completer<void>();
    bool isCompleterCompleted = false;

    void completeOverlay() {
      if (!isCompleterCompleted) {
        isCompleterCompleted = true;
        completer.complete();
      }
    }

    context.read<OverlayBloc>().add(
          ShowOverlay(
            StatefulBuilder(
              builder: (context, setState) => Stack(
                children: [
                  Container(
                    color: Colors.white,
                    child: SafeArea(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 16.h),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Xem lại vùng ký',
                                    style: getTypoSkin().title2Medium),
                                IconButton(
                                  onPressed: () {
                                    context
                                        .read<OverlayBloc>()
                                        .add(HideOverlay());
                                    completer.complete(null);
                                  },
                                  icon: const Icon(Icons.close),
                                ),
                              ],
                            ),
                            Expanded(
                              child: localPath == null || localPath.isEmpty
                                  ? const Center(
                                      child: Text('Không có file ký'),
                                    )
                                  : PDFViewWrapper(filePath: localPath),
                            ),
                            if (signatureResponse != null ||
                                isPrintFile == true) ...[
                              SizedBox(height: 16.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  ElevatedButton(
                                    onPressed: () {
                                      context
                                          .read<OverlayBloc>()
                                          .add(HideOverlay());
                                      completeOverlay();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          getColorSkin().transparent,
                                      padding: EdgeInsets.symmetric(
                                          vertical: 12.h, horizontal: 16.h),
                                      elevation: 0,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                        side: BorderSide(
                                            color: getColorSkin().primaryBlue),
                                      ),
                                    ),
                                    child: Text('Quay về',
                                        style: getTypoSkin()
                                            .body2Regular
                                            .copyWith(
                                                color: getColorSkin()
                                                    .primaryBlue)),
                                  ),
                                  SizedBox(width: 16.w),
                                  ElevatedButton(
                                    onPressed: () async {
                                      setState(() => _isLoadingInFile = true);

                                      try {
                                        final uploadedFiles = stateManager
                                            .getUploadedFiles()
                                            .where(
                                                (e) => e['isDefault'] != true)
                                            .toList();
                                        final attachmentFiles = uploadedFiles
                                            .where((e) =>
                                                e['fileType'] != 'signature')
                                            .toList();
                                        final attachmentResponses =
                                            await _uploadAttachments(
                                                attachmentFiles);

                                        final uploadData = <String, dynamic>{};

                                        if (signatureResponse != null) {
                                          uploadData.addAll(
                                              transformUploadResponseToFormData(
                                            stateManager.taskDefKey,
                                            signatureFileName.toString(),
                                            signatureResponse,
                                            stateManager,
                                          ));
                                        }
                                        for (final wrapper
                                            in attachmentResponses) {
                                          uploadData.addAll(
                                              transformUploadResponseToFormData(
                                            stateManager.taskDefKey,
                                            wrapper.fieldName,
                                            wrapper.response,
                                            stateManager,
                                          ));
                                        }

                                        _submitFormWithUploadData(uploadData,
                                            signatureResponse:
                                                signatureResponse);
                                      } catch (e) {
                                        if (mounted) {
                                          Navigator.of(context,
                                                  rootNavigator: true)
                                              .pop();
                                          SnackbarCore.error(
                                              'Có lỗi xảy ra khi upload file: $e');
                                        }
                                      }

                                      completeOverlay();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          getColorSkin().primaryBlue,
                                      padding: EdgeInsets.symmetric(
                                          vertical: 12.h, horizontal: 16.h),
                                      elevation: 0,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                      ),
                                    ),
                                    child: Text('Xác nhận',
                                        style: getTypoSkin()
                                            .body2Regular
                                            .copyWith(
                                                color: getColorSkin().white)),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (_isLoadingInFile)
                    Container(
                      color: Colors.black.withOpacity(0.3),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                ],
              ),
            ),
          ),
        );
    return completer.future;
  }

  void _submitFormWithUploadData(
    Map<String, dynamic> uploadData, {
    UploadSingleFileResponseModel? signatureResponse,
  }) {
    debugPrint('uploadData: $uploadData');
    final fileUpload = stateManager.getUploadedFiles();
    final fileDefault =
        fileUpload.where((element) => element['isDefault'] == true).toList();
    debugPrint('fileDefault: $fileDefault');
    if (fileDefault.isNotEmpty) {
      final transFileDefault = transformUploadResponseToFormData(
        stateManager.taskDefKey,
        fileDefault.first['name'],
        null,
        stateManager,
      );
      uploadData.addAll(transFileDefault);
    }

    final requestSubject = _stateInfoBase.requestSubject;
    if (requestSubject == "" || requestSubject == null) {
      SnackbarCore.error('Vui lòng nhập tên phiếu yêu cầu');
      return;
    }
    final validate = stateManager.validateAll();
    if (!validate) {
      SnackbarCore.error('Vui lòng kiểm tra lại thông tin');
      return;
    }
    final formData = _transformFormValuesToVariables();
    formData.addAll(uploadData);
    final checkTypeState = context.read<CheckTypeBloc>().state;
    final checkTypeResponse =
        checkTypeState is CheckTypeSuccess ? checkTypeState.response : null;

    if (checkTypeResponse?.listSignForm.isNotEmpty == true) {
      _submitFormHasFileSignatureDoc(formData);
    } else {
      developer.log(
          'checkTypeResponse?.processType: ${checkTypeResponse?.processType}',
          name: 'checkTypeResponse?.processType');
      // return;
      if (checkTypeResponse?.processType == 1) {
        _submitFormHasFileSignaturePDF(formData,
            signatureResponse: signatureResponse);
        return;
      }
      FormSubmitService.submitForm(
        context: context,
        formData: formData,
        procDefId: widget.procDefId ?? "",
        ticketId: widget.ticketId ?? 0,
        requestSubject: requestSubject.toString(),
      );
    }
    //
  }

  void _submitFormHasFileSignatureDoc(Map<String, dynamic> formData) {
    final formBloc = context.read<FormBloc>();
    final formState = formBloc.state;
    final checkTypeState = context.read<CheckTypeBloc>().state;
    final checkTypeResponse =
        checkTypeState is CheckTypeSuccess ? checkTypeState.response : null;

    final bpmnDefinition = formState.bpmnDefinition;
    final process = bpmnDefinition?.process;
    final elements = process?.elements
        .map((e) => {
              'id': e.id,
              'name': e.name,
              if (e is BpmnUserTask)
                'assignee': e.assignee?.contains('jsonArrayToString') == true
                    ? e.assignee?.split('(').last.split(')').first
                    : e.assignee,
              if (e is BpmnStartEvent) 'formKey': e.formKey,
            })
        .toList();

    developer.log('All elements before sort: $elements',
        name: 'PdfViewerScreen');

    // Sort elements: startEvent first, then by id
    elements?.sort((a, b) {
      if (a['formKey'] != null) return -1; // startEvent comes first
      if (b['formKey'] != null) return 1;
      final idA = a['id']?.toString() ?? '';
      final idB = b['id']?.toString() ?? '';
      return idA.compareTo(idB);
    });

    developer.log('All elements after sort: $elements',
        name: 'PdfViewerScreen');

    // Create a list to store all user assignees
    final userAssignees = <String>[];
    final Box authBox = Hive.box('authentication');
    final String? username = authBox.get('username');
    for (final element in elements ?? []) {
      if (element['assignee'] != null || element['id'] == 'start') {
        final userAssignee = element['id'] == 'start'
            ? username
            : formData[element['assignee']] is Map
                ? formData[element['assignee']]['value']
                : formData[element['assignee']];

        if (userAssignee != null) {
          userAssignees.add(userAssignee);
        }
      }
    }

    // Trigger API calls for all users at once
    if (userAssignees.isNotEmpty) {
      // Get default signature for start user
      formBloc.add(GetDefaultSignatureRequested(username: username ?? ''));
      formBloc.add(GetUserInfoByUsername(username: userAssignees.first));
      formBloc.add(GetFinalTitleByListUser(usernames: userAssignees));
    }

    _subscription = formBloc.stream.listen((state) {
      if (state.userInfoByUsernameResponse != null &&
          state.titleByUserResponse != null &&
          state.defaultSignatureResponse != null) {
        _subscription.cancel();

        // Create zones after getting responses
        _zones = elements
                ?.where((element) =>
                    element['assignee'] != null || element['id'] == 'start')
                .map((element) {
              final Box authBox = Hive.box('authentication');
              final String? username = authBox.get('username');

              final userAssignee = element['id'] == 'start'
                  ? username
                  : formData[element['assignee']] is Map
                      ? formData[element['assignee']]['value']
                      : formData[element['assignee']];

              final user = state.userInfoByUsernameResponse;
              final title = state.titleByUserResponse;
              final defaultSignature = state.defaultSignatureResponse;

              return {
                "taskDefKey": element['id'],
                "orderSign": 1,
                "email": user?.data.username ?? "employee",
                "page": 0,
                "wResize": 200,
                "hResize": 100,
                "sign": element['id'] == 'start' ? defaultSignature : null,
                "x": 0,
                "y": 10,
                "w": 0,
                "h": 100,
                "scale": 1,
                "firstName": user?.data.firstname ?? "",
                "lastName": user?.data.lastname ?? "",
                "position": user?.data.positionName ?? "",
                "name": user?.data.name ?? "",
                "signType": "TEMPLATE",
                "chartNodeLevel": title?.data.first.chartNodeLevel ?? ""
              };
            }).toList() ??
            [];

        developer.log('zones: ${_zones.map((zone) => zone.toString())}',
            name: 'PdfViewerScreen');

        try {
          formBloc.add(
            CreateBpmProcInstRequested(
              procDefId: widget.procDefId ?? '',
              ticketId: widget.ticketId ?? 0,
              requestBody: BpmProcInstCreateRequestModel(
                oldProcInstId: '',
                variables: formData,
                isDraft: false,
                serviceId: widget.ticketId ?? 0,
                linkProcInstId: [],
                receiveMail: _stateInfoBase.receiveMail,
                isAssistant: _stateInfoBase.isAssistantCheck,
                notifyUsers: [],
                priorityId: int.parse(_stateInfoBase.selectedPriority ?? '0'),
                chartId: formState.chartData?.data.first.id,
                assistantEmail: [],
                submissionType:
                    _stateInfoBase.submissionType?.isNotEmpty == true
                        ? int.parse(_stateInfoBase.submissionType ?? '0')
                        : null,
                companyCode: formState.chartData?.data.first.code,
                chartNodeName:
                    formState.chartNodeData?.data.first.chartNodeName,
                chartNodeCode:
                    formState.chartNodeData?.data.first.chartNodeCode,
                chartNodeId: formState.chartNodeData?.data.first.chartNodeId,
                chartName: formState.chartData?.data.first.name,
                forceViewUrl: "",
                cancelTicketId: "",
                approvedBudgetIds: [],
              ),
              printSignZoneRequest: TaoToTrinhRequestBody(
                zones: _zones.map((zone) => Zones.fromJson(zone)).toList(),
                name: checkTypeResponse
                        ?.listSignForm.firstOrNull?.uploadWordsChange ??
                    "",
                procDefId: widget.procDefId ?? "",
                pdfContent: checkTypeResponse
                        ?.listSignForm.firstOrNull?.uploadWordsChange ??
                    "",
                bpmPrintPhaseId: "",
                procInstId: "",
                taskKey: [
                  TaskKey.fromJson({"taskType": 0})
                ],
                uploadWordsChange: checkTypeResponse
                        ?.listSignForm.firstOrNull?.uploadWordsChange ??
                    "",
              ),
            ),
          );

          if (formState.bpmProcInstCreateResponse?.code == 1 &&
              formState.bpmProcInstCreateResponse?.message == 'Success') {
            debugPrint('Form submission successful');
            // widget.onConfirm();
          }
        } catch (e, stack) {
          print('Error: $e');
          print('Stack: $stack');
        }
      }
    });
  }

  void _submitFormHasFileSignaturePDF(Map<String, dynamic> formData,
      {UploadSingleFileResponseModel? signatureResponse}) async {
    final formBloc = context.read<FormBloc>();
    final formState = formBloc.state;
    final checkTypeState = context.read<CheckTypeBloc>().state;
    final checkTypeResponse =
        checkTypeState is CheckTypeSuccess ? checkTypeState.response : null;

    final userAssignees = <UserTaskModel>[];
    final Box authBox = Hive.box('authentication');
    final String? currentUsername = authBox.get('username');
    final listUserTask = checkTypeResponse?.listUserTask;
    final defaultSignature = formBloc.state.defaultSignatureResponse;
    _zones = [];

    userAssignees.add(UserTaskModel(
      taskDefKey: "start",
      zoomeField: currentUsername ?? "",
      taskName: "",
    ));
    listUserTask?.sort((a, b) => a.taskDefKey.compareTo(b.taskDefKey));
    userAssignees.addAll(listUserTask ?? []);
    // return;
    try {
      for (final userTask in userAssignees) {
        final zoomField = userTask.zoomeField.startsWith('start_')
            ? userTask.zoomeField.replaceAll('start_', '')
            : userTask.zoomeField;
        final getAllFormData = stateManager.getAllFormData();
        developer.log('getAllFormData: $getAllFormData',
            name: 'PdfViewerScreen');
        developer.log('zoomField: $zoomField', name: 'PdfViewerScreen');
        developer.log('formData: ${getAllFormData[zoomField]}',
            name: 'PdfViewerScreen');
        final username = userTask.taskDefKey == 'start'
            ? currentUsername
            : getAllFormData[zoomField] as String?;

        // if (username == null || username.isEmpty) {
        //   developer.log('Skipping user task due to null/empty username: ${userTask.zoomeField}',
        //       name: 'PdfViewerScreen');
        //   continue;
        // }
        dynamic userResponse = "";
        dynamic titleResponse = "";

        if (username != null && username.isNotEmpty) {
          userResponse = await ApiFormService().getByUsername(username);
          titleResponse = await ApiFormService().getTitleByUsername(username);
        }

        // if (userResponse == null || titleResponse == null) {
        //   developer.log('Skipping user task due to null user/title data: $username',
        //       name: 'PdfViewerScreen');
        //   continue;
        // }

        developer.log('User response: $userResponse', name: 'PdfViewerScreen');
        developer.log('Title response: $titleResponse',
            name: 'PdfViewerScreen');

        // Safely extract user data
        final userData = userResponse != null &&
                userResponse.isNotEmpty &&
                userResponse is Map
            ? userResponse['data']
            : null;
        final titleData = titleResponse != null &&
                titleResponse.isNotEmpty &&
                titleResponse is Map
            ? titleResponse['data']
            : null;

        // if (userData == null || titleData == null) {
        //   developer.log('Skipping user task due to invalid response structure: $username',
        //       name: 'PdfViewerScreen');
        //   continue;
        // }

        final fieldValue = getAllFormData[zoomField] as String?;

        developer.log('fieldValue: $fieldValue', name: 'PdfViewerScreen');
        developer.log('userData: $userData', name: 'PdfViewerScreen');
        developer.log('userTask.zoomeField: $zoomField',
            name: 'PdfViewerScreen');
        developer.log('currentUsername: $currentUsername',
            name: 'PdfViewerScreen');

        final email = userTask.taskDefKey == 'start'
            ? currentUsername
            : (fieldValue?.isNotEmpty == true
                ? userData['username'] ?? fieldValue
                : '@${userTask.zoomeField}');
        final name = fieldValue?.isNotEmpty == true
            ? userData['name'] ?? fieldValue
            : '@${userTask.zoomeField}';

        _zones.add({
          "taskDefKey": userTask.taskDefKey,
          "orderSign": userAssignees.indexOf(userTask) + 1,
          "email": email,
          "page": 0,
          "wResize": 200,
          "hResize": 100,
          "sign": userTask.taskDefKey == 'start' ? defaultSignature : null,
          "x": 0,
          "y": 10,
          "w": 0,
          "h": 100,
          "scale": 1,
          "firstName": userData?['firstname'] ?? "",
          "lastName": userData?['lastname'] ?? "",
          "position": titleData is List && titleData.isNotEmpty
              ? titleData[0]['title'] ?? ""
              : "",
          "name": name,
          "signType": "AUTO",
          "chartNodeLevel": titleData is List && titleData.isNotEmpty
              ? titleData[0]['chartNodeLevel'] ?? ""
              : ""
        });
      }

      if (_zones.isEmpty) {
        throw Exception('Không thể lấy thông tin người dùng');
      }
      developer.log('zones: ${_zones.map((zone) => zone.toString())}',
          name: 'PdfViewerScreen');

      developer.log('uploadData: $signatureResponse', name: 'PdfViewerScreen');

      setState(() {
        _isLoadingInFile = false;
      });
      // Submit form
      formBloc.add(
        CreateBpmProcInstRequested(
          procDefId: widget.procDefId ?? '',
          ticketId: widget.ticketId ?? 0,
          requestBody: BpmProcInstCreateRequestModel(
            oldProcInstId: '',
            variables: formData,
            isDraft: false,
            serviceId: widget.ticketId ?? 0,
            linkProcInstId: [],
            receiveMail: _stateInfoBase.receiveMail,
            isAssistant: _stateInfoBase.isAssistantCheck,
            notifyUsers: [],
            priorityId: int.parse(_stateInfoBase.selectedPriority ?? '0'),
            chartId: formState.chartData?.data.first.id,
            assistantEmail: [],
            submissionType: formState
                .serviceByIdWithPermissionResponse?.data?.submissionType,
            companyCode: formState.chartData?.data.first.code,
            submissionTypeName: formState.submissionTypeName,
            chartNodeName: formState.chartNodeData?.data.first.chartNodeName,
            chartNodeCode: formState.chartNodeData?.data.first.chartNodeCode,
            chartNodeId: formState.chartNodeData?.data.first.chartNodeId,
            chartName: formState.chartData?.data.first.name,
            forceViewUrl: "",
            cancelTicketId: "",
            approvedBudgetIds: [],
          ),
          printSignZoneRequest: TaoToTrinhRequestBody(
            zones: _zones.map((zone) => Zones.fromJson(zone)).toList(),
            name: signatureResponse?.data?.fileName?.split('/').last ?? "",
            procDefId: widget.procDefId ?? "",
            pdfContent: signatureResponse?.data?.fileName ?? "",
            bpmPrintPhaseId: "",
            procInstId: "",
            taskKey: [
              TaskKey.fromJson({"taskType": 0})
            ],
            uploadWordsChange: signatureResponse?.data?.fileName ?? "",
          ),
        ),
      );
    } catch (e) {
      print('Error: $e');
      setState(() => _isLoadingInFile = false);
      Navigator.pop(context);
      SnackbarCore.error(
          'Có lỗi xảy ra khi lấy thông tin người dùng. Vui lòng thử lại.');
    }
  }
}

Future<String> downloadAndSavePdf(String url) async {
  final response = await http.get(Uri.parse(url));
  final bytes = response.bodyBytes;

  final dir = await getTemporaryDirectory();
  final file = File('${dir.path}/temp.pdf');
  await file.writeAsBytes(bytes);
  return file.path;
}

Map<String, dynamic> transformUploadResponseToFormData(
  String taskDefKey,
  String name,
  UploadSingleFileResponseModel? uploadResponse,
  FormStateManager stateManager,
) {
  final result = <String, dynamic>{};
  final now = DateTime.now();
  final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(now);
  final Box authBox = Hive.box('authentication');
  final String? username = authBox.get('username');

  // If uploadResponse is null or empty, check for attachFileObjDefault
  if (uploadResponse?.data == null) {
    // Find the widget with the matching name to get attachFileObjDefault
    final widget = stateManager.findWidgetByName(name);
    if (widget?.attachFileObjDefault != null) {
      final defaultFile = widget!.attachFileObjDefault!;

      // Create history entry from default file
      final historyEntry = {
        "createdUser": defaultFile['createdUser'] ?? username ?? "",
        "createdTime": now.toIso8601String(),
        "uploadTime": formattedDate,
        "filename": defaultFile['filename'] ?? defaultFile['fileName'] ?? "",
        "downloadUrl":
            defaultFile['downloadUrl'] ?? defaultFile['orgFilePath'] ?? "",
        "idHistory": defaultFile['idHistory'] ?? "",
        "displayName": defaultFile['filename'] ?? defaultFile['fileName'] ?? "",
        "fileSize": defaultFile['fileSize'] ?? 0,
        "isSave": true
      };

      // 1. History field
      result['${taskDefKey}_${name}_history'] = {
        "value": jsonEncode([historyEntry]),
        "type": "String"
      };

      // 2. MultiName field (empty for single file)
      result['${taskDefKey}_${name}_multiName'] = {
        "value": "",
        "type": "String"
      };

      // 3. Main file field
      result['${taskDefKey}_$name'] = {
        "value": "",
        "type": "File",
        "valueInfo": {
          "filename":
              defaultFile['downloadUrl'] ?? defaultFile['orgFilePath'] ?? "",
          "mimeType": defaultFile['fileType'] ?? "",
          "data": historyEntry
        }
      };

      return result;
    }
  }

  // Create history entry for new upload
  final historyEntry = {
    "createdUser": username ?? "",
    "createdTime": now.toIso8601String(),
    "uploadTime": formattedDate,
    "filename": uploadResponse?.data?.fileName?.split('/').last ?? "",
    "downloadUrl": uploadResponse?.data?.downloadUrl ?? "",
    "idHistory": "",
    "displayName": uploadResponse?.data?.fileName?.split('/').last ?? "",
    "fileSize": uploadResponse?.data?.size ?? 0,
    "isSave": true
  };

  // 1. History field
  result['${taskDefKey}_${name}_history'] = {
    "value": jsonEncode([historyEntry]),
    "type": "String"
  };

  // 2. MultiName field (empty for single file)
  result['${taskDefKey}_${name}_multiName'] = {"value": "", "type": "String"};

  // 3. Main file field
  result['${taskDefKey}_$name'] = {
    "value": uploadResponse?.data?.fileName?.split('/').last,
    "type": "File",
    "valueInfo": {
      "filename": uploadResponse?.data?.downloadUrl ?? "",
      "mimeType": uploadResponse?.data?.contentType ?? "",
      "data": historyEntry
    }
  };

  return result;
}
