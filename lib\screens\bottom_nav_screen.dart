import 'dart:developer' as developer;

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/authentication/authentication_bloc.dart';
import 'package:eapprove/blocs/authentication/authentication_event.dart';
import 'package:eapprove/blocs/notification/notification_bloc.dart';
import 'package:eapprove/blocs/notification/notification_event.dart';
import 'package:eapprove/blocs/service/service_bloc.dart';
import 'package:eapprove/blocs/service/service_event.dart';
import 'package:eapprove/blocs/user/user_bloc.dart';
import 'package:eapprove/blocs/user/user_event.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/screens/home_nav_screen.dart';
import 'package:eapprove/screens/login/login_screen.dart';
import 'package:eapprove/screens/notification_screen/notification_screen.dart';
import 'package:eapprove/screens/ticket_service/create_ticket_screen.dart';
import 'package:eapprove/services/global_navigation.dart';
import 'package:eapprove/widgets/custom_bottom_navigationbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:eapprove/services/navigation_service.dart';

final GlobalKey<_BottomNavScreenState> bottomNavKey = GlobalKey<_BottomNavScreenState>();

class BottomNavScreen extends StatefulWidget {
  static const routeName = "/bottom_nav";

  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => BottomNavScreen(key: bottomNavKey));
  }

  final VoidCallback? onHomeScreenBack;
  final bool? isEmbedded;
  final VoidCallback? onLogout;

  BottomNavScreen({Key? key, this.isEmbedded, this.onHomeScreenBack, this.onLogout}) : super(key: key) {
    ConfigImg.setHostApp(isEmbedded ?? false);
    // Set callbacks in NavigationService
    NavigationService().setCallbacks(
      onHomeScreenBack: onHomeScreenBack,
      onLogout: onLogout,
    );
  }

  @override
  State<BottomNavScreen> createState() => _BottomNavScreenState();
}

class _BottomNavScreenState extends State<BottomNavScreen> {
  String _isSubScreen = 'home';
  final _homeNavigatorKey = GlobalKey<NavigatorState>();
  late final List<Widget> _screens = [
    HomeNavigator(
      navigatorKey: _homeNavigatorKey,
      isEmbedded: widget.isEmbedded,
      onNavigateToSubScreen: (screenName) {
        Future.microtask(() {
      setState(() {
     _isSubScreen = screenName;
      });
    });
       
      },
    ),
    CreateTicketScreen(
      title: 'Tạo tờ trình',
      
    ),
    Container()
  ];

  @override
  void initState() {
    context.read<UserBloc>().add(FetchUserInfo());
    context.read<NotiBloc>().add(FetchUnreadAmount());
    super.initState();
  }

  void handleTabTap(int index) async {
    developer.log('handleTabTap called with index: $index', name: 'BottomNavScreen');
    final currentIndex = context.read<BottomNavBloc>().state.currentIndex;
    if (index == 0 && currentIndex == 0) {
      developer.log('handleTabTap - case 1: index == 0 && currentIndex == 0', name: 'BottomNavScreen');
      if (_homeNavigatorKey.currentState?.canPop() ?? false) {
        developer.log('handleTabTap - popping to first route', name: 'BottomNavScreen');
        _homeNavigatorKey.currentState?.popUntil((route) => route.isFirst);
      }
    } else {
      developer.log('handleTabTap - case 2: else branch', name: 'BottomNavScreen');
      if (index == 1 && index != currentIndex) {
        developer.log('handleTabTap - case 2.1: index == 1', name: 'BottomNavScreen');
        if (context.mounted) {
          context.read<ServiceBloc>().add(const ClearServiceState());
          context.read<ServiceBloc>().add(const ServiceFetchRoot());
        }
      } else if (index == 2) {
        developer.log('handleTabTap - case 2.2: index == 2', name: 'BottomNavScreen');
        context.read<NotiBloc>().add(GetNotification());
        setState(() {
          _screens[2] = NotificationScreen(
            showInitialLoading: true,
          );
        });
        context.read<BottomNavBloc>().add(SwitchTab(index));
        return;
      } else if (index == 3) {
        developer.log('handleTabTap - case 2.3: index == 3', name: 'BottomNavScreen');
        if (widget.isEmbedded != null) {
          GlobalNavigation().handleLogout(context).then((value) {
            if (value == true) {
              Navigator.of(context, rootNavigator: true).pop('logout');
            }
          });
        } else {
          final authBloc = context.read<AuthenticationBloc>();
          final userBloc = context.read<UserBloc>();
          final router = GoRouter.of(context);

          await userBloc.clearUserInfo();
          authBloc.add(LogoutRequested());
          await Future.delayed(const Duration(milliseconds: 300));
          router.go(LoginScreen.routeName);
        }
        return;
      }
      if (index != 3) {
        developer.log('handleTabTap - setting currentIndex to $index', name: 'BottomNavScreen');
        context.read<BottomNavBloc>().add(SwitchTab(index));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BottomNavBloc, BottomNavState>(
      builder: (context, state) {
        developer.log('BottomNavScreen: Building with visibility ${state.isVisible}, currentIndex: ${state.currentIndex}', name: 'BottomNavScreen');
        return PopScope(
          canPop: state.currentIndex == 0 && _isSubScreen == 'home',
          onPopInvokedWithResult: (bool didPop, dynamic result) {
            if (!didPop) {
              if (state.currentIndex == 0) {
                final navigator = _homeNavigatorKey.currentState;
                if (navigator != null && navigator.canPop()) {
                  navigator.pop();
                  return;
                }
              } else if (state.currentIndex != 0) {
                context.read<BottomNavBloc>().add(const SwitchTab(0));
                return;
              }
            }
          },
          child: Scaffold(
            body: IndexedStack(
              index: state.currentIndex,
              children: _screens,
            ),
            bottomNavigationBar: state.isVisible
                ? CustomBottomNavigationBar(
                    selectedIndex: state.currentIndex,
                    onTap: handleTabTap,
                    selectedBackgroundColor: Colors.blue.withOpacity(0.1),
                  )
                : null,
          ),
        );
      },
    );
  }
}
