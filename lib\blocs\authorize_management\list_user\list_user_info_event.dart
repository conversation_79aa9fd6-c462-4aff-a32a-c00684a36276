abstract class UserInfoEvent {}

class GetUserInfo extends UserInfoEvent {
  final List<int> concurrently;
  final List<String> managerLevel;
  final String search;
  final String sortBy;
  final String sortType;
  final int page;
  final int limit;
  final int size;

  GetUserInfo({
    this.concurrently = const [0],
    this.managerLevel = const ['primary', 'secondary', 'staff'],
    this.search = '',
    this.sortBy = 'id',
    this.sortType = 'DESC',
    this.page = 1,
    this.limit = 99999999,
    this.size = 99999999,
  });
} 