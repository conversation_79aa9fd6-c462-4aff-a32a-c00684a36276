import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class CustomTabBarOverTest extends StatefulWidget {
  final List<String> tabTitles;
  final List<Widget> tabContents;
  final int initialIndex;
  final Function(int)? onTabSelected;
  final void Function(int index)? onTabChanged;
  final TabController? tabController;
  final Color? selectedBackgroundColor;
  final Color? unselectedBackgroundColor;
  final Color? selectedTextColor;
  final Color? unselectedTextColor;
  final TextStyle? textStyle;
  final EdgeInsets? tabPadding;
  final double? borderRadius;
  final Duration animationDuration;
  final EdgeInsets? tabBarPadding;
  final EdgeInsets? labelPadding;
  final EdgeInsets? contentPadding;
  final bool lazyLoad;
  final Curve animationCurve;
  final List<int>? dropdownTabIndex;
  final Widget dropdown;

  const CustomTabBarOverTest({
    super.key,
    required this.tabTitles,
    required this.tabContents,
    this.initialIndex = 0,
    this.onTabSelected,
    this.tabController,
    this.selectedBackgroundColor,
    this.unselectedBackgroundColor,
    this.selectedTextColor,
    this.unselectedTextColor,
    this.textStyle,
    this.tabPadding,
    this.borderRadius,
    this.animationDuration = const Duration(milliseconds: 300),
    this.tabBarPadding,
    this.labelPadding,
    this.lazyLoad = false,
    this.animationCurve = Curves.easeInOut,
    this.contentPadding,
    this.dropdownTabIndex,
    this.dropdown = const SizedBox.shrink(),
    this.onTabChanged,
  }) : assert(tabTitles.length == tabContents.length, 'Number of tab titles must match number of tab contents');

  @override
  State<CustomTabBarOverTest> createState() => _CustomTabBarState();
}

class _CustomTabBarState extends State<CustomTabBarOverTest> with TickerProviderStateMixin {
  late ValueNotifier<int> selectedTab;
  late TabController _tabController;
  late List<bool> _initializedTabs;

  @override
  void initState() {
    super.initState();
    selectedTab = ValueNotifier<int>(widget.initialIndex);

    _initializedTabs =
        List.generate(widget.tabTitles.length, (index) => !widget.lazyLoad || index == widget.initialIndex);

    if (widget.tabController == null) {
      _tabController = TabController(
        length: widget.tabTitles.length,
        vsync: this,
        initialIndex: widget.initialIndex,
      );
    } else {
      _tabController = widget.tabController!;
    }

    _tabController.addListener(_handleTabChange);
    _tabController.animation?.addListener(_handleSwipeChange);
  }

  @override
  void dispose() {
    if (widget.tabController == null) {
      _tabController.dispose();
    }
    _tabController.removeListener(_handleTabChange);
    selectedTab.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant CustomTabBarOverTest oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.tabTitles.length != oldWidget.tabTitles.length) {
      _tabController.removeListener(_handleTabChange);
      _tabController.dispose();

      _tabController = TabController(
        length: widget.tabTitles.length,
        vsync: this,
        initialIndex: widget.initialIndex,
      );

      _tabController.addListener(_handleTabChange);
      _tabController.animation?.addListener(_handleSwipeChange);

      _initializedTabs = List.generate(
        widget.tabTitles.length,
        (index) => !widget.lazyLoad || index == widget.initialIndex,
      );

      selectedTab.value = widget.initialIndex;
    }
  }

  void _handleSwipeChange() {
    final newIndex = _tabController.animation!.value.round();
    if (newIndex != selectedTab.value) {
      _updateSelectedTab(newIndex);
      widget.onTabChanged?.call(newIndex);
    }
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging || _tabController.index != selectedTab.value) {
      final newIndex = _tabController.index;

      if (widget.lazyLoad && !_initializedTabs[newIndex]) {
        setState(() {
          _initializedTabs[newIndex] = true;
        });
      }

      selectedTab.value = newIndex;
      widget.onTabSelected?.call(newIndex);
      widget.onTabChanged?.call(newIndex);
    }
  }

  void _updateSelectedTab(int index) {
    if (widget.lazyLoad && !_initializedTabs[index]) {
      setState(() => _initializedTabs[index] = true);
    }
    selectedTab.value = index;
    if (widget.onTabSelected != null) {
      widget.onTabSelected!(index);
    }
  }

  // Widget _buildTabBar() {
  //   return ValueListenableBuilder<int>(
  //     valueListenable: selectedTab,
  //     builder: (context, index, child) {
  //       return TabBar(
  //         controller: _tabController,
  //         isScrollable: false,
  //         padding: widget.tabBarPadding ?? EdgeInsets.only(left: 12.w),
  //         labelPadding: widget.labelPadding ?? EdgeInsets.symmetric(horizontal: 4.h),
  //         labelColor: widget.selectedTextColor ?? getColorSkin().secondaryColor1,
  //         unselectedLabelColor: widget.unselectedTextColor ?? getColorSkin().subtitle,
  //         dividerColor: getColorSkin().transparent,
  //         indicator: BoxDecoration(
  //           color: Colors.transparent,
  //           borderRadius: BorderRadius.circular(8.r),
  //         ),
  //         overlayColor: WidgetStateProperty.all(getColorSkin().transparent),
  //         onTap: (index) {
  //           selectedTab.value = index;
  //           if (widget.onTabSelected != null) {
  //             widget.onTabSelected!(index);
  //           }
  //         },
  //         tabs: List.generate(widget.tabTitles.length, (i) {
  //           return Tab(
  //             child: Container(
  //               padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
  //               decoration: BoxDecoration(
  //                 color: i == selectedTab.value ? Colors.white : getColorSkin().grey3Background,
  //                 borderRadius: BorderRadius.circular(8.r),
  //               ),
  //               child: Text(
  //                 widget.tabTitles[i],
  //                 style: getTypoSkin().buttonText2Regular.copyWith(
  //                       fontWeight: FontWeight.w500,
  //                       fontSize: 14.sp,
  //                       color: i == selectedTab.value ? getColorSkin().secondaryColor1 : getColorSkin().subtitle,
  //                     ),
  //               ),
  //             ),
  //           );
  //         }),
  //       );
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildCustomTabBar(), // 👈 dùng custom tab bar mới
        ValueListenableBuilder<int>(
          valueListenable: selectedTab,
          builder: (context, index, child) {
            final showDropdown = widget.dropdownTabIndex != null && widget.dropdownTabIndex!.contains(index);
            return showDropdown ? widget.dropdown : SizedBox.shrink();
          },
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: List.generate(widget.tabContents.length, (i) {
              return SingleChildScrollView(
                padding: widget.contentPadding ?? EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
                child: widget.lazyLoad && !_initializedTabs[i]
                    ? const Center(child: CircularProgressIndicator())
                    : widget.tabContents[i],
              );
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomTabBar() {
    return Container(
      padding: widget.tabBarPadding ?? EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
      child: Row(
        children: List.generate(widget.tabTitles.length, (i) {
          final isSelected = i == selectedTab.value;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                _tabController.animateTo(i);
                selectedTab.value = i;
                widget.onTabSelected?.call(i);
                widget.onTabChanged?.call(i);
              },
              child: Container(
                height: 32.h,
                width: 343.w,
                margin: EdgeInsets.fromLTRB(3.w, 0, 5.w, 10.h),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white : getColorSkin().grey3Background,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                alignment: Alignment.center,
                child: Text(
                  widget.tabTitles[i],
                  style: (widget.textStyle ?? getTypoSkin().buttonText2Regular).copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 14.sp,
                    color: isSelected ? getColorSkin().secondaryColor1 : getColorSkin().subtitle,
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
