import 'package:eapprove/common/form_v2/form_state_manager.dart';
import 'package:eapprove/helper/data_form.dart';
import 'package:flutter/material.dart';
import 'form_builder_v2.dart';
import 'package:eapprove/models/form/form_item_info.dart';
import 'dart:developer' as developer;

class TestFormV2 extends StatefulWidget {
  const TestFormV2({Key? key}) : super(key: key);

  @override
  State<TestFormV2> createState() => _TestFormV2State();
}

class _TestFormV2State extends State<TestFormV2> {
  late final FormStateManager stateManager;
  @override
  void initState() {
    super.initState();
    stateManager = FormStateManager();
  }

  @override
  void dispose() {
    stateManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Parse form data from test_form_data.dart
    final formData = dataForm["form"] as List<dynamic>;
    // final formData = testFormData['form'] as List<dynamic>;
    final widgets =
        formData.map((json) => FormItemInfo.fromJson(json)).toList();

    // Initialize form values in stateManager
    final initialValues = <String, dynamic>{};
    for (var widget in widgets) {
      if (widget.value != null) {
        initialValues[widget.name.toString()] = widget.value;
        // }
      }
    }

    stateManager.setAllFields(initialValues);
    // Set widgets for state manager
    stateManager.setWidgets(widgets);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Form V2'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            FormBuilderV2(
              widgets: widgets,
              stateManager: stateManager,
             
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    // Clear all form data
                    stateManager.clearForm();
                    print('Form cleared');
                  },
                  child: const Text('Clear'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // Validate all fields
                    // final isValid = stateManager.validateAll();
                    // developer.log('Form validation: $isValid', name: 'Form validation');
                     final saveData = stateManager.getSaveFormState();
                      developer.log('Save data: $saveData', name: 'Save data');
                     final data = stateManager.getAllFormData();
                      developer.log('getAllFormDataSave data: $data', name: 'Save data');
                    // if (isValid) {
                    //   // Get all form data
                    //   final saveData = stateManager.getSaveFormState();
                    //   developer.log('Save data: $saveData', name: 'Save data');
                      
                    //   // TODO: Add your submission logic here
                    // }
                    // setState(() {}); // Trigger rebuild to show errors
                  },
                  child: const Text('Submit'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
