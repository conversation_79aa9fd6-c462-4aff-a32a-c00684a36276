class CommentModel {
  final int id;
  final String username;
  final String createdUser;
  final String content;
  final int? createdDate;
  final int typeDiscussion;
  final List? files;
  final List<CommentModel>? replies;
  final String formattedDate;

  CommentModel({
    required this.id,
    required this.username,
    required this.createdUser,
    required this.content,
    this.createdDate,
    required this.typeDiscussion,
    this.files,
    this.replies,
    required this.formattedDate,
  });

  factory CommentModel.fromMap(Map<String, dynamic> map, String displayUsername) {
    return CommentModel(
      id: map['id'] ?? 0,
      username: displayUsername,
      createdUser: map['createdUser'] ?? '',
      content: map['content'] ?? '',
      createdDate: map['createdDate'],
      typeDiscussion: map['typeDiscussion'] ?? 1,
      files: map['fileResponse'],
      formattedDate: '',
    );
  }
}
