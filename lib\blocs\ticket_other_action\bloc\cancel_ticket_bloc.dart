import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/blocs/ticket_other_action/event/cancel_ticket_event.dart';
import 'package:eapprove/blocs/ticket_other_action/state/cancel_ticket_state.dart';
import 'package:eapprove/repositories/ticket_other_action_repository.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';

class CancelTicketBloc extends Bloc<CancelTicketEvent, CancelTicketState> {
  final TicketOtherActionRepository repository;

  CancelTicketBloc({required this.repository})
      : super(CancelTicketState.initial()) {
    on<UploadCancelFileEvent>(_onUploadFiles);
    on<SaveCancelDraftEvent>(_onSaveDraft);
    on<SubmitCancelTicketEvent>(_onSubmitCancel);
  }

  Future<void> _onUploadFiles(
      UploadCancelFileEvent event, Emitter<CancelTicketState> emit) async {
    emit(state.copyWith(status: CancelTicketStatus.loading));
    try {
      final filePaths = await repository.uploadlFile(event.files);
      emit(state.copyWith(
          status: CancelTicketStatus.success, filePaths: filePaths));
      print("🔥 Error in submitCancelTicket:");

    } catch (e) {
      print("🔥 Error in submitCancelTicket: $e");

      emit(state.copyWith(
          status: CancelTicketStatus.failure, error: e.toString()));
    }
  }

  Future<void> _onSaveDraft(
      SaveCancelDraftEvent event, Emitter<CancelTicketState> emit) async {
    emit(state.copyWith(status: CancelTicketStatus.loading));
    try {
      await repository.saveCancelDraft(
        ticketProcId: event.ticketProcId,
        reason: event.reason,
        filePath: event.filePath,
      );
      emit(state.copyWith(status: CancelTicketStatus.success, actionType: "draft"));
    } catch (e) {
      emit(state.copyWith(
          status: CancelTicketStatus.failure, error: e.toString()));
    }
  }

  Future<void> _onSubmitCancel(
      SubmitCancelTicketEvent event, Emitter<CancelTicketState> emit) async {
    emit(state.copyWith(status: CancelTicketStatus.loading));
    try {
    print("🔥 in in submitCancelTicket:");

      await repository.submitCancelTicket(
        procInstId: event.ticketProcId,
        taskDefKey: event.taskDefKey,
        ticketId: event.ticketId,
        reason: event.reason,
        filePaths: event.filePaths,
      );
    print("🔥 success in submitCancelTicket:");
      
      emit(state.copyWith(status: CancelTicketStatus.success,actionType: "submit"));
    } catch (e) {
      print("🔥 Error in submitCancelTicket: $e");

      emit(state.copyWith(
          status: CancelTicketStatus.failure, error: e.toString()));
    }
  }
}
