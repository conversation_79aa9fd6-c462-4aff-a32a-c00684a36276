import 'dart:developer';

import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/models/common/document_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_svg/svg.dart';
import 'package:webview_flutter/webview_flutter.dart';

class FileViewer extends StatefulWidget {
  final DocumentModel item;

  const FileViewer({super.key, required this.item});

  @override
  State<FileViewer> createState() => _FileViewerState();
}

class _FileViewerState extends State<FileViewer> {
  String? originalUrl;
  String? previewUrl;
  WebViewController? controller;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _initializeController() async {
    final fileUrl = await widget.item.getFileUrl();
    originalUrl = Uri.encodeFull(fileUrl).split("?")[0];
    previewUrl = "https://docs.google.com/gview?embedded=true&url=${originalUrl}";
        log("previewUrl: $previewUrl");
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(previewUrl!));
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      showBottomImage: false,
      child: Scaffold(
        backgroundColor: getColorSkin().transparent,
        appBar: CustomAppBar(
          automaticallyImplyLeading: true,
          centerTitle: true,
          title: widget.item.name,
          titleTextStyle: getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
          textColor: getColorSkin().black,
          actionsPadding: EdgeInsets.only(right: 18.w),
          actions: [
            IconButton(
              onPressed: () async {
                await widget.item.downloadFile();
              },
              icon: SvgPicture.asset(StringImage.ic_download, width: 22.w, height: 22.w),
            ),
          ],
        ),
        body: FutureBuilder(
          future: _initializeController(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                  child: AppConstraint.buildShimmer(
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  margin: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: getColorSkin().white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ));
            }

            final fileType = FileType.checkTypeDocument(widget.item.type.extension);

            if (fileType == 'document') {
              return controller != null
                  ? WebViewWidget(controller: controller!)
                  : const Center(child: CircularProgressIndicator());
            } else if (fileType == 'image') {
              return originalUrl != null && originalUrl!.isNotEmpty
                  ? Center(child: Image.network(originalUrl!))
                  : const Center(child: CircularProgressIndicator());
            } else {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text("Không hỗ trợ loại file này"),
                  TextButton(
                    onPressed: () async {
                      await widget.item.downloadFile();
                    },
                    child: const Text(
                       "Tải về",
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }
}
