import 'package:eapprove/models/ticket_other_action/ticket_history_model.dart';

class TicketOtherActionResponse {
  final List<dynamic> content;
  final int? totalElements;
  final int? number;
  final int? numberOfElements;
  final int? totalPages;
  final bool? first;
  final bool? last;

  const TicketOtherActionResponse({
    required this.content,
    this.totalElements,
    this.number,
    this.numberOfElements,
    this.totalPages,
    this.first,
    this.last,
  });

  static const empty = TicketOtherActionResponse(content: []);

  factory TicketOtherActionResponse.mappingFromTicketHistoryJSON(
      Map<String, dynamic> json) {
    return TicketOtherActionResponse(
      content: json['content'],
      totalElements: json['totalElements'],
      number: json['number'],
      numberOfElements: json['numberOfElements'],
      totalPages: json['totalPages'],
      first: json['first'],
      last: json['last'],
    );
  }

  factory TicketOtherActionResponse.mappingFromRequestRelationTicketHistoryJSON(
      Map<String, dynamic> json) {
    return TicketOtherActionResponse(
      content: json['data'],
    );
  }

  factory TicketOtherActionResponse.mappingFromTicketAttachmentListJSON(
      Map<String, dynamic> json) {
    return TicketOtherActionResponse(
      content: json['data'],
    );
  }

  factory TicketOtherActionResponse.fromConsultationJson(
      Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>;
    return TicketOtherActionResponse(
      content: data['content'] ?? [],
      totalElements: data['totalElements'] ?? 0,
      number: data['number'] ?? 0,
      numberOfElements: data['numberOfElements'] ?? 0,
      totalPages: data['totalPages'] ?? 1,
      first: data['first'] ?? false,
      last: data['last'] ?? false,
    );
  }
  
  List<dynamic> getVisibleContent(String username) {
    return content.where((item) {
      final typeDiscussion = item['typeDiscussion'] ?? 1;
      final createdUser = item['createdUser'];
      
      return typeDiscussion == 1 || (typeDiscussion == 0 && createdUser == username);
    }).toList();
  }
}
