import 'package:eapprove/models/authorize_management/list_user_info_response.dart';
import 'package:eapprove/repositories/list_user_info_repository.dart';

class UserInfoService {
  final UserInfoRepository _repository;

  UserInfoService(this._repository);

  Future<UserInfoResponse> getUserInfo({
    List<int> concurrently = const [0],
    List<String> managerLevel = const ['primary', 'secondary', 'staff'],
    String search = '',
    String sortBy = 'id',
    String sortType = 'DESC',
    int page = 1,
    int limit = 99999999,
    int size = 99999999,
  }) async {
    try {
      return await _repository.getUserInfo(
        concurrently: concurrently,
        managerLevel: managerLevel, 
        search: search,
        sortBy: sortBy,
        sortType: sortType,
        page: page,
        limit: limit,
        size: size,
      );
    } catch (e) {
      throw Exception('Failed to get user info: $e');
    }
  }
} 