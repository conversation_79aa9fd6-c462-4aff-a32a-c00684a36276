import 'package:eapprove/models/ticket_other_action/status_model.dart';
import 'package:eapprove/utils/extension.dart';

class RequestRelationTicketHistoryModel {
  final String id; //Mã tờ trình
  final String code; //Mã tờ trình
  final String user; //Người trình phiếu
  final String name; //Tên tờ trình
  final StatusCode status; //Trạng thái tờ trình
  final String fromDate; //<PERSON><PERSON>y bắt đầu
  final String toDate; //Ngày kết thúc
  final String description; //Tên dịch vụ

  RequestRelationTicketHistoryModel({
    required this.id,
    required this.code,
    required this.user,
    required this.name,
    required this.status,
    required this.fromDate,
    required this.toDate,
    required this.description,
  });

  //Mapping dữ liệu,
  //inputFormat và outputFormat sẽ lấy theo format thời gian của hệ thống
  //outputFormat sẽ trả về format thời gian mong muốn
  factory RequestRelationTicketHistoryModel.mappingData(
      {code,
      user,
      name,
      required String status,
      fromDate,
      toDate,
      description,
      String? inputFormat,
      String? outputFormat,
      id}) {
    return RequestRelationTicketHistoryModel(
      id: id,
      code: code,
      user: user,
      name: name,
      status: StatusCode.fromCode(status),
      fromDate: fromDate!.toString().formatCustomDate(
          inputFormat: inputFormat ?? 'dd/MMM/yy hh:mm a',
          outputFormat: outputFormat ?? 'dd/MM/yy',
          locale: null,
          context: null,
          checkLocale: true),
      toDate: toDate!.toString().formatCustomDate(
          inputFormat: inputFormat ?? 'dd/MMM/yy hh:mm a',
          outputFormat: outputFormat ?? 'dd/MM/yy',
          locale: null,
          context: null,
          checkLocale: true),
      description: description,
    );
  }

  factory RequestRelationTicketHistoryModel.fromJson(
      Map<String, dynamic> json) {
    return RequestRelationTicketHistoryModel(
      id: json['id'],
      code: json['requestCode'],
      user: json['user'] ?? '',
      name: json['serviceName'] ?? '',
      status: json['status'] ?? '',
      fromDate: json['fromDate'] ?? '',
      toDate: json['toDate'] ?? '',
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'user': user,
      'name': name,
      'status': status,
      'fromDate': fromDate,
      'toDate': toDate,
      'description': description,
    };
  }

  @override
  String toString() {
    return 'RequestRelationTicketHistoryModel(code: $code, user: $user, name: $name, status: $status, fromDate: $fromDate, toDate: $toDate, description: $description)';
  }
}
