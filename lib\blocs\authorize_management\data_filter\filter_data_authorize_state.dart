import 'package:eapprove/models/authorize_management/filter_data_authorize_response.dart';

abstract class FilterDataAuthorizeState {}

class FilterDataAuthorizeInitial extends FilterDataAuthorizeState {}

class FilterDataAuthorizeLoading extends FilterDataAuthorizeState {}

class FilterDataAuthorizeLoaded extends FilterDataAuthorizeState {
  final FilterDataAuthorizeResponse filterData;

  FilterDataAuthorizeLoaded(this.filterData);
}

class FilterDataAuthorizeError extends FilterDataAuthorizeState {
  final String message;

  FilterDataAuthorizeError(this.message);
}
