import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eapprove/models/authorize_management/filter_data_authorize_response.dart';
import 'package:eapprove/repositories/filter_data_authorize_repository.dart';

// Events
abstract class FilterDataAuthorizeEvent {}

class LoadFilterDataAuthorize extends FilterDataAuthorizeEvent {
  final String search;
  final String sortBy;
  final String sortType;
  final String limit;
  final String page;
  final int totalPages;
  final int totalElements;
  final List<String> listAssignUser;
  final List<String> listAssignedUser;
  final List<int> status;
  final List<String> listCreatedUser;
  final List<String> listUpdatedUser;
  final List<Map<String, String>> listDateFilter;
  final String userLogin;

  LoadFilterDataAuthorize({
    this.search = '',
    this.sortBy = 'createdDate',
    this.sortType = 'DESC',
    this.limit = '10',
    this.page = '1',
    this.totalPages = 0,
    this.totalElements = 0,
    this.listAssignUser = const ['-1'],
    this.listAssignedUser = const ['-1'],
    this.status = const [-3, -2, -1, 0, 1, -4],
    this.listCreatedUser = const ['-1'],
    this.listUpdatedUser = const ['-1'],
    this.listDateFilter = const [
      {'fromDate': '', 'toDate': '', 'type': ''}
    ],
    this.userLogin = 'employee',
  });
}

// States
abstract class FilterDataAuthorizeState {}

class FilterDataAuthorizeInitial extends FilterDataAuthorizeState {}

class FilterDataAuthorizeLoading extends FilterDataAuthorizeState {}

class FilterDataAuthorizeLoaded extends FilterDataAuthorizeState {
  final FilterDataAuthorizeResponse filterData;

  FilterDataAuthorizeLoaded(this.filterData);
}

class FilterDataAuthorizeError extends FilterDataAuthorizeState {
  final String message;

  FilterDataAuthorizeError(this.message);
}

// Bloc
class FilterDataAuthorizeBloc
    extends Bloc<FilterDataAuthorizeEvent, FilterDataAuthorizeState> {
  final FilterDataAuthorizeRepository _repository;

  FilterDataAuthorizeBloc(this._repository)
      : super(FilterDataAuthorizeInitial()) {
    on<LoadFilterDataAuthorize>(_onLoadFilterData);
  }

  Future<void> _onLoadFilterData(
    LoadFilterDataAuthorize event,
    Emitter<FilterDataAuthorizeState> emit,
  ) async {
    try {
      emit(FilterDataAuthorizeLoading());

      final filterData = await _repository.getFilterData(
        search: event.search,
        sortBy: event.sortBy,
        sortType: event.sortType,
        limit: event.limit,
        page: event.page,
        totalPages: event.totalPages,
        totalElements: event.totalElements,
        listAssignUser: event.listAssignUser,
        listAssignedUser: event.listAssignedUser,
        status: event.status,
        listCreatedUser: event.listCreatedUser,
        listUpdatedUser: event.listUpdatedUser,
        listDateFilter: event.listDateFilter,
        userLogin: event.userLogin,
      );

      emit(FilterDataAuthorizeLoaded(filterData));
    } catch (e) {
      emit(FilterDataAuthorizeError(e.toString()));
    }
  }
}
