import 'dart:convert';
import 'package:eapprove/assets/StringIcon.dart';
import 'package:eapprove/blocs/account/user_list_bloc.dart';
import 'package:eapprove/blocs/business_process/bpm_proc_inst_bloc.dart';
import 'package:eapprove/blocs/business_process/check_type_bloc.dart';
import 'package:eapprove/blocs/business_process/print_file_template_bloc.dart';
import 'package:eapprove/blocs/chart/chart_bloc.dart';
import 'package:eapprove/blocs/dropdown/dropdown_bloc.dart';
import 'package:eapprove/blocs/form/form_bloc.dart';
import 'package:eapprove/blocs/navigation/bottom_nav_bloc.dart';
import 'package:eapprove/blocs/service/service_bloc.dart';
import 'package:eapprove/blocs/service/service_event.dart';
import 'package:eapprove/blocs/service/service_state.dart';
import 'package:eapprove/blocs/ticket_other_action/bloc/consultation_opinion_bloc.dart';
import 'package:eapprove/models/helper_model/service_child_item_model.dart';
import 'package:eapprove/models/service/service_data_model.dart';
import 'package:eapprove/screens/bottom_nav_screen.dart';
import 'package:eapprove/screens/common/error_handler.dart';
import 'package:eapprove/screens/payment_proposal/ticket_form_screen.dart';
import 'package:eapprove/screens/setting_screen.dart';
import 'package:eapprove/screens/ticket_service/service_search_screen.dart';
import 'package:eapprove/screens/ticket_service/widget/pdf_download_dialog.dart';
import 'package:eapprove/screens/ticket_service/widget/service_bottomsheet.dart';
import 'package:eapprove/services/navigation_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/app_constraint.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_sdk/widgets/custom_app_bar.dart';
import 'package:flutter_sdk/widgets/custom_background.dart';
import 'package:flutter_sdk/widgets/custom_expand_item.dart';
import 'package:flutter_sdk/widgets/custom_snack_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

class CreateTicketScreen extends StatefulWidget {
  final String title;
  static const String routeName = '/create_ticket';
  final VoidCallback? onBack;

  static Route route(String title) {
    return MaterialPageRoute<void>(
        builder: (_) => CreateTicketScreen(title: title));
  }

  const CreateTicketScreen({super.key, required this.title, this.onBack});

  @override
  State<CreateTicketScreen> createState() => CreateTicketScreenState();
}

// Spinner widget for pull-to-refresh
class _RefreshSpinner extends StatelessWidget {
  final double progress;
  final bool isLoading;
  final double size;

  const _RefreshSpinner({
    super.key,
    required this.progress,
    required this.isLoading,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    final Color blue = getColorSkin().primaryBlue;

    if (progress > 0.0) {
      // Show spinning animation only when user is pulling or refreshing via pull-to-refresh
      return SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          value: isLoading ? null : progress,
          valueColor: AlwaysStoppedAnimation<Color>(blue),
          backgroundColor: isLoading ? null : blue.withAlpha(40),
          strokeWidth: 3.5,
        ),
      );
    } else {
      return SizedBox(width: size, height: size);
    }
  }
}

class CreateTicketScreenState extends State<CreateTicketScreen>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  bool isSearchVisible = false;
  String searchTerm = '';
  bool hasPerformedSearch = false;
  final FocusNode _searchFocusNode = FocusNode();
  bool _hasSetupInitialFetch = false;
  Set<String> _expandedParentIds = {};
  late final AnimationController _refreshIconController;
  static const double _spinnerSizeMax = 30.0;
  static const double _spinnerSizeMin = 16.0;
  static const double _gapMax = 12.0;
  static const double _paddingMax = 12.0;
  static const double _gapItemMax = 4.0;
  static const double _pullThreshold = 100.0;

  double _pullDistance = 0.0;
  bool _isRefreshing = false;
  bool _isPulling = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _refreshIconController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Only set up the first fetch when dependencies are available
    if (!_hasSetupInitialFetch) {
      _hasSetupInitialFetch = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Only fetch if we're visible and it's the first time or the state is cleared
          final state = context.read<ServiceBloc>().state;
          if (state.status == ServiceStatus.initial) {
            _fetchData();
          }
        }
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchFocusNode.dispose();
    _refreshIconController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _fetchData() {
    if (!mounted) return;
    context.read<ServiceBloc>().add(const ServiceFetchRoot());
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
      _refreshIconController.repeat();
    });
    _fetchData();
    await Future.delayed(const Duration(milliseconds: 800));
    if (mounted) {
      setState(() {
        _isRefreshing = false;
        _pullDistance = 0.0;
        _refreshIconController.reset();
      });
    }
  }

  void _toggleSearch() {
    final currentBloc = BlocProvider.of<ServiceBloc>(context);
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: currentBloc,
          child: const ServiceSearchScreen(),
        ),
      ),
    )
        .then((_) {
      if (mounted) {
        _fetchData();
      }
    });
  }

  Future<void> _handleServiceTap(ServiceData serviceData) async {
    final allData = context.read<ServiceBloc>().state.serviceModels?.data ?? [];
    final hasChildren = allData.any((item) =>
        item.parentId == serviceData.id && item.notShowingMoblie == false);

    if (hasChildren) {
      showServiceBottomSheet(context, serviceData);
      return;
    }

    // Handle special flow case
    if (serviceData.specialFlow == true) {
      try {
        final specialService = await context
            .read<ServiceBloc>()
            .repository
            .getServiceSpecialByParentId(serviceData.id!);
        debugPrint('specialService: ${specialService.data.procDefId}');

        if (specialService.data != null) {
          // If the special service has a procDefId, navigate to the form
          if (specialService.data.procDefId != null) {
            final currentBloc = BlocProvider.of<FormBloc>(context);
            final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
            final printFileTemplateBloc =
                BlocProvider.of<PrintFileTemplateBloc>(context);
            final bpmProcInstBloc = BlocProvider.of<BpmProcInstBloc>(context);
            final dropdownBloc = BlocProvider.of<DropdownBloc>(context);
            final chartBloc = BlocProvider.of<ChartBloc>(context);
            final userListBloc = BlocProvider.of<UserListBloc>(context);

            context
                .read<BottomNavBloc>()
                .add(const SetBottomNavVisibility(false));
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MultiBlocProvider(
                    providers: [
                      BlocProvider.value(
                        value: currentBloc,
                      ),
                      BlocProvider.value(
                        value: checkTypeBloc,
                      ),
                      BlocProvider.value(
                        value: printFileTemplateBloc,
                      ),
                      BlocProvider.value(
                        value: bpmProcInstBloc,
                      ),
                      BlocProvider.value(
                        value: dropdownBloc,
                      ),
                      BlocProvider.value(
                        value: chartBloc,
                      ),
                      BlocProvider.value(
                        value: userListBloc,
                      ),
                    ],
                    child: TicketFormScreen(
                      procDefId: specialService.data.procDefId!,
                      title: specialService.data.serviceName ?? '',
                      processId: specialService.data.processId,
                      ticketId: specialService.data.id,
                    ),
                  ),
                )).then(
              (_) {
                context
                    .read<BottomNavBloc>()
                    .add(const SetBottomNavVisibility(true));
              },
            );
          } else {
            // If no procDefId, show the special service in bottom sheet
            showServiceBottomSheet(context, specialService.data);
          }
          return;
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading special services: $e'),
              backgroundColor: getColorSkin().red,
            ),
          );
        }
        return;
      }
    }

    // Handle different service types
    switch (serviceData.serviceType.toString()) {
      case "3": // URL type
        if (serviceData.url != null && serviceData.url!.isNotEmpty) {
          try {
            final uri = Uri.parse(serviceData.url!);
            await launchUrl(
              uri,
              mode: LaunchMode.externalApplication,
            );
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Invalid URL format: ${serviceData.url}'),
                  backgroundColor: getColorSkin().red,
                ),
              );
            }
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('No URL provided for this service'),
                backgroundColor: getColorSkin().red,
              ),
            );
          }
        }
        break;

      case "4": // PDF type
        if (serviceData.url != null && serviceData.url!.isNotEmpty) {
          context.read<ServiceBloc>().add(const ClearPdfStatus());
          context.read<ServiceBloc>().add(DownloadPdf(serviceData.url!));
        } else {
          if (mounted) {
            ErrorHandler.showErrorSnackBar(
                context, 'No PDF URL provided for this service');
          }
        }
        break;
      default:
        if (serviceData.procDefId != null) {
          final currentBloc = BlocProvider.of<FormBloc>(context);
          final checkTypeBloc = BlocProvider.of<CheckTypeBloc>(context);
          final printFileTemplateBloc =
              BlocProvider.of<PrintFileTemplateBloc>(context);
          final bpmProcInstBloc = BlocProvider.of<BpmProcInstBloc>(context);
          final dropdownBloc = BlocProvider.of<DropdownBloc>(context);
          final chartBloc = BlocProvider.of<ChartBloc>(context);
          final userListBloc = BlocProvider.of<UserListBloc>(context);

          context
              .read<BottomNavBloc>()
              .add(const SetBottomNavVisibility(false));

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TicketFormScreen(
                procDefId: serviceData.procDefId!,
                title: serviceData.serviceName ?? '',
                processId: serviceData.processId,
                ticketId: serviceData.id,
              ),
            ),
          ).then((_) {
            context
                .read<BottomNavBloc>()
                .add(const SetBottomNavVisibility(true));
          });

          // Navigator.push(
          //     context,
          //     MaterialPageRoute(
          //       builder: (context) => MultiBlocProvider(
          //         providers: [
          //           BlocProvider.value(
          //             value: currentBloc,
          //           ),
          //           BlocProvider.value(
          //             value: checkTypeBloc,
          //           ),
          //           BlocProvider.value(
          //             value: printFileTemplateBloc,
          //           ),
          //           BlocProvider.value(
          //             value: bpmProcInstBloc,
          //           ),
          //           BlocProvider.value(
          //             value: dropdownBloc,
          //           ),
          //           BlocProvider.value(
          //             value: chartBloc,
          //           ),
          //           BlocProvider.value(
          //             value: userListBloc,
          //           ),
          //         ],
          //         child: TicketFormScreen(
          //           procDefId: serviceData.procDefId!,
          //           title: serviceData.serviceName ?? '',
          //           processId: serviceData.processId,
          //           ticketId: serviceData.id,
          //         ),
          //       ),
          //     )).then(
          //   (_) {
          //     context
          //         .read<BottomNavBloc>()
          //         .add(const SetBottomNavVisibility(true));
          //   },
          // );
        }
    }
  }

  double get _progress =>
      _isRefreshing ? 1.0 : (_pullDistance / _pullThreshold).clamp(0.0, 1.0);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ServiceBloc, ServiceState>(
      buildWhen: (previous, current) {
        return previous.status != current.status ||
            previous.serviceModels != current.serviceModels ||
            previous.errorMessage != current.errorMessage ||
            previous.pdfFilePath != current.pdfFilePath ||
            previous.pdfDownloadStatus != current.pdfDownloadStatus;
      },
      builder: (context, state) {
        if (state.pdfDownloadStatus == PdfDownloadStatus.success &&
            state.pdfFilePath != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            SnackbarCore.success('Tải PDF thành công');
            context.read<ServiceBloc>().add(const ClearPdfStatus());
          });
        } else if (state.pdfDownloadStatus == PdfDownloadStatus.failure &&
            state.errorMessage != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            SnackbarCore.error('Tải PDF thất bại');
            context.read<ServiceBloc>().add(const ClearPdfStatus());
          });
        }

        final bool isBlocLoading = state.status == ServiceStatus.loading ||
            state.status == ServiceStatus.initial;
        final bool showSpinner = _progress > 0;
        final double spinnerProgress = _progress;
        final double spinnerHeight = _progress * 60.0;
        final double spinnerSize = _spinnerSizeMin +
            (_spinnerSizeMax - _spinnerSizeMin) * spinnerProgress;

        return Stack(
          children: [
            GradientBackground(
              child: Scaffold(
                backgroundColor: getColorSkin().transparent,
                appBar: CustomAppBar(
                  titleTextStyle: getTypoSkin()
                      .medium20
                      .copyWith(color: getColorSkin().ink1),
                  leading: IconButton(
                    icon: SvgPicture.asset(StringImage.ic_arrow_left),
                    onPressed: () {
                     context.read<BottomNavBloc>().add(const SwitchTab(0));
                    },
                  ),
                  titleSpacing: -10.w,
                  title: widget.title,
                  textColor: getColorSkin().black,
                  actionsPadding: EdgeInsets.zero,
                  actions: [
                    Transform.translate(
                      offset: const Offset(10, 0),
                      child: IconButton(
                        iconSize: 34.w.h,
                        highlightColor: Colors.transparent,
                        icon: SvgPicture.asset(StringImage.ic_search),
                        onPressed: _toggleSearch,
                      ),
                    ),
                    IconButton(
                      iconSize: 40.w.h,
                      highlightColor: Colors.transparent,
                      icon: SvgPicture.asset(StringImage.ic_user),
                      onPressed: () {
                        // Ẩn bottom navigation bar trước khi điều hướng
                        context
                            .read<BottomNavBloc>()
                            .add(const SetBottomNavVisibility(false));

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const EAppSettingScreen(),
                          ),
                        ).then((_) {
                          // Hiển thị lại bottom navigation bar khi quay lại
                          context
                              .read<BottomNavBloc>()
                              .add(const SetBottomNavVisibility(true));
                        });
                      },
                    ),
                  ],
                  backgroundColor: Colors.transparent,
                ),
                body: NotificationListener<ScrollNotification>(
                  onNotification: (notification) {
                    if (_isRefreshing || isBlocLoading) return false;
                    if (notification is OverscrollNotification &&
                        notification.overscroll < 0 &&
                        _scrollController.position.pixels <= 0) {
                      setState(() {
                        _isPulling = true;
                        _pullDistance -= notification.overscroll;
                      });
                    } else if (notification is ScrollEndNotification ||
                        notification is ScrollUpdateNotification &&
                            notification.dragDetails == null) {
                      if (_isPulling) {
                        if (_pullDistance >= _pullThreshold) {
                          _handleRefresh();
                        } else {
                          setState(() {
                            _pullDistance = 0.0;
                          });
                        }
                        _isPulling = false;
                      }
                    } else if (notification is ScrollUpdateNotification &&
                        notification.dragDetails != null &&
                        notification.metrics.pixels > 0) {
                      // If user scrolls up, reset pull
                      if (_pullDistance != 0.0) {
                        setState(() {
                          _pullDistance = 0.0;
                        });
                      }
                    }
                    return false;
                  },
                  child: Column(
                    children: [
                      if (!isBlocLoading) ...[
                        // Spinner header
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 120),
                          switchInCurve: Curves.ease,
                          switchOutCurve: Curves.ease,
                          child: showSpinner
                              ? SizedBox(
                                  key: const ValueKey('spinner'),
                                  height: spinnerHeight,
                                  child: Center(
                                    child: AnimatedContainer(
                                      duration:
                                          const Duration(milliseconds: 120),
                                      curve: Curves.ease,
                                      width: spinnerSize,
                                      height: spinnerSize,
                                      child: _RefreshSpinner(
                                        progress: spinnerProgress,
                                        isLoading:
                                            isBlocLoading || _isRefreshing,
                                        size: spinnerSize,
                                      ),
                                    ),
                                  ),
                                )
                              : const SizedBox(
                                  key: ValueKey('empty'),
                                  height: 0,
                                ),
                        ),
                        // Gap below spinner
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 120),
                          curve: Curves.ease,
                          height: showSpinner ? _gapMax * spinnerProgress : 0.0,
                        ),
                      ],
                      // Main content with proper state handling
                      Expanded(
                        child: _buildBody(context, state),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (state.pdfDownloadStatus == PdfDownloadStatus.downloading)
              GestureDetector(
                onTap: () {
                  context.read<ServiceBloc>().add(const CancelDownload());
                  SnackbarCore.error('Tải PDF đã bị hủy');
                },
                child: Container(
                  color: Colors.black.withOpacity(0.1),
                  child: Center(
                    child: GestureDetector(
                      onTap: () {},
                      child: const PdfDownloadDialog(
                        message: 'Đang tải PDF...',
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  List<ServiceData> _getParentItems(ServiceState state) {
    final allData = state.serviceModels?.data ?? [];
    final parentItems = allData
        .where(
            (item) => item.parentId == null && item.notShowingMoblie == false)
        .toList();
    parentItems.sort(
        (a, b) => (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));
    return parentItems;
  }

  Widget _buildBody(BuildContext context, ServiceState state) {
    // Handle error state
    if (state.status == ServiceStatus.failure) {
      return ErrorHandler.wrapWithErrorHandling(
        hasError: true,
        onRetry: _handleRefresh,
        child: const SizedBox.shrink(),
      );
    }

    // Handle loading state
    if (state.status == ServiceStatus.loading ||
        state.status == ServiceStatus.initial) {
      return Center(child: AppConstraint.buildLoading(context));
    }

    final allData = state.serviceModels?.data ?? [];
    final parentItems = _getParentItems(state);

    // Handle empty search results
    if (parentItems.isEmpty &&
        state.searchKey != null &&
        state.searchKey!.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              StringImage.ic_no_result,
              width: 100.w,
              height: 100.h,
            ),
            SizedBox(height: 16.h),
            Text(
              "Không có dịch vụ nào á",
              style: getTypoSkin().medium16.copyWith(
                    color: getColorSkin().ink1,
                  ),
            ),
          ],
        ),
      );
    }

    // Main content with pull-to-refresh
    return AnimatedPadding(
      duration: const Duration(milliseconds: 120),
      curve: Curves.ease,
      padding: EdgeInsets.symmetric(horizontal: _paddingMax * _progress),
      child: ListView.separated(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: parentItems.length,
        separatorBuilder: (context, index) => AnimatedContainer(
          duration: const Duration(milliseconds: 120),
          curve: Curves.ease,
          height: _gapItemMax * _progress,
        ),
        itemBuilder: (context, index) {
          final parent = parentItems[index];
          final directChildren = allData
              .where((child) =>
                  child.parentId == parent.id &&
                  child.notShowingMoblie == false)
              .toList()
            ..sort((a, b) =>
                (a.positionPackage ?? 0).compareTo(b.positionPackage ?? 0));

          final childrenItems = directChildren.map((child) {
            final hasChildren = allData.any((item) =>
                item.parentId == child.id && child.notShowingMoblie == false);

            return TicketChildItem(
              title: child.serviceName ?? '',
              procDefId: child.procDefId,
              hasChevron: hasChildren,
              serviceType: child.serviceType,
              url: child.url,
            );
          }).toList();

          return CustomExpandedList<TicketChildItem>(
            key: ValueKey(parent.id),
            isBuildLeading: parent.icon != null && parent.icon!.isNotEmpty,
            childrenPadding: EdgeInsets.only(left: 25.w),
            title: parent.serviceName ?? '',
            imageNetwork: parent.icon,
            isExpanded: _expandedParentIds.contains(parent.id),
            children: childrenItems,
            cardMargin: EdgeInsets.zero,
            getTitle: (child) => child.title,
            hasChevron: (child) => child.hasChevron,
            onChildTap: (child) {
              final serviceData = directChildren.firstWhere(
                (item) =>
                    item.serviceName == child.title &&
                    item.notShowingMoblie == false,
                orElse: () => const ServiceData(),
              );

              _handleServiceTap(serviceData);
            },
          );
        },
      ),
    );
  }

  void showServiceBottomSheet(BuildContext context, ServiceData serviceData) {
    final allServices =
        context.read<ServiceBloc>().state.serviceModels?.data ?? [];
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => BlocProvider.value(
        value: context.read<ServiceBloc>(),
        child: ServiceBottomSheet(
          initialServiceData: serviceData,
          allServices: allServices,
        ),
      ),
    );
  }
}
