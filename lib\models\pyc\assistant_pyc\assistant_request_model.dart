import 'package:eapprove/models/pyc/assistant_pyc/date_filter_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

class AssisRequestModel {
  final int? page;
  final int? totalPages;
  final int? totalElements;
  final int? limit;
  final String? search;
  final String? ticketStatus;
  final String? sortBy;
  final String? sortType;
  final List<String>? listUser;
  final List<String>? listStatus;
  final String? pageType;
  final String? fromDate;
  final String? toDate;
  final List<DateFilterModel>? listDateFilter;
  final List<String>? listTicketStartUserId;
  final List<String>? listCompanyName;
  final List<String>? listCompanyCode;
  final List<String>? listChartNodeName;
  final List<String>? listProcTitle;
  final String? assistantEmail;
  final int? serviceId;

  AssisRequestModel({
    this.page,
    this.totalPages,
    this.totalElements,
    this.limit,
    this.search,
    this.ticketStatus,
    this.sortBy,
    this.sortType,
    this.listUser,
    this.listStatus,
    this.pageType,
    this.fromDate,
    this.toDate,
    this.listDateFilter,
    this.listTicketStartUserId,
    this.listCompanyName,
    this.listCompanyCode,
    this.listChartNodeName,
    this.listProcTitle,
    this.assistantEmail,
    this.serviceId,
  });

  factory AssisRequestModel.fromCategory(AssisPycTicketCategory category,
      {int page = 1, int limit = 5, Map<String, dynamic>? additionalParams}) {
    final data = assisPycTicketStatusMapping[category]!;
    Box box = Hive.box('authentication');
    final username = box.get('username', defaultValue: '');
    final requestData = {
      "ticketStatus": data["ticketStatus"],
      "listStatus": List<String>.from(data["listStatus"]),
      "page": page,
      "totalPages": 0,
      "totalElements": 0,
      "limit": limit,
      "search": "",
      "sortBy": "ticketCreatedTime",
      "sortType": "DESC",
      "listUser": ["-1"],
      "pageType": "ticket",
      "fromDate": "",
      "toDate": "",
      "listDateFilter": [
        {"type": "", "fromDate": "", "toDate": ""}
      ],
      "listTicketStartUserId": ["-1"],
      "listCompanyName": ["-1"],
      "listCompanyCode": ["-1"],
      "listChartNodeName": ["-1"],
      "listProcTitle": ["-1"],
      "assistantEmail": username
    };
    return AssisRequestModel(
      search: requestData["search"],
      page: requestData["page"],
      limit: requestData["limit"],
      ticketStatus: requestData["ticketStatus"],
      sortBy: requestData["sortBy"],
      totalElements: requestData["totalElements"],
      totalPages: requestData["totalPages"],
      sortType: requestData["sortType"],
      listUser: requestData["listUser"],
      listStatus: requestData["listStatus"],
      pageType: requestData["pageType"],
      fromDate: requestData["fromDate"],
      toDate: requestData["toDate"],
      listDateFilter: List<DateFilterModel>.from(requestData["listDateFilter"].map((x) => DateFilterModel.fromJson(x))),
      listTicketStartUserId: requestData["listTicketStartUserId"],
      listCompanyName: requestData["listCompanyName"],
      listCompanyCode: requestData["listCompanyCode"],
      listChartNodeName: requestData["listChartNodeName"],
      listProcTitle: requestData["listProcTitle"],
      assistantEmail: requestData["assistantEmail"],
      serviceId: additionalParams?["serviceId"],
    );
  }

  AssisRequestModel copyWith({
    int? page,
    int? totalPages,
    int? totalElements,
    int? limit,
    String? search,
    String? ticketStatus,
    String? sortBy,
    String? sortType,
    List<String>? listUser,
    List<String>? listStatus,
    String? pageType,
    String? fromDate,
    String? toDate,
    List<DateFilterModel>? listDateFilter,
    List<String>? listTicketStartUserId,
    List<String>? listCompanyName,
    List<String>? listCompanyCode,
    List<String>? listChartNodeName,
    List<String>? listProcTitle,
    String? assistantEmail,
    int? serviceId,
  }) {
    return AssisRequestModel(
      page: page ?? this.page,
      totalPages: totalPages ?? this.totalPages,
      totalElements: totalElements ?? this.totalElements,
      limit: limit ?? this.limit,
      search: search ?? this.search,
      ticketStatus: ticketStatus ?? this.ticketStatus,
      sortBy: sortBy ?? this.sortBy,
      sortType: sortType ?? this.sortType,
      listUser: listUser ?? this.listUser,
      listStatus: listStatus ?? this.listStatus,
      pageType: pageType ?? this.pageType,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      listDateFilter: listDateFilter ?? this.listDateFilter,
      listTicketStartUserId: listTicketStartUserId ?? this.listTicketStartUserId,
      listCompanyName: listCompanyName ?? this.listCompanyName,
      listCompanyCode: listCompanyCode ?? this.listCompanyCode,
      listChartNodeName: listChartNodeName ?? this.listChartNodeName,
      listProcTitle: listProcTitle ?? this.listProcTitle,
      assistantEmail: assistantEmail ?? this.assistantEmail,
      serviceId: serviceId ?? this.serviceId,
    );
  }

  Map<String, dynamic> toJson() => {
        "page": page,
        "totalPages": totalPages,
        "totalElements": totalElements,
        "limit": limit,
        "search": search,
        "ticketStatus": ticketStatus,
        "sortBy": sortBy,
        "sortType": sortType,
        "listUser": listUser,
        "listStatus": listStatus,
        "pageType": pageType,
        "fromDate": fromDate,
        "toDate": toDate,
        "listDateFilter": listDateFilter,
        "listTicketStartUserId": listTicketStartUserId,
        "listCompanyName": listCompanyName,
        "listCompanyCode": listCompanyCode,
        "listChartNodeName": listChartNodeName,
        "listProcTitle": listProcTitle,
        "assistantEmail": assistantEmail,
        "serviceId": serviceId,
      };
}

enum AssisPycTicketCategory { execution, completed, canceled, sharedFollowing, search }

Map<AssisPycTicketCategory, Map<String, dynamic>> assisPycTicketStatusMapping = {
  AssisPycTicketCategory.execution: {
    "ticketStatus": "ONGOING",
    "listStatus": ["-1", "PROCESSING", "OPENED", "DELETED_BY_RU"],
  },
  AssisPycTicketCategory.completed: {
    "ticketStatus": "COMPLETED",
    "listStatus": ["COMPLETED", "CLOSED", "-1"],
  },
  AssisPycTicketCategory.canceled: {
    "ticketStatus": "CANCEL",
    "listStatus": ["CANCEL", "-1"],
  },
  AssisPycTicketCategory.sharedFollowing: {
    "ticketStatus": "SHARED",
    "listStatus": [
      "COMPLETED",
      "RECALLED",
      "RECALLING",
      "DELETED_BY_RU",
      "PROCESSING",
      "ADDITIONAL_REQUEST",
      "OPENED",
      "-1",
      "CLOSED",
      "FOLLOWED",
      "SHARED"
    ],
  },
  AssisPycTicketCategory.search: {
    "ticketStatus": "SEARCH",
    "listStatus": [
      "COMPLETED",
      "CANCEL",
      "RECALLED",
      "RECALLING",
      "DELETED_BY_RU",
      "PROCESSING",
      "ADDITIONAL_REQUEST",
      "OPENED",
      "-1",
      "CLOSED",
      "FOLLOWED",
      "SHARED"
    ],
  }
};
