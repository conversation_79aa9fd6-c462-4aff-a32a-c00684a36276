import 'package:flutter/material.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomBottomSheetTest<T> extends StatefulWidget {
  final String? title;
  final Widget? children;
  final Widget? btnBottom;
  final bool isScrollControlled;
  final Color? backgroundColor;
  final bool isDismissible;
  final bool enableDrag;
  final double? height;
  final double? maxHeight;
  final ShapeBorder? shape;
  final List<T>? menuItems;
  final Function(T)? onMenuItemTap;
  final Widget Function(T item, Function(T)? onTap)? itemBuilder;
  final bool showDividers;
  final IconButton? closeIcon;
  final bool useRootNavigator;
  final TextStyle? titleStyle;
  final TextAlign? titleAlign;
  final EdgeInsets? padding;
  final bool closeIconAfterTitle;
  final EdgeInsets? titlePadding;
  final bool useSafeArea;
  // New parameters for search functionality
  final bool showSearchBar;
  final String searchHintText;
  final TextStyle? searchHintStyle;
  final InputDecoration? searchDecoration;
  // Search callbacks
  final Function(String)? onSearchChanged;
  final Function(String)? onSearchSubmitted;
  final TextEditingController? searchController;

  const CustomBottomSheetTest({
    super.key,
    this.title,
    this.children,
    this.btnBottom,
    this.isScrollControlled = true,
    this.backgroundColor,
    this.isDismissible = true,
    this.enableDrag = true,
    this.height,
    this.maxHeight,
    this.shape,
    this.menuItems,
    this.onMenuItemTap,
    this.itemBuilder,
    this.showDividers = true,
    this.closeIcon,
    this.useRootNavigator = true,
    this.titleStyle,
    this.titleAlign,
    this.padding,
    this.closeIconAfterTitle = false,
    this.titlePadding,
    this.useSafeArea = true,
    // Initialize search parameters
    this.showSearchBar = false,
    this.searchHintText = 'Tìm kiếm tên dịch vụ',
    this.searchHintStyle,
    this.searchDecoration,
    // Search callbacks
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.searchController,
  });

  static Future<dynamic> show<T>({
    required BuildContext context,
    String? title,
    Widget? children,
    Widget? btnBottom,
    bool isScrollControlled = true,
    Color? backgroundColor,
    bool isDismissible = true,
    bool enableDrag = true,
    double? height,
    double? maxHeight,
    ShapeBorder? shape,
    List<T>? menuItems,
    Function(T)? onMenuItemTap,
    Widget Function(T item, Function(T)? onTap)? itemBuilder,
    bool showDividers = true,
    IconButton? closeIcon,
    EdgeInsets? padding,
    TextStyle? titleStyle,
    TextAlign? titleAlign,
    bool useRootNavigator = true,
    bool closeIconAfterTitle = false,
    EdgeInsets? titlePadding,
    bool useSafeArea = true,
    // Add search parameters to the show method
    bool showSearchBar = false,
    String searchHintText = 'Tìm kiếm tên dịch vụ',
    TextStyle? searchHintStyle,
    InputDecoration? searchDecoration,
    // Search callbacks
    Function(String)? onSearchChanged,
    Function(String)? onSearchSubmitted,
    TextEditingController? searchController,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      useRootNavigator: useRootNavigator,
      useSafeArea: useSafeArea,
      barrierColor: const Color(0xff000810).withOpacity(0.7),
      backgroundColor: backgroundColor ?? getColorSkin().white,
      shape: shape ?? const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      constraints: maxHeight != null
          ? BoxConstraints(maxHeight: maxHeight)
          : null,
      builder: (context) {
        return CustomBottomSheetTest<T>(
          title: title,
          btnBottom: btnBottom,
          backgroundColor: backgroundColor,
          isDismissible: isDismissible,
          enableDrag: enableDrag,
          height: height,
          maxHeight: maxHeight,
          menuItems: menuItems,
          onMenuItemTap: onMenuItemTap,
          itemBuilder: itemBuilder,
          showDividers: showDividers,
          closeIcon: closeIcon,
          padding: padding,
          useRootNavigator: useRootNavigator,
          titleStyle: titleStyle,
          titleAlign: titleAlign,
          closeIconAfterTitle: closeIconAfterTitle,
          titlePadding: titlePadding,
          useSafeArea: useSafeArea,
          children: children,
          // Pass search parameters
          showSearchBar: showSearchBar,
          searchHintText: searchHintText,
          searchHintStyle: searchHintStyle,
          searchDecoration: searchDecoration,
          onSearchChanged: onSearchChanged,
          onSearchSubmitted: onSearchSubmitted,
          searchController: searchController,
        );
      },
    );
  }

  @override
  State<CustomBottomSheetTest<T>> createState() => _CustomBottomSheetTestState<T>();
}

class _CustomBottomSheetTestState<T> extends State<CustomBottomSheetTest<T>> {
  // Search functionality
  late TextEditingController _searchController;
  String _searchQuery = '';
  List<T>? _filteredItems;

  @override
  void initState() {
    super.initState();
    // Use provided controller or create a new one
    _searchController = widget.searchController ?? TextEditingController();
    _filteredItems = widget.menuItems;

    // Add listener for search text changes
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    // Only dispose if we created the controller
    if (widget.searchController == null) {
      _searchController.removeListener(_onSearchChanged);
      _searchController.dispose();
    } else {
      _searchController.removeListener(_onSearchChanged);
    }
    super.dispose();
  }

  // Filter items based on search query
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;

      // Call the external search changed callback if provided
      if (widget.onSearchChanged != null) {
        widget.onSearchChanged!(_searchQuery);
      }

      // Update filtered items
      if (_searchQuery.isEmpty) {
        _filteredItems = widget.menuItems;
      } else {
        _filteredItems = widget.menuItems?.where((item) {
          // If item is a ServiceItem, search in title
          if (item is ServiceItem) {
            return item.title.toLowerCase().contains(_searchQuery.toLowerCase());
          }
          // Otherwise search in string representation
          return item.toString().toLowerCase().contains(_searchQuery.toLowerCase());
        }).toList();
      }
    });
  }

  // Build search bar widget
  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: getColorSkin().grey4Background,
        borderRadius: BorderRadius.circular(24),
      ),
      child: TextField(
        controller: _searchController,
        decoration: widget.searchDecoration ?? InputDecoration(
          hintText: widget.searchHintText,
          hintStyle: widget.searchHintStyle ?? getTypoSkin().regular12.copyWith(color: getColorSkin().grey2),
          border: InputBorder.none,
          prefixIcon: Icon(Icons.search, color: getColorSkin().grey2),
          contentPadding: EdgeInsets.symmetric(vertical: 12.h),
        ),
        style: getTypoSkin().regular12,
        onSubmitted: (value) {
          // Call the external search submitted callback if provided
          if (widget.onSearchSubmitted != null) {
            widget.onSearchSubmitted!(value);
          }
        },
      ),
    );
  }

  // Build default menu item
  Widget _buildDefaultMenuItem(T item, Function(T)? onTap) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Text(
        item.toString(),
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Widget content = Container(
      padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      constraints: widget.maxHeight != null
          ? BoxConstraints(maxHeight: widget.maxHeight!)
          : null,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Drag handle
          if (widget.enableDrag)
            Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: getColorSkin().grey4Background,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),

          // Title section
          if (widget.title != null) ...[
            Row(
              children: [
                if (widget.closeIcon != null && !widget.closeIconAfterTitle)
                  IconButton(
                    icon: widget.closeIcon!.icon,
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                Expanded(
                  child: Padding(
                    padding: widget.titlePadding ?? EdgeInsets.zero,
                    child: Text(
                      widget.title ?? '',
                      style: widget.titleStyle ?? getTypoSkin().medium16.copyWith(color: getColorSkin().ink1),
                      textAlign: widget.titleAlign ?? TextAlign.center,
                    ),
                  ),
                ),
                if (widget.closeIcon != null && widget.closeIconAfterTitle)
                  IconButton(
                    icon: widget.closeIcon!.icon,
                    onPressed: () => Navigator.of(context).pop(),
                  ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Search bar
          if (widget.showSearchBar)
            _buildSearchBar(),

          // Menu items or children
          if (widget.menuItems != null)
            Flexible(
              child: _filteredItems?.isEmpty ?? true
                  ? Center(
                child: Text(
                  'Không tìm thấy kết quả',
                  style: getTypoSkin().regular12.copyWith(color: getColorSkin().grey2),
                ),
              )
                  : ListView.separated(
                shrinkWrap: true,
                itemCount: _filteredItems!.length,
                separatorBuilder: (context, index) => widget.showDividers
                    ? Divider(color: Colors.grey.shade300)
                    : const SizedBox.shrink(),
                itemBuilder: (context, index) =>
                    GestureDetector(
                      onTap: () => widget.onMenuItemTap?.call(_filteredItems![index]),
                      child: widget.itemBuilder?.call(_filteredItems![index], widget.onMenuItemTap) ??
                          _buildDefaultMenuItem(_filteredItems![index], widget.onMenuItemTap),
                    ),
              ),
            )
          else if (widget.children != null)
            Flexible(child: widget.children!),

          // Bottom button
          if (widget.btnBottom != null) ...[
            const SizedBox(height: 16),
            widget.btnBottom!,
          ],
        ],
      ),
    );

    return widget.useSafeArea ? SafeArea(child: content) : content;
  }
}

class ServiceItem {
  final String title;
  final String? iconPath;
  final bool hasChevron;
  final List<ServiceItem>? children;
  bool isExpanded;

  ServiceItem({
    required this.title,
    this.iconPath,
    this.hasChevron = false,
    this.children,
    this.isExpanded = false,
  });

  @override
  String toString() => title;
}