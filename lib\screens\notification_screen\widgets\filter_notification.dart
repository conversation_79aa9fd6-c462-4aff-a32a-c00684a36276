import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sdk/utils/theme_utils.dart';

class NotificationFilterSheet extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilterSheet({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: 15.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 32.w,
              height: 4.h,
              decoration: BoxDecoration(color: const Color(0xFFE8E8E8), borderRadius: BorderRadius.circular(2.r)),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context), // This will close the bottom sheet
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                Column(
                  children: [
                    Text("Trạng thái thông báo", style: getTypoSkin().title3Medium),
                  ],
                ),
                Column(
                  children: [
                    SizedBox(width: 40.w),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10.h),
            Container(
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Colors.grey, width: 0.5),
                    bottom: BorderSide(color: Colors.grey, width: 0.5),
                  ),
                ),
                child: _buildRadioOption(context, "Tất cả", 'all')),
            Container(
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey, width: 0.5),
                  ),
                ),
                child: _buildRadioOption(context, "Đã đọc", 'read')),
            Container(
                margin: EdgeInsets.only(bottom: 10.h),
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey, width: 0.5),
                  ),
                ),
                child: _buildRadioOption(context, "Chưa đọc", 'unread')),
          ],
        ),
      ),
    );
  }

  Widget _buildRadioOption(BuildContext context, String label, String value) {
    return InkWell(
      onTap: () {
        onFilterChanged(value); // Apply filter
        Navigator.pop(context); // Close bottom sheet
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 15.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // Label left, Radio right
          children: [
            // Text label
            Text(
              label,
              style: getTypoSkin().title4Regular.copyWith(color: getColorSkin().ink1),
            ),

            // Custom Radio button with blue color
            Radio<String>(
              value: value,
              groupValue: selectedFilter,
              activeColor: Colors.blue, // Make selected radio blue
              onChanged: (val) {
                if (val != null) {
                  onFilterChanged(val);
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
