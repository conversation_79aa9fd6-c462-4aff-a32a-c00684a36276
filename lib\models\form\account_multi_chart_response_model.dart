class AccountMultiChartResponse {
  final int code;
  final String message;
  final List<AccountMultiChartData> data;

  AccountMultiChartResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory AccountMultiChartResponse.fromJson(Map<String, dynamic> json) {
    return AccountMultiChartResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      data: (json['data'] as List)
          .map((e) => AccountMultiChartData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class AccountMultiChartData {
  final int id;
  final String firstname;
  final String lastname;
  final String chartName;
  final int chartId;
  final String email;
  final String username;
  final String shortName;
  final String chartNodeName;

  AccountMultiChartData({
    required this.id,
    required this.firstname,
    required this.lastname,
    required this.chartName,
    required this.chartId,
    required this.email,
    required this.username,
    required this.shortName,
    required this.chartNodeName,
  });

  factory AccountMultiChartData.fromJson(Map<String, dynamic> json) {
    return AccountMultiChartData(
      id: json['id'] as int,
      firstname: json['firstname'] as String,
      lastname: json['lastname'] as String,
      chartName: json['chartName'] as String,
      chartId: json['chartId'] as int,
      email: json['email'] as String,
      username: json['username'] as String,
      shortName: json['shortName'] as String,
      chartNodeName: json['chartNodeName'] as String,
    );
  }
} 