name: eapprove
description: "A new Flutter module project."

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter
  flutter_sdk:
    # path: ../flutter_sdk
    git:
      url: "https://sdk:<EMAIL>/ddc/template/flutter_sdk.git"
      ref: "dev"
  http: ^1.3.0
  flutter_riverpod: ^2.4.9
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  flutter_screenutil: ^5.9.3
  file_picker: ^9.0.1
  flutter_pdfview: ^1.4.0
  path_provider: ^2.1.5
  bot_toast: ^4.1.3
  shared_preferences: ^2.4.0
  flutter_bloc: ^9.0.0
  equatable: ^2.0.7
  logger: ^2.5.0
  intl: ^0.20.2
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.2.4
  http_interceptor: ^2.0.0
  connectivity_plus: ^6.1.3
  go_router: ^14.8.1
  url_launcher: ^6.3.1
  open_file: ^3.5.10
  permission_handler: ^11.4.0
  device_info_plus: 11.3.0
  share_plus: ^10.1.4
  flutter_slidable: ^4.0.0
  expressions: ^0.2.5+2
  collection: ^1.17.0
  flutter_html: ^3.0.0
  flutter_localizations:
    sdk: flutter
  syncfusion_flutter_pdfviewer: ^29.1.40
  webview_flutter: ^4.13.0
  dio: ^5.8.0+1
  math_expressions: ^2.1.0
  flutter_mentions: ^2.0.1
  extended_text_field: ^16.0.2
  flutter_debug_helper: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add Flutter specific assets to your application, add an assets section,
  # like this:
  assets:
    - lib/assets/images/
    - lib/assets/icon/
    - lib/assets/svg/
    - lib/assets/test.pdf
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.example.eapprove
    iosBundleIdentifier: com.example.eapprove
file_preview:
  git:
    url: git://github.com/aliyoge/flutter_file_preview.git
